import { ConsultationType } from "@/types/doctor"

export const CONSULTATION_TYPES: ConsultationType[] = [
  {
    name: "Consulta General",
    availableOnline: false,
    onlineBookingHoursByDay: {},
    requiresMedicalOrder: false,
    duration: "default",
    dailyLimit: "unlimited",
    basePrice: 0,
    copays: [],
    excludedCoverages: [],
    acceptsPrivatePay: true,
  },
  {
    name: "Primera Consulta",
    availableOnline: true,
    onlineBookingHoursByDay: {},
    requiresMedicalOrder: false,
    duration: "default",
    dailyLimit: "unlimited",
    basePrice: 0,
    copays: [],
    excludedCoverages: [],
    acceptsPrivatePay: true,
  },
  {
    name: "Consulta Consecutiva",
    availableOnline: true,
    onlineBookingHoursByDay: {},
    requiresMedicalOrder: false,
    duration: "default",
    dailyLimit: "unlimited",
    basePrice: 0,
    copays: [],
    excludedCoverages: [],
    acceptsPrivatePay: true,
  },
  {
    name: "Electrocardiogram<PERSON>",
    availableOnline: false,
    onlineBookingHoursByDay: {},
    requiresMedicalOrder: true,
    duration: "2slots",
    dailyLimit: 5,
    basePrice: 0,
    copays: [],
    excludedCoverages: [],
    acceptsPrivatePay: true,
  },
  {
    name: "Urgencia",
    availableOnline: false,
    onlineBookingHoursByDay: {},
    requiresMedicalOrder: true,
    duration: "2slots",
    dailyLimit: 3,
    basePrice: 0,
    copays: [],
    excludedCoverages: [],
    acceptsPrivatePay: true,
  },
  {
    name: "Consulta Virtual",
    availableOnline: true,
    onlineBookingHoursByDay: {},
    requiresMedicalOrder: false,
    duration: "default",
    dailyLimit: "unlimited",
    basePrice: 0,
    copays: [],
    excludedCoverages: [],
    acceptsPrivatePay: true,
  },
]