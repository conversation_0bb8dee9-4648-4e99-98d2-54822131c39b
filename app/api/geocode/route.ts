import { NextRequest } from 'next/server'

// Simple in-memory cache and throttle across server instances
// Note: This is per-server process and not shared. Good enough to reduce load.
const memoryCache = new Map<string, { lat: number, lon: number, expiresAt: number }>()
let lastOutboundAt = 0

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url)
  const query = (searchParams.get('q') || '').trim()

  if (!query || query.length < 3) {
    return new Response(JSON.stringify({ error: 'Invalid query' }), { status: 400, headers: { 'content-type': 'application/json' } })
  }

  const key = query.toLowerCase()
  const now = Date.now()
  const cached = memoryCache.get(key)
  if (cached && cached.expiresAt > now) {
    return new Response(JSON.stringify({ lat: cached.lat, lon: cached.lon }), {
      status: 200,
      headers: {
        'content-type': 'application/json',
        'cache-control': 'public, max-age=86400, s-maxage=86400, stale-while-revalidate=604800'
      }
    })
  }

  // Respect Nominatim usage policy: at most 1 req/sec per client
  const elapsed = now - lastOutboundAt
  if (elapsed < 1100) {
    await delay(1100 - elapsed)
  }

  try {
    const url = `https://nominatim.openstreetmap.org/search?format=json&addressdetails=0&limit=1&q=${encodeURIComponent(query)}`
    const res = await fetch(url, {
      headers: {
        // Identify the application as required by Nominatim usage policy
        'User-Agent': 'turnera-medical-schedule/1.0 (<EMAIL>)',
        'Accept-Language': 'es-AR,es;q=0.9,en-US;q=0.8,en;q=0.7'
      },
      // Avoid Next.js caching of external request; we implement our own
      cache: 'no-store'
    })
    lastOutboundAt = Date.now()

    if (!res.ok) {
      return new Response(JSON.stringify({ error: `Upstream status ${res.status}` }), { status: 502, headers: { 'content-type': 'application/json' } })
    }
    const data = await res.json()
    if (Array.isArray(data) && data.length > 0) {
      const lat = parseFloat(data[0].lat)
      const lon = parseFloat(data[0].lon)
      if (Number.isFinite(lat) && Number.isFinite(lon)) {
        memoryCache.set(key, { lat, lon, expiresAt: Date.now() + 1000 * 60 * 60 * 24 }) // 24h
        return new Response(JSON.stringify({ lat, lon }), {
          status: 200,
          headers: {
            'content-type': 'application/json',
            'cache-control': 'public, max-age=86400, s-maxage=86400, stale-while-revalidate=604800'
          }
        })
      }
    }
    return new Response(JSON.stringify({ error: 'Not found' }), { status: 404, headers: { 'content-type': 'application/json' } })
  } catch (err: any) {
    return new Response(JSON.stringify({ error: 'Fetch failed', details: String(err?.message || err) }), { status: 502, headers: { 'content-type': 'application/json' } })
  }
}


