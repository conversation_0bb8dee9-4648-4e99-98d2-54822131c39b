import {MedicalCenterPermission} from "@/types/MedicalCenter/medicalCenterPermission";
import {medicalCenterSchedule} from "@/types/MedicalCenter/medicalCenterSchedule";

export interface MedicalCenterRoleForEmployeeUser {
    id: number;
    address: string;
    name: string;
    phoneNumber: string;
    doctorsCount: number;
    role: MedicalCenterPermission;
    workingDays: medicalCenterSchedule[]
}


export function getMedicalCenterWorkingDays(
    medicalCenterRole: MedicalCenterRoleForEmployeeUser
): number {
    return medicalCenterRole.workingDays ? medicalCenterRole.workingDays.length : 0;
}