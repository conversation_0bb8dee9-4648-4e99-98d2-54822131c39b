"use client"

import { useMemo } from "react"
import React from "react"
import { format, startOfWeek, endOfWeek, addDays, subDays } from "date-fns"
import { es } from "date-fns/locale"
import type { MedicalCenter } from "@/types/medical-center"
import type { Doctor } from "@/types/doctor"
import type { Appointment } from "@/types/scheduler"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { getWeeksDifference, REFERENCE_DATE, getActiveHoursForDate } from '@/utils/scheduleUtils'

interface GeneralWeekViewProps {
  selectedDate: Date
  medicalCenter: MedicalCenter
  doctors: Doctor[]
  appointments: Record<string, Appointment[]>
  doctor?: Doctor
  onWeekChange?: (newDate: Date) => void
  onDateSelect?: (date: Date) => void
}

export function GeneralWeekView({
  selectedDate,
  medicalCenter,
  doctors,
  appointments,
  doctor,
  onWeekChange,
  onDateSelect,
}: GeneralWeekViewProps) {
  const weekStart = startOfWeek(selectedDate, { weekStartsOn: 1 })
  const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 1 })
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i))

  const workingDoctors = useMemo(() => {
    return doctor ? [doctor] : doctors.filter((d) => medicalCenter.doctors.includes(d.id))
  }, [doctor, medicalCenter.doctors, doctors])

  const hoursRange = useMemo(() => {
    let minHour = 23
    let maxHour = 0
    let hasWorkingDays = false

    weekDays.forEach((day) => {
      const dateStr = format(day, "yyyy-MM-dd")
      const dayOfWeek = day.getDay().toString()
      const mcDateExceptions = medicalCenter.dateExceptions || {}
      const mcWorkingDay = dateStr in mcDateExceptions ? mcDateExceptions[dateStr] : medicalCenter.workingDays[dayOfWeek]

      if (mcWorkingDay?.enabled && mcWorkingDay.hours && mcWorkingDay.hours.length > 0) {
        hasWorkingDays = true
        mcWorkingDay.hours.forEach(({ start, end }: { start: string; end: string }) => {
          const [startH] = start.split(":").map(Number)
          const [endH] = end.split(":").map(Number)
          minHour = Math.min(minHour, startH)
          maxHour = Math.max(maxHour, endH)
        })
      }

      workingDoctors.forEach((doctor) => {
        const docDateExceptions = doctor.dateExceptions || {}

        // Check for date exceptions first
        if (dateStr in docDateExceptions) {
          if (docDateExceptions[dateStr].enabled && docDateExceptions[dateStr].hours && docDateExceptions[dateStr].hours.length > 0) {
            hasWorkingDays = true
            docDateExceptions[dateStr].hours.forEach(({ start, end }) => {
              const [startH] = start.split(":").map(Number)
              const [endH] = end.split(":").map(Number)
              minHour = Math.min(minHour, startH)
              maxHour = Math.max(maxHour, endH)
            })
          }
          return; // Skip regular day processing if there's an exception
        }

        // Check normal working day
        const docWorkingDay = doctor.workingDays[dayOfWeek]
        if (!docWorkingDay?.enabled) return;

        // Filter hours based on date ranges
        const activeHours = getActiveHoursForDate(docWorkingDay.hours, day);
        if (activeHours.length > 0) {
          hasWorkingDays = true
          activeHours.forEach(({ start, end }) => {
            const [startH] = start.split(":").map(Number)
            const [endH] = end.split(":").map(Number)
            minHour = Math.min(minHour, startH)
            maxHour = Math.max(maxHour, endH)
          })
        }
      })
    })

    return {
      minHour: hasWorkingDays ? minHour : 8,
      maxHour: hasWorkingDays ? maxHour : 19,
    }
  }, [weekDays, medicalCenter.workingDays, medicalCenter.dateExceptions, workingDoctors])

  const hours = Array.from(
    { length: hoursRange.maxHour - hoursRange.minHour + 1 },
    (_, i) => i + hoursRange.minHour
  )

  const weeklyStats = useMemo(() => {
    let totalAppointments = 0
    let pendingCount = 0
    let absentCount = 0
    let peakDay = { date: "", count: 0 }

    weekDays.forEach((day) => {
      const dateStr = format(day, "yyyy-MM-dd")
      const dayAppointments = (appointments[dateStr] || []).filter((apt) =>
        workingDoctors.some((doctor) => doctor.id === apt.doctorId) &&
        (!apt.medicalCenterId || apt.medicalCenterId === medicalCenter.id) &&
        apt.status !== "Cancelado"
      )

      totalAppointments += dayAppointments.length
      pendingCount += dayAppointments.filter((apt) => apt.status === "Agendado").length
      absentCount += dayAppointments.filter((apt) => apt.status === "Ausente").length

      const dayOfMonth = format(day, "d")
      if (dayAppointments.length > peakDay.count) {
        peakDay = { date: dayOfMonth, count: dayAppointments.length }
      }
    })

    return { totalAppointments, pendingCount, absentCount, peakDay }
  }, [appointments, weekDays, workingDoctors, medicalCenter.id])

  const hasAvailableDoctors = (day: Date) => {
    const dateStr = format(day, "yyyy-MM-dd")
    const dayOfWeek = day.getDay().toString()
    const mcDateExceptions = medicalCenter.dateExceptions || {}

    if (dateStr in mcDateExceptions) {
      if (!mcDateExceptions[dateStr].enabled) return false
    } else {
      const mcWorkingDay = medicalCenter.workingDays[dayOfWeek]
      if (!mcWorkingDay?.enabled) return false

      // Add week frequency check for medical center
      const frequency = mcWorkingDay.weeksFrequency || 1
      const weeksSinceReference = getWeeksDifference(day, REFERENCE_DATE)
      if (weeksSinceReference % frequency !== 0) return false
    }

    return workingDoctors.some((doctor) => {
      const docDateExceptions = doctor.dateExceptions || {}

      // Check date exceptions first
      if (dateStr in docDateExceptions) {
        return docDateExceptions[dateStr].enabled;
      }

      // Check normal working day
      const workingDay = doctor.workingDays[dayOfWeek]
      if (!workingDay?.enabled) return false

      // Add week frequency check for doctor
      const frequency = workingDay.weeksFrequency || 1
      const weeksSinceReference = getWeeksDifference(day, REFERENCE_DATE)
      if (weeksSinceReference % frequency !== 0) return false

      // Check if any hours are active for this date based on date ranges
      const activeHours = getActiveHoursForDate(workingDay.hours, day)
      return activeHours.length > 0
    })
  }

  const getWorkingDoctorsCount = (day: Date): number => {
    const dateStr = format(day, "yyyy-MM-dd")
    const dayOfWeek = day.getDay().toString()

    return workingDoctors.filter((doctor) => {
      const docDateExceptions = doctor.dateExceptions || {}

      // Check date exceptions first
      if (dateStr in docDateExceptions) {
        return docDateExceptions[dateStr].enabled;
      }

      // Check normal working day
      const workingDay = doctor.workingDays[dayOfWeek]
      if (!workingDay?.enabled) return false

      // Check if any hours are active for this date based on date ranges
      const activeHours = getActiveHoursForDate(workingDay.hours, day)
      return activeHours.length > 0
    }).length
  }

  const isDoctorAvailableAtHour = (doctor: Doctor, day: Date, hour: number): boolean => {
    const dateStr = format(day, "yyyy-MM-dd")
    const dayOfWeek = day.getDay().toString()
    const docDateExceptions = doctor.dateExceptions || {}

    // Check date exceptions first
    if (dateStr in docDateExceptions) {
      if (!docDateExceptions[dateStr].enabled || !docDateExceptions[dateStr].hours) return false
      return docDateExceptions[dateStr].hours.some(({ start, end }) => {
        const [startH] = start.split(":").map(Number)
        const [endH] = end.split(":").map(Number)
        return hour >= startH && hour < endH
      })
    }

    // Check normal working day
    const workingDay = doctor.workingDays[dayOfWeek]
    if (!workingDay?.enabled || !workingDay.hours) return false

    // Filter hours based on date ranges
    const activeHours = getActiveHoursForDate(workingDay.hours, day)
    return activeHours.some(({ start, end }) => {
      const [startH] = start.split(":").map(Number)
      const [endH] = end.split(":").map(Number)
      return hour >= startH && hour < endH
    })
  }

  const getDoctorsCountAtHour = (day: Date, hour: number): number => {
    return workingDoctors.filter((doctor) => isDoctorAvailableAtHour(doctor, day, hour)).length
  }

  const getHourStatus = (day: Date, hour: number, hourAppointments: Appointment[]): "unavailable" | "free" | "partial" => {
    const doctorsAtHour = getDoctorsCountAtHour(day, hour)
    if (doctorsAtHour === 0) return "unavailable"
    if (hourAppointments.length === 0) return "free"
    return "partial"
  }

  const findFirstAvailableDayInWeek = (startDate: Date): Date => {
    let currentDate = startDate
    while (!hasAvailableDoctors(currentDate)) {
      currentDate = addDays(currentDate, 1)
      if (currentDate > endOfWeek(startDate, { weekStartsOn: 1 })) {
        return startDate
      }
    }
    return currentDate
  }

  const handlePrevWeek = () => {
    const newWeekStart = startOfWeek(subDays(selectedDate, 7), { weekStartsOn: 1 })
    const newDate = findFirstAvailableDayInWeek(newWeekStart)
    onWeekChange?.(newDate)
  }

  const handleNextWeek = () => {
    const newWeekStart = startOfWeek(addDays(selectedDate, 7), { weekStartsOn: 1 })
    const newDate = findFirstAvailableDayInWeek(newWeekStart)
    onWeekChange?.(newDate)
  }

  return (
    <div className="flex-1 overflow-auto">
      <div className="bg-white rounded-lg border border-black">
        <div className="flex items-center justify-between p-4 border-b">
          <Button onClick={handlePrevWeek} variant="outline" size="sm">
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex flex-col items-center">
            <h2 className="text-center text-lg font-semibold">
              {format(weekStart, "d 'de' MMMM", { locale: es })} - {format(weekEnd, "d 'de' MMMM yyyy", { locale: es })}
            </h2>
            <div className="text-xs text-gray-500 mt-1">
              {weeklyStats.totalAppointments} turnos | {weeklyStats.pendingCount} pendientes | {weeklyStats.absentCount} ausentes
              {weeklyStats.peakDay.count > 0 && (
                <span> | Pico: {weeklyStats.peakDay.count} el {weeklyStats.peakDay.date}</span>
              )}
            </div>
          </div>
          <Button onClick={handleNextWeek} variant="outline" size="sm">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="grid grid-cols-[80px_repeat(7,1fr)] border-b sticky top-0 bg-white z-10">
          <div className="p-4 text-sm font-medium text-gray-500">Hora</div>
          {weekDays.map((day, i) => {
            const workingDoctorsCount = getWorkingDoctorsCount(day)
            return (
              <div
                key={i}
                className={`p-4 text-sm font-medium text-center border-l ${
                  hasAvailableDoctors(day)
                    ? "hover:cursor-pointer text-black-500"
                    : "text-gray-300 bg-gray-100 cursor-not-allowed"
                } ${day.toDateString() === selectedDate.toDateString() ? "bg-blue-50" : ""}`}
                onClick={() => hasAvailableDoctors(day) && onDateSelect?.(day)}
              >
                <div>
                  {day
                    .toLocaleDateString("es-ES", { weekday: "long", day: "numeric" })
                    .split(", ")
                    .map((part, index) => (index === 0 ? part.charAt(0).toUpperCase() + part.slice(1) : part))
                    .join(", ")}
                </div>
                {hasAvailableDoctors(day) && (
                  <div className="text-xs text-gray-500">
                    {workingDoctorsCount} {workingDoctorsCount === 1 ? "profesional" : "profesionales"}
                  </div>
                )}
              </div>
            )
          })}
        </div>

        <div className="grid grid-cols-[80px_repeat(7,1fr)]">
          {hours.map((hour) => (
            <React.Fragment key={hour}>
              <div className="border-b h-[100px] flex items-start pt-2 px-4 text-sm text-gray-500">
                {`${hour}:00`}
              </div>

              {weekDays.map((day, dayIndex) => {
                const dateStr = format(day, "yyyy-MM-dd")
                const hourAppointments = (appointments[dateStr] || []).filter(
                  (apt) =>
                    apt.time.startsWith(`${hour.toString().padStart(2, "0")}:`) &&
                    workingDoctors.some((doctor) => doctor.id === apt.doctorId) &&
                    (!apt.medicalCenterId || apt.medicalCenterId === medicalCenter.id) &&
                    apt.status !== "Cancelado"
                )

                const status = getHourStatus(day, hour, hourAppointments)
                const doctorsAtHour = getDoctorsCountAtHour(day, hour)

                const statusStyles = {
                  free: "bg-green-100 text-green-700",
                  partial: "bg-blue-100 text-blue-700",
                  unavailable: "bg-gray-100",
                }[status]

                return (
                  <div
                    key={`${hour}-${dayIndex}`}
                    className={`border-b h-[100px] relative border-l ${
                      !hasAvailableDoctors(day) ? "bg-gray-100" : ""
                    }`}
                    onClick={() => hasAvailableDoctors(day) && onDateSelect?.(day)}
                  >
                    {hasAvailableDoctors(day) && doctorsAtHour > 0 && (
                      <div
                        className={`absolute rounded-sm text-xs p-1 cursor-pointer ${statusStyles}`}
                        style={{
                          top: "4px",
                          left: "4px",
                          right: "4px",
                          height: "calc(100% - 8px)",
                        }}
                      >
                        {hourAppointments.length > 0 ? (
                          <div className="font-medium">
                            {hourAppointments.length} {hourAppointments.length === 1 ? "turno" : "turnos"}
                          </div>
                        ) : null}
                        <div className="text-xs">
                          {doctorsAtHour} {doctorsAtHour === 1 ? "profesional" : "profesionales"}
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  )
}