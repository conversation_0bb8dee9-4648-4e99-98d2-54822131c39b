"use client"

import {use<PERSON><PERSON>back, useContext, useEffect, useMemo, useRef, useState} from "react"
import {use<PERSON><PERSON><PERSON>, useRouter} from "next/navigation"
import Image from "next/image"
import {useAppointments} from "@/contexts/AppointmentContext"
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import {DoctorContext} from "@/contexts/DoctorContext"
import {addDays, eachDayOfInterval, format, startOfWeek} from "date-fns"
import {es} from "date-fns/locale"
import {getMonthData} from "@/utils/dateUtils"
import {Calendar, ChevronLeft, ChevronRight, LogOut, Settings, User} from "lucide-react"
import {Button} from "@/components/ui/button"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue,} from "@/components/ui/select"
import {getActiveHoursForDate, getWeeksDifference, REFERENCE_DATE} from '@/utils/scheduleUtils'
import type {Doctor} from "@/types/doctor"
import type {Appointment} from "@/types/scheduler"
import type {Patient} from "@/types/patient"
import {useAuth} from "@/hooks/useAuth"
import {UserRole} from "@/types/users"
import {MedicalCenter} from "@/types/medical-center"
import {PatientMedicalDataDialog} from "@/components/dialogs/patient-medical-data-dialog"
import {usePatients} from "@/contexts/PatientContext"
import {Toaster} from "@/components/ui/sonner"

const findNextAvailableDate = (startDate: Date, availableDays: number[], doctor: Doctor | null): Date => {
    const currentDate = new Date(startDate)
    let iterations = 0
    const maxIterations = 30 // Prevent infinite loops

    while (iterations < maxIterations) {
        const dayOfWeek = currentDate.getDay()

        // Check if this is an available day
        if (availableDays.includes(dayOfWeek)) {
            // Get the date string to check for exceptions
            const year = currentDate.getFullYear()
            const month = String(currentDate.getMonth() + 1).padStart(2, '0')
            const date = String(currentDate.getDate()).padStart(2, '0')
            const dateStr = `${year}-${month}-${date}`

            // Check exceptions
            const docDateExceptions = doctor?.dateExceptions || {}
            if (dateStr in docDateExceptions) {
                if (docDateExceptions[dateStr].enabled) {
                    return currentDate // Date is explicitly enabled
                } else {
                    // Date is explicitly disabled, move to next day
                    currentDate.setDate(currentDate.getDate() + 1)
                    iterations++
                    continue
                }
            }

            // Check week frequency
            const workingDay = doctor?.workingDays?.[dayOfWeek]
            if (!workingDay?.enabled) {
                // Move to next day if this day is not enabled
                currentDate.setDate(currentDate.getDate() + 1)
                iterations++
                continue
            }

            // Check if date matches week frequency
            const frequency = workingDay.weeksFrequency || 1
            const weeksSinceReference = getWeeksDifference(currentDate, REFERENCE_DATE)
            if (weeksSinceReference % frequency !== 0) {
                // Move to next day if not in frequency cycle
                currentDate.setDate(currentDate.getDate() + 1)
                iterations++
                continue
            }

            // Check if any time blocks are active for this date
            const activeHours = getActiveHoursForDate(workingDay.hours, currentDate)
            if (activeHours.length > 0) {
                return currentDate // Found an available date with active hours
            }
        }

        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1)
        iterations++
    }

    // Fallback - return the original date if we couldn't find an available one
    console.warn("Could not find an available date within reasonable iterations")
    return startDate
}

export default function SharedAgenda() {
    // Track component mounting to prevent problems with Next.js strict mode
    const isMountedRef = useRef(false)
    const initializationCompleteRef = useRef(false)
    const shouldUpdateStateRef = useRef(true)
    const hasLoadedDoctorRef = useRef(false)

    // Get route params
    const params = useParams()
    const router = useRouter()
    const doctorId = params.doctorId as string
    const medicalCenterId = params.medicalCenterId as string

    // Get data from contexts
    const {medicalCenters, setActiveMedicalCenterId} = useContext(MedicalCenterContext)
    const {doctors, loadDoctorById} = useContext(DoctorContext)
    const {appointments, getPatientNameForAppointment, updateAppointment} = useAppointments()
    const {currentUser, isAuthenticated, isLoading, logout} = useAuth()
    const {getPatientById} = usePatients()

    // Authentication check
    useEffect(() => {
        if (!isLoading && !isAuthenticated) {
            // Not authenticated, redirect to login
            router.push('/plataforma/profesional/login')
            return
        }

        if (isAuthenticated && currentUser) {
            // Check if user is a doctor
            if (currentUser.roles === UserRole.DOCTOR) {
                // Check if doctor is accessing their own agenda
                if (currentUser.doctorId !== doctorId) {
                    console.warn(`Doctor ${currentUser.name} attempted to access agenda for doctor ${doctorId}`)
                    // Redirect to their own selection page
                    router.push(`/plataforma/profesional/${currentUser.doctorId}`)
                    return
                }

                // Check if the doctor works at this medical center
                const allMedicalCenters = storage.getMedicalCenters()
                const medicalCenter = allMedicalCenters.find((center: MedicalCenter) => center.id === medicalCenterId)

                if (!medicalCenter || !medicalCenter.doctors.includes(doctorId)) {
                    console.warn(`Doctor ${currentUser.name} attempted to access agenda for medical center ${medicalCenterId} where they don't work`)
                    // Redirect to their selection page
                    router.push(`/plataforma/profesional/${currentUser.doctorId}`)
                    return
                }
            } else if (currentUser.roles !== UserRole.ADMIN && currentUser.roles !== UserRole.SUPERUSER) {
                // Non-admin, non-doctor users should not access this page
                console.warn(`User ${currentUser.name} with role ${currentUser.roles} attempted to access doctor agenda`)
                router.push(`/plataforma/establecimiento/${currentUser.medicalCenterId}`)
                return
            }
        }
    }, [isLoading, isAuthenticated, currentUser, doctorId, medicalCenterId, router])

    // State for loading
    const [loading, setLoading] = useState(true)
    const [doctor, setDoctor] = useState<Doctor | null>(null)

    // Static selections
    const [selectedMedicalCenterId, setSelectedMedicalCenterId] = useState(medicalCenterId)

    // Only set the active medical center once per mount
    useEffect(() => {
        if (!isMountedRef.current) {
            isMountedRef.current = true

            if (medicalCenterId) {
                setActiveMedicalCenterId(medicalCenterId)
            }
        }
    }, [medicalCenterId, setActiveMedicalCenterId])

    // Load doctor data when needed
    useEffect(() => {
        if (!doctorId || !medicalCenterId) return;

        // Reset the hasLoadedDoctorRef when doctorId or medicalCenterId changes
        hasLoadedDoctorRef.current = false;

        const loadDoctorData = async () => {
            // If we've already loaded this doctor, don't reload
            if (hasLoadedDoctorRef.current) return;

            console.log(`SharedAgenda - Loading doctor ${doctorId} for medical center ${medicalCenterId}`);
            setLoading(true);

            try {
                // First check if the doctor is already in the context
                const contextDoctor = doctors.find(d => d.id === doctorId);
                if (contextDoctor) {
                    console.log(`SharedAgenda - Found doctor ${doctorId} in context`);
                    setDoctor(contextDoctor);
                    hasLoadedDoctorRef.current = true;
                    setLoading(false);
                    return;
                }

                // Try to load using DoctorContext's loadDoctorById
                const loadedDoctor = await loadDoctorById(doctorId, medicalCenterId);

                if (loadedDoctor) {
                    console.log(`SharedAgenda - Successfully loaded doctor ${doctorId}`);
                    setDoctor(loadedDoctor);
                    hasLoadedDoctorRef.current = true;
                } else {
                    console.log(`SharedAgenda - Couldn't load doctor ${doctorId}`);

                    // Try storage directly as fallback

                    // Run storage validation to fix inconsistencies
                    storage.validateStorage();

                    // Try to find the doctor in any medical center
                    const allDoctors = storage.getAllDoctorsInSystem();
                    console.log(`SharedAgenda - Found ${allDoctors.length} doctors in the entire system`);

                    const doctorFromSystem = allDoctors.find(d => d.id === doctorId);

                    if (doctorFromSystem) {
                        console.log(`Found doctor ${doctorId} in system`);
                        setDoctor(doctorFromSystem);
                        hasLoadedDoctorRef.current = true;
                    }
                }
            } catch (error) {
                console.error("Error loading doctor data:", error);
            } finally {
                setLoading(false);
            }
        };

        loadDoctorData();
    }, [doctorId, medicalCenterId, doctors, loadDoctorById]);

    // Create a stable available days reference
    const availableDays = useMemo(() => {
        if (!doctor?.workingDays) return []

        return Object.keys(doctor.workingDays)
            .filter(day => doctor.workingDays[day].enabled)
            .map(day => Number.parseInt(day))
    }, [doctor?.workingDays])

    // Date state management
    const [currentDate, setCurrentDate] = useState<Date | null>(null)
    const [weekStart, setWeekStart] = useState<Date | null>(null)

    // Medical data dialog state
    const [isMedicalDataDialogOpen, setIsMedicalDataDialogOpen] = useState(false)
    const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null)
    const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)

    // Initialize dates once when doctor and availableDays load
    useEffect(() => {
        if (doctor && availableDays.length > 0 && !initializationCompleteRef.current && shouldUpdateStateRef.current) {
            initializationCompleteRef.current = true

            const initialDate = findNextAvailableDate(new Date(), availableDays, doctor)
            const initialWeekStart = startOfWeek(initialDate, {weekStartsOn: 1})

            setCurrentDate(initialDate)
            setWeekStart(initialWeekStart)
        }
    }, [doctor, availableDays])

    // Create stabilized references to medical centers
    const medicalCenter = useMemo(() => {
        return medicalCenters.find(mc => mc.id === selectedMedicalCenterId) || null
    }, [medicalCenters, selectedMedicalCenterId])

    const doctorMedicalCenters = useMemo(() => {
        if (!doctorId) return [];
        return medicalCenters.filter(mc => mc.doctors.includes(doctorId))
    }, [medicalCenters, doctorId])

    // Helper function to check if a day is available considering exceptions and week frequency
    const isDayAvailable = useCallback((date: Date): boolean => {
        if (!doctor) return false

        const dayOfWeek = date.getDay()

        // Format date string to check exceptions
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const dateStr = `${year}-${month}-${day}`

        // Check date exceptions FIRST - these have highest priority
        const dateExceptions = doctor.dateExceptions || {}
        if (dateStr in dateExceptions) {
            // If it's an exception day, its enabled status determines availability
            return dateExceptions[dateStr].enabled
        }

        // If not an exception day, check normal working days
        if (!availableDays.includes(dayOfWeek)) return false

        // Check week frequency for regular working days
        const workingDay = doctor.workingDays[dayOfWeek.toString()]
        if (!workingDay?.enabled) return false

        // Check week frequency
        if (workingDay?.weeksFrequency && workingDay.weeksFrequency > 1) {
            const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE)
            if (weeksSinceReference % workingDay.weeksFrequency !== 0) return false
        }

        // Check if any hours are active for this date
        const activeHours = getActiveHoursForDate(workingDay.hours, date)
        return activeHours.length > 0
    }, [doctor, availableDays])

    // Calculate calendar data
    const {days, startingDay} = useMemo(() => {
        if (!currentDate) {
            return {days: [], startingDay: 0}
        }

        const data = getMonthData(currentDate.getFullYear(), currentDate.getMonth())
        const adjustedStartingDay = (data.startingDay - 1 + 7) % 7
        return {days: data.days, startingDay: adjustedStartingDay}
    }, [currentDate])

    // Calculate week days with appointments
    const weekDays = useMemo(() => {
        if (!weekStart || !doctor) {
            return []
        }

        const weekEnd = addDays(weekStart, 6)
        const daysInWeek = eachDayOfInterval({start: weekStart, end: weekEnd})

        return daysInWeek.map(day => {
            const year = day.getFullYear()
            const month = String(day.getMonth() + 1).padStart(2, '0')
            const date = String(day.getDate()).padStart(2, '0')
            const dateStr = `${year}-${month}-${date}`

            // Filter out appointments with null time values and cancelled appointments
            const dayAppointments = (appointments[dateStr] || [])
                .filter(apt => apt.doctorId === doctorId && (!apt.medicalCenterId || apt.medicalCenterId === medicalCenterId) && apt.time && apt.status !== "Cancelado")
                .sort((a, b) => a.time.localeCompare(b.time))

            const hasAppointments = dayAppointments.length > 0
            const isWorkingDay = isDayAvailable(day)

            return {
                date: day,
                appointments: dayAppointments,
                // A day is considered "available" in the weekly view if:
                // 1. It's a working day according to the schedule, OR
                // 2. It has appointments (even if it's not normally a working day)
                isAvailable: isWorkingDay || hasAppointments
            }
        })
    }, [weekStart, doctor, appointments, doctorId, medicalCenterId, isDayAvailable])

    // Safe handlers with guards
    const handlePrevMonth = useCallback(() => {
        if (!currentDate) return
        setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
    }, [currentDate])

    const handleNextMonth = useCallback(() => {
        if (!currentDate) return
        setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
    }, [currentDate])

    const handleDateSelect = useCallback((date: Date) => {
        setCurrentDate(date)
        setWeekStart(startOfWeek(date, {weekStartsOn: 1}))
    }, [])

    const handleMedicalCenterChange = useCallback((value: string) => {
        shouldUpdateStateRef.current = false // Prevent re-initialization on doctor change
        setSelectedMedicalCenterId(value)
        router.push(`/plataforma/profesional/${doctorId}/agenda/${value}`)
    }, [doctorId, router])

    // Check if user has admin, receptionist, or superuser role
    const hasAdminRole = currentUser && (
        currentUser.roles === 'admin' ||
        currentUser.roles === 'receptionist' ||
        currentUser.roles === 'superuser' ||
        (currentUser.medicalCenterRoles &&
            currentUser.medicalCenterRoles.some(mcr =>
                mcr.role === 'admin' || mcr.role === 'receptionist' || mcr.role === 'superuser'
            ))
    );

    const handleGoToAdminView = () => {
        router.push("/plataforma/establecimiento");
    };

    // Handle opening medical data dialog
    const handleAppointmentClick = useCallback((appointment: Appointment) => {
        const patient = getPatientById(appointment.patient)
        if (patient) {
            setSelectedAppointment(appointment)
            setSelectedPatient(patient)
            setIsMedicalDataDialogOpen(true)
        } else {
            console.warn(`Patient not found for appointment ${appointment.id}`)
        }
    }, [getPatientById])

    // Handle appointment status changes from medical dialog
    const handleAppointmentStatusChange = useCallback((appointmentId: string, newStatus: Appointment['status']) => {
        updateAppointment(appointmentId, {status: newStatus})
    }, [updateAppointment])

    // Handle closing medical data dialog
    const handleCloseMedicalDataDialog = useCallback(() => {
        setIsMedicalDataDialogOpen(false)
        setSelectedAppointment(null)
        setSelectedPatient(null)
    }, [])

    // Loading state
    if (loading || !doctor || !medicalCenter || !currentDate || !weekStart) {
        return (
            <div className="min-h-screen bg-blue-50 flex flex-col items-center justify-center">
                <div className="text-blue-500">Cargando agenda...</div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-blue-50 flex flex-col">
            <style jsx global>{`
                html {
                    font-size: 13px;
                }

                @media (min-width: 1500px) {
                    html {
                        font-size: 15px;
                    }
                }

                @media (min-width: 2560px) {
                    html {
                        font-size: 17px;
                    }
                }

                .container {
                    max-width: 1400px;
                }
            `}</style>

            <header className="bg-white shadow-md border-b border-blue-100">
                <div
                    className="container mx-auto px-6 py-4 flex flex-col lg:flex-row items-center justify-between gap-4">
                    <div className="flex items-center justify-between w-full lg:w-auto">
                        <Image
                            src="/images/turnera-logo.svg"
                            alt="Turnera Logo"
                            width={120}
                            height={40}
                            className="h-8 w-auto"
                            priority
                        />

                    </div>
                    <div className="flex flex-col lg:flex-row items-center gap-4 w-full lg:w-auto">
                        {hasAdminRole && (
                            <Button
                                variant="outline"
                                onClick={handleGoToAdminView}
                                className="hidden sm:flex border-slate-200 text-slate-700 hover:bg-slate-50 bg-white"
                            >
                                <Settings className="h-4 w-4 mr-2"/>
                                Administración
                            </Button>
                        )}
                        <Select value={selectedMedicalCenterId} onValueChange={handleMedicalCenterChange}>
                            <SelectTrigger className="w-full lg:w-[200px] bg-blue-50 border-blue-200">
                                <SelectValue placeholder="Select Medical Center"/>
                            </SelectTrigger>
                            <SelectContent>
                                {doctorMedicalCenters.map(mc => (
                                    <SelectItem key={mc.id} value={mc.id}>{mc.name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <div
                            className="flex rounded-lg bg-white border border-blue-200 shadow-sm p-1 gap-1 w-full lg:w-auto">
                            <Button
                                variant="default"
                                className="flex-1 lg:flex-none px-3 py-1 text-sm font-medium rounded-md bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                            >
                                Agenda
                            </Button>
                            <Button
                                variant="ghost"
                                className="flex-1 lg:flex-none px-3 py-1 text-sm font-medium rounded-md text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                                onClick={() => router.push(`/plataforma/profesional/${doctorId}/analytics/${selectedMedicalCenterId}`)}
                            >
                                Estadísticas
                            </Button>
                        </div>
                    </div>
                    <div
                        className="bg-blue-50 rounded-lg px-4 py-2 border border-blue-100 w-full lg:w-auto text-center lg:text-left flex justify-between items-center">
                        <div>
                            <h1 className="text-lg font-semibold text-blue-800">{medicalCenter.name}</h1>
                            <div className="flex items-center justify-center lg:justify-start text-blue-600 mt-1">
                                <User size={14} className="mr-1"/>
                                <p className="text-sm">{doctor.name}</p>
                            </div>
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={logout}
                            className="text-gray-600 hover:text-red-600 flex items-center gap-1 ml-4"
                        >
                            <LogOut size={16}/>
                            <span className="hidden sm:inline">Salir</span>
                        </Button>
                    </div>
                </div>
            </header>

            <main className="container mx-auto px-6 py-6 flex-1 flex flex-col lg:flex-row gap-6">
                <div className="lg:w-1/3">
                    <div className="bg-white border border-blue-100 rounded-xl shadow-sm p-5">
                        <div className="flex items-center justify-between mb-4">
                            <button onClick={handlePrevMonth}
                                    className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-blue-50 text-blue-600 transition-colors">
                                <ChevronLeft size={20}/>
                            </button>
                            <h2 className="text-xl font-semibold text-blue-800 capitalize">{format(currentDate, "MMMM yyyy", {locale: es})}</h2>
                            <button onClick={handleNextMonth}
                                    className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-blue-50 text-blue-600 transition-colors">
                                <ChevronRight size={20}/>
                            </button>
                        </div>
                        <div className="grid grid-cols-7 gap-x-0 gap-y-2 text-center text-xs font-medium">
                            {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                                <div key={i} className="text-gray-600">{day}</div>
                            ))}
                            {Array.from({length: startingDay}).map((_, i) => (
                                <div key={`empty-${i}`} className="p-2"/>
                            ))}
                            {days.map((day, i) => {
                                const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day.date)
                                const dateStr = date.toISOString().split("T")[0]
                                const hasAppointments = (appointments[dateStr] || []).some(apt => apt.doctorId === doctorId && (!apt.medicalCenterId || apt.medicalCenterId === medicalCenterId) && apt.status !== "Cancelado")
                                const isSelected = date.toDateString() === currentDate.toDateString()
                                const isWorkingDay = isDayAvailable(date)

                                // A day is clickable if:
                                // 1. It's a working day according to schedule/exceptions, OR
                                // 2. It already has appointments
                                const isClickable = isWorkingDay || hasAppointments

                                return (
                                    <button
                                        key={i}
                                        onClick={() => isClickable && handleDateSelect(date)}
                                        className={`relative group w-[2rem] h-[2rem] p-0 rounded-full text-base mx-auto
                      ${isClickable ? 'text-gray-900' : 'text-gray-400 cursor-not-allowed'}
                      ${isSelected ? 'bg-blue-500 text-white' : ''}
                      ${hasAppointments && !isSelected ? 'hover:bg-blue-50' : ''}
                      ${isWorkingDay && !hasAppointments && !isSelected ? 'hover:bg-blue-50' : ''}
                    `}
                                    >
                                        <div className="w-full h-full flex items-center justify-center">{day.date}</div>
                                        {hasAppointments && (
                                            <div className={`absolute left-1/2 -translate-x-1/2 w-[0.375rem] h-[0.375rem] rounded-full transition-all duration-200 bg-blue-500
                        ${isSelected ? "-bottom-[0.625rem]" : "-bottom-[0.125rem] group-hover:-bottom-[0.625rem]"}`}
                                            />
                                        )}
                                    </button>
                                )
                            })}
                        </div>
                        <div className="mt-6 pt-4 border-t border-blue-100">
                            <h3 className="text-sm font-semibold text-blue-800 mb-2">Estado de turnos</h3>
                            <div className="grid grid-cols-2 gap-2 text-xs">
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                    <span>En Atención</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                    <span>Agendado</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                    <span>Atendido</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                    <span>Ausente</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-violet-500 mr-2"></div>
                                    <span>Recepcionado</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="lg:w-2/3">
                    <div className="flex items-center mb-4">
                        <Calendar size={20} className="text-blue-600 mr-2"/>
                        <h2 className="text-xl font-semibold text-blue-800">Agenda Semanal</h2>
                    </div>
                    {weekDays.length === 0 ? (
                        <div className="bg-white rounded-xl border border-blue-100 p-8 text-center shadow-sm">
                            <Calendar size={40} className="text-blue-300 mx-auto mb-3"/>
                            <p className="text-blue-600">No hay turnos programados para esta semana.</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {weekDays.map(({date, appointments, isAvailable}) => {
                                const isSelectedDate = date.toDateString() === currentDate.toDateString()
                                const hasAppointments = appointments.length > 0

                                // Skip days that are neither available nor have appointments
                                if (!isAvailable && !hasAppointments) {
                                    return null
                                }

                                // Determine the style based on availability and appointments
                                let dayHeaderClass = 'bg-blue-600'

                                if (isSelectedDate) {
                                    dayHeaderClass = 'bg-blue-600 shadow-md'
                                } else if (!isAvailable && hasAppointments) {
                                    dayHeaderClass = 'bg-blue-400' // Exception day with current appointments
                                }

                                return (
                                    <div key={date.toISOString()} className="flex gap-3">
                                        <div className="w-16 flex-shrink-0 text-left">
                                            <div
                                                className={`${dayHeaderClass} text-white rounded-lg p-1 text-center mb-1 transition-all duration-200`}>
                                                <p className="text-xs font-medium">{format(date, "EEEE", {locale: es})}</p>
                                                <p className="text-lg font-bold leading-none">{format(date, "d", {locale: es})}</p>
                                                <p className="text-xs text-center">{format(date, "MMM", {locale: es})}</p>
                                            </div>
                                        </div>
                                        <div className="flex-1 space-y-2">
                                            {appointments.map((appointment) => {
                                                const statusColor = appointment.status === "Ausente" ? "text-red-700" : appointment.status === "Atendido" ? "text-green-700" :
                                                    appointment.status === "Agendado" ? "text-yellow-700" : appointment.status === "Recepcionado" ? "text-violet-800" : "text-blue-700"
                                                const borderClass = isSelectedDate ? "border-2 shadow-md" : "border border-[0.05vw] shadow-sm"
                                                return (
                                                    <div
                                                        key={appointment.id}
                                                        onClick={() => handleAppointmentClick(appointment)}
                                                        className={`${borderClass} rounded-lg p-2 text-sm cursor-pointer hover:shadow-lg hover:scale-[1.02] transition-all duration-200
                              ${appointment.status === "Ausente" ? "bg-red-50 border-red-300 hover:bg-red-100" : appointment.status === "Atendido" ? "bg-green-50 border-green-300 hover:bg-green-100" :
                                                            appointment.status === "Agendado" ? "bg-yellow-50 border-yellow-300 hover:bg-yellow-100" : appointment.status === "Recepcionado" ? "bg-violet-50 border-violet-300 hover:bg-violet-100" : "bg-blue-50 border-blue-300 hover:bg-blue-100"}
                              ${isSelectedDate ? "shadow-md" : ""}`}
                                                    >
                                                        <p className={`font-semibold ${statusColor}`}>{appointment.time}</p>
                                                        <p className={statusColor}>{getPatientNameForAppointment(appointment)}</p>
                                                        <p className={statusColor}>{appointment.type}</p>
                                                    </div>
                                                )
                                            })}
                                            {hasAppointments === false && isAvailable && (
                                                <div className={`border border-dashed rounded-lg p-4 text-center text-sm
                          border-blue-200 text-blue-400`}>
                                                    No hay turnos programados para este día
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    )}
                </div>
            </main>

            <footer className="bg-white border-t border-blue-100 py-3 mt-auto">
                <div className="container mx-auto px-6 text-center text-sm text-blue-500">
                    Turnera © {new Date().getFullYear()} - Sistema de Gestión de Turnos
                </div>
            </footer>

            {/* Medical Data Dialog */}
            <PatientMedicalDataDialog
                isOpen={isMedicalDataDialogOpen}
                onClose={handleCloseMedicalDataDialog}
                appointment={selectedAppointment}
                patient={selectedPatient}
                doctorId={doctorId}
                onAppointmentStatusChange={handleAppointmentStatusChange}
            />

            {/* Toast Notifications */}
            <Toaster/>
        </div>
    )
}