export class BookingPolicies {
    constructor(
        public maximumAnticipationAppointmentTimeLimit: string,
        public minimumAnticipationAppointmentTimeLimit: string,
        public overlappedAppointmentLimit: string,
    ) {
    }

    static fromJSON(json: Record<string, unknown>): BookingPolicies {
        if (!json) {
            // Return a default or null object if the json is null or undefined
            // This depends on how you want to handle missing booking policies
            return new BookingPolicies('', '', '');
        }
        return new BookingPolicies(
            json.maximumAnticipationAppointmentTimeLimit as string,
            json.minimumAnticipationAppointmentTimeLimit as string,
            json.overlappedAppointmentLimit as string
        );
    }
}