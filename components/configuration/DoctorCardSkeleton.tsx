"use client"

export default function DoctorCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-md border-2 border-transparent overflow-hidden animate-pulse">
      <div className="p-[1.25rem]">
        <div className="flex items-center gap-4 mb-4">
          <div className="w-12 h-12 rounded-md bg-blue-200"></div>
          <div className="flex-1">
            <div className="h-5 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="flex items-center gap-2">
              <div className="h-4 bg-gray-200 rounded w-1/6"></div>
              <div className="block w-1 h-1 rounded-full bg-gray-200"></div>
              <div className="flex items-center">
                <div className="h-3 w-3 bg-gray-200 rounded-full mr-1"></div>
                <div className="h-4 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
            <div className="h-4 bg-gray-200 rounded w-1/3 mt-1"></div>
          </div>
          <div className="flex gap-2">
            <div className="h-10 w-[130px] bg-blue-50 rounded-md"></div>
            <div className="h-10 w-10 bg-blue-50 rounded-md"></div>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex flex-wrap gap-2 mb-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="h-6 bg-blue-50 rounded-full w-32"></div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-1">
            {Array(7).fill(0).map((_, i) => (
              <div key={i} className="h-6 bg-gray-100 rounded text-center py-1 px-2"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
