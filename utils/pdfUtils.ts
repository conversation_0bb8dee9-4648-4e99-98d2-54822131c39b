import {jsPDF} from 'jspdf';
import {autoTable} from 'jspdf-autotable';
import html2canvas from 'html2canvas';
import {format} from 'date-fns';
import {es} from 'date-fns/locale';
import {Doctor} from '@/types/doctor';
import {AppointmentState} from "@/types/professional-schedules";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

/**
 * Generates a PDF of the doctor's agenda for a specific date using jsPDF-AutoTable
 * @param doctor The doctor object
 * @param selectedDate The selected date
 * @param appointments The appointments for the selected date
 * @param getPatientNameForAppointment Function to get patient name from appointment
 */
export const generateDoctorAgendaPDF = async (
    doctor: DoctorsForMedicalCenter,
    selectedDate: Date,
    appointments: ProfessionalAppointment[],
    getPatientNameForAppointment: (appointment: ProfessionalAppointment) => string
): Promise<void> => {
    // Create a new jsPDF instance with grayscale theme
    const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        putOnlyUsedFonts: true,
        compress: true
    });

    // We'll use smaller margins to maximize page space

    // Ensure all colors are set to grayscale
    pdf.setTextColor(0, 0, 0); // Black text
    pdf.setDrawColor(128, 128, 128); // Medium gray for lines
    pdf.setFillColor(240, 240, 240); // Light gray for fills

    // Filter out cancelled appointments
    const filteredAppointments = appointments.filter(apt => apt.state !== AppointmentState.CANCELLED);

    // Sort appointments by time
    const sortedAppointments = [...filteredAppointments].sort((a, b) => a.startTime.localeCompare(b.startTime));

    // Set up PDF content
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 10; // Smaller margin to maximize page space

    // Add header with doctor name on left and Turnera logo on right
    try {
        // In Next.js, we need to use the full URL or a relative path from the public directory
        const logoPath = window.location.origin + '/images/turnera-logo-png.png';
        const desiredWidth = 30; // Slightly bigger logo size
        const calculatedHeight = desiredWidth / 4; // Adjusted aspect ratio to be less stretched

        // Add doctor name on the left
        pdf.setFontSize(16); // Smaller font size
        pdf.setFont('helvetica', 'bold');
        pdf.text(`Agenda de ${doctor.fullName}`, margin, 12);

        // Add date below the doctor name
        pdf.setFontSize(12); // Smaller font size
        pdf.setFont('helvetica', 'normal');
        const formattedDate = format(selectedDate, "EEEE, d 'de' MMMM 'de' yyyy", {locale: es});
        pdf.text(formattedDate, margin, 18);

        // Add logo on the right side
        const logoX = pageWidth - margin - desiredWidth; // Position logo at the right margin
        pdf.addImage(logoPath, 'PNG', logoX, 8, desiredWidth, calculatedHeight);
    } catch (error) {
        console.error('Error adding logo:', error);
        // Fallback to text if image loading fails
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        pdf.text(`Agenda de ${doctor.fullName}`, margin, 12);

        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        const formattedDate = format(selectedDate, "EEEE, d 'de' MMMM 'de' yyyy", {locale: es});
        pdf.text(formattedDate, margin, 18);

        // Add TURNERA text on the right
        pdf.setFontSize(16);
        pdf.setFont('helvetica', 'bold');
        const turneraWidth = pdf.getStringUnitWidth('TURNERA') * 16 * 0.352778;
        pdf.text('TURNERA', pageWidth - margin - turneraWidth, 12);
    }

    // Add a simple line under the header (lighter grayscale)
    pdf.setDrawColor(180, 180, 180); // Lighter gray for header line
    pdf.setLineWidth(0.5);
    pdf.line(margin, 25, pageWidth - margin, 25);

    // Prepare table data
    const tableData = sortedAppointments.map(appointment => [
        appointment.startTime,
        getPatientNameForAppointment(appointment),
        appointment.consultationTypes.toString(), // TODO FACU check format here
        appointment.healthInsuranceInformation || '',
        ''
    ]);

    // If no appointments, show a message
    if (tableData.length === 0) {
        pdf.setFontSize(12);
        pdf.text('No hay turnos agendados para esta fecha.', margin, 35);
    } else {
        // Create the table using autoTable with grayscale theme
        autoTable(pdf, {
            startY: 35, // Start table closer to header to maximize space
            head: [['Hora', 'Paciente', 'Atención', 'Cobertura', 'Observaciones']],
            body: tableData,
            margin: {top: 35, right: margin, bottom: 10, left: margin}, // Reduced margins to maximize space
            theme: 'grid', // Use the grid theme which works well in grayscale
            styles: {
                fontSize: 10,
                cellPadding: 2, // Reduced padding to fit more content
                lineColor: [210, 210, 210], // Lighter gray for lines
                lineWidth: 0.1,
                textColor: [0, 0, 0], // Black text
            },
            headStyles: {
                fillColor: [235, 235, 235], // Lighter gray for header background
                textColor: [0, 0, 0], // Black text
                fontStyle: 'bold',
            },
            alternateRowStyles: {
                fillColor: [248, 248, 248], // Very light gray for alternate rows (lighter)
            },
            // Customize column widths
            columnStyles: {
                0: {cellWidth: 14}, // Hora (increased from 12mm as requested)
                1: {cellWidth: 40}, // Paciente (fixed width)
                2: {cellWidth: 30}, // Atención (fixed width)
                3: {cellWidth: 30}, // Cobertura (fixed width)
                4: {cellWidth: 'auto'}, // Observaciones (takes remaining space)
            },
            // Handle page breaks automatically
            didDrawPage: (data) => {
                // Add header to new pages
                if (data.pageNumber > 1) {
                    pdf.setFontSize(12);
                    pdf.setFont('helvetica', 'bold');

                    // Format date for subsequent pages
                    const pageFormattedDate = format(selectedDate, "dd/MM/yyyy", {locale: es});

                    try {
                        // Add doctor name and date on the left
                        pdf.text(`Agenda de ${doctor.fullName} - ${pageFormattedDate}`, margin, 12);

                        // Add logo on the right
                        const logoPath = window.location.origin + '/images/turnera-logo-png.png';
                        const desiredWidth = 25; // Slightly bigger, but still smaller than first page
                        const calculatedHeight = desiredWidth / 4;
                        const logoX = pageWidth - margin - desiredWidth;
                        pdf.addImage(logoPath, 'PNG', logoX, 5, desiredWidth, calculatedHeight);
                    } catch (error) {
                        console.error('Error adding logo:', error);
                        // Just show the text if logo fails
                        pdf.text(`Agenda de ${doctor.fullName} - ${pageFormattedDate}`, margin, 12);

                        // Add TURNERA text on the right
                        const turneraWidth = pdf.getStringUnitWidth('TURNERA') * 12 * 0.352778;
                        pdf.text('TURNERA', pageWidth - margin - turneraWidth, 12);
                    }

                    // Add line under header (lighter grayscale)
                    pdf.setDrawColor(180, 180, 180); // Lighter gray for header line
                    pdf.setLineWidth(0.5);
                    pdf.line(margin, 30, pageWidth - margin, 30);
                }

                // Add footer with date and time of generation
                const now = new Date();
                const footerText = `Generado el ${format(now, "dd/MM/yyyy 'a las' HH:mm", {locale: es})}`;

                // Add footer with simple line (print-friendly, lighter grayscale)
                pdf.setDrawColor(180, 180, 180); // Lighter gray for footer line
                pdf.setLineWidth(0.3);
                pdf.line(margin, pageHeight - 15, pageWidth - margin, pageHeight - 15);

                pdf.setFontSize(8);
                pdf.text(footerText, margin, pageHeight - 10);

                // Add page numbers
                const totalPages = pdf.internal.pages.length - 1; // -1 because jsPDF uses 1-based indexing with an empty first page
                pdf.text(`Página ${data.pageNumber} de ${totalPages}`, pageWidth - margin - 25, pageHeight - 10);
            },
        });
    }

    // Set PDF metadata and properties
    pdf.setProperties({
        title: `Agenda de ${doctor.fullName} - ${format(selectedDate, 'yyyy-MM-dd')}`,
        subject: 'Agenda médica',
        creator: 'Turnera',
        author: 'Turnera',
        keywords: 'agenda, médica, turnos',
    });

    // Note: jsPDF doesn't directly support setting print options like grayscale
    // The PDF will use the grayscale colors we've defined

    // Save the PDF
    const fileName = `agenda_${doctor.fullName.replace(/\s+/g, '_')}_${format(selectedDate, 'yyyy-MM-dd')}.pdf`;
    pdf.save(fileName);
};

/**
 * Alternative implementation that captures the current view as a PDF
 * This is useful when we want to preserve the exact styling of the UI
 */
export const captureAgendaAsPDF = async (
    elementId: string,
    doctor: Doctor,
    selectedDate: Date
): Promise<void> => {
    const element = document.getElementById(elementId);
    if (!element) {
        console.error(`Element with ID ${elementId} not found`);
        return;
    }

    try {
        // Capture the element as canvas
        const canvas = await html2canvas(element, {
            scale: 2, // Higher scale for better quality
            useCORS: true,
            logging: false,
            allowTaint: true,
            backgroundColor: '#ffffff'
        });

        // Create PDF with appropriate dimensions
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 210; // A4 width in mm
        const pageHeight = 297; // A4 height in mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;

        const pdf = new jsPDF('p', 'mm', 'a4');
        let position = 0;

        // Add title
        pdf.setFontSize(18);
        pdf.setFont('helvetica', 'bold');
        pdf.text(`Agenda de ${doctor.name}`, 10, 10);

        pdf.setFontSize(14);
        pdf.setFont('helvetica', 'normal');
        const formattedDate = format(selectedDate, "EEEE, d 'de' MMMM 'de' yyyy", {locale: es});
        pdf.text(formattedDate, 10, 20);

        // Add the image, creating new pages as needed
        position = 25; // Start after the title
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= (pageHeight - position);

        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // Add page numbers
        // Use the pages array length to get the total number of pages
        const totalPages = pdf.internal.pages.length - 1; // -1 because jsPDF uses 1-based indexing with an empty first page
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfMargin = 10;

        for (let i = 1; i <= totalPages; i++) {
            pdf.setPage(i);
            pdf.setFontSize(8);
            pdf.text(`Página ${i} de ${totalPages}`, pdfWidth - pdfMargin - 25, pageHeight - 10);
        }

        // Save the PDF
        const fileName = `agenda_${doctor.name.replace(/\s+/g, '_')}_${format(selectedDate, 'yyyy-MM-dd')}.pdf`;
        pdf.save(fileName);
    } catch (error) {
        console.error('Error generating PDF:', error);
    }
};
