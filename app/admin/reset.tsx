"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

// This page redirects to the main admin page with the reset dialog open
export default function ResetRedirect() {
  const router = useRouter()
  
  useEffect(() => {
    // Redirect to admin page with a query parameter to open the reset dialog
    router.replace("/admin?openResetDialog=true")
  }, [router])
  
  return (
    <div className="min-h-screen bg-blue-50 flex flex-col items-center justify-center p-4">
      <div className="text-center space-y-4">
        <div className="text-blue-500 mb-4">Redirigiendo...</div>
        <p className="text-gray-600">Por favor espere mientras le redirigimos a la página de administración.</p>
      </div>
    </div>
  )
} 