"use client"

import { useContext, useState, useEffect, useCallback, useRef, useLayoutEffect } from "react"
import { DoctorContext } from "@/contexts/DoctorContext"
import { ScheduleContext } from "@/contexts/ScheduleContext"
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext"
import { AppStateContext } from "@/contexts/AppStateContext"
import { useAppointments } from "@/contexts/AppointmentContext"
import { useScheduleChanges } from "@/contexts/ScheduleChangesContext"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { format, parseISO } from "date-fns"
import { CalendarIcon, Plus, Trash, ChevronDown, ChevronUp, AlertCircle, Clock, FileDown, Eye } from "lucide-react"
import { toast } from "react-toastify"
import { DAYS } from "@/utils/constants"
import { DoctorConfiguration } from "@/types/doctor"
import { getScheduleForDay } from "@/utils/scheduleUtils"
import type { Appointment } from "@/types/scheduler"
import * as XLSX from 'xlsx'

// Define a more specific type for pending changes
type PendingChange =
  | { type: "endDate"; dayId: string; index: number; value: string | undefined }
  | { type: "removeHour"; dayId: string; index: number }
  | { type: "disableDay"; dayId: string };

// Define WorkingHour type locally if not exported
type WorkingHour = {
  start: string;
  end: string;
  startDate?: string;
  endDate?: string;
};

export default function DoctorWeeklyScheduleTab() {
  const { editedConfig, setEditedConfig, refreshDoctorsFromStorage } = useContext(DoctorContext)
  const { handleUpdateWeeksFrequency } = useContext(ScheduleContext)
  const { medicalCenters, activeMedicalCenterId } = useContext(MedicalCenterContext)
  const { setHasUnsavedChanges } = useContext(AppStateContext)
  const { appointments, getPatientNameForAppointment } = useAppointments()
  const { addAppointmentsToCancel, setCancellationOption, setCancellationReason } = useScheduleChanges()

  // Get selectedCenter from activeMedicalCenterId
  const selectedCenter = medicalCenters.find(mc => mc.id === activeMedicalCenterId) || null

  // Use a ref to track if we've already refreshed the data
  const hasRefreshedRef = useRef(false);

  // Refresh doctors from storage when component mounts, but only once
  useLayoutEffect(() => {
    if (!hasRefreshedRef.current && editedConfig) {
      console.log("DoctorWeeklyScheduleTab: Refreshing doctors from storage on mount")
      hasRefreshedRef.current = true;

      // Force a refresh of the data from localStorage
      setTimeout(() => {
        refreshDoctorsFromStorage();

        // Log the current working days for debugging
        if (editedConfig && editedConfig.workingDays) {
          console.log("Current working days in editedConfig:",
            Object.entries(editedConfig.workingDays).map(([day, config]) => {
              // Type assertion to fix TypeScript error
              const typedConfig = config as { enabled: boolean; hours: Array<{ start: string; end: string }>; weeksFrequency?: number };
              return `${day}: ${typedConfig.enabled ? 'enabled' : 'disabled'}`;
            }).join(', ')
          );
        }
      }, 0);
    }
    // We only want to run this once when the component mounts
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editedConfig])

  const [expandedDays, setExpandedDays] = useState<{ [key: string]: boolean }>(() => {
    const initial: { [key: string]: boolean } = {};
    DAYS.forEach(day => {
      initial[day.id] = false;
    });
    return initial;
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [localWorkingHours, setLocalWorkingHours] = useState<DoctorConfiguration["workingDays"]>(() => {
    const initialHours = editedConfig?.workingDays || {}
    DAYS.forEach(day => {
      if (!initialHours[day.id]) {
        // Ensure default weeksFrequency is 1
        initialHours[day.id] = { enabled: false, hours: [], weeksFrequency: 1 }
      } else if (initialHours[day.id].weeksFrequency === undefined) {
         // Ensure existing entries also have a default frequency if missing
         initialHours[day.id].weeksFrequency = 1;
      }
    })
    return initialHours
  })

  // State for handling affected appointments
  const [showWarning, setShowWarning] = useState<string | null>(null)
  const [affectedAppointments, setAffectedAppointments] = useState<Appointment[]>([])
  const [localCancellationOption, setLocalCancellationOption] = useState<"auto" | "manual" | null>(null)
  const [localCancellationReason, setLocalCancellationReason] = useState<string>("")
  const [showAffectedAppointments, setShowAffectedAppointments] = useState<boolean>(false)
  // Use the new PendingChange type
  const [pendingChanges, setPendingChanges] = useState<PendingChange | null>(null)

  // Track if initial sync has been done
  const hasInitialSyncRef = useRef(false);

  // Sync localWorkingHours with editedConfig on mount or external change
  useEffect(() => {
    if (!editedConfig) return;

    const newWorkingHours = { ...editedConfig.workingDays }
    DAYS.forEach(day => {
      if (!newWorkingHours[day.id]) {
        newWorkingHours[day.id] = { enabled: false, hours: [], weeksFrequency: 1 }
      }
    });

    // Only update if this is the initial sync or if editedConfig changed externally
    if (!hasInitialSyncRef.current) {
      setLocalWorkingHours(newWorkingHours)
      hasInitialSyncRef.current = true
      console.log("Initial sync of localWorkingHours from editedConfig:", newWorkingHours)
    }
  }, [editedConfig]); // Remove localWorkingHours from dependencies

  // Removed auto-expansion effect to keep collapsibles collapsed by default
  // The expandedDays state will now only be updated through explicit user interaction

  // Sync localWorkingHours to editedConfig and update hasUnsavedChanges
  // Use a ref to track if an update is already in progress to avoid loops
  const isUpdatingRef = useRef(false);

  useEffect(() => {
    if (!editedConfig || isUpdatingRef.current) return;

    const currentWorkingDays = editedConfig?.workingDays || {};
    const localStr = JSON.stringify(localWorkingHours);
    const currentStr = JSON.stringify(currentWorkingDays);

    if (localStr !== currentStr) {
      console.log("Updating editedConfig with localWorkingHours");
      console.log("Current working days in editedConfig:",
        Object.entries(currentWorkingDays).map(([day, config]) => {
          // Type assertion to fix TypeScript error
          const typedConfig = config as { enabled: boolean; hours: Array<{ start: string; end: string }>; weeksFrequency?: number };
          return `${day}: ${typedConfig.enabled ? 'enabled' : 'disabled'}`;
        }).join(', ')
      );
      console.log("New working days from localWorkingHours:",
        Object.entries(localWorkingHours).map(([day, config]) => {
          // Type assertion to fix TypeScript error
          const typedConfig = config as { enabled: boolean; hours: Array<{ start: string; end: string }>; weeksFrequency?: number };
          return `${day}: ${typedConfig.enabled ? 'enabled' : 'disabled'}`;
        }).join(', ')
      );

      isUpdatingRef.current = true;

      // Schedule the update to avoid nested state updates
      setTimeout(() => {
        setEditedConfig(prev => {
          if (!prev) return prev;

          // Create a deep copy to ensure all nested objects are properly updated
          const prevCopy = JSON.parse(JSON.stringify(prev));
          const updatedWorkingDays = JSON.parse(JSON.stringify(localWorkingHours));

          const result = {
            ...prevCopy,
            workingDays: updatedWorkingDays
          };

          console.log("Updated editedConfig with new working days:",
            Object.entries(result.workingDays).map(([day, config]) => {
              // Type assertion to fix TypeScript error
              const typedConfig = config as { enabled: boolean; hours: Array<{ start: string; end: string }>; weeksFrequency?: number };
              return `${day}: ${typedConfig.enabled ? 'enabled' : 'disabled'}`;
            }).join(', ')
          );

          return result;
        });
        isUpdatingRef.current = false;
      }, 0);
    }

    // Check for unsaved changes
    const hasUnsaved = DAYS.some(day => {
      const local = localWorkingHours[day.id] || { enabled: false, hours: [], weeksFrequency: 1 }
      const initial = currentWorkingDays[day.id] || { enabled: false, hours: [], weeksFrequency: 1 }
      return (
        local.enabled !== initial.enabled ||
        JSON.stringify(local.hours || []) !== JSON.stringify(initial.hours || []) ||
        local.weeksFrequency !== initial.weeksFrequency
      )
    });

    setHasUnsavedChanges(hasUnsaved);
  }, [localWorkingHours, setEditedConfig, setHasUnsavedChanges, editedConfig]);

  const validateWorkingHoursArray = useCallback((dayId: string, hours: WorkingHour[], errorsObj: { [key: string]: string }) => {
    if (!selectedCenter) {
      hours.forEach((_, index) => {
        errorsObj[`${dayId}-${index}`] = "No se ha seleccionado un establecimiento"
      })
      return false
    }

    if (!editedConfig) return false

    const slotDuration = editedConfig.appointmentSlotDuration

    hours.forEach((hour, index) => {
      // Ensure hour has default properties if partially defined (shouldn't happen often)
      const currentHour = { ...hour }; // Rely on spread for start/end
      const startTime = new Date(`2000-01-01T${currentHour.start || '00:00'}:00`) // Add fallback for safety
      const endTime = new Date(`2000-01-01T${currentHour.end || '00:00'}:00`)   // Add fallback for safety
      const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60)

      if (durationMinutes < slotDuration) {
        errorsObj[`${dayId}-${index}`] = `El rango debe permitir al menos un turno (${slotDuration} minutos)`
        return
      }

      if (startTime >= endTime) {
        errorsObj[`${dayId}-${index}`] = "La hora de inicio debe ser anterior a la hora de fin"
        return
      }

      hours.forEach((otherHourData, otherIndex) => {
        if (index === otherIndex) return

        // Ensure otherHour has default properties
        const otherHour = { ...otherHourData }; // Rely on spread for start/end
        const otherStart = new Date(`2000-01-01T${otherHour.start || '00:00'}:00`) // Add fallback for safety
        const otherEnd = new Date(`2000-01-01T${otherHour.end || '00:00'}:00`)     // Add fallback for safety
        const timeOverlap = startTime < otherEnd && endTime > otherStart

        if (!timeOverlap) return

        // Date range overlap check, handling undefined start/end dates
        const hourStartDate = currentHour.startDate ? new Date(currentHour.startDate) : null;
        const hourEndDate = currentHour.endDate ? new Date(currentHour.endDate) : null;
        const otherStartDate = otherHour.startDate ? new Date(otherHour.startDate) : null;
        const otherEndDate = otherHour.endDate ? new Date(otherHour.endDate) : null;

        // Clear time part for date comparisons
        hourStartDate?.setHours(0, 0, 0, 0);
        hourEndDate?.setHours(0, 0, 0, 0);
        otherStartDate?.setHours(0, 0, 0, 0);
        otherEndDate?.setHours(0, 0, 0, 0);

        // Check for non-overlap scenarios
        // 1. This hour ends before the other starts (and both have defined dates for this comparison)
        const endsBeforeOtherStarts = hourEndDate && otherStartDate && hourEndDate < otherStartDate;
        // 2. This hour starts after the other ends (and both have defined dates for this comparison)
        const startsAfterOtherEnds = hourStartDate && otherEndDate && hourStartDate > otherEndDate;

        // If they definitively don't overlap in dates, no error
        if (endsBeforeOtherStarts || startsAfterOtherEnds) {
          return;
        }

        // If we reach here, the time ranges overlap, and the date ranges either overlap
        // or at least one range is indefinite (startDate or endDate is undefined),
        // meaning a potential conflict exists.
        errorsObj[`${dayId}-${index}`] = "Los horarios no pueden superponerse en el mismo rango de fechas"
        // No need to check further overlaps for this hour once one is found
        return;
      });

      // Check against center hours only if no overlap error was found yet
      if (errorsObj[`${dayId}-${index}`]) return;

      const centerSchedule = getScheduleForDay(selectedCenter, dayId);

      if (!centerSchedule.enabled && hours.length > 0) {
        errorsObj[`${dayId}-${index}`] = "El establecimiento no opera este día"
        return
      }

      if (centerSchedule.enabled && centerSchedule.hours.length > 0) {
        let withinCenterHours = false
        for (const centerHour of centerSchedule.hours) {
          const centerStart = new Date(`2000-01-01T${centerHour.start}:00`)
          const centerEnd = new Date(`2000-01-01T${centerHour.end}:00`)
          if (startTime >= centerStart && endTime <= centerEnd) {
            withinCenterHours = true
            break
          }
        }
        if (!withinCenterHours) {
          const centerHoursText = formatWorkingHours(centerSchedule.hours)
          errorsObj[`${dayId}-${index}`] = `Horario fuera del rango permitido del establecimiento (${centerHoursText})`
        }
      }
    })

    return Object.keys(errorsObj).length === 0
  }, [selectedCenter, editedConfig]);

  // Use a debounced effect for validation to prevent rapid re-renders
  useEffect(() => {
    const validateTimer = setTimeout(() => {
      const newErrors: { [key: string]: string } = {}
      DAYS.forEach(day => {
        const hours = localWorkingHours[day.id]?.hours || []
        validateWorkingHoursArray(day.id, hours, newErrors)
      })

      if (JSON.stringify(newErrors) !== JSON.stringify(errors)) {
        setErrors(newErrors)
      }
    }, 200); // Add a small delay

    return () => clearTimeout(validateTimer);
  }, [localWorkingHours, validateWorkingHoursArray, errors]);

  const toggleDayExpansion = (dayId: string) => {
    setExpandedDays(prev => ({
      ...prev,
      [dayId]: !prev[dayId],
    }))
  }

  const formatWorkingHours = (hours: { start: string; end: string }[]) => {
    return hours.map(hour => `${hour.start} - ${hour.end}`).join(" | ")
  }

  const generateTimeOptions = () => {
    const options = []
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        const hourStr = hour.toString().padStart(2, "0")
        const minuteStr = minute.toString().padStart(2, "0")
        options.push(`${hourStr}:${minuteStr}`)
      }
    }
    return options
  }

  const timeOptions = generateTimeOptions()

  const getTimeOptionsForDay = (dayId: string) => {
    if (!selectedCenter) {
      return timeOptions
    }

    const schedule = getScheduleForDay(selectedCenter, dayId);
    if (!schedule.enabled || schedule.hours.length === 0) {
      return timeOptions;
    }

    return timeOptions.filter(time => {
      const timeDate = new Date(`2000-01-01T${time}:00`)
      return schedule.hours.some((hour: { start: string; end: string }) => {
        const start = new Date(`2000-01-01T${hour.start}:00`)
        const end = new Date(`2000-01-01T${hour.end}:00`)
        return timeDate >= start && timeDate <= end
      })
    })
  }

  const getNextTime = (currentTime: string, dayId: string, slotDuration: number): string => {
    const dayOptions = getTimeOptionsForDay(dayId)
    const currentIndex = dayOptions.indexOf(currentTime)
    if (currentIndex === -1) return dayOptions[0]
    const slotsNeeded = Math.ceil(slotDuration / 5)
    return dayOptions[Math.min(currentIndex + slotsNeeded, dayOptions.length - 1)]
  }

  const getPreviousTime = (currentTime: string, dayId: string, slotDuration: number): string => {
    const dayOptions = getTimeOptionsForDay(dayId)
    const currentIndex = dayOptions.indexOf(currentTime)
    if (currentIndex === -1) return dayOptions[0]
    const slotsNeeded = Math.ceil(slotDuration / 5)
    return dayOptions[Math.max(currentIndex - slotsNeeded, 0)]
  }

  const sortHoursByInicio = (hours: { start: string; end: string }[]) => {
    return [...hours].sort((a, b) => {
      const timeA = new Date(`2000-01-01T${a.start}:00`)
      const timeB = new Date(`2000-01-01T${b.start}:00`)
      return timeA.getTime() - timeB.getTime()
    })
  }

  const getCenterHoursForDay = (dayId: string) => {
    if (!selectedCenter) {
      return [];
    }

    const schedule = getScheduleForDay(selectedCenter, dayId);
    return schedule.enabled ? schedule.hours : [];
  }

  const getDefaultTimesForDay = (dayId: string): { defaultStart: string; defaultEnd: string } => {
    const centerHours = getCenterHoursForDay(dayId)
    const slotDuration = editedConfig?.appointmentSlotDuration || 15
    const existingHours = localWorkingHours[dayId]?.hours || []

    if (centerHours.length === 0) {
      return { defaultStart: "09:00", defaultEnd: getNextTime("09:00", dayId, slotDuration) }
    }

    if (existingHours.length > 0) {
      const lastHour = existingHours[existingHours.length - 1]
      const defaultStart = getNextTime(lastHour.end, dayId, slotDuration)
      const defaultEnd = getNextTime(defaultStart, dayId, slotDuration)
      return { defaultStart, defaultEnd }
    }

    const firstBlock = centerHours[0]
    const defaultStart = firstBlock.start
    const defaultEnd = getNextTime(defaultStart, dayId, slotDuration)
    return { defaultStart, defaultEnd }
  }

  const handleHoursUpdate = (dayId: string, index: number, field: "start" | "end" | "startDate" | "endDate", value: string) => {
    const updatedHours = [...(localWorkingHours[dayId]?.hours || [])]
    // Ensure currentHour exists or provide a default structure
    const currentHour: WorkingHour = updatedHours[index] || {
      start: "09:00", // Fallback start time
      end: "09:15",   // Fallback end time
      // No default dates initially
    };
    const slotDuration = editedConfig?.appointmentSlotDuration || 15

    if (field === "startDate" || field === "endDate") {
      const newValue = value === "clear" ? undefined : value

      // Prevent setting end date before start date
      if (field === "endDate" && currentHour.startDate && newValue && newValue < currentHour.startDate) {
        toast.error("La fecha de finalización debe ser posterior a la fecha de inicio");
        return;
      }
      // Prevent setting start date after end date
      if (field === "startDate" && currentHour.endDate && newValue && newValue > currentHour.endDate) {
        toast.error("La fecha de inicio debe ser anterior a la fecha de finalización");
        return;
      }

      // Check for affected appointments ONLY when setting an END date
      if (field === "endDate" && value !== "clear" && editedConfig) {
        // Pass the hour being modified and the new end date to check *after* it
        const affectedAppts = findAffectedAppointments(dayId, [currentHour], newValue); // Check after the new end date

        if (affectedAppts.length > 0) {
          setPendingChanges({ type: "endDate", dayId, index, value: newValue })
          setAffectedAppointments(affectedAppts)
          setShowWarning(`Se cancelarán ${affectedAppts.length} turnos existentes fuera del nuevo período configurado.`)
          return // Stop processing until user confirms/cancels
        }
      }

      // Proceed with update if no conflicts or not setting endDate
      updatedHours[index] = {
        ...currentHour,
        [field]: newValue
      }
    } else { // Handling "start" or "end" time changes
      let newStart = field === "start" ? value : currentHour.start
      let newEnd = field === "end" ? value : currentHour.end

      // Ensure minimum duration
      const startTime = new Date(`2000-01-01T${newStart}:00`)
      const endTime = new Date(`2000-01-01T${newEnd}:00`)
      const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60)

      if (durationMinutes < slotDuration) {
        if (field === "start") {
          newEnd = getNextTime(newStart, dayId, slotDuration)
        } else {
          newStart = getPreviousTime(newEnd, dayId, slotDuration)
        }
      }

      updatedHours[index] = {
        ...currentHour, // Keep existing dates
        start: newStart,
        end: newEnd,
      }
    }

    const sortedHours = sortHoursByInicio(updatedHours)

    setLocalWorkingHours(prev => ({
      ...prev,
      [dayId]: {
        ...(prev[dayId] || { enabled: true, hours: [], weeksFrequency: 1 }), // Ensure day config exists
        hours: sortedHours
      }
    }))
  }

  const handleAddHours = (dayId: string) => {
    const { defaultStart, defaultEnd } = getDefaultTimesForDay(dayId)

    // StartDate is undefined by default, meaning indefinite start
    const newHour: WorkingHour = {
      start: defaultStart,
      end: defaultEnd,
      startDate: undefined, // Default to indefinite start
      endDate: undefined   // Default to indefinite end
    };

    setLocalWorkingHours(prev => {
      // Ensure day config exists before accessing hours
      const currentDayConfig = prev[dayId] || { enabled: true, hours: [], weeksFrequency: 1 };
      const updatedHours = [...currentDayConfig.hours, newHour];
      const sortedHours = sortHoursByInicio(updatedHours);
      console.log(`Adding hours to ${dayId}:`, { newHour, updatedHours: sortedHours });
      return {
        ...prev,
        [dayId]: {
          ...currentDayConfig,
          hours: sortedHours
        }
      };
    });
    // Expand the day when hours are added (this is expected behavior)
    setExpandedDays(prev => ({ ...prev, [dayId]: true }));
  };

  const handleRemoveHours = (dayId: string, index: number) => {
    const currentHours = localWorkingHours[dayId]?.hours || [];
    if (index < 0 || index >= currentHours.length) return; // Invalid index

    const hourToRemove = currentHours[index];

    // Check for affected appointments within the range being removed
    const affectedAppts = findAffectedAppointments(dayId, [hourToRemove]); // Check within the hour's full range

    if (affectedAppts.length > 0) {
      setPendingChanges({ type: "removeHour", dayId, index });
      setAffectedAppointments(affectedAppts);
      setShowWarning(`Eliminar esta franja horaria (${hourToRemove.start} - ${hourToRemove.end}) cancelará ${affectedAppts.length} turnos existentes.`);
      return; // Stop processing until user confirms/cancels
    }

    // If no conflicts, proceed with removal
    performRemoveHours(dayId, index);
  };

  // Separated logic for actual removal after check/confirmation
  const performRemoveHours = (dayId: string, index: number) => {
    const currentHours = localWorkingHours[dayId]?.hours || [];
    const currentDayEnabled = localWorkingHours[dayId]?.enabled || false;

    // If removing this hour leaves no hours and the day is enabled, disable the day
    if (currentHours.length === 1 && currentDayEnabled) {
      console.log(`Removing last hour for ${dayId}, disabling day.`);
      // Directly call performToggleDay to handle disabling and clearing hours
      // Pass checked=false to disable the day
      performToggleDay(dayId, false);
    } else {
      // Otherwise, just remove the specific hour range
      setLocalWorkingHours(prev => {
        const dayConfig = prev[dayId];
        if (!dayConfig || !dayConfig.hours || index < 0 || index >= dayConfig.hours.length) return prev; // Should not happen if index was valid

        const updatedHours = [...dayConfig.hours];
        updatedHours.splice(index, 1);
        const sortedHours = sortHoursByInicio(updatedHours); // Sort again just in case

        console.log(`Removing hour at index ${index} for ${dayId}. Remaining hours:`, sortedHours);

        return {
          ...prev,
          [dayId]: {
            ...dayConfig,
            hours: sortedHours
          }
        };
      });
    }
  };

  const handleToggleDay = (dayId: string, checked: boolean) => {
    if (!selectedCenter) return;

    const schedule = getScheduleForDay(selectedCenter, dayId);
    if (checked && (!schedule.enabled)) {
      toast.error("El establecimiento no opera este día. No se puede habilitar.", {
        position: "top-right",
        autoClose: 3000,
      })
      return
    }

    // If disabling the day, check for affected appointments first
    if (!checked) {
      const hoursToClear = localWorkingHours[dayId]?.hours || [];
      if (hoursToClear.length > 0) {
        const affectedAppts = findAffectedAppointments(dayId, hoursToClear); // Check all hours for the day
        if (affectedAppts.length > 0) {
          setPendingChanges({ type: "disableDay", dayId });
          setAffectedAppointments(affectedAppts);
          setShowWarning(`Deshabilitar ${DAYS.find(d => d.id === dayId)?.name} cancelará ${affectedAppts.length} turnos existentes.`);
          return; // Stop processing until user confirms/cancels
        }
      }
    }

    // If enabling, or disabling with no conflicts, proceed
    performToggleDay(dayId, checked);
  };

  // Separated logic for actual toggle after check/confirmation
  const performToggleDay = (dayId: string, checked: boolean) => {
    console.log(`Performing toggle for day ${dayId} to ${checked ? 'enabled' : 'disabled'}`);

    // Update context state (this might be redundant if local state syncs correctly, review if needed)
    // handleToggleWorkingDay(dayId, checked);

    // Update local working hours
    setLocalWorkingHours(prev => {
      const currentDayConfig = prev[dayId] || { enabled: false, hours: [], weeksFrequency: 1 };
      const updated = {
        ...prev,
        [dayId]: {
          ...currentDayConfig,
          enabled: checked,
          // Clear hours array only when day is disabled
          hours: checked ? currentDayConfig.hours : [],
          // Preserve weeksFrequency or default to 1
          weeksFrequency: currentDayConfig.weeksFrequency || 1
        }
      };

      console.log(`Updated localWorkingHours for ${dayId}:`, updated[dayId]);
      return updated;
    });

    // Automatically expand the day when toggled on, collapse when toggled off
    setExpandedDays(prev => ({
      ...prev,
      [dayId]: checked // Set to true when enabled, false when disabled
    }));

    // Sync with editedConfig is handled by the useEffect watching localWorkingHours
  };

  const getCenterHoursInfo = (dayId: string) => {
    if (!selectedCenter) return "Sin establecimiento seleccionado";

    const schedule = getScheduleForDay(selectedCenter, dayId);
    if (!schedule.enabled || schedule.hours.length === 0) {
      return "Establecimiento cerrado este día"
    }
    return `Horario del establecimiento: ${formatWorkingHours(schedule.hours)}`
  }

  // Refactored function to find affected appointments based on various criteria
  const findAffectedAppointments = (
    dayId: string,
    hourRangesToCheck: WorkingHour[],
    checkAppointmentsAfter?: string | undefined // Optional: Only find appointments strictly AFTER this date (YYYY-MM-DD)
  ): Appointment[] => {
    if (!editedConfig || !hourRangesToCheck || hourRangesToCheck.length === 0) return [];

    const dayOfWeekJs = parseInt(dayId); // Assuming dayId is '0' (Sun) to '6' (Sat)
    const affectedAppts: Appointment[] = [];
    const checkDateThreshold = checkAppointmentsAfter ? parseISO(checkAppointmentsAfter) : null;
    if (checkDateThreshold) {
      // Set threshold to the very END of the day BEFORE the provided date
      // So checks like appointmentDate > checkDateThreshold correctly capture appointments ON or AFTER the checkAppointmentsAfter date.
      checkDateThreshold.setDate(checkDateThreshold.getDate() - 1);
      checkDateThreshold.setHours(23, 59, 59, 999);
      console.log(`Checking for appointments occurring after ${checkDateThreshold.toISOString()}`);
    }

    Object.entries(appointments).forEach(([dateStr, apptsOnDate]) => {
      const appointmentDate = parseISO(dateStr);
      appointmentDate.setHours(0, 0, 0, 0); // Compare dates only

      // 1. Filter by date threshold if provided (e.g., for endDate changes)
      if (checkDateThreshold && appointmentDate <= checkDateThreshold) {
        // console.log(`Skipping date ${dateStr}: before threshold ${checkDateThreshold.toISOString()}`);
        return; // Skip appointments on or before the threshold date
      }

      // 2. Check if the appointment is on the correct day of the week
      if (appointmentDate.getDay() !== dayOfWeekJs) {
        // console.log(`Skipping date ${dateStr}: wrong day of week (${appointmentDate.getDay()} vs ${dayOfWeekJs})`);
        return; // Skip if not the target day
      }

      // 3. Filter appointments for the current doctor AND the active medical center
      const doctorAppts = apptsOnDate.filter(apt =>
        apt.doctorId === editedConfig.id &&
        // Only consider appointments for the active medical center
        (apt.medicalCenterId === activeMedicalCenterId || !apt.medicalCenterId)
      );
      if (doctorAppts.length === 0) return; // Skip if no appointments for this doctor on this date at this medical center

      // 4. Check if any doctor appointment falls within ANY of the hourRangesToCheck
      doctorAppts.forEach(apt => {
        const aptTime = apt.time; // Format "HH:MM"

        const fallsWithinAnyRange = hourRangesToCheck.some(range => {
          // Check time range
          if (aptTime < range.start || aptTime >= range.end) {
            return false; // Outside time range
          }

          // Check date range (if defined)
          const rangeStartDate = range.startDate ? parseISO(range.startDate) : null;
          const rangeEndDate = range.endDate ? parseISO(range.endDate) : null;

          // Clear time part for date comparisons
          rangeStartDate?.setHours(0, 0, 0, 0);
          rangeEndDate?.setHours(0, 0, 0, 0);

          // Check if appointment date is before range start date
          if (rangeStartDate && appointmentDate < rangeStartDate) {
            return false;
          }
          // Check if appointment date is after range end date
          if (rangeEndDate && appointmentDate > rangeEndDate) {
            return false;
          }

          // If time is within range and date is within range (or range is indefinite)
          return true;
        });

        if (fallsWithinAnyRange) {
          // console.log(`Found affected appointment: ${apt.patient} on ${dateStr} at ${aptTime}`);
          affectedAppts.push(apt);
        }
      });
    });

    console.log(`Total affected appointments for day ${dayId} in ranges [${hourRangesToCheck.map(r => `${r.start}-${r.end}`).join(', ')}]${checkAppointmentsAfter ? ` after ${checkAppointmentsAfter}` : ''}: ${affectedAppts.length}`);
    return affectedAppts;
  };

  // Function to apply pending changes after confirmation
  const applyPendingChange = () => {
    if (!pendingChanges) return;

    console.log("Applying pending change:", pendingChanges);

    switch (pendingChanges.type) {
      case "endDate": {
        const { dayId, index, value } = pendingChanges;
        // Re-apply the date update logic directly to local state
        setLocalWorkingHours(prev => {
          const dayConfig = prev[dayId];
          if (!dayConfig || !dayConfig.hours || index < 0 || index >= dayConfig.hours.length) return prev;

          const updatedHours = [...dayConfig.hours];
          updatedHours[index] = {
            ...updatedHours[index],
            endDate: value // Apply the stored end date value (string or undefined)
          };
          // No need to sort here as only date changes
          return { ...prev, [dayId]: { ...dayConfig, hours: updatedHours } };
        });
        break;
      }
      case "removeHour": {
        const { dayId, index } = pendingChanges;
        // Call the separated removal logic
        performRemoveHours(dayId, index);
        break;
      }
      case "disableDay": {
        const { dayId } = pendingChanges;
        // Call the separated toggle logic with checked=false
        performToggleDay(dayId, false);
        break;
      }
      default:
        console.error("Unhandled pending change type");
    }

    setPendingChanges(null); // Clear pending change after applying
  };

  // Handle cancellation option change
  const handleCancellationOptionChange = (value: "auto" | "manual") => {
    setLocalCancellationOption(value)
  }

  // Handle warning confirmation
  const handleWarningConfirm = () => {
    if (!localCancellationOption || !pendingChanges) return;

    // Apply the schedule change FIRST
    applyPendingChange();

    // THEN handle the cancellation logic based on user choice
    if (localCancellationOption === "auto") {
      addAppointmentsToCancel(affectedAppointments);
      setCancellationOption("auto");
      setCancellationReason(localCancellationReason);
      console.log(`Scheduled ${affectedAppointments.length} appointments for auto-cancellation.`);
    } else if (localCancellationOption === "manual") {
      // For manual, we still add them to the context for tracking,
      // but the user is responsible for notification.
      addAppointmentsToCancel(affectedAppointments);
      setCancellationOption("manual");
      // Optionally show the list immediately after confirmation
      setShowAffectedAppointments(true);
      console.log(`Marked ${affectedAppointments.length} appointments for manual cancellation/notification.`);
    }

    // Reset dialog state
    setShowWarning(null);
    setAffectedAppointments([]); // Clear affected appointments list
    setLocalCancellationOption(null);
    setLocalCancellationReason("");
    // pendingChanges is cleared within applyPendingChange
  };

  // Handle warning cancellation
  const handleCancelWarning = () => {
    setShowWarning(null)
    setLocalCancellationOption(null)
    setLocalCancellationReason("")
    setPendingChanges(null)
  }

  // Export to Excel function
  const exportToExcel = (appointments: Appointment[], filename: string) => {
    // Create worksheet data
    const headers = ["Paciente", "Fecha", "Hora", "Contacto", "Cobertura"]

    // Create data rows
    const data = appointments.map(apt => [
      apt.patient,
      apt.date,
      apt.time,
      apt.contact || "No disponible",
      apt.coverage
    ])

    // Add headers to the beginning of the data array
    const wsData = [headers, ...data]

    // Create a worksheet
    const ws = XLSX.utils.aoa_to_sheet(wsData)

    // Create a workbook
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, "Turnos Cancelados")

    // Generate the Excel file and trigger download
    XLSX.writeFile(wb, `${filename}_${new Date().toISOString().split("T")[0]}.xlsx`)
  }

  // Render the main component
  const renderMainComponent = () => {
    if (!editedConfig || !selectedCenter) return null

    return (
      <Card className="border-0 shadow-none">
        <CardHeader className="p-0 pb-4">
          <CardTitle className="text-lg text-gray-900">Horarios Semanales</CardTitle>
        </CardHeader>
        <CardContent className="p-0 space-y-4">
        <div className="flex items-center gap-4">
          <Label className="w-30">Duración base por turno</Label>
          <Select
            value={editedConfig.appointmentSlotDuration.toString()}
            onValueChange={value => setEditedConfig({ ...editedConfig, appointmentSlotDuration: parseInt(value) })}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5 minutos</SelectItem>
              <SelectItem value="10">10 minutos</SelectItem>
              <SelectItem value="15">15 minutos</SelectItem>
              <SelectItem value="20">20 minutos</SelectItem>
              <SelectItem value="30">30 minutos</SelectItem>
              <SelectItem value="45">45 minutos</SelectItem>
              <SelectItem value="60">60 minutos</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3">
          {DAYS.map(day => {
            const isEnabled = localWorkingHours[day.id]?.enabled || false
            const isExpanded = expandedDays[day.id]
            const hours = localWorkingHours[day.id]?.hours || []
            const hasErrors = Object.keys(errors).some(key => key.startsWith(`${day.id}-`))
            const centerSchedule = selectedCenter ? getScheduleForDay(selectedCenter, day.id) : null;
            const centerIsClosedToday = !centerSchedule || !centerSchedule.enabled || centerSchedule.hours.length === 0;
            const dayTimeOptions = getTimeOptionsForDay(day.id)

            return (
              <Card
                key={day.id}
                className={`overflow-hidden border ${
                  hasErrors ? "border-red-200" : isEnabled ? "border-blue-200" : "border-gray-200"
                } shadow-sm transition-all duration-200 ${centerIsClosedToday ? "opacity-50" : ""}`}
              >
                <div className="flex">
                  <div
                    className={`w-2 ${
                      hasErrors ? "bg-red-500" : isEnabled ? "bg-green-500" : "bg-gray-300"
                    }`}
                  ></div>
                  <div className="flex-grow">
                    <div
                      className={`flex items-center justify-between p-4 cursor-pointer ${
                        hasErrors ? "bg-red-50 hover:bg-red-100" :
                        isEnabled ? "bg-blue-50 hover:bg-blue-100" :
                        "hover:bg-gray-50"
                      }`}
                      onClick={() => toggleDayExpansion(day.id)}
                    >
                      <div className="flex flex-col md:flex-row md:items-center gap-1 md:gap-3">
                        <Label className="font-medium text-base text-gray-900 cursor-pointer">{day.name}</Label>
                        <div className="flex flex-wrap items-center gap-2">
                          {hasErrors && (
                            <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                              <AlertCircle className="h-3 w-3 mr-1" /> Error
                            </Badge>
                          )}
                          <Badge
                            variant="outline"
                            className={isEnabled ? "bg-green-50 text-green-700 border-green-200" : "bg-gray-100 text-gray-500 border-gray-200"}
                          >
                            {isEnabled ? "Habilitado" : "Deshabilitado"}
                          </Badge>
                          {centerIsClosedToday && (
                            <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                              Establecimiento cerrado.
                            </Badge>
                          )}
                          {isEnabled && hours.length > 0 && (
                            <span className="text-sm text-gray-500">{formatWorkingHours(hours)}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        {isEnabled && (
                          <div className="flex items-center gap-2">
                            <Label className="text-sm text-gray-600">Frecuencia:</Label>
                            <Select
                              value={(localWorkingHours[day.id]?.weeksFrequency || 1).toString()}
                              onValueChange={value => {
                                const frequency = parseInt(value)
                                setLocalWorkingHours(prev => ({
                                  ...prev,
                                  [day.id]: {
                                    ...prev[day.id],
                                    weeksFrequency: frequency
                                  }
                                }))
                                handleUpdateWeeksFrequency(day.id, frequency)
                              }}
                            >
                              <SelectTrigger className="w-40">
                                <SelectValue placeholder="Cada..." />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">Cada semana</SelectItem>
                                <SelectItem value="2">Cada 2 semanas</SelectItem>
                                <SelectItem value="4">Cada 4 semanas</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                        <div className="flex items-center gap-3">
                          <Switch
                            checked={isEnabled}
                            onCheckedChange={checked => handleToggleDay(day.id, checked)}
                            onClick={e => e.stopPropagation()}
                            disabled={centerIsClosedToday}
                          />
                          {isExpanded ? (
                            <ChevronUp className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          )}
                        </div>
                      </div>
                    </div>
                    {isExpanded && (
                      <div
                        className={`p-4 pt-0 ${isEnabled ? "bg-white" : "bg-gray-50"} border-t border-gray-100 transition-all duration-300`}
                      >
                        {isEnabled ? (
                          <div className="mt-4 space-y-3">

                            {hours.length === 0 ? (
                              <div className="text-center text-gray-500 py-2">No hay horarios configurados</div>
                            ) : (
                              hours.map((hour, index) => (
                                <div
                                  key={index}
                                  className={`rounded-md border ${
                                    errors[`${day.id}-${index}`] ? "bg-red-50 border-red-200" : "bg-blue-50 border-blue-100"
                                  } p-4`}
                                >
                                  {/* Time Range Row - Simplified and more compact */}
                                  <div className="flex flex-wrap gap-4 items-center mb-3">
                                    {/* Time Selection with a cleaner look */}
                                    <div className="flex items-center gap-2 bg-white rounded-md p-1.5 border border-gray-200 shadow-sm">
                                      <Clock className="h-4 w-4 text-blue-500 ml-1" />
                                      <div className="flex items-center gap-2">
                                        <Select
                                          value={hour.start}
                                          onValueChange={value => handleHoursUpdate(day.id, index, "start", value)}
                                        >
                                          <SelectTrigger className="w-[5.5rem] border-0 p-1 shadow-none h-8">
                                            <SelectValue placeholder="Inicio" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            {dayTimeOptions.map(time => (
                                              <SelectItem key={time} value={time}>
                                                {time}
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                        <span className="text-gray-400">→</span>
                                        <Select
                                          value={hour.end}
                                          onValueChange={value => handleHoursUpdate(day.id, index, "end", value)}
                                        >
                                          <SelectTrigger className="w-[5.5rem] border-0 p-1 shadow-none h-8">
                                            <SelectValue placeholder="Fin" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            {dayTimeOptions.map(time => (
                                              <SelectItem key={time} value={time}>
                                                {time}
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                      </div>
                                    </div>

                                    {/* Date Range Row */}
                                    <div className="flex flex-wrap items-center gap-2 ml-auto">
                                      <div className="flex items-center mr-1">
                                        <span className="text-sm text-gray-500">Período:</span>
                                      </div>

                                      {/* Start Date */}
                                      <Popover>
                                        <PopoverTrigger asChild>
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            className={cn(
                                              "h-8 text-xs px-2",
                                              !hour.startDate && "border-dashed text-blue-500" // Style for undefined start date
                                            )}
                                          >
                                            <CalendarIcon className="h-3.5 w-3.5 mr-1" />
                                            {hour.startDate ? (
                                              // Add 1 day to display correctly due to timezone offset on selection
                                              format(new Date(parseISO(hour.startDate)), "dd/MM/yyyy")
                                            ) : (
                                              "Indefinido" // Display "Indefinido"
                                            )}
                                          </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0" align="start">
                                          {/* Add button to clear start date */}
                                          <div className="p-2 border-b border-gray-100">
                                            <Button
                                              variant="ghost"
                                              className="w-full justify-start text-left font-normal text-blue-500 text-sm"
                                              onClick={() => handleHoursUpdate(day.id, index, "startDate", "clear")}
                                            >
                                              Indefinido (sin fecha de inicio)
                                            </Button>
                                          </div>
                                          <Calendar
                                            mode="single"
                                            selected={hour.startDate ? new Date(parseISO(hour.startDate)) : undefined}
                                            onSelect={(date) => {
                                              if (date) {
                                                // Adjust date before formatting to handle timezone offset on input
                                                const adjustedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                                                // No +1 day adjustment needed here if parseISO handles the string correctly later
                                                handleHoursUpdate(day.id, index, "startDate", format(adjustedDate, 'yyyy-MM-dd'));
                                              } else {
                                                // Handle case where user deselects date (might not happen with mode="single")
                                                handleHoursUpdate(day.id, index, "startDate", "clear");
                                              }
                                            }}
                                            disabled={(date) => {
                                              // Disable past dates ONLY if an end date is set and the date is after it
                                              if (hour.endDate) {
                                                 const endDate = new Date(parseISO(hour.endDate));
                                                 endDate.setHours(0, 0, 0, 0);
                                                 const targetDate = new Date(date);
                                                 targetDate.setHours(0,0,0,0);
                                                 if (targetDate > endDate) return true;
                                              }
                                              // Don't disable past dates by default for start date
                                              // Allow setting start date in the past if needed
                                              return false;
                                            }}
                                            weekStartsOn={1} // Monday
                                            initialFocus
                                          />
                                        </PopoverContent>
                                      </Popover>

                                      <span className="text-gray-400">→</span>

                                      {/* End Date */}
                                      <Popover>
                                        <PopoverTrigger asChild>
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            className={cn(
                                              "h-8 text-xs px-2",
                                              !hour.endDate && "border-dashed text-blue-500" // Style for undefined end date
                                            )}
                                          >
                                            <CalendarIcon className="h-3.5 w-3.5 mr-1" />
                                            {hour.endDate ? (
                                              // Add 1 day to display correctly due to timezone offset on selection
                                              format(new Date(parseISO(hour.endDate)), "dd/MM/yyyy")
                                            ) : (
                                              "Indefinido" // Display "Indefinido"
                                            )}
                                          </Button>
                                        </PopoverTrigger>
                                        <PopoverContent className="w-auto p-0" align="start">
                                          {/* Add button to clear end date */}
                                          <div className="p-2 border-b border-gray-100">
                                            <Button
                                              variant="ghost"
                                              className="w-full justify-start text-left font-normal text-blue-500 text-sm"
                                              onClick={() => handleHoursUpdate(day.id, index, "endDate", "clear")}
                                            >
                                              Indefinido (sin fecha de fin)
                                            </Button>
                                          </div>
                                          <Calendar
                                            mode="single"
                                            selected={hour.endDate ? new Date(parseISO(hour.endDate)) : undefined}
                                            onSelect={(date) => {
                                              if (date) {
                                                const adjustedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                                                handleHoursUpdate(day.id, index, "endDate", format(adjustedDate, 'yyyy-MM-dd'));
                                              } else {
                                                handleHoursUpdate(day.id, index, "endDate", "clear");
                                              }
                                            }}
                                            disabled={(date) => {
                                              const today = new Date();
                                              today.setHours(0, 0, 0, 0);
                                              const targetDate = new Date(date);
                                              targetDate.setHours(0,0,0,0);

                                              // Disable dates before today
                                              if (targetDate < today) return true;

                                              // Disable dates before the start date, if start date is set
                                              if (hour.startDate) {
                                                const startDate = new Date(parseISO(hour.startDate));
                                                startDate.setHours(0, 0, 0, 0);
                                                if (targetDate < startDate) return true;
                                              }

                                              return false;
                                            }}
                                            weekStartsOn={1} // Monday
                                            initialFocus
                                          />
                                        </PopoverContent>
                                      </Popover>

                                      <Button
                                        variant="outline"
                                        size="icon"
                                        className="h-8 w-8 border-red-200 hover:bg-red-50 ml-1"
                                        onClick={() => handleRemoveHours(day.id, index)}
                                      >
                                        <Trash className="h-3.5 w-3.5 text-red-500" />
                                      </Button>
                                    </div>
                                  </div>

                                  {/* Error Message - More visible and better formatted */}
                                  {errors[`${day.id}-${index}`] && (
                                    <div className="text-red-600 text-sm p-2 bg-red-50 rounded border border-red-200 mt-2 flex items-start">
                                      <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                                      <span>{errors[`${day.id}-${index}`]}</span>
                                    </div>
                                  )}

                                </div>
                              ))
                            )}
                            <div className="mt-3">
                              {/* Add tiny indicator with establishment hours */}
                              <div className="flex items-center justify-end mb-2">
                                <div className="flex items-center text-xs text-blue-500">
                                  <Clock className="h-3 w-3 mr-1" />
                                  <span>{getCenterHoursInfo(day.id)}</span>
                                </div>
                              </div>

                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleAddHours(day.id)}
                                className="w-full border-blue-200 text-blue-600 hover:bg-blue-50 bg-white shadow-sm"
                                disabled={getCenterHoursForDay(day.id).length === 0}
                              >
                                <Plus className="h-4 w-4 mr-2" />
                                Agregar franja horaria
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center text-sm text-gray-500 py-4">
                            {centerIsClosedToday
                              ? "El establecimiento no abre este día."
                              : "Habilite este día para configurar horarios."}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            )
          })}
        </div>
      </CardContent>
    </Card>
    )
  }

  return (
    <>
      {/* Main component JSX */}
      {renderMainComponent()}

      {/* Warning Dialog for Affected Appointments */}
      <AlertDialog open={!!showWarning} onOpenChange={handleCancelWarning}>
        <AlertDialogContent className="sm:max-w-[600px]">
          <AlertDialogHeader>
            <AlertDialogTitle>Advertencia</AlertDialogTitle>
            <AlertDialogDescription>{showWarning}</AlertDialogDescription>
          </AlertDialogHeader>
          {/* Show affected appointments list if there are any, regardless of the action */}
          {affectedAppointments.length > 0 && (
            <ul className="mt-2 max-h-[150px] overflow-y-auto">
              {affectedAppointments.map((apt, index) => (
                <li key={index} className="text-sm">
                  {/* Use getPatientNameForAppointment from context */}
                  {getPatientNameForAppointment(apt)} - {apt.date} {apt.time}
                </li>
              ))}
            </ul>
          )}

          <div className="py-4">
            <RadioGroup value={localCancellationOption || ""} onValueChange={(value) => handleCancellationOptionChange(value as "auto" | "manual")}>
              <div className="flex items-start space-x-2 mb-4">
                <RadioGroupItem value="auto" id="auto-cancel" className="mt-1" />
                <div className="grid gap-1.5">
                  <Label htmlFor="auto-cancel" className="font-medium">Cancelar automáticamente todos los turnos afectados</Label>
                  {localCancellationOption === "auto" && (
                    <div className="mt-2">
                      <Label htmlFor="cancel-reason" className="text-sm text-gray-600 mb-1 block">Motivo de la cancelación (se enviará por email a los pacientes)</Label>
                      <Textarea
                        id="cancel-reason"
                        placeholder="Ingrese el motivo de la cancelación"
                        value={localCancellationReason}
                        onChange={(e) => setLocalCancellationReason(e.target.value)}
                        className="resize-none"
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-start space-x-2">
                <RadioGroupItem value="manual" id="manual-cancel" className="mt-1" />
                <div className="grid gap-1.5">
                  <Label htmlFor="manual-cancel" className="font-medium">Cancelar y notificar manualmente a los pacientes</Label>
                  <p className="text-sm text-gray-600">Los turnos serán cancelados en el sistema, pero deberá notificar a los pacientes manualmente.</p>
                  {localCancellationOption === "manual" && (
                    <div className="mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() => setShowAffectedAppointments(true)}
                      >
                        <Eye className="h-4 w-4" />
                        Ver turnos afectados
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </RadioGroup>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelWarning}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleWarningConfirm}
              disabled={!localCancellationOption || (localCancellationOption === "auto" && !localCancellationReason.trim())}
            >
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog for Viewing Affected Appointments */}
      <Dialog open={showAffectedAppointments} onOpenChange={() => setShowAffectedAppointments(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Pacientes afectados</DialogTitle>
          </DialogHeader>
          <div className="max-h-[400px] overflow-y-auto">
            <div className="mb-4">
              <div className="bg-gray-50 p-3 rounded-md mb-3">
                <h3 className="font-medium mb-1">Turnos afectados por cambio de período</h3>
                <p className="text-sm text-gray-600">Estos turnos serán cancelados al guardar los cambios.</p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Paciente</th>
                      <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Fecha</th>
                      <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Hora</th>
                      <th className="py-2 px-3 text-left text-sm font-medium text-gray-700">Contacto</th>
                    </tr>
                  </thead>
                  <tbody>
                    {affectedAppointments.map((apt, index) => (
                      <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                        {/* Use getPatientNameForAppointment from context */}
                        <td className="py-2 px-3 text-sm">{getPatientNameForAppointment(apt)}</td>
                        <td className="py-2 px-3 text-sm">{apt.date}</td>
                        <td className="py-2 px-3 text-sm">{apt.time}</td>
                        <td className="py-2 px-3 text-sm">{apt.contact || "No disponible"}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => exportToExcel(affectedAppointments, "turnos_cancelados")}
                >
                  <FileDown className="h-4 w-4" />
                  Exportar a Excel
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowAffectedAppointments(false)}
            >
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}