import {NextRequest, NextResponse} from 'next/server';

export interface AppointmentCreationRequest {
    medicalCenterId: number;
    professionalId: number;
    healthInsuranceId: number;
    patientId: number;
    consultationTypeId: number;
    patientNotes: string;
    consultationTypeDescription: string;
    price: number;
    appointmentSlotDuration: number;
    employeeUserId: number;
    date: string; // Format: "DD-MM-YYYY"
    startTime: string; // Format: "HH:MM:SS"
}

export async function POST(request: NextRequest) {
    try {
        const body: AppointmentCreationRequest = await request.json();

        // Validate required fields
        const requiredFields: (keyof AppointmentCreationRequest)[] = [
            'medicalCenterId',
            'professionalId',
            'healthInsuranceId',
            'patientId',
            'consultationTypeId',
            'appointmentSlotDuration',
            'employeeUserId',
            'date',
            'startTime'
        ];

        for (const field of requiredFields) {
            if (body[field] === undefined || body[field] === null) {
                return NextResponse.json(
                    {error: `Missing required field: ${field}`},
                    {status: 400}
                );
            }
        }

        // Validate date format (DD-MM-YYYY)
        const dateRegex = /^\d{2}-\d{2}-\d{4}$/;
        if (!dateRegex.test(body.date)) {
            return NextResponse.json(
                {error: 'Invalid date format. Expected DD-MM-YYYY'},
                {status: 400}
            );
        }

        // Validate time format (HH:MM:SS)
        const timeRegex = /^\d{2}:\d{2}:\d{2}$/;
        if (!timeRegex.test(body.startTime)) {
            return NextResponse.json(
                {error: 'Invalid time format. Expected HH:MM:SS'},
                {status: 400}
            );
        }

        // Make request to external API
        const response = await fetch(
            `${process.env.BACKEND_URL}/appointment/from-medical-center/TURNERA`,
            {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to create appointment',
                    details: errorText,
                    status: response.status
                },
                {status: response.status}
            );
        }

        const result = await response.json();
        return NextResponse.json(result, {status: 201});

    } catch (error) {
        console.error('Error creating appointment:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            {status: 500}
        );
    }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
    return NextResponse.json({}, {status: 200});
}
