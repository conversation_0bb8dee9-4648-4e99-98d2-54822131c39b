"use client"

import {useEffect, useState} from "react"
import {
    AlertTriangle,
    ArrowUpDown,
    BarChart2,
    Calendar,
    CalendarDays,
    CalendarRange,
    Check,
    ChevronLeft,
    Clock,
    Eye,
    Info,
    Menu,
    Plus,
    Search,
    Settings,
    Share2,
    SortAsc,
    Timer,
    Users,
    X
} from "lucide-react"
import {useAuth} from "@/hooks/useAuth"
import {Input} from "@/components/ui/input"
import {But<PERSON>} from "@/components/ui/button"
import {<PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger} from "@/components/ui/tooltip"
import {Card, CardContent, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
import {cn} from "@/lib/utils"
import Link from "next/link"
import Image from "next/image"
import {useParams, usePathname, useRouter} from "next/navigation"
import {MedicalCenterUserPill} from "@/components/ui/MedicalCenterUserPill"
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar"
import {Doctor<PERSON>ardLoading} from "@/components/ui/doctor-card-loading"
import {LoadingButton} from "@/components/ui/loading-button"
import MedicalCenterPageSkeleton from "@/components/ui/MedicalCenterPageSkeleton"
import {getEmployeeUserIdOrFail} from "@/utils/userUtils"
import {PatientDetailsDialog} from "@/components/schedulecomponents/patient-details-dialog"
import {PatientListDialog} from "@/components/schedulecomponents/patient-list-dialog"
import {useMedicalCenterEventsContext} from "@/contexts/MedicalCenterEventsContext"
import {MedicalCenterRoleForEmployeeUser} from "@/types/MedicalCenter/medicalCenterRoleForEmployeeUser";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {PatientResponse} from "@/types/patient/patientResponse";
import {PatientAppointment} from "@/types/patient/patientAppointment";
import {UserRole} from "@/types/users";
import {useContext} from "react"
import {NewDoctorContext} from "@/contexts/NewDoctorContext"
import NewDoctorDialog from "@/components/configuration/NewDoctorDialog"

export default function MedicalCenterDashboardPage() {
    const [searchTerm, setSearchTerm] = useState("")
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
    const [selectedPatient, setSelectedPatient] = useState<PatientResponse | null>(null)
    const [showPatientDetails, setShowPatientDetails] = useState(false)
    const [showPatientList, setShowPatientList] = useState(false)
    const [sortOrder, setSortOrder] = useState<"alphabetical" | "earliestAgenda">("alphabetical")
    const [copiedDoctorId, setCopiedDoctorId] = useState<string | null>(null)
    const [loadingDoctorId, setLoadingDoctorId] = useState<string | null>(null)
    const [loadingView, setLoadingView] = useState<"day" | "week" | "month" | null>(null)
    const [isAnalyticsLoading, setIsAnalyticsLoading] = useState(false)
    const [isConfigLoading, setIsConfigLoading] = useState(false)
    const [isShareCopied, setIsShareCopied] = useState(false)
    const [showGreeting, setShowGreeting] = useState(true)
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
    const [professionalsView, setProfessionalsView] = useState<"cards" | "compact" | "table">("cards")
    
    // New Doctor Dialog context
    const { setIsNewDoctorDialogOpen } = useContext(NewDoctorContext)
    const getTodayKey = () => {
        const today = new Date()
        return `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`
    }
    const handleCloseGreeting = () => {
        try {
            localStorage.setItem('greetingDismissedAt', getTodayKey())
        } catch {
        }
        setShowGreeting(false)
    }
    useEffect(() => {
        try {
            const saved = localStorage.getItem('greetingDismissedAt')
            if (saved) {
                const todayKey = getTodayKey()
                if (saved === todayKey) {
                    setShowGreeting(false)
                }
            }
        } catch {
            // ignore storage errors
        }
    }, [])

    const SIDEBAR_STATE_KEY = 'sidebarCollapsed'
    const PROFESSIONALS_VIEW_KEY = 'professionalsView'

    // Restore sidebar collapsed state from localStorage per user (browser)
    useEffect(() => {
        try {
            const saved = localStorage.getItem(SIDEBAR_STATE_KEY)
            if (saved !== null) setIsSidebarCollapsed(saved === '1')
        } catch {
            // ignore storage errors
        }
    }, [])

    // Restore professionals view preference from localStorage
    useEffect(() => {
        try {
            const saved = localStorage.getItem(PROFESSIONALS_VIEW_KEY)
            if (saved && ['cards', 'compact', 'table'].includes(saved)) {
                setProfessionalsView(saved as "cards" | "compact" | "table")
            }
        } catch {
            // ignore storage errors
        }
    }, [])

    const handleToggleSidebar = () => {
        setIsSidebarCollapsed((prev) => {
            const next = !prev
            try {
                localStorage.setItem(SIDEBAR_STATE_KEY, next ? '1' : '0')
            } catch {
                // ignore storage errors
            }
            return next
        })
    }

    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    const router = useRouter()
    const pathname = usePathname()
    const params = useParams()
    const medicalCenterId = Number.parseInt(params.medicalCenterId as string)


    const {currentUser, isAuthenticated, isLoading, logout} = useAuth()
    const [medicalCenter, setMedicalCenter] = useState<MedicalCenterRoleForEmployeeUser | undefined>()


    const {
        isConnected: sseConnected,
        error: sseError,
        lastEvent,
        eventManager,
        disconnect: disconnectSSE,
        reconnect: reconnectSSE,
        dataStore,
        initialize,
        isInitialized
    } = useMedicalCenterEventsContext()

    // Initialize the connection when component mounts
    useEffect(() => {
        setLoading(true)
        if (isLoading) return;
        if (!isAuthenticated || !currentUser) {
            // Not authenticated, redirect to login
            router.push("/plataforma/establecimiento/login");
            return;
        }
        const employeeUserId = getEmployeeUserIdOrFail(currentUser)
        setMedicalCenter(currentUser.medicalCenters?.find(center => center.id === medicalCenterId))
        if (medicalCenterId && employeeUserId && !isInitialized) {
            initialize(medicalCenterId, employeeUserId)
        }
        setLoading(false)
    }, [medicalCenterId, isAuthenticated, isLoading, currentUser, initialize, isInitialized, dataStore])


    // Left intentionally available for future inline patient search integration

    useEffect(() => {
        const cookieElement = document.getElementById('mc-cookie-value');
        if (cookieElement) {
            cookieElement.textContent = document.cookie || 'No cookies found';
        }

        const checkAuthBtn = document.getElementById('mc-check-auth-btn');

        const handleCheckAuth = () => {
            const email = localStorage.getItem('authenticatedUserEmail');
            alert(`localStorage email: ${email || 'not found'}`);

            if (email) {
                const users = JSON.parse(localStorage.getItem('medical-scheduler-users') || '[]');
                const user = users.find((u: { email: string }) => u.email.toLowerCase() === email.toLowerCase());
                alert(`User found: ${user ? JSON.stringify(user) : 'not found'}`);
            }
        };

        if (checkAuthBtn) {
            checkAuthBtn.addEventListener('click', handleCheckAuth);
        }

        return () => {
            if (checkAuthBtn) {
                checkAuthBtn.removeEventListener('click', handleCheckAuth);
            }
        };
    }, [])

    if (loading || !dataStore) {
        return <MedicalCenterPageSkeleton/>;
    }
    console.log({...dataStore})

    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white">
                <div className="text-center bg-white p-8 rounded-xl shadow-lg border border-red-100">
                    <div className="relative">
                        <div className="rounded-full h-16 w-16 bg-red-50 mx-auto mb-6 flex items-center justify-center">
                            <AlertTriangle className="h-8 w-8 text-red-500"/>
                        </div>
                    </div>
                    <h3 className="text-[1.125rem] font-medium text-gray-900 mb-1">Error al cargar información</h3>
                    <p className="text-gray-500 mb-4">{error}</p>
                    <Button
                        variant="outline"
                        className="border-blue-200 text-blue-600 hover:bg-blue-50"
                        onClick={() => window.location.reload()}
                    >
                        Reintentar
                    </Button>
                </div>
            </div>
        );
    }

    if (!medicalCenter) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white">
                <div className="text-center bg-white p-8 rounded-xl shadow-lg border border-red-100">
                    <div className="relative">
                        <div className="rounded-full h-16 w-16 bg-red-50 mx-auto mb-6 flex items-center justify-center">
                            <AlertTriangle className="h-8 w-8 text-red-500"/>
                        </div>
                    </div>
                    <h3 className="text-[1.125rem] font-medium text-gray-900 mb-1">Establecimiento no encontrado</h3>
                    <p className="text-gray-500 mb-4">No pudimos encontrar el establecimiento solicitado.</p>
                    <Button
                        variant="outline"
                        className="border-blue-200 text-blue-600 hover:bg-blue-50"
                        onClick={() => router.push('/plataforma/establecimiento')}
                    >
                        Volver a establecimientos
                    </Button>
                </div>
            </div>
        );
    }

    const filteredInitialInfo = dataStore.doctors.filter(
        info =>
            info.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            info.specialties.some(specialty => specialty.toLowerCase().includes(searchTerm.toLowerCase()))
    )

    // Sort initial information
    const sortedInitialInfo = [...filteredInitialInfo].sort((a, b) => {
        if (sortOrder === "alphabetical") {
            return a.fullName.localeCompare(b.fullName)
        } else {
            // Sort by next appointment (earliest first)
            if (!a.nextAppointment && !b.nextAppointment) return a.fullName.localeCompare(b.fullName)
            if (!a.nextAppointment) return 1
            if (!b.nextAppointment) return -1

            const timeComparison = new Date(a.nextAppointment).getTime() - new Date(b.nextAppointment).getTime()
            if (timeComparison === 0) return a.fullName.localeCompare(b.fullName)
            return timeComparison
        }
    })

    // Compact stats for header chips
    const totalProfessionals = dataStore.doctors.length
    const handleViewChange = (view: "day" | "week" | "month") => {
        setLoadingView(view)
        router.push(`/plataforma/establecimiento/${medicalCenterId}/general?view=${view}`)
    }

    // Greeting and day stats for assistant banner
    const getGreeting = () => {
        const hour = new Date().getHours()
        if (hour < 12) return "Buen día"
        if (hour < 19) return "Buenas tardes"
        return "Buenas noches"
    }

    const userDisplayName = (() => {
        return currentUser?.name || "usuario"
    })()

    const totalUpcomingAppointments = dataStore.doctors.reduce((sum: number, d: DoctorsForMedicalCenter) => sum + (d.nextAppointmentQuantity ?? 0), 0)
    const totalAppointmentsToday = dataStore.doctors.reduce((sum: number, d: DoctorsForMedicalCenter) => {
        if (!d.nextAppointment) return sum
        const appt = new Date(d.nextAppointment)
        const now = new Date()
        const isSameDay = appt.getFullYear() === now.getFullYear() && appt.getMonth() === now.getMonth() && appt.getDate() === now.getDate()
        return isSameDay ? sum + (d.nextAppointmentQuantity ?? 0) : sum
    }, 0)
    const doctorsWorkingToday = dataStore.doctors.reduce((count: number, d: DoctorsForMedicalCenter) => {
        if (!d.nextAppointment) return count
        const appt = new Date(d.nextAppointment)
        const now = new Date()
        const isSameDay = appt.getFullYear() === now.getFullYear() && appt.getMonth() === now.getMonth() && appt.getDate() === now.getDate()
        return isSameDay ? count + 1 : count
    }, 0)
    const approachingAppointments = Math.max(totalUpcomingAppointments - totalAppointmentsToday, 0)

    const handleAnalyticsClick = () => {
        setIsAnalyticsLoading(true)
        router.push(`/plataforma/establecimiento/${medicalCenterId}/analytics`)
    }

    const handleConfigClick = () => {
        setIsConfigLoading(true)
        router.push(`/plataforma/establecimiento/${medicalCenterId}/configuration`)
    }

    const handleAgendasClick = () => {
        router.push(`/plataforma/establecimiento/${medicalCenterId}`)
    }

    const copyMedicalCenterBookingUrl = () => {
        const url = `${window.location.origin}/plataforma/reservar/cm/${medicalCenterId}`
        navigator.clipboard.writeText(url)
            .then(() => {
                setIsShareCopied(true)
                setTimeout(() => setIsShareCopied(false), 2000)
            })
            .catch(err => console.error('Failed to copy URL: ', err))
    }

    const getTruncatedSpecialties = (specialties: string[]) => {
        const maxDisplay = 2
        if (specialties.length <= maxDisplay) {
            return specialties.join(", ")
        }
        return `${specialties.slice(0, maxDisplay).join(", ")} +${specialties.length - maxDisplay} más`
    }

    // Patient selection from other UI surfaces can open details dialog

    const handleAppointmentSelect = (appointment: PatientAppointment) => {
        const {doctorId, date} = appointment
        router.push(`/plataforma/profesional/${doctorId}?date=${date}`)
        setShowPatientDetails(false)
    }

    const handleDoctorSelect = (doctorInfo: DoctorsForMedicalCenter) => {
        // Store the selected doctor information in localStorage
        localStorage.setItem('selectedDoctorInfo', JSON.stringify(doctorInfo))
        setLoadingDoctorId(doctorInfo.id.toString())
        router.push(`/plataforma/establecimiento/${medicalCenterId}/${doctorInfo.id}`)
    }

    const copyDoctorBookingUrl = (doctorId: string, e: React.MouseEvent) => {
        e.stopPropagation()
        e.preventDefault()

        const url = `${window.location.origin}/plataforma/reservar/cita?doctorId=${doctorId}&medicalCenterId=${medicalCenterId}&source=medicalCenter`
        navigator.clipboard.writeText(url)
            .then(() => {
                setCopiedDoctorId(doctorId)
                setTimeout(() => setCopiedDoctorId(null), 2000)
            })
            .catch(err => console.error('Failed to copy URL: ', err))
    }

    const formatNextAppointment = (nextAppointment: string | null) => {
        if (!nextAppointment) return "Sin turnos futuros"

        try {
            const date = new Date(nextAppointment)
            const now = new Date()
            const diffTime = date.getTime() - now.getTime()
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

            if (diffDays === 0) return "Próximo turno hoy"
            if (diffDays === 1) return "Próximo turno mañana"
            if (diffDays > 1) return `Próximo turno en ${diffDays} días`
            return "Turno pendiente"
        } catch {
            return "Sin turnos futuros"
        }
    }

    const getStatusColor = (nextAppointment: string | null) => {
        if (!nextAppointment) return "bg-muted-foreground"

        try {
            const date = new Date(nextAppointment)
            const now = new Date()
            const diffTime = date.getTime() - now.getTime()
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

            if (diffDays === 0) return "bg-green-500"
            if (diffDays === 1) return "bg-yellow-500"
            if (diffDays > 1) return "bg-blue-500"
            return "bg-muted-foreground"
        } catch {
            return "bg-muted-foreground"
        }
    }

    const truncateName = (name: string): string => {
        return name.length > 26 ? `${name.substring(0, 24)}...` : name
    }

    return (
        <>
            {/* ...existing styles... */}
            <style jsx global>{`
                body {
                    background: #ffffff;
                    position: relative;
                }
                html, body {
                    height: 100%;
                    overflow: hidden; /* Prevent window scroll; we'll scroll the main content area */
                }

                /* Card styling */
                .card-grid-item {
                    opacity: 1;
                }

                /* Mobile bottom navigation */
                @media (max-width: 768px) {
                    .mobile-bottom-nav {
                        position: fixed;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        background: white;
                        border-top: 1px solid #e5e9f2;
                        z-index: 50;
                        padding: 0.5rem 1rem;
                        height: 4.5rem;
                        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
                    }
                    
                    .mobile-content {
                        padding-bottom: 7rem; /* Increased padding to ensure content is not hidden */
                        min-height: calc(100vh - 4.5rem - 4rem); /* viewport height minus bottom nav and top bar */
                        /* Ensure content can scroll to the very bottom */
                        scroll-behavior: smooth;
                        /* Prevent content from being cut off */
                        overflow-y: auto;
                        -webkit-overflow-scrolling: touch;
                    }

                    /* Use dynamic viewport units for better mobile support */
                    .mobile-layout {
                        height: 100vh;
                        height: 100dvh; /* dynamic viewport height */
                        /* Prevent iOS Safari from adding extra padding */
                        -webkit-overflow-scrolling: touch;
                        /* Ensure proper mobile layout */
                        display: flex;
                        flex-direction: column;
                    }

                    /* Ensure the last item is fully visible */
                    .mobile-content .grid > *:last-child {
                        margin-bottom: 2rem;
                    }

                    /* Additional safety margin for the last card */
                    .mobile-content .grid:last-child {
                        padding-bottom: 1rem;
                    }
                }
            `}</style>

            <div className="h-screen overflow-hidden bg-white">
                {/* Desktop Layout */}
                <div className={cn("hidden md:grid w-full gap-0 transition-[grid-template-columns] duration-300 ease-in-out", isSidebarCollapsed ? "grid-cols-[4rem_1fr]" : "grid-cols-[15rem_1fr]") }>
                    {/* Left Sidebar */}
                    <aside
                        className={cn("flex flex-col sticky top-0 h-screen bg-white py-1.5 transition-[padding] duration-300 ease-in-out", isSidebarCollapsed ? "px-2" : "px-2")}>
                        <div className="flex items-center justify-between gap-2 px-2 pb-2">
                            <Link
                                href={`/plataforma/establecimiento/${medicalCenterId}`}
                                className={cn(
                                    "flex items-center gap-2 transition-all duration-300",
                                    isSidebarCollapsed ? "opacity-0 translate-x-2 pointer-events-none" : "opacity-100 translate-x-0 delay-150"
                                )}
                            >
                                <Image src="/images/turnera-logo.svg" alt="Turnera" width={120} height={32}
                                       className="h-7 w-auto align-middle relative top-[4.5px]"/>
                            </Link>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-9 w-9 p-0 shrink-0 rounded-full text-slate-600 hover:bg-slate-100 ml-auto relative top-[4.5px]"
                                onClick={handleToggleSidebar}
                                aria-label={isSidebarCollapsed ? "Expandir barra lateral" : "Contraer barra lateral"}
                            >
                                <ChevronLeft
                                    className={cn("h-4 w-4 transition-transform duration-300", isSidebarCollapsed ? "rotate-180" : "rotate-0")}/>
                            </Button>
                        </div>
                        <div
                            className={cn("px-2 py-3 flex items-center justify-between gap-2 overflow-hidden transition-[max-height,opacity] duration-300 ease-in-out", isSidebarCollapsed ? "opacity-0 max-h-0" : "opacity-100 max-h-20")}>
                            <p className="text-[1.1rem] text-slate-700 font-semibold truncate"
                               title={medicalCenter?.name}>{medicalCenter?.name}</p>
                            <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8 rounded-full text-blue-700 border-blue-300/60 hover:bg-blue-50"
                                onClick={copyMedicalCenterBookingUrl}
                                aria-label="Compartir enlace del centro"
                            >
                                {isShareCopied ? <Check className="h-4 w-4"/> : <Share2 className="h-4 w-4"/>}
                            </Button>
                        </div>
                        <nav className="mt-2 space-y-1 flex-1">
                            <Button
                                variant={pathname === `/plataforma/establecimiento/${medicalCenterId}` ? "secondary" : "ghost"}
                                className={cn("w-full text-[0.95rem] font-medium justify-start gap-2 pl-4")}
                                onClick={handleAgendasClick}
                                title="Agenda"
                            >
                                <Calendar className="h-3.5 w-3.5"/>
                                <span
                                    className={cn(
                                        "whitespace-nowrap overflow-hidden inline-block transition-all duration-300",
                                        isSidebarCollapsed ? "opacity-0 -translate-x-2 max-w-0" : "opacity-100 translate-x-0 max-w-[10rem] delay-150"
                                    )}
                                >
                                    Agenda
                                </span>
                            </Button>
                            <LoadingButton
                                isLoading={isAnalyticsLoading}
                                variant={pathname === `/plataforma/establecimiento/${medicalCenterId}/analytics` ? "secondary" : "ghost"}
                                className={cn("w-full text-[0.95rem] font-medium [&>div]:gap-2 justify-start gap-2 pl-4")}
                                onClick={handleAnalyticsClick}
                                title="Estadísticas"
                            >
                                <BarChart2 className="h-3.5 w-3.5"/>
                                <span
                                    className={cn(
                                        "whitespace-nowrap overflow-hidden inline-block transition-all duration-300",
                                        isSidebarCollapsed ? "opacity-0 -translate-x-2 max-w-0" : "opacity-100 translate-x-0 max-w-[12rem] delay-150"
                                    )}
                                >
                                    Estadísticas
                                </span>
                            </LoadingButton>
                            <Button
                                variant="ghost"
                                className={cn("w-full text-[0.95rem] font-medium justify-start gap-2 pl-4")}
                                onClick={() => setShowPatientList(true)}
                                title="Pacientes"
                            >
                                <Users className="h-3.5 w-3.5"/>
                                <span
                                    className={cn(
                                        "whitespace-nowrap overflow-hidden inline-block transition-all duration-300",
                                        isSidebarCollapsed ? "opacity-0 -translate-x-2 max-w-0" : "opacity-100 translate-x-0 max-w-[10rem] delay-150"
                                    )}
                                >
                                    Pacientes
                                </span>
                            </Button>
                        </nav>
                        <div className="mt-auto px-2 pt-3 pb-1 border-t border-[#e5e9f2]">
                            <a
                                href="https://wa.me/541156098040?text=Hola!%20Tengo%20consultas%20sobre%20mi%20agenda."
                                target="_blank"
                                rel="noopener noreferrer"
                                className={cn("flex w-full items-center gap-2 rounded-md p-2 text-left text-[0.95rem] font-medium transition-all hover:bg-slate-100 justify-start pl-2")}
                                title="Hablá con soporte"
                            >
                                <div className="flex-shrink-0 w-4 h-4 flex items-center justify-center">
                                    <Image 
                                        src="/images/whatsapp-icon.svg" 
                                        alt="WhatsApp" 
                                        width={16} 
                                        height={16}
                                        className="h-4 w-4"
                                    />
                                </div>
                                <span
                                    className={cn(
                                        "whitespace-nowrap overflow-hidden inline-block transition-all duration-300",
                                        isSidebarCollapsed ? "opacity-0 -translate-x-2 max-w-0" : "opacity-100 translate-x-0 max-w-[12rem] delay-150"
                                    )}
                                >
                                    Hablá con soporte
                                </span>
                            </a>
                            <LoadingButton
                                isLoading={isConfigLoading}
                                variant={pathname === `/plataforma/establecimiento/${medicalCenterId}/configuration` ? "secondary" : "ghost"}
                                className={cn("w-full text-[0.95rem] font-medium [&>div]:gap-2 justify-start gap-2 pl-2")}
                                onClick={handleConfigClick}
                                title="Configuración"
                            >
                                <Settings className="h-3.5 w-3.5"/>
                                <span
                                    className={cn(
                                        "whitespace-nowrap overflow-hidden inline-block transition-all duration-300",
                                        isSidebarCollapsed ? "opacity-0 -translate-x-2 max-w-0" : "opacity-100 translate-x-0 max-w-[12rem] delay-150"
                                    )}
                                >
                                    Configuración
                                </span>
                            </LoadingButton>
                        </div>
                    </aside>

                    {/* Right Content */}
                    <section className="min-w-0 h-screen flex flex-col">
                        {/* Top bar with user pill at top right */}
                        <div
                            className={cn("grid items-center bg-white px-12 py-2 gap-3", isSidebarCollapsed ? "grid-cols-[auto_minmax(480px,900px)_auto]" : "grid-cols-[1fr_minmax(480px,900px)_auto]")}>
                            {isSidebarCollapsed && (
                                <div className="flex items-center gap-3">
                                    <Link href={`/plataforma/establecimiento/${medicalCenterId}`}
                                          className="inline-flex items-center">
                                        <Image src="/images/turnera-logo.svg" alt="Turnera" width={120} height={32}
                                               className="h-7 w-auto align-middle relative -top-[1.5px]"/>
                                    </Link>
                                    <div className="h-7 w-px bg-slate-200"/>
                                    <div className="flex items-center gap-2 min-w-0">
                                        <p className="text-[1.1rem] leading-none text-slate-700 font-semibold truncate max-w-[14rem]"
                                           title={medicalCenter?.name}>{medicalCenter?.name}</p>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            className="h-8 w-8 rounded-full text-blue-700 border-blue-300/60 hover:bg-blue-50"
                                            onClick={copyMedicalCenterBookingUrl}
                                            aria-label="Compartir enlace del centro"
                                        >
                                            {isShareCopied ? <Check className="h-4 w-4"/> :
                                                <Share2 className="h-4 w-4"/>}
                                        </Button>
                                    </div>
                                </div>
                            )}
                            {/* Search moved to topbar */}
                            <div className="w-full col-start-2 justify-self-center max-w-md xl:max-w-lg">
                                <div className="relative">
                                    <Search
                                        className="absolute left-4 top-1/2 -translate-y-1/2 text-slate-400 h-5 w-5"/>
                                    <Input
                                        type="text"
                                        className="w-full pl-12 pr-4 h-10 rounded-full border-[#e5e9f2] bg-[#f7f8fb] text-[#2d2f46] placeholder:text-slate-400 placeholder:text-[0.9rem] shadow-inner focus:border-blue-400 focus:ring-2 focus:ring-blue-100"
                                        placeholder="Buscar un profesional o especialidad"
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="justify-self-end">
                                <MedicalCenterUserPill
                                    medicalCenter={medicalCenter}
                                    currentUser={currentUser}
                                    medicalCenterId={medicalCenterId}
                                    logout={logout}
                                    showShare={false}
                                />
                            </div>
                        </div>

                        {/* Main content box with gradient background touching sidebar and topbar */}
                        <div className="bg-gradient-to-b from-[#f3f8fc] to-[#f4f8fa] border border-[#e5e9f2] shadow-inner rounded-tl-2xl flex-1 overflow-y-auto pt-12 pb-6 pl-12 pr-6">
                            <div className="w-full">
                                {/* Assistant-style greeting banner */}
                                {showGreeting && (
                                    <div
                                        className="mb-6 relative mr-auto max-w-3xl bg-gradient-to-b from-white via-slate-50 to-blue-50 rounded-2xl shadow-2xl shadow-slate-400/30 border border-slate-300/80 p-5">
                                        <button aria-label="Cerrar mensaje" onClick={handleCloseGreeting}
                                                className="absolute right-3 top-3 text-slate-500 hover:text-slate-700">
                                            <X className="h-4 w-4"/>
                                        </button>
                                        <p className="text-[1.125rem] font-semibold text-slate-700 truncate">{getGreeting()}, <span
                                            className="text-blue-700">{userDisplayName}</span></p>
                                        <p className="text-[0.95rem] text-slate-500 truncate">Hoy {doctorsWorkingToday === 1 ? 'atiende ' : 'atienden '}
                                            <span
                                                className="font-semibold text-blue-700">{doctorsWorkingToday}</span> {doctorsWorkingToday === 1 ? 'profesional' : 'profesionales'},
                                            hay <span
                                                className="font-semibold text-blue-700">{totalAppointmentsToday}</span> {totalAppointmentsToday === 1 ? 'turno' : 'turnos'} para
                                            hoy y <span
                                                className="font-semibold text-blue-700">{approachingAppointments}</span> {approachingAppointments === 1 ? 'turno para los próximos días' : 'turnos para los próximos días'}.
                                        </p>
                                    </div>
                                )}

                                {/* Title + segmented view (simple header) */}
                                <div className="flex items-center gap-3">
                                    <h2 className="text-[1.35rem] sm:text-[1.45rem] font-semibold tracking-tight text-slate-700">Agenda general</h2>
                                    {!showGreeting && (
                                        <button
                                            aria-label="Mostrar mensaje del día"
                                            onClick={() => setShowGreeting(true)}
                                            className="h-7 w-7 inline-flex items-center justify-center rounded-full bg-blue-50 text-blue-600 border border-blue-200 hover:bg-blue-100"
                                        >
                                            <Info className="h-3.5 w-3.5"/>
                                        </button>
                                    )}
                                </div>
                                <div
                                    className="mt-3 inline-flex rounded-lg border border-slate-200 bg-white p-1 shadow-sm">
                                    <button onClick={() => handleViewChange('day')}
                                            className={`px-4 py-2 rounded-md text-[0.95rem] font-medium inline-flex items-center gap-2 transition-all ${loadingView === 'day' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-700'}`}>
                                        <Calendar
                                            className={`h-4 w-4 ${loadingView === 'day' ? 'text-white' : 'text-blue-600'}`}/>
                                        Día
                                    </button>
                                    <button onClick={() => handleViewChange('week')}
                                            className={`px-4 py-2 rounded-md text-[0.95rem] font-medium inline-flex items-center gap-2 transition-all ${loadingView === 'week' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-700'}`}>
                                        <CalendarDays
                                            className={`h-4 w-4 ${loadingView === 'week' ? 'text-white' : 'text-blue-600'}`}/>
                                        Semana
                                    </button>
                                    <button onClick={() => handleViewChange('month')}
                                            className={`px-4 py-2 rounded-md text-[0.95rem] font-medium inline-flex items-center gap-2 transition-all ${loadingView === 'month' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-700'}`}>
                                        <CalendarRange
                                            className={`h-4 w-4 ${loadingView === 'month' ? 'text-white' : 'text-blue-600'}`}/>
                                        Mes
                                    </button>
                                </div>

                                {/* Separator between Agenda and Profesionales */}
                                <div className="mt-8 mb-4 h-px w-full bg-[#e5e9f2]"/>

                                {/* Professionals header + controls and list (single column) */}
                                <div className="mt-10">
                                                                    <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <h3 className="text-[1.35rem] sm:text-[1.45rem] font-semibold text-slate-700">Profesionales</h3>
                                        <TooltipProvider delayDuration={200}>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <button
                                                        onClick={() => setSortOrder(sortOrder === 'alphabetical' ? 'earliestAgenda' : 'alphabetical')}
                                                        className="inline-flex items-center gap-2 px-3 py-1 rounded-lg border border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-700 text-[0.875rem] transition-all shadow-sm"
                                                    >
                                                        {sortOrder === 'alphabetical' ? (
                                                            <>
                                                                <SortAsc className="h-4 w-4"/>
                                                                <span>A-Z</span>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <Timer className="h-4 w-4"/>
                                                                <span>Próximos</span>
                                                            </>
                                                        )}
                                                        <ArrowUpDown className="h-3 w-3 text-slate-400"/>
                                                    </button>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    {sortOrder === 'alphabetical'
                                                        ? 'Cambiar a ordenar por próximos turnos'
                                                        : 'Cambiar a ordenar alfabéticamente'
                                                    }
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>

                                                                        {/* View Toggle */}
                                    <button
                                        onClick={() => {
                                            const views: ("cards" | "compact" | "table")[] = ["cards", "compact", "table"];
                                            const currentIndex = views.indexOf(professionalsView);
                                            const nextIndex = (currentIndex + 1) % views.length;
                                            const nextView = views[nextIndex];
                                            setProfessionalsView(nextView);
                                            try {
                                                localStorage.setItem(PROFESSIONALS_VIEW_KEY, nextView);
                                            } catch {
                                                // ignore storage errors
                                            }
                                        }}
                                        className="inline-flex items-center gap-2 px-3 py-1.5 rounded-lg border border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-700 text-[0.875rem] transition-all shadow-sm"
                                    >
                                        <span>{professionalsView === "cards" ? "Tarjetas" : professionalsView === "compact" ? "Compacto" : "Lista"}</span>
                                        <Eye className="h-4 w-4 text-slate-400"/>
                                    </button>
                                </div>
                                    {/* Professionals */}
                                    <div className="mt-6">
                                        {professionalsView === "cards" && (
                                            <div className={cn(
                                                "grid grid-cols-1 xl:grid-cols-3 2xl:grid-cols-4 gap-6",
                                                isSidebarCollapsed ? "sm:grid-cols-3 lg:grid-cols-3" : "sm:grid-cols-2 lg:grid-cols-2"
                                            )}>
                                                {sortedInitialInfo.map((info) => (
                                                    <Card key={info.id}
                                                          className="group relative overflow-hidden border rounded-2xl transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 min-h-[12rem] flex flex-col">
                                                        <DoctorCardLoading doctorId={info.id.toString()}
                                                                           isLoading={loadingDoctorId === info.id.toString()}/>
                                                        <div
                                                            className="absolute top-3 right-3 z-20 opacity-0 group-hover:opacity-100 transition-opacity">
                                                            <TooltipProvider delayDuration={200}>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <Button variant="secondary" size="icon"
                                                                                className="h-8 w-8 rounded-full bg-white/80 backdrop-blur text-slate-600 hover:bg-blue-50"
                                                                                onClick={(e) => copyDoctorBookingUrl(info.id.toString(), e)}>
                                                                            {copiedDoctorId === info.id.toString() ?
                                                                                <Check className="h-4 w-4"/> :
                                                                                <Share2 className="h-4 w-4"/>}
                                                                        </Button>
                                                                    </TooltipTrigger>
                                                                    <TooltipContent
                                                                        side="left">{copiedDoctorId === info.id.toString() ? '¡URL copiada!' : 'Copiar URL de reserva'}</TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                        </div>

                                                        <div onClick={() => handleDoctorSelect(info)}
                                                             className={cn('flex flex-col h-full cursor-pointer', loadingDoctorId === info.id.toString() && 'cursor-wait')}>
                                                            <CardHeader
                                                                className="p-4 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white">
                                                                <div className="flex items-center gap-4">
                                                                                                                                         <div className="h-10 w-10 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 flex items-center justify-center">
                                                                         <span className="text-white font-semibold text-base">{info.fullName.substring(0, 2).toUpperCase()}</span>
                                                                     </div>
                                                                    <div className="min-w-0">
                                                                                                                                            <CardTitle
                                                                        className="font-semibold truncate text-[1.2rem]">{truncateName(info.fullName)}</CardTitle>
                                                                        <p className="text-[0.875rem] text-blue-100 truncate">{getTruncatedSpecialties(info.specialties)}</p>
                                                                    </div>
                                                                </div>
                                                            </CardHeader>
                                                            <CardContent className="p-4">
                                                                <div className="flex items-center justify-between">
                                                                    <div className="flex items-center gap-2">
                                                                        <span
                                                                            className={cn('inline-flex h-2.5 w-2.5 rounded-full', getStatusColor(info.nextAppointment))}></span>
                                                                        <span
                                                                            className="text-[0.9rem] text-slate-600">{formatNextAppointment(info.nextAppointment)}</span>
                                                                    </div>
                                                                    {info.nextAppointmentQuantity && info.nextAppointmentQuantity > 0 && (
                                                                        <span
                                                                            className="rounded-full bg-blue-100 text-blue-800 text-[0.8rem] font-semibold px-2 py-0.5">
                                                                            {info.nextAppointmentQuantity} {info.nextAppointmentQuantity === 1 ? 'turno' : 'turnos'}
                                                                        </span>
                                                                    )}
                                                                </div>
                                                            </CardContent>
                                                            <CardFooter
                                                                className="px-4 pb-4 flex items-center justify-between mt-auto">
                                                                <div className="flex items-center gap-2 text-blue-700">
                                                                    <Clock className="h-4 w-4"/>
                                                                    <span
                                                                        className="text-[0.9rem] font-medium">Ver agenda</span>
                                                                </div>
                                                                <div
                                                                    className="text-[0.9rem] text-slate-500">{info.medicalLicense && `MN: ${info.medicalLicense}`}</div>
                                                            </CardFooter>
                                                        </div>
                                                    </Card>
                                                ))}

                                                {/* Add new doctor placeholder card */}
                                                <div className="group relative overflow-hidden border-2 border-dashed border-blue-300/60 rounded-2xl transition-all duration-300 hover:border-blue-400/80 min-h-[12rem] flex flex-col bg-white hover:shadow-lg cursor-pointer"
                                                      onClick={() => setIsNewDoctorDialogOpen(true)}>
                                                    <div className="p-4 bg-gradient-to-br from-blue-50 via-blue-100 to-sky-100 border-b border-blue-200/30">
                                                        <div className="flex items-center gap-4">
                                                            <div className="h-10 w-10 rounded-full ring-2 ring-white/40 bg-blue-200 flex items-center justify-center">
                                                                <Users className="h-5 w-5 text-blue-600" />
                                                            </div>
                                                            <div className="min-w-0 flex-1">
                                                                <div className="font-medium truncate text-[1.2rem] text-blue-700">Agregar Profesional</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="p-4 flex-1 flex flex-col justify-center">
                                                        <div className="flex items-start gap-3">
                                                            <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center border-2 border-dashed border-blue-300/50 flex-shrink-0">
                                                                <Plus className="h-6 w-6 text-blue-400" />
                                                            </div>
                                                            <div className="flex-1">
                                                                <h3 className="text-[1.05rem] font-semibold text-blue-600 mb-0">
                                                                    Agregá un nuevo profesional
                                                                </h3>
                                                                <p className="text-blue-500/70 text-sm leading-relaxed">
                                                                    Sumá un profesional a tu equipo.
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {professionalsView === "compact" && (
                                            <div className={cn(
                                                "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-4",
                                                isSidebarCollapsed ? "lg:grid-cols-5 xl:grid-cols-5 2xl:grid-cols-6" : "lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5"
                                            )}>
                                                {sortedInitialInfo.map((info) => (
                                                    <Card key={info.id}
                                                          className="group relative overflow-hidden border rounded-lg transition-all duration-300 hover:shadow-md hover:-translate-y-0.5 min-h-[4.5rem] flex flex-col">
                                                        <DoctorCardLoading doctorId={info.id.toString()}
                                                                           isLoading={loadingDoctorId === info.id.toString()}/>
                                                        <div
                                                            className="absolute top-1.5 right-1.5 z-20 opacity-0 group-hover:opacity-100 transition-opacity">
                                                            <TooltipProvider delayDuration={200}>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <Button variant="secondary" size="icon"
                                                                                className="h-5 w-5 rounded-full bg-white/80 backdrop-blur text-slate-600 hover:bg-blue-50"
                                                                                onClick={(e) => copyDoctorBookingUrl(info.id.toString(), e)}>
                                                                            {copiedDoctorId === info.id.toString() ?
                                                                                <Check className="h-2.5 w-2.5"/> :
                                                                                <Share2 className="h-2.5 w-2.5"/>}
                                                                        </Button>
                                                                    </TooltipTrigger>
                                                                    <TooltipContent
                                                                        side="left">{copiedDoctorId === info.id.toString() ? '¡URL copiada!' : 'Copiar URL de reserva'}</TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                        </div>

                                                        <div onClick={() => handleDoctorSelect(info)}
                                                             className={cn('flex flex-col h-full cursor-pointer p-3', loadingDoctorId === info.id.toString() && 'cursor-wait')}>
                                                            <div className="flex items-start gap-3 mb-2">
                                                                <div className="h-8 w-8 rounded-lg bg-blue-100 border border-blue-200 flex items-center justify-center flex-shrink-0">
                                                                    <span className="text-blue-700 font-semibold text-sm">{info.fullName.substring(0, 2).toUpperCase()}</span>
                                                                </div>
                                                                <div className="min-w-0 flex-1">
                                                                    <h4 className="font-semibold text-[1rem] text-slate-700 truncate leading-tight">{info.fullName}</h4>
                                                                    <p className="text-[0.85rem] text-slate-500 truncate leading-tight mt-0.5">{getTruncatedSpecialties(info.specialties)}</p>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center justify-between mt-auto">
                                                                <div className="flex items-center gap-2 min-w-0 flex-1">
                                                                    <span className={cn('inline-flex h-2.5 w-2.5 rounded-full flex-shrink-0', getStatusColor(info.nextAppointment))}></span>
                                                                    <span className="text-[0.85rem] text-slate-600 truncate">{formatNextAppointment(info.nextAppointment)}</span>
                                                                </div>
                                                                {info.nextAppointmentQuantity && info.nextAppointmentQuantity > 0 && (
                                                                    <span className="rounded-full bg-blue-100 text-blue-800 text-[0.85rem] font-semibold px-2 py-1 flex-shrink-0">
                                                                        {info.nextAppointmentQuantity}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </Card>
                                                ))}

                                                {/* Add new doctor compact card */}
                                                <div className="group relative overflow-hidden border-2 border-dashed border-blue-300/60 rounded-lg transition-all duration-300 hover:border-blue-400/80 min-h-[4.5rem] flex flex-col bg-white hover:shadow-md cursor-pointer"
                                                      onClick={() => setIsNewDoctorDialogOpen(true)}>
                                                    <div className="flex flex-col h-full justify-center items-center p-3 text-center">
                                                        <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center border-2 border-dashed border-blue-300/50 mb-2">
                                                            <Plus className="h-4 w-4 text-blue-400" />
                                                        </div>
                                                        <h4 className="text-[0.9rem] font-semibold text-blue-600">
                                                            Agregar Profesional
                                                        </h4>
                                                        <p className="text-[0.8rem] text-blue-500/70">
                                                            Sumá un profesional
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {professionalsView === "table" && (
                                            <div className="bg-white rounded-xl border border-slate-200 overflow-hidden">
                                                <table className="w-full">
                                                    <thead className="bg-slate-50 border-b border-slate-200">
                                                        <tr>
                                                            <th className="text-left p-4 font-semibold text-slate-700">Profesional</th>
                                                            <th className="text-left p-4 font-semibold text-slate-700">Especialidades</th>
                                                            <th className="text-left p-4 font-semibold text-slate-700">Próximo turno</th>
                                                            <th className="text-left p-4 font-semibold text-slate-700">Turnos pendientes</th>
                                                            <th className="text-left p-4 font-semibold text-slate-700">Matrícula</th>
                                                            <th className="text-left p-4 font-semibold text-slate-700">Acciones</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="divide-y divide-slate-200">
                                                        {sortedInitialInfo.map((info) => (
                                                            <tr key={info.id}
                                                                className="hover:bg-slate-50 transition-colors cursor-pointer"
                                                                onClick={() => handleDoctorSelect(info)}
                                                            >
                                                                <td className="p-4">
                                                                    <div className="flex items-center gap-3">
                                                                        <div className="h-11 w-11 rounded-lg bg-blue-100 border border-blue-200 flex items-center justify-center">
                                                                            <span className="text-blue-700 font-semibold text-sm">{info.fullName.substring(0, 2).toUpperCase()}</span>
                                                                        </div>
                                                                        <div>
                                                                            <div className="font-medium text-slate-700">{info.fullName}</div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td className="p-4">
                                                                    <div className="text-sm text-slate-600 max-w-xs">
                                                                        {getTruncatedSpecialties(info.specialties)}
                                                                    </div>
                                                                </td>
                                                                <td className="p-4">
                                                                    <div className="flex items-center gap-2">
                                                                        <span className={cn('inline-flex h-2.5 w-2.5 rounded-full', getStatusColor(info.nextAppointment))}></span>
                                                                        <span className="text-sm text-slate-600">{formatNextAppointment(info.nextAppointment)}</span>
                                                                    </div>
                                                                </td>
                                                                <td className="p-4">
                                                                    {info.nextAppointmentQuantity && info.nextAppointmentQuantity > 0 ? (
                                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                            {info.nextAppointmentQuantity} {info.nextAppointmentQuantity === 1 ? 'turno' : 'turnos'}
                                                                        </span>
                                                                    ) : (
                                                                        <span className="text-sm text-slate-400">Sin turnos</span>
                                                                    )}
                                                                </td>
                                                                <td className="p-4">
                                                                    <span className="text-sm text-slate-500">{info.medicalLicense || '-'}</span>
                                                                </td>
                                                                <td className="p-4">
                                                                    <Button 
                                                                        variant="outline" 
                                                                        size="sm"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            copyDoctorBookingUrl(info.id.toString(), e);
                                                                        }}
                                                                        className="border-blue-300 text-blue-600 hover:bg-blue-50"
                                                                    >
                                                                        {copiedDoctorId === info.id.toString() ? 
                                                                            <Check className="h-4 w-4 mr-1"/> : 
                                                                            <Share2 className="h-4 w-4 mr-1"/>
                                                                        }
                                                                        {copiedDoctorId === info.id.toString() ? '¡Copiado!' : 'Compartir agenda'}
                                                                    </Button>
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                                
                                                {/* Add new doctor table row */}
                                                <div className="border-t border-slate-200 p-4 cursor-pointer hover:bg-slate-50 transition-colors"
                                                     onClick={() => setIsNewDoctorDialogOpen(true)}>
                                                    <div className="flex items-center gap-3">
                                                        <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center">
                                                            <Plus className="h-5 w-5 text-blue-500" />
                                                        </div>
                                                        <div className="flex-1">
                                                            <div className="font-medium text-blue-600">Agregar Profesional</div>
                                                            <div className="text-sm text-blue-500/70">Sumá un profesional a tu equipo</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                {/* Mobile Layout */}
                <div className="md:hidden mobile-layout flex flex-col">
                    {/* Mobile Top Bar */}
                    <div className="bg-white border-b border-[#e5e9f2] px-6 py-3 relative">
                        <div className="flex items-center gap-3">
                            <Link href={`/plataforma/establecimiento/${medicalCenterId}`} className="flex-shrink-0">
                                <Image src="/images/turnera-logo-small.svg" alt="Turnera" width={80} height={20}
                                       className="h-7 w-auto"/>
                            </Link>
                            <div className="flex-1">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 h-4 w-4"/>
                                    <Input
                                        type="text"
                                        className="w-full pl-9 pr-3 h-9 rounded-full border-[#e5e9f2] bg-[#f7f8fb] text-[#2d2f46] placeholder:text-slate-400 placeholder:text-[0.95rem] shadow-inner focus:border-blue-400 focus:ring-2 focus:ring-blue-100"
                                        placeholder="Buscar profesional..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="flex-shrink-0">
                                <MedicalCenterUserPill
                                    medicalCenter={medicalCenter}
                                    currentUser={currentUser}
                                    medicalCenterId={medicalCenterId}
                                    logout={logout}
                                    showShare={false}
                                    dropUp={false}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Mobile Content */}
                    <div className="flex-1 overflow-y-auto bg-gradient-to-b from-[#f3f8fc] to-[#f4f8fa] mobile-content">
                        <div className="px-6 pt-6 pb-8">
                            {/* Assistant-style greeting banner */}
                            {showGreeting && (
                                <div className="mb-4 relative bg-gradient-to-b from-white via-slate-50 to-blue-50 rounded-xl shadow-lg shadow-slate-400/20 border border-slate-300/60 p-4">
                                    <button aria-label="Cerrar mensaje" onClick={handleCloseGreeting}
                                            className="absolute right-2 top-2 text-slate-500 hover:text-slate-700">
                                        <X className="h-4 w-4"/>
                                    </button>
                                    <p className="text-[1.1rem] font-semibold text-slate-700">{getGreeting()}, <span
                                        className="text-blue-700">{userDisplayName}</span></p>
                                    <p className="text-[1rem] text-slate-500 mt-1">Hoy {doctorsWorkingToday === 1 ? 'atiende ' : 'atienden '}
                                        <span className="font-semibold text-blue-700">{doctorsWorkingToday}</span> {doctorsWorkingToday === 1 ? 'profesional' : 'profesionales'},
                                        hay <span className="font-semibold text-blue-700">{totalAppointmentsToday}</span> {totalAppointmentsToday === 1 ? 'turno' : 'turnos'} para
                                        hoy y <span className="font-semibold text-blue-700">{approachingAppointments}</span> {approachingAppointments === 1 ? 'turno para los próximos días' : 'turnos para los próximos días'}.
                                    </p>
                                </div>
                            )}

                            {/* Title + segmented view */}
                            <div className="flex items-center gap-2 mb-3">
                                <h2 className="text-[1.5rem] font-semibold tracking-tight text-slate-700">Agenda general</h2>
                                {!showGreeting && (
                                    <button
                                        aria-label="Mostrar mensaje del día"
                                        onClick={() => setShowGreeting(true)}
                                        className="h-7 w-7 inline-flex items-center justify-center rounded-full bg-blue-50 text-blue-600 border border-blue-200 hover:bg-blue-100"
                                    >
                                        <Info className="h-3.5 w-3.5"/>
                                    </button>
                                )}
                            </div>
                            <div className="inline-flex rounded-lg border border-slate-200 bg-white p-1 shadow-sm mb-6">
                                <button onClick={() => handleViewChange('day')}
                                        className={`px-4 py-2 rounded-md text-[1rem] font-medium inline-flex items-center gap-1.5 transition-all ${loadingView === 'day' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-700'}`}>
                                    <Calendar className={`h-4 w-4 ${loadingView === 'day' ? 'text-white' : 'text-blue-600'}`}/>
                                    Día
                                </button>
                                <button onClick={() => handleViewChange('week')}
                                        className={`px-4 py-2 rounded-md text-[1rem] font-medium inline-flex items-center gap-1.5 transition-all ${loadingView === 'week' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-700'}`}>
                                    <CalendarDays className={`h-4 w-4 ${loadingView === 'week' ? 'text-white' : 'text-blue-600'}`}/>
                                    Semana
                                </button>
                                <button onClick={() => handleViewChange('month')}
                                        className={`px-4 py-2 rounded-md text-[1rem] font-medium inline-flex items-center gap-1.5 transition-all ${loadingView === 'month' ? 'bg-blue-600 text-white shadow-sm' : 'text-slate-600 hover:bg-slate-50 hover:text-slate-700'}`}>
                                    <CalendarRange className={`h-4 w-4 ${loadingView === 'month' ? 'text-white' : 'text-blue-600'}`}/>
                                    Mes
                                </button>
                            </div>

                            {/* Separator */}
                            <div className="mb-6 h-px w-full bg-[#e5e9f2]"/>

                            {/* Professionals section */}
                            <div className="mb-6">
                                <div className="flex items-center justify-between gap-2 mb-4">
                                    <div className="flex items-center gap-2">
                                        <h3 className="text-[1.5rem] font-semibold text-slate-700">Profesionales</h3>
                                        <button
                                            onClick={() => setSortOrder(sortOrder === 'alphabetical' ? 'earliestAgenda' : 'alphabetical')}
                                            className="inline-flex items-center gap-1.5 px-3 py-1 rounded-lg border border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-700 text-[0.95rem] transition-all shadow-sm"
                                        >
                                            {sortOrder === 'alphabetical' ? (
                                                <>
                                                    <SortAsc className="h-3.5 w-3.5"/>
                                                    <span>A-Z</span>
                                                </>
                                            ) : (
                                                <>
                                                    <Timer className="h-3.5 w-3.5"/>
                                                    <span>Próximos</span>
                                                </>
                                            )}
                                            <ArrowUpDown className="h-3 w-3 text-slate-400"/>
                                        </button>
                                    </div>
                                    <button
                                        onClick={() => {
                                            const views: ("cards" | "compact" | "table")[] = ["cards", "compact", "table"];
                                            const currentIndex = views.indexOf(professionalsView);
                                            const nextIndex = (currentIndex + 1) % views.length;
                                            const nextView = views[nextIndex];
                                            setProfessionalsView(nextView);
                                            try {
                                                localStorage.setItem(PROFESSIONALS_VIEW_KEY, nextView);
                                            } catch {
                                                // ignore storage errors
                                            }
                                        }}
                                        className="inline-flex items-center gap-2 px-3 py-1.5 rounded-lg border border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-700 text-[0.875rem] transition-all shadow-sm"
                                    >
                                        <span>{professionalsView === "cards" ? "Tarjetas" : professionalsView === "compact" ? "Compacto" : "Lista"}</span>
                                        <Eye className="h-4 w-4 text-slate-400"/>
                                    </button>
                                </div>

                                {/* Mobile Professionals */}
                                <div className="pb-4">
                                    {professionalsView === "cards" && (
                                        <div className="grid grid-cols-1 gap-6">
                                            {sortedInitialInfo.map((info) => (
                                                <Card key={info.id}
                                                      className="group relative overflow-hidden border rounded-xl transition-all duration-300 hover:shadow-md">
                                                    <DoctorCardLoading doctorId={info.id.toString()}
                                                                       isLoading={loadingDoctorId === info.id.toString()}/>
                                                    <div
                                                        className="absolute top-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <Button variant="secondary" size="icon"
                                                                className="h-7 w-7 rounded-full bg-white/80 backdrop-blur text-slate-600 hover:bg-blue-50"
                                                                onClick={(e) => copyDoctorBookingUrl(info.id.toString(), e)}>
                                                            {copiedDoctorId === info.id.toString() ?
                                                                <Check className="h-3 w-3"/> :
                                                                <Share2 className="h-3 w-3"/>}
                                                        </Button>
                                                    </div>

                                                    <div onClick={() => handleDoctorSelect(info)}
                                                         className={cn('flex flex-col cursor-pointer', loadingDoctorId === info.id.toString() && 'cursor-wait')}>
                                                                                                                    <CardHeader className="p-3 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white">
                                                            <div className="flex items-center gap-3">
                                                                                                                                 <div className="h-9 w-9 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 flex items-center justify-center">
                                                                     <span className="text-white font-semibold text-base">{info.fullName.substring(0, 2).toUpperCase()}</span>
                                                                 </div>
                                                                <div className="min-w-0 flex-1">
                                                                    <CardTitle className="font-semibold truncate text-[1.2rem]">{info.fullName}</CardTitle>
                                                                    <p className="text-[0.9rem] text-blue-100 truncate">{getTruncatedSpecialties(info.specialties)}</p>
                                                                </div>
                                                            </div>
                                                        </CardHeader>
                                                        <CardContent className="p-3">
                                                            <div className="flex items-center justify-between">
                                                                <div className="flex items-center gap-2">
                                                                    <span className={cn('inline-flex h-2.5 w-2.5 rounded-full', getStatusColor(info.nextAppointment))}></span>
                                                                    <span className="text-[1rem] text-slate-600">{formatNextAppointment(info.nextAppointment)}</span>
                                                                </div>
                                                                {info.nextAppointmentQuantity && info.nextAppointmentQuantity > 0 && (
                                                                    <span className="rounded-full bg-blue-100 text-blue-800 text-[0.9rem] font-semibold px-2.5 py-1">
                                                                        {info.nextAppointmentQuantity} {info.nextAppointmentQuantity === 1 ? 'turno' : 'turnos'}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </CardContent>
                                                        <CardFooter className="px-3 pb-3 flex items-center justify-between">
                                                            <div className="flex items-center gap-2 text-blue-700">
                                                                <Clock className="h-4 w-4"/>
                                                                <span className="text-[1rem] font-medium">Ver agenda</span>
                                                            </div>
                                                            <div className="text-[0.9rem] text-slate-500">{info.medicalLicense && `MN: ${info.medicalLicense}`}</div>
                                                        </CardFooter>
                                                    </div>
                                                </Card>
                                            ))}

                                            {/* Add new doctor placeholder card */}
                                            <div className="group relative overflow-hidden border-2 border-dashed border-blue-300/60 rounded-xl transition-all duration-300 hover:border-blue-400/80 bg-white hover:shadow-md cursor-pointer mb-4"
                                                  onClick={() => setIsNewDoctorDialogOpen(true)}>
                                                <div className="p-3 bg-gradient-to-br from-blue-50 via-blue-100 to-sky-100 border-b border-blue-200/30">
                                                    <div className="flex items-center gap-3">
                                                        <div className="h-9 w-9 rounded-full ring-2 ring-white/40 bg-blue-200 flex items-center justify-center">
                                                            <Users className="h-4 w-4 text-blue-600" />
                                                        </div>
                                                        <div className="min-w-0 flex-1">
                                                            <div className="font-medium truncate text-[1.2rem] text-blue-700">Agregar Profesional</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="p-3 flex items-center gap-3">
                                                    <div className="w-11 h-11 bg-blue-50 rounded-full flex items-center justify-center border-2 border-dashed border-blue-300/50 flex-shrink-0">
                                                        <Plus className="h-5 w-5 text-blue-400" />
                                                    </div>
                                                    <div className="flex-1">
                                                        <h3 className="text-[1.1rem] font-semibold text-blue-600">
                                                            Agregá un nuevo profesional
                                                        </h3>
                                                        <p className="text-blue-500/70 text-[0.9rem]">
                                                            Sumá un profesional a tu equipo.
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {professionalsView === "compact" && (
                                        <div className="grid grid-cols-2 gap-5">
                                            {sortedInitialInfo.map((info) => (
                                                <Card key={info.id}
                                                      className="group relative overflow-hidden border rounded-xl transition-all duration-300 hover:shadow-md hover:-translate-y-0.5 h-[6rem] flex flex-col">
                                                    <DoctorCardLoading doctorId={info.id.toString()}
                                                                       isLoading={loadingDoctorId === info.id.toString()}/>
                                                    <div
                                                        className="absolute top-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity">
                                                        <Button variant="secondary" size="icon"
                                                                className="h-6 w-6 rounded-full bg-white/80 backdrop-blur text-slate-600 hover:bg-blue-50"
                                                                onClick={(e) => copyDoctorBookingUrl(info.id.toString(), e)}>
                                                            {copiedDoctorId === info.id.toString() ?
                                                                <Check className="h-3 w-3"/> :
                                                                <Share2 className="h-3 w-3"/>}
                                                        </Button>
                                                    </div>

                                                        <div onClick={() => handleDoctorSelect(info)}
                                                             className={cn('flex flex-col h-full cursor-pointer p-3', loadingDoctorId === info.id.toString() && 'cursor-wait')}>
                                                            <div className="flex items-start gap-2.5 mb-2.5 flex-shrink-0">
                                                                <div className="h-8 w-8 rounded-lg bg-blue-100 border border-blue-200 flex items-center justify-center flex-shrink-0">
                                                                    <span className="text-blue-700 font-semibold text-xs">{info.fullName.substring(0, 2).toUpperCase()}</span>
                                                                </div>
                                                            <div className="min-w-0 flex-1 overflow-hidden">
                                                                <h4 className="font-semibold text-[0.9rem] text-slate-700 truncate leading-tight">{info.fullName}</h4>
                                                                <p className="text-[0.75rem] text-slate-500 truncate leading-tight mt-0.5">{getTruncatedSpecialties(info.specialties)}</p>
                                                            </div>
                                                        </div>
                                                        <div className="flex items-center justify-between mt-auto flex-shrink-0">
                                                            <div className="flex items-center gap-1.5 min-w-0 flex-1">
                                                                <span className={cn('inline-flex h-2 w-2 rounded-full flex-shrink-0', getStatusColor(info.nextAppointment))}></span>
                                                                <span className="text-[0.8rem] text-slate-600 truncate">{formatNextAppointment(info.nextAppointment)}</span>
                                                            </div>
                                                            {info.nextAppointmentQuantity && info.nextAppointmentQuantity > 0 && (
                                                                <span className="rounded-full bg-blue-100 text-blue-800 text-[0.75rem] font-semibold px-1.5 py-0.5 flex-shrink-0">
                                                                    {info.nextAppointmentQuantity}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </Card>
                                            ))}

                                            {/* Add new doctor compact card */}
                                            <div className="group relative overflow-hidden border-2 border-dashed border-blue-300/60 rounded-xl transition-all duration-300 hover:border-blue-400/80 h-[6rem] flex flex-col bg-white hover:shadow-md cursor-pointer"
                                                  onClick={() => setIsNewDoctorDialogOpen(true)}>
                                                <div className="flex flex-col h-full justify-center items-center p-3 text-center">
                                                    <div className="w-7 h-7 bg-blue-50 rounded-full flex items-center justify-center border-2 border-dashed border-blue-300/50 mb-2">
                                                        <Plus className="h-3.5 w-3.5 text-blue-400" />
                                                    </div>
                                                    <h4 className="text-[0.85rem] font-semibold text-blue-600">
                                                        Agregar
                                                    </h4>
                                                    <p className="text-[0.7rem] text-blue-500/70">
                                                        Profesional
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {professionalsView === "table" && (
                                        <div className="bg-white rounded-xl border border-slate-200 overflow-hidden">
                                            <div className="overflow-x-auto">
                                                <table className="w-full min-w-[300px]">
                                                    <thead className="bg-slate-50 border-b border-slate-200">
                                                        <tr>
                                                            <th className="text-left p-3 font-semibold text-slate-700 text-base">Profesional</th>
                                                            <th className="text-left p-3 font-semibold text-slate-700 text-base">Próximo turno</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="divide-y divide-slate-200">
                                                        {sortedInitialInfo.map((info) => (
                                                            <tr key={info.id} 
                                                                className="hover:bg-slate-50 transition-colors cursor-pointer"
                                                                onClick={() => handleDoctorSelect(info)}
                                                            >
                                                                <td className="p-3">
                                                                    <div className="flex items-center gap-2">
                                                                        <div className="h-9 w-9 rounded-lg bg-blue-100 border border-blue-200 flex items-center justify-center">
                                                                            <span className="text-blue-700 font-semibold text-sm">{info.fullName.substring(0, 2).toUpperCase()}</span>
                                                                        </div>
                                                                        <div>
                                                                            <div className="font-medium text-slate-700 text-base">{info.fullName}</div>
                                                                            <div className="text-sm text-slate-500 max-w-[120px] truncate">
                                                                                {getTruncatedSpecialties(info.specialties)}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td className="p-3">
                                                                    <div className="flex items-center gap-1.5">
                                                                        <span className={cn('inline-flex h-2 w-2 rounded-full', getStatusColor(info.nextAppointment))}></span>
                                                                        <span className="text-sm text-slate-600">{formatNextAppointment(info.nextAppointment)}</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                            
                                            {/* Add new doctor table row */}
                                            <div className="border-t border-slate-200 p-3 cursor-pointer hover:bg-slate-50 transition-colors"
                                                 onClick={() => setIsNewDoctorDialogOpen(true)}>
                                                <div className="flex items-center gap-3">
                                                    <div className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
                                                        <Plus className="h-4 w-4 text-blue-500" />
                                                    </div>
                                                    <div className="flex-1">
                                                        <div className="font-medium text-blue-600 text-sm">Agregar Profesional</div>
                                                        <div className="text-xs text-blue-500/70">Sumá un profesional a tu equipo</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Mobile Bottom Navigation */}
                    <div className="mobile-bottom-nav">
                        <div className="flex items-center justify-between px-4">
                            <Button
                                variant={pathname === `/plataforma/establecimiento/${medicalCenterId}` ? "secondary" : "ghost"}
                                className={`flex flex-col items-center justify-center gap-1 p-2 h-auto text-[0.9rem] font-medium ${
                                    pathname === `/plataforma/establecimiento/${medicalCenterId}` 
                                        ? 'bg-blue-600 text-white hover:bg-blue-700' 
                                        : 'text-slate-600 hover:text-slate-700'
                                }`}
                                onClick={handleAgendasClick}
                            >
                                <div className="flex flex-col items-center">
                                    <Calendar className={`h-5 w-5 ${
                                        pathname === `/plataforma/establecimiento/${medicalCenterId}` ? 'text-white' : 'text-slate-600'
                                    }`}/>
                                    <span>Agenda</span>
                                </div>
                            </Button>
                            <LoadingButton
                                isLoading={isAnalyticsLoading}
                                variant={pathname === `/plataforma/establecimiento/${medicalCenterId}/analytics` ? "secondary" : "ghost"}
                                className={`flex flex-col items-center justify-center gap-1 p-2 h-auto text-[0.9rem] font-medium ${
                                    pathname === `/plataforma/establecimiento/${medicalCenterId}/analytics` 
                                        ? 'bg-blue-600 text-white hover:bg-blue-700' 
                                        : 'text-slate-600 hover:text-slate-700'
                                }`}
                                onClick={handleAnalyticsClick}
                            >
                                <div className="flex flex-col items-center">
                                    <BarChart2 className={`h-5 w-5 ${
                                        pathname === `/plataforma/establecimiento/${medicalCenterId}/analytics` ? 'text-white' : 'text-slate-600'
                                    }`}/>
                                    <span>Estadísticas</span>
                                </div>
                            </LoadingButton>
                            <Button
                                variant="ghost"
                                className="flex flex-col items-center justify-center gap-1 p-2 h-auto text-[0.9rem] font-medium text-slate-600 hover:text-slate-700"
                                onClick={() => setShowPatientList(true)}
                            >
                                <div className="flex flex-col items-center">
                                    <Users className="h-5 w-5 text-slate-600"/>
                                    <span>Pacientes</span>
                                </div>
                            </Button>
                            <Button
                                variant="ghost"
                                className="flex flex-col items-center justify-center gap-1 p-2 h-auto text-[0.9rem] font-medium text-slate-600 hover:text-slate-700"
                                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                                aria-label="Más opciones"
                            >
                                <div className="flex flex-col items-center">
                                    <Menu className="h-5 w-5 text-slate-600"/>
                                    <span>Más</span>
                                </div>
                            </Button>
                        </div>
                        
                        {/* Mobile Menu Dropdown - Fixed to bottom */}
                        {isMobileMenuOpen && (
                            <div className="absolute bottom-full left-0 right-0 bg-white border-t border-[#e5e9f2] shadow-lg z-50">
                                <div className="px-4 py-3 space-y-2">
                                    <div className="flex items-center justify-between">
                                        <p className="text-[1.1rem] text-slate-700 font-semibold truncate">{medicalCenter?.name}</p>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            className="h-8 w-8 rounded-full text-blue-700 border-blue-300/60 hover:bg-blue-50"
                                            onClick={copyMedicalCenterBookingUrl}
                                            aria-label="Compartir enlace del centro"
                                        >
                                            {isShareCopied ? <Check className="h-4 w-4"/> : <Share2 className="h-4 w-4"/>}
                                        </Button>
                                    </div>
                                    <div className="pt-2 border-t border-[#e5e9f2]">
                                        <LoadingButton
                                            isLoading={isConfigLoading}
                                            variant="ghost"
                                            className="w-full text-[1rem] font-medium justify-start gap-4 p-3"
                                            onClick={handleConfigClick}
                                        >
                                            <Settings className="h-5 w-5 mr-3"/>
                                            Configuración
                                        </LoadingButton>
                                        <a
                                            href="https://wa.me/541156098040?text=Hola!%20Tengo%20consultas%20sobre%20mi%20agenda."
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="flex w-full items-center gap-4 p-3 text-[1rem] font-medium hover:bg-slate-50 rounded-md"
                                        >
                                            <Image 
                                                src="/images/whatsapp-icon.svg" 
                                                alt="WhatsApp" 
                                                width={16} 
                                                height={16}
                                                className="h-4 w-4 -mr-1"
                                            />
                                            Hablá con soporte
                                        </a>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <PatientDetailsDialog
                patient={selectedPatient}
                isOpen={showPatientDetails}
                onClose={() => setShowPatientDetails(false)}
                onAppointmentSelect={handleAppointmentSelect}
            />
            <PatientListDialog
                isOpen={showPatientList}
                onClose={() => setShowPatientList(false)}
                patients={dataStore.patients} // Use patients from dataStore
            />

            {/* New Doctor Dialog */}
            <NewDoctorDialog />

        </>
    )
}
