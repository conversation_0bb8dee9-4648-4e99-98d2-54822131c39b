import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

interface ConfigurationNavProps {
  medicalCenterId: string
}

export default function ConfigurationNav({ medicalCenterId }: ConfigurationNavProps) {
  return (
    <nav className="border-b bg-white">
      <div className="container mx-auto px-[1.5rem] py-[0.75rem] flex items-center">
        <Link href={`/${medicalCenterId}`}>
          <Button variant="ghost" className="text-blue-600 hover:bg-blue-50">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Atrás
          </Button>
        </Link>
      </div>
    </nav>
  )
}