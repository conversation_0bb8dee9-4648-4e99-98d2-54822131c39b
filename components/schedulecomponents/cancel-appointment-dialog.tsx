import {useState} from "react"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Label} from "@/components/ui/label"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Textarea} from "@/components/ui/textarea"
import {AlertTriangle, Mail, Phone} from "lucide-react"
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

interface CancelAppointmentDialogProps {
    isOpen: boolean
    onClose: () => void
    onConfirm: (option: "auto" | "manual", reason?: string) => void
    appointment?: ProfessionalAppointment | null
}

export function CancelAppointmentDialog({
                                            isOpen,
                                            onClose,
                                            onConfirm,
                                            appointment
                                        }: CancelAppointmentDialogProps) {
    const [cancellationOption, setCancellationOption] = useState<"auto" | "manual" | null>(null)
    const [cancellationReason, setCancellationReason] = useState<string>("")
    const [selectedReasonTemplate, setSelectedReasonTemplate] = useState<string>("")

    // Standard cancellation reason templates
    const reasonTemplates = [
        {id: "agenda", text: "Cambios en la agenda del profesional"},
        {id: "ausencia", text: "Ausencia temporal del profesional"},
        {id: "emergencia", text: "Emergencia médica"},
    ]

    const handleConfirm = () => {
        if (!cancellationOption) return

        if (cancellationOption === "auto") {
            onConfirm(cancellationOption, cancellationReason)
        } else {
            onConfirm(cancellationOption)
        }
    }

    const handleReasonTemplateChange = (templateId: string) => {
        setSelectedReasonTemplate(templateId)

        if (templateId === "custom") {
            // Keep the current text if it's a custom reason
            return
        }

        // Find the template text
        const template = reasonTemplates.find(t => t.id === templateId)
        if (template) {
            setCancellationReason(template.text)
        }
    }

    return (
        <AlertDialog open={isOpen} onOpenChange={onClose}>
            <AlertDialogContent className="max-w-md">
                <AlertDialogHeader>
                    <AlertDialogTitle>¿Cancelar turno?</AlertDialogTitle>
                    <AlertDialogDescription>
                        Esta acción no se puede deshacer. El turno quedará disponible para otros pacientes.
                    </AlertDialogDescription>
                </AlertDialogHeader>

                <div className="py-4">
                    <RadioGroup value={cancellationOption || ""}
                                onValueChange={(value) => setCancellationOption(value as "auto" | "manual")}>
                        <div className="flex items-start space-x-2 mb-4">
                            <RadioGroupItem value="auto" id="auto-cancel" className="mt-1"/>
                            <div className="grid gap-1.5">
                                <Label htmlFor="auto-cancel" className="font-medium">Cancelar y enviar notificación
                                    automática</Label>
                                <p className="text-sm text-gray-600">Se enviará un email de cancelación al paciente.</p>

                                {cancellationOption === "auto" && (
                                    <div className="mt-2 space-y-2">
                                        <Label htmlFor="cancel-reason-template" className="text-sm text-gray-600 block">
                                            Motivo de la cancelación
                                        </Label>
                                        <Select
                                            value={selectedReasonTemplate}
                                            onValueChange={handleReasonTemplateChange}
                                        >
                                            <SelectTrigger id="cancel-reason-template" className="w-full">
                                                <SelectValue placeholder="Seleccionar motivo"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {reasonTemplates.map(template => (
                                                    <SelectItem key={template.id} value={template.id}>
                                                        {template.text}
                                                    </SelectItem>
                                                ))}
                                                <SelectItem value="custom">Motivo personalizado</SelectItem>
                                            </SelectContent>
                                        </Select>

                                        {selectedReasonTemplate === "custom" && (
                                            <Textarea
                                                placeholder="Ingrese el motivo de la cancelación"
                                                value={cancellationReason}
                                                onChange={(e) => setCancellationReason(e.target.value)}
                                                className="mt-2"
                                            />
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="flex items-start space-x-2">
                            <RadioGroupItem value="manual" id="manual-cancel" className="mt-1"/>
                            <div className="grid gap-1.5">
                                <Label htmlFor="manual-cancel" className="font-medium">Cancelar y notificar manualmente
                                    al paciente</Label>
                                <p className="text-sm text-gray-600">El turno será cancelado en el sistema, pero deberá
                                    notificar al paciente manualmente.</p>

                                {cancellationOption === "manual" && appointment && (
                                    <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                                        <div className="flex items-center gap-2 mb-2">
                                            <AlertTriangle className="h-4 w-4 text-red-500"/>
                                            <p className="text-sm font-medium text-red-700">Anote los datos de contacto
                                                del paciente</p>
                                        </div>

                                        <div className="space-y-2">
                                            {appointment.phone && (
                                                <div className="flex items-center gap-2">
                                                    <Phone className="h-4 w-4 text-gray-500"/>
                                                    <span className="text-sm">{appointment.phone}</span>
                                                </div>
                                            )}

                                            {appointment.email && (
                                                <div className="flex items-center gap-2">
                                                    <Mail className="h-4 w-4 text-gray-500"/>
                                                    <span className="text-sm">{appointment.email}</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </RadioGroup>
                </div>

                <AlertDialogFooter>
                    <AlertDialogCancel onClick={onClose}>No, mantener turno</AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleConfirm}
                        className="bg-red-600 hover:bg-red-700"
                        disabled={!cancellationOption || (cancellationOption === "auto" && !cancellationReason.trim())}
                    >
                        Sí, Cancelar turno
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}

