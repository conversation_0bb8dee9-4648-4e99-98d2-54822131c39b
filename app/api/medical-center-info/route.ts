import {NextRequest, NextResponse} from 'next/server';

export async function GET(request: NextRequest) {
    try {
        // Extract query parameters
        const {searchParams} = new URL(request.url);
        const medicalCenterId = searchParams.get('medicalCenterId');
        const employeeUserId = searchParams.get('employeeUserId');

        // Validate required parameters
        if (!medicalCenterId || !employeeUserId) {
            return NextResponse.json(
                {error: 'Missing required parameters: medicalCenterId and employeeUserId are required'},
                {status: 400}
            );
        }

        const response = await fetch(
            `${process.env.BACKEND_URL}/medical-center/${medicalCenterId}/initial-information?employeeUserId=${employeeUserId}`,
            {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        return NextResponse.json(data, {
            status: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            },
        });
    } catch (error) {
        console.error('Error fetching medical center info:', error);

        return NextResponse.json(
            {error: 'Failed to fetch medical center information'},
            {
                status: 500,
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
                },
            }
        );
    }
}

export async function OPTIONS(request: NextRequest) {
    return new NextResponse(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
    });
}
