import { Specialty } from "@/data/specialties"
import { OpeningHours } from "@/types/medical-center"

export interface DoctorConfiguration {
  id: string
  name: string
  specialties: Specialty[]
  consultationTypes: ConsultationType[]
  workingDays: {
    [key: string]: {
      enabled: boolean
      hours: Array<{
        start: string;
        end: string;
        startDate?: string; // ISO string format YYYY-MM-DD for when this schedule begins
        endDate?: string;   // ISO string format YYYY-MM-DD for when this schedule ends (null/undefined = infinity)
      }>
      weeksFrequency?: number
    }
  }
  dateExceptions?: {
    [date: string]: {
      enabled: boolean
      hours?: Array<{ start: string; end: string }>
    }
  }
  mn: string
  email?: string
  phone?: string // Phone number for patient profile creation
  dni?: string // DNI for patient profile creation
  onlineBookingAdvanceDays: number
  onlineBookingMinHours: number
  appointmentSlotDuration: number
  lastToFirstRangesByDay?: {
    [dayId: string]: Array<{
      start: string;
      end: string;
      enabled: boolean
    }>
  }
  overbookingConfig?: {
    ranges: Array<{
      start: string
      end: string
      ratio: "none" | "2per1" | "1per1" | "1per2" | "1per3"
    }>
  }
  overbookingByDay?: {
    [dayId: string]: Array<{
      start: string
      end: string
      ratio: "none" | "2per1" | "1per1" | "1per2" | "1per3"
    }>
  }
  onlyConsecutiveBookings?: boolean
  maxConsecutiveBookingsVisible?: number
  openingHours?: OpeningHours
}

export interface Doctor extends DoctorConfiguration {
  initial: string
}

export interface ConsultationType {
  name: string
  availableOnline?: boolean
  onlineBookingHours?: { start: string; end: string }
  onlineBookingHoursByDay?: { [dayId: string]: { start: string; end: string; enabled: boolean }[] }
  availableOnExtraordinaryDates?: boolean
  requiresMedicalOrder: boolean
  hasInstructions?: boolean
  instructions?: string
  duration: "default" | "2slots" | "3slots" | "4slots"
  dailyLimit: number | "unlimited"
  basePrice: number
  copays?: Array<{ coverageId: string; planId: string | null; amount: number }>
  excludedCoverages?: Array<{ coverageId: string; planId: string | null }>
  acceptsPrivatePay?: boolean
}

export interface MedicalCoverage {
  id: string
  name: string
  plans: string[]
}

export interface CoverageException {
  coverageId: string
  planId: string | null
  excluded: boolean
}

