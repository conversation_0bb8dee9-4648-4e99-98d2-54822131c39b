"use client"

import React, {useEffect, useRef} from "react"
import {format, isSameDay} from "date-fns"
import {es} from "date-fns/locale"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Label} from "@/components/ui/label"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {
  AlertCircle,
  Building2,
  ChevronLeft,
  ChevronRight,
  Clock,
  CreditCard,
  Info,
  MapPin,
  Stethoscope,
  X
} from "lucide-react"
import {Doctor} from "@/types/doctor"
import {MedicalCenter} from "@/types/medical-center"

interface DateTimeSelectionStepProps {
    doctor: Doctor
    medicalCenter: MedicalCenter
    selectedUrlCoverage: string | null
    selectedConsultationType: string | null
    setSelectedConsultationType: (value: string) => void
    selectedDate: Date | null
    displayedDates: Date[]
    availableTimeSlots: string[]
    selectedTime: string | null
    availableDates: Date[]
    handleDateChange: (direction: number) => void
    handleDateSelect: (date: Date) => void
    handleTimeSelect: (time: string) => void
    urlNoCoverage: boolean
    urlCoverage: string | null
    urlPlan: string | null
    coverageId: string | null
}

export const DateTimeSelectionStep: React.FC<DateTimeSelectionStepProps> = ({
                                                                                doctor,
                                                                                medicalCenter,
                                                                                selectedUrlCoverage,
                                                                                selectedConsultationType,
                                                                                setSelectedConsultationType,
                                                                                selectedDate,
                                                                                displayedDates,
                                                                                availableTimeSlots,
                                                                                selectedTime,
                                                                                availableDates,
                                                                                handleDateChange,
                                                                                handleDateSelect,
                                                                                handleTimeSelect,
                                                                                urlNoCoverage,
                                                                                urlCoverage,
                                                                                urlPlan,
                                                                                coverageId
                                                                            }) => {
    const dateSelectionRef = useRef<HTMLDivElement>(null)

    // Scroll to date selection when consultation type is selected
    useEffect(() => {
        if (selectedConsultationType && dateSelectionRef.current) {
            setTimeout(() => {
                const element = dateSelectionRef.current
                if (element) {
                    const elementPosition = element.getBoundingClientRect().top + window.pageYOffset
                    const offsetPosition = elementPosition - 80 // 80px offset from top

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    })
                }
            }, 300) // Small delay to allow UI to update
        }
    }, [selectedConsultationType])

    return (
        <div className="space-y-6">
            {/* Appointment Summary Card */}
            <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Resumen de tu turno</h2>

                <div className="space-y-4">
                    {/* Doctor Information */}
                    <div className="flex items-start space-x-3">
                        <div
                            className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <Stethoscope className="h-5 w-5 text-blue-600"/>
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="text-base font-medium text-gray-900">Dr. {doctor.name}</h3>
                            {doctor.specialties && doctor.specialties.length > 0 && (
                                <p className="text-sm text-gray-500 mt-0.5">
                                    {doctor.specialties.join(", ")}
                                </p>
                            )}
                        </div>
                    </div>

                    {/* Medical Center Information */}
                    <div className="flex items-start space-x-3">
                        <div
                            className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                            <Building2 className="h-5 w-5 text-gray-600"/>
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="text-base font-medium text-gray-900">{medicalCenter.name}</h3>
                            <div className="flex items-start mt-0.5">
                                <MapPin className="h-4 w-4 text-gray-400 mr-1 mt-0.5 flex-shrink-0"/>
                                <p className="text-sm text-gray-500">
                                    {formatMedicalCenterLocation(medicalCenter, LocationFormatType.SIMPLE)}
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Coverage Information */}
                    {selectedUrlCoverage && (
                        <div className="flex items-start space-x-3">
                            <div
                                className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                <CreditCard className="h-5 w-5 text-green-600"/>
                            </div>
                            <div className="flex-1 min-w-0">
                                <h3 className="text-base font-medium text-gray-900">Cobertura médica</h3>
                                <p className="text-sm text-gray-500 mt-0.5">{selectedUrlCoverage}</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Consultation Type Selection */}
            {doctor.consultationTypes && doctor.consultationTypes.length > 0 && (
                <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                    <div className="mb-4">
                        <Label className="text-lg font-semibold text-gray-900">Tipo de atención</Label>
                        <p className="text-sm text-gray-500 mt-1">Seleccioná el tipo de atención que necesitás</p>
                    </div>

                    {selectedConsultationType ? (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <span
                                        className="text-blue-900 font-medium text-base">{selectedConsultationType}</span>
                                    {doctor.consultationTypes.find(t => t.name === selectedConsultationType)?.requiresMedicalOrder && (
                                        <span className="block text-sm text-amber-600 mt-1">Requiere orden médica</span>
                                    )}
                                </div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-blue-700 hover:text-blue-900 hover:bg-blue-100"
                                    onClick={() => setSelectedConsultationType("")}
                                >
                                    <X className="h-4 w-4"/>
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <Select
                            value={selectedConsultationType || undefined}
                            onValueChange={(value) => {
                                setSelectedConsultationType(value);
                            }}
                        >
                            <SelectTrigger className="w-full h-12 text-base">
                                <SelectValue placeholder="Seleccioná el tipo de atención"/>
                            </SelectTrigger>
                            <SelectContent>
                                {doctor.consultationTypes
                                    .filter(type => {
                                        // Only show types available online
                                        if (type.availableOnline === false) return false;

                                        // If "Sin Cobertura" is selected, only show types that accept private patients
                                        if (urlNoCoverage && type.acceptsPrivatePay === false) return false;

                                        // If a coverage is selected, check if it's excluded for this consultation type
                                        if (urlCoverage && !urlNoCoverage && coverageId) {
                                            // Check if this coverage is excluded for this consultation type
                                            const isExcluded = type.excludedCoverages?.some(
                                                exclusion =>
                                                    exclusion.coverageId === coverageId &&
                                                    (exclusion.planId === null || (urlPlan && exclusion.planId === urlPlan))
                                            );

                                            if (isExcluded) return false;
                                        }

                                        return true;
                                    })
                                    .map((type, index) => (
                                        <SelectItem key={index} value={type.name}>
                                            {type.name}{type.requiresMedicalOrder ? " (Requiere orden médica)" : ""}
                                        </SelectItem>
                                    ))
                                }
                            </SelectContent>
                        </Select>
                    )}

                    {/* Consultation Information Cards */}
                    {selectedConsultationType && (
                        <div className="mt-4 space-y-3">
                            {(() => {
                                const selectedType = doctor.consultationTypes.find(t => t.name === selectedConsultationType);
                                if (!selectedType) return null;

                                const elements = [];

                                // Add instructions if available
                                if (selectedType?.hasInstructions && selectedType?.instructions) {
                                    elements.push(
                                        <div key="instructions"
                                             className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <div className="flex items-start space-x-3">
                                                <div
                                                    className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <Info className="h-4 w-4 text-blue-600"/>
                                                </div>
                                                <div className="flex-1">
                                                    <h4 className="font-semibold text-blue-900 text-sm mb-2">Instrucciones
                                                        importantes</h4>
                                                    <p className="text-blue-800 text-sm leading-relaxed whitespace-pre-line">{selectedType.instructions}</p>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                }

                                // If "Sin Cobertura" is selected, show the base price
                                if (urlNoCoverage && selectedType.acceptsPrivatePay) {
                                    elements.push(
                                        <div key="price" className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center space-x-3">
                                                    <div
                                                        className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                                        <CreditCard className="h-4 w-4 text-gray-600"/>
                                                    </div>
                                                    <span className="font-medium text-gray-900">Precio particular</span>
                                                </div>
                                                <span
                                                    className="text-lg font-semibold text-gray-900">${selectedType.basePrice}</span>
                                            </div>
                                        </div>
                                    );
                                }

                                // If a coverage is selected, check if it has a copay
                                if (urlCoverage && !urlNoCoverage && coverageId) {
                                    // Find the copay for this coverage and plan
                                    const copay = selectedType.copays?.find(
                                        c => c.coverageId === coverageId &&
                                            (c.planId === null || (urlPlan && c.planId === urlPlan))
                                    );

                                    if (copay) {
                                        elements.push(
                                            <div key="copay"
                                                 className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-3">
                                                        <div
                                                            className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                                                            <CreditCard className="h-4 w-4 text-amber-600"/>
                                                        </div>
                                                        <span className="font-medium text-amber-900">Copago</span>
                                                    </div>
                                                    <span
                                                        className="text-lg font-semibold text-amber-900">${copay.amount}</span>
                                                </div>
                                            </div>
                                        );
                                    }
                                }

                                return elements.length > 0 ? elements : null;
                            })()}
                        </div>
                    )}

                    {/* Show message if no dates are available */}
                    {selectedConsultationType &&
                        availableDates.length === 0 && (
                            <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                                <div className="flex items-center space-x-2">
                                    <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0"/>
                                    <span className="text-amber-800 font-medium">No hay fechas disponibles para este tipo de atención</span>
                                </div>
                            </div>
                        )}
                </div>
            )}

            {/* Date Selection - Keep original design */}
            {selectedConsultationType && (
                <div className="mb-6" ref={dateSelectionRef}>
                    <div
                        className="flex flex-col sm:flex-row justify-start items-start sm:items-center mb-3 sm:mb-4 space-y-2 sm:space-y-0">
                        <h3 className="mr-6 text-base sm:text-lg font-semibold">Seleccioná la fecha</h3>
                        <div className="flex items-center space-x-1">
                            <Button
                                variant="outline"
                                className="shadow"
                                size="icon"
                                onClick={() => handleDateChange(-1)}
                            >
                                <ChevronLeft className="h-4 w-4"/>
                            </Button>
                            <span className="text-sm font-medium w-28 text-center">
                {selectedDate && format(selectedDate, "MMMM yyyy", {locale: es})}
              </span>
                            <Button
                                variant="outline"
                                className="shadow"
                                size="icon"
                                onClick={() => handleDateChange(1)}
                            >
                                <ChevronRight className="h-4 w-4"/>
                            </Button>
                        </div>
                    </div>

                    <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 gap-1 sm:gap-4">
                        {displayedDates.map((date) => (
                            <Button
                                key={format(date, "yyyy-MM-dd")}
                                variant="outline"
                                className={`text-center sm:text-left justify-center sm:justify-start flex flex-col items-center sm:items-start h-auto py-2 sm:py-2 px-1 sm:px-2 ${selectedDate && isSameDay(date, selectedDate) ? 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:text-white' : 'hover:bg-blue-100 hover:text-blue-700 hover:border-blue-200'}`}
                                onClick={() => handleDateSelect(date)}
                            >
                                <div className="py-1 sm:py-0">
                                    <span
                                        className="text-[10px] sm:text-xs font-semibold leading-none mb-0 sm:mb-1 block">{format(date, "EEEE", {locale: es})}</span>
                                    <span
                                        className="text-base sm:text-lg leading-tight block">{format(date, "d")}</span>
                                </div>
                            </Button>
                        ))}
                    </div>
                </div>
            )}

            {/* Time Selection - Keep original design */}
            {selectedConsultationType && selectedDate && (
                <div className="mt-4">
                    <h4 className="text-xs sm:text-sm font-medium mb-2">
                        {format(selectedDate, "EEEE d 'de' MMMM", {locale: es})}:
                    </h4>
                    {availableTimeSlots.length === 0 ? (
                        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
                            <Clock className="h-10 w-10 text-gray-400 mx-auto mb-2"/>
                            <p className="text-gray-600">No hay horarios disponibles para esta fecha.</p>
                            <p className="text-gray-500 text-sm mt-1">Por favor, seleccioná otra fecha.</p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-3 sm:grid-cols-4 gap-1 sm:gap-2">
                            {availableTimeSlots.map((time) => (
                                <Button
                                    key={time}
                                    variant="outline"
                                    className={`text-xs sm:text-sm py-1 sm:py-2 px-1 sm:px-2 ${selectedTime === time ? 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 hover:text-white' : 'hover:bg-blue-100 hover:text-blue-700 hover:border-blue-200'}`}
                                    onClick={() => handleTimeSelect(time)}
                                >
                                    {time}
                                </Button>
                            ))}
                        </div>
                    )}
                </div>
            )}
        </div>
    )
}
