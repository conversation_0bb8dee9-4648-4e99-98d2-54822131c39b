import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Patient } from "@/types/patient"
import { useState, useEffect } from "react"
import "react-international-phone/style.css"

interface PatientEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  patient: Patient | null
  onSave: (patient: Patient) => void
  onCancel: () => void
  plansByCoverage: Record<string, string[]>
}

export function PatientEditDialog({
  open,
  onOpenChange,
  patient,
  onSave,
  onCancel,
  plansByCoverage
}: PatientEditDialogProps) {
  const [patientName, setPatientName] = useState("")
  const [patientDni, setPatientDni] = useState("")
  const [patientPhone, setPatientPhone] = useState("")
  const [patientEmail, setPatientEmail] = useState("")
  const [patientCoverage, setPatientCoverage] = useState("")
  const [patientPlan, setPatientPlan] = useState("")
  const [noCoverage, setNoCoverage] = useState(false)
  const [hasDefaultCoverage, setHasDefaultCoverage] = useState(false)
  const [useDefaultCoverage, setUseDefaultCoverage] = useState(true)
  const [formError, setFormError] = useState("")

  // Reset form when patient changes or dialog opens
  useEffect(() => {
    if (open && patient) {
      setPatientName(patient.name)
      setPatientDni(patient.dni)
      setPatientPhone(patient.phone)
      setPatientEmail(patient.email || '')

      // Handle coverage information
      if (patient.coverage) {
        const defaultCoverage = patient.defaultCoverage || patient.coverage
        setHasDefaultCoverage(true)
        setUseDefaultCoverage(true)

        const coverageParts = defaultCoverage.split(' ')
        if (coverageParts.length > 1) {
          setPatientCoverage(coverageParts[0])
          setPatientPlan(coverageParts.slice(1).join(' '))
          setNoCoverage(false)
        } else if (defaultCoverage === "Sin Cobertura") {
          setPatientCoverage('')
          setPatientPlan('')
          setNoCoverage(true)
        } else {
          setPatientCoverage(defaultCoverage)
          setPatientPlan('')
          setNoCoverage(false)
        }
      } else {
        setPatientCoverage('')
        setPatientPlan('')
        setNoCoverage(true)
        setHasDefaultCoverage(false)
        setUseDefaultCoverage(false)
      }

      setFormError("") // Clear any error messages
    }
  }, [patient, open])

  const handleSave = () => {
    // Validate form
    if (!patientName || !patientDni) {
      setFormError("Nombre y DNI son campos obligatorios")
      return
    }

    // Format coverage string
    let coverageString = ""
    if (noCoverage) {
      coverageString = "Sin Cobertura"
    } else if (patientCoverage) {
      coverageString = patientCoverage
      if (patientPlan) {
        coverageString += " " + patientPlan
      }
    }

    // If no coverage is selected, default to "Sin Cobertura"
    if (!coverageString) {
      coverageString = "Sin Cobertura"
    }

    // Create updated patient object
    const updatedPatient: Patient = {
      ...patient!,
      name: patientName,
      dni: patientDni,
      phone: patientPhone,
      email: patientEmail,
      coverage: coverageString,
      // Only update defaultCoverage if it doesn't exist or if we're not using it
      ...((!patient?.defaultCoverage || !useDefaultCoverage) ? { defaultCoverage: coverageString } : {})
    }

    onSave(updatedPatient)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar información del paciente</DialogTitle>
          <DialogDescription>
            Actualice los datos del paciente. Haga clic en guardar cuando termine.
          </DialogDescription>
        </DialogHeader>

        {formError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md mb-4">
            {formError}
          </div>
        )}

        <div className="space-y-6 max-h-[60vh] overflow-y-auto pr-1">
          {/* Patient Data Section */}
          <div className="space-y-4">
            <div className="border-b pb-2">
              <h3 className="text-lg font-semibold text-gray-900">Datos del paciente</h3>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="patientName">Nombre y Apellido</Label>
                <Input
                  id="patientName"
                  value={patientName}
                  placeholder="Ingrese nombre completo"
                  className="mt-1 bg-gray-50 text-gray-500"
                  disabled
                  readOnly
                />
              </div>

              <div>
                <Label htmlFor="patientDni">DNI</Label>
                <Input
                  id="patientDni"
                  value={patientDni}
                  placeholder="Ingrese DNI sin puntos"
                  className="mt-1 bg-gray-50 text-gray-500"
                  disabled
                  readOnly
                />
              </div>

              <div>
                <Label htmlFor="patientPhone">Teléfono</Label>
                <Input
                  id="patientPhone"
                  value={patientPhone}
                  placeholder="Teléfono (opcional)"
                  className="mt-1 bg-gray-50 text-gray-500"
                  disabled
                  readOnly
                />
              </div>

              <div>
                <Label htmlFor="patientEmail">Email</Label>
                <Input
                  id="patientEmail"
                  type="email"
                  value={patientEmail}
                  placeholder="Ingrese correo electrónico (opcional)"
                  className="mt-1 bg-gray-50 text-gray-500"
                  disabled
                  readOnly
                />
              </div>
            </div>
          </div>

          {/* Coverage Section */}
          <div className="space-y-4">
            <div className="border-b pb-2">
              <h3 className="text-lg font-semibold text-gray-900">Cobertura</h3>
            </div>

            {hasDefaultCoverage && (
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-800">Cobertura registrada:</p>
                    <p className="text-sm text-blue-700">{patient?.coverage}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="use-default-coverage"
                      checked={useDefaultCoverage}
                      onCheckedChange={(checked) => {
                        setUseDefaultCoverage(checked)
                        if (checked && patient) {
                          // Restore default coverage
                          const coverageParts = patient.coverage.split(' ')
                          if (coverageParts.length > 1) {
                            setPatientCoverage(coverageParts[0])
                            setPatientPlan(coverageParts.slice(1).join(' '))
                          } else {
                            setPatientCoverage(patient.coverage)
                            setPatientPlan('')
                          }
                          setNoCoverage(patient.coverage === "Sin Cobertura")
                        } else {
                          // Clear coverage values when switching to override
                          setPatientCoverage('')
                          setPatientPlan('')
                          setNoCoverage(false)
                        }
                      }}
                    />
                    <Label htmlFor="use-default-coverage" className="text-sm text-blue-800">
                      Usar esta cobertura
                    </Label>
                  </div>
                </div>
              </div>
            )}

            {(!hasDefaultCoverage || !useDefaultCoverage) && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="patientCoverage">Cobertura</Label>
                    <Select
                      key={`coverage-${useDefaultCoverage}-${hasDefaultCoverage}`}
                      value={patientCoverage}
                      onValueChange={setPatientCoverage}
                      disabled={noCoverage}
                    >
                      <SelectTrigger
                        id="patientCoverage"
                        className="mt-1"
                      >
                        <SelectValue placeholder="Seleccione cobertura" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="OSDE">OSDE</SelectItem>
                        <SelectItem value="Swiss Medical">Swiss Medical</SelectItem>
                        <SelectItem value="Medicus">Medicus</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {patientCoverage && (
                    <div>
                      <Label htmlFor="patientPlan">Plan</Label>
                      <Select
                        key={`plan-${patientCoverage}-${useDefaultCoverage}-${hasDefaultCoverage}`}
                        value={patientPlan}
                        onValueChange={setPatientPlan}
                        disabled={noCoverage}
                      >
                        <SelectTrigger
                          id="patientPlan"
                          className="mt-1"
                        >
                          <SelectValue placeholder="Seleccione plan" />
                        </SelectTrigger>
                        <SelectContent>
                          {plansByCoverage[patientCoverage]?.map((plan) => (
                            <SelectItem key={plan} value={plan}>
                              {plan}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="no-coverage"
                    checked={noCoverage}
                    onCheckedChange={(checked) => {
                      setNoCoverage(checked)
                      if (checked) {
                        setPatientCoverage('')
                        setPatientPlan('')
                        if (hasDefaultCoverage) {
                          setUseDefaultCoverage(false)
                        }
                      }
                    }}
                  />
                  <Label htmlFor="no-coverage">No tengo cobertura</Label>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onCancel}
            className="sm:flex-1"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 sm:flex-1"
          >
            Guardar cambios
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
