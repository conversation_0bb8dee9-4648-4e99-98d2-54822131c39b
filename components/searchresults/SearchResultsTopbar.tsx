"use client"

import { useState } from "react"
import { createPortal } from "react-dom"
import { List, Map, Menu, X, Search as SearchIcon, MapPin } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import UnifiedSearch from "@/components/landingpage/UnifiedSearch"
import { useAuth } from "@/contexts/AuthContext"
import { Auth0LoginButton } from "@/components/auth/Auth0LoginButton"
import { Auth0RegisterButton } from "@/components/auth/Auth0RegisterButton"
import { PatientUserPill } from "@/components/ui/PatientUserPill"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import SearchFiltersDesktop from "@/components/searchresults/SearchFiltersDesktop"

interface SearchResultsTopBarProps {
  view: "list" | "map"
  setView: (view: "list" | "map") => void
  searchType: string
  onOpenSidebar: () => void
  searchQuery?: string
  locationQuery?: string
  // Filters state/handlers for desktop popover
  noInsurance?: boolean
  setNoInsurance?: (value: boolean) => void
  onApplyFilters?: (coverageData: {
    noInsurance: boolean,
    coverageId: string,
    coverageName: string,
    plan: string
  }) => void
  onTimeFilterChange?: (value: string) => void
  onSortChange?: (value: "date" | "distance") => void
  sortBy?: "date" | "distance"
  coverage?: string
  plan?: string
  // Control desktop view toggle visibility (for split view pages)
  hideDesktopViewToggle?: boolean
}

export default function SearchResultsTopBar({
  view,
  setView,
  searchType,
  onOpenSidebar,
  searchQuery = "",
  locationQuery = "",
  noInsurance = false,
  setNoInsurance,
  onApplyFilters,
  onTimeFilterChange,
  onSortChange,
  sortBy = "date",
  coverage = "",
  plan = "",
  hideDesktopViewToggle = false
}: SearchResultsTopBarProps) {
  // Compact, clear title text based on search parameters
  const formatLocationDisplay = (loc: string): string => {
    const l = (loc || "").trim().toLowerCase()
    if (l === "caba") return "CABA"
    if (l === "gba") return "GBA"
    return loc
  }
  const searchDisplay = searchQuery ? `${searchQuery}` : `${searchType}`
  const locationDisplay = locationQuery && locationQuery !== "all" ? ` en ${formatLocationDisplay(locationQuery)}` : ""
  const coverageDisplay = noInsurance
    ? " • Sin cobertura"
    : coverage
    ? ` • ${coverage}${plan ? ` ${plan}` : ""}`
    : ""
  const fullSearchText = `${searchDisplay}${locationDisplay}${coverageDisplay}`
  const { currentUser, logout } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  const wrapperZ = isSearchOpen ? "z-[1200]" : "z-50"
  return (
    <div className={`sticky top-0 ${wrapperZ} w-full bg-white border-b border-slate-200 rounded-none shadow-none`}>
      <div className="w-full max-w-[1300px] mx-auto px-5 py-4 sm:px-5 sm:py-4 md:px-0 md:py-2 md:pb-1">
        {/* Mobile header-like topbar */}
        <div className="relative flex items-center justify-between gap-1 md:hidden">
          {/* Logo */}
          <Link href="/" className="shrink-0 ml-1">
            <Image src="/images/turnera-logo-small.svg" alt="Turnera" width={30} height={30} priority className="h-7 w-7" />
          </Link>
          {/* Search pill */}
          <button
            type="button"
            onClick={() => setIsSearchOpen(true)}
            className="min-w-0 flex-1 mx-3 rounded-md border border-[#0070F3]/30 pl-10 pr-4 h-11 text-left text-base bg-white text-[#1c2533] shadow-sm hover:bg-[#0070F3]/5 relative"
            aria-label="Editar búsqueda"
          >
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-[#1c2533]/70" />
            <span className="block truncate">{fullSearchText || "Buscar"}</span>
          </button>
          {/* Hamburger */}
          <button
            aria-label="Abrir menú"
            onClick={() => setIsMenuOpen((v) => !v)}
            className="mr-6"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
          {/* Mobile auth menu dropdown anchored below the hamburger/first row */}
          {isMenuOpen && (
            <div className="absolute top-full left-[-1.25rem] right-[-0.75rem] bg-white md:hidden z-[55] mt-3 pt-2 border-t border-slate-200">
              <div className="p-3 flex flex-col gap-2">
                {currentUser ? (
                  <>
                    <Link href="/plataforma/paciente" className="px-2 py-2 text-slate-800 hover:text-blue-600">
                      Mis turnos
                    </Link>
                    <button
                      onClick={() => {
                        setIsMenuOpen(false)
                        logout()
                      }}
                      className="px-2 py-2 text-left text-slate-700 hover:text-red-600"
                    >
                      Cerrar sesión
                    </button>
                  </>
                ) : (
                  <div className="grid grid-cols-2 gap-2">
                    <Auth0LoginButton className="w-full rounded-full bg-white/70 backdrop-blur border border-blue-600 text-blue-700 hover:bg-white/90 hover:border-blue-600 hover:text-blue-700 px-4 py-3 text-base shadow-sm transition-all duration-200 group focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50 focus-visible:ring-offset-1">
                      <span className="font-medium">Iniciar sesión</span>
                    </Auth0LoginButton>
                    <Auth0RegisterButton className="w-full rounded-full bg-blue-600 text-white border border-blue-600 hover:bg-blue-700 px-4 py-3 text-base shadow-sm transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50 focus-visible:ring-offset-1">
                      <span className="font-medium">Registrarme</span>
                    </Auth0RegisterButton>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Mobile second row: Location + Filters/Toggle */}
        <div className="mt-4 flex items-center justify-between md:hidden">
          {/* Location indicator */}
          <div className="min-w-0 flex items-center gap-2 text-[#1c2533]/80 text-base ml-1">
            <MapPin className="h-5 w-5" />
            <span className="block truncate max-w-[55vw]">
              {locationQuery && locationQuery !== "all" ? formatLocationDisplay(locationQuery) : "Todas las ubicaciones"}
            </span>
          </div>
          {/* Controls (aligned right) */}
          <div className="flex items-center gap-2">
            <div
              role="tablist"
              aria-label="Cambiar vista"
              className="shrink-0 inline-flex rounded-md border border-[#0070F3]/20 overflow-hidden"
            >
              <button
                type="button"
                role="tab"
                aria-selected={view === "list"}
                onClick={() => setView("list")}
                className={`h-10 px-3 flex items-center ${
                  view === "list" ? "bg-[#0070F3] text-white" : "bg-white text-[#1c2533] hover:bg-[#0070F3]/5"
                }`}
              >
                <List className="h-5 w-5" />
                <span className="sr-only">Lista</span>
              </button>
              <button
                type="button"
                role="tab"
                aria-selected={view === "map"}
                onClick={() => setView("map")}
                className={`h-10 px-3 flex items-center ${
                  view === "map" ? "bg-[#0070F3] text-white" : "bg-white text-[#1c2533] hover:bg-[#0070F3]/5"
                }`}
              >
                <Map className="h-5 w-5" />
                <span className="sr-only">Mapa</span>
              </button>
            </div>
            <Button
              variant="outline"
              onClick={onOpenSidebar}
              className="shrink-0 rounded-md border-[#0070F3]/20 hover:bg-[#0070F3]/5 hover:text-[#0070F3] h-10 px-3"
            >
              Filtros
            </Button>
          </div>
        </div>

        {/* Desktop full-width topbar (replaces header) */}
        <div className="hidden md:block">
          <div className="w-full px-6 md:px-8 lg:px-12 xl:px-16">
            {/* Row 1: Logo + Search, Auth controls */}
            <div className="flex items-center justify-between gap-3 h-12">
              <div className="min-w-0 flex items-center gap-3">
                <Link href="/" className="shrink-0">
                  <Image src="/images/turnera-logo.svg" alt="Turnera" width={100} height={30} priority className="h-7 w-auto" />
                </Link>
                <button
                  type="button"
                  onClick={() => setIsSearchOpen(true)}
                  className="min-w-[28rem] max-w-[40rem] flex-1 rounded-md border border-[#0070F3]/30 pl-9 pr-3 py-2 text-left text-sm bg-white text-[#1c2533] shadow-sm hover:bg-[#0070F3]/5 relative ml-6 lg:ml-8 xl:ml-12"
                  aria-label="Editar búsqueda"
                >
                  <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-[#1c2533]/70" />
                  <span className="block truncate max-w-[52rem]">{fullSearchText || "Buscar"}</span>
                </button>
              </div>
              <div className="flex items-center gap-4 lg:gap-6 xl:gap-8">
                {currentUser ? (
                  <PatientUserPill currentUser={currentUser} currentPatient={null} logout={logout} variant="light" />
                ) : (
                  <div className="flex items-center gap-2">
                    <Auth0LoginButton className="rounded-full bg-white border border-blue-600 text-blue-700 hover:bg-blue-50 px-4 py-2 text-sm shadow-sm transition-all">
                      <span className="font-medium">Iniciar sesión</span>
                    </Auth0LoginButton>
                    <Auth0RegisterButton className="rounded-full bg-blue-600 text-white border border-blue-600 hover:bg-blue-700 px-4 py-2 text-sm shadow-sm transition-all">
                      <span className="font-medium">Registrarme</span>
                    </Auth0RegisterButton>
                  </div>
                )}
              </div>
            </div>
            {/* Row 2: Location indicator + View toggle */}
            <div className="flex w-full items-center justify-between border-t border-slate-200 pt-2 mt-2 pb-1">
              <div className="min-w-0 flex flex-1 items-center gap-2 text-[#1c2533]/80 text-sm">
                <MapPin className="h-4 w-4" />
                <span className="block truncate max-w-[60vw]">
                  {locationQuery && locationQuery !== "all" ? formatLocationDisplay(locationQuery) : "Todas las ubicaciones"}
                </span>
              </div>
            <div className="flex items-center gap-4 lg:gap-6 xl:gap-8 ml-auto">
                {!hideDesktopViewToggle && (
                  <div
                    role="tablist"
                    aria-label="Cambiar vista"
                    className="inline-flex rounded-md border border-[#0070F3]/20 overflow-hidden"
                  >
                    <button
                      type="button"
                      role="tab"
                      aria-selected={view === "list"}
                      onClick={() => setView("list")}
                      className={`h-8 px-3 text-sm flex items-center gap-2 ${
                        view === "list" ? "bg-[#0070F3] text-white" : "bg-white text-[#1c2533] hover:bg-[#0070F3]/5"
                      }`}
                    >
                      <List className="h-4 w-4" />
                      <span>Lista</span>
                    </button>
                    <button
                      type="button"
                      role="tab"
                      aria-selected={view === "map"}
                      onClick={() => setView("map")}
                      className={`h-8 px-3 text-sm flex items-center gap-2 ${
                        view === "map" ? "bg-[#0070F3] text-white" : "bg-white text-[#1c2533] hover:bg-[#0070F3]/5"
                      }`}
                    >
                      <Map className="h-4 w-4" />
                      <span>Mapa</span>
                    </button>
                  </div>
                )}
                {/* Desktop filter popover */}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="ml-4 lg:ml-6 xl:ml-8 rounded-md border-[#0070F3]/20 hover:bg-[#0070F3]/5 hover:text-[#0070F3] h-8 px-3"
                    >
                      Filtros
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent align="end" className="p-0 w-[44rem]">
                    <SearchFiltersDesktop
                      noInsurance={noInsurance}
                      setNoInsurance={setNoInsurance ?? (() => {})}
                      onApplyFilters={onApplyFilters}
                      onTimeFilterChange={onTimeFilterChange}
                      onSortChange={onSortChange}
                      sortBy={sortBy}
                      searchType={searchType}
                      searchQuery={searchQuery}
                      locationQuery={locationQuery}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Removed old dropdown (replaced by anchored dropdown above) */}

      {/* UnifiedSearch overlay (portal to body to escape stacking contexts) */}
      {isSearchOpen && typeof document !== 'undefined' && createPortal(
        (
          <div className="fixed inset-0 z-[9999]">
            <style jsx global>{`
              /* While the search overlay is open, push Leaflet layers behind and disable interactions */
              .leaflet-pane, .leaflet-container, .leaflet-control-container { z-index: 0 !important; pointer-events: none !important; }
            `}</style>
            {/* Backdrop: light blue on mobile, gray on desktop */}
            <div
              className="absolute inset-0 bg-blue-50/95 md:bg-gray-900/30"
              onClick={() => setIsSearchOpen(false)}
            />
            <div className="relative z-10 h-full overflow-auto" onClick={() => setIsSearchOpen(false)}>
              <div className="sticky top-0 flex items-center justify-end p-3 md:p-4">
                <button
                  aria-label="Cerrar"
                  onClick={() => setIsSearchOpen(false)}
                  className="h-10 w-10 grid place-items-center rounded-md bg-white/90 border border-gray-200 shadow-sm"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="max-w-7xl mx-auto px-4 md:px-6 pb-10" onClick={(e) => e.stopPropagation()}>
                <UnifiedSearch
                  onClose={() => setIsSearchOpen(false)}
                  initialCoverage={noInsurance ? "Sin Cobertura" : coverage}
                  initialPlan={noInsurance ? "" : plan}
                />
              </div>
            </div>
          </div>
        ),
        document.body
      )}
    </div>
  )
}

