"use client"

import React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { CollapsibleSection } from "@/components/ui/collapsible-section"
import { PhoneInput } from "react-international-phone"
import type { CountryData } from 'react-international-phone'
import { DEFAULT_COVERAGES } from "@/data/coverages"
import { Patient } from "@/types/patient"
import { User } from "@/types/users"
import { PhoneNumberUtil } from 'google-libphonenumber'

interface PatientDataAndCoverageSectionProps {
  searchPerformed: boolean
  selectedAssociatedPatient: Patient | null
  patientFound: Patient | null
  patientToVerify: Patient | null
  patientDni: string
  patientName: string
  patientLastName: string
  patientPhone: string
  patientEmail: string
  phoneError: string
  phoneUtil: PhoneNumberUtil
  isPatientUser: boolean
  currentUser: User | null
  activeSection: "dni" | "datos" | "cobertura" | ""
  completedSections: {
    dni: { completed: boolean, value: string }
    datos: { completed: boolean, value: string }
    cobertura: { completed: boolean, value: string }
  }
  patientCoverage: string
  patientPlan: string
  noCoverage: boolean
  hasDefaultCoverage: boolean
  useDefaultCoverage: boolean
  plansByCoverage: Record<string, string[]>
  getPatientById: (id: string) => Patient | undefined
  setPatientName: (name: string) => void
  setPatientLastName: (lastName: string) => void
  setPatientPhone: (phone: string) => void
  setPatientEmail: (email: string) => void
  setPhoneError: (error: string) => void
  setPatientError: (error: string) => void
  setCompletedSections: React.Dispatch<React.SetStateAction<{
    dni: { completed: boolean, value: string }
    datos: { completed: boolean, value: string }
    cobertura: { completed: boolean, value: string }
  }>>
  setActiveSection: (section: "dni" | "datos" | "cobertura" | "") => void
  setPatientCoverage: (coverage: string) => void
  setPatientPlan: (plan: string) => void
  setNoCoverage: (noCoverage: boolean) => void
  setUseDefaultCoverage: (useDefaultCoverage: boolean) => void
  setPatientFound: (patient: Patient | null) => void
  setSelectedAssociatedPatient: (patient: Patient | null) => void
  addPatient: (patient: Patient) => string
  associatePatientWithUser: (patientId: string, userId: string, isDefault: boolean) => void
  capitalizeName: (name: string) => string
  validatePatientForm: () => { valid: boolean, error?: string }
  getSpanishCountries: () => CountryData[]
}

export const PatientDataAndCoverageSection: React.FC<PatientDataAndCoverageSectionProps> = ({
  searchPerformed,
  selectedAssociatedPatient,
  patientFound,
  patientToVerify,
  patientDni,
  patientName,
  patientLastName,
  patientPhone,
  patientEmail,
  phoneError,
  phoneUtil,
  isPatientUser,
  currentUser,
  activeSection,
  completedSections,
  patientCoverage,
  patientPlan,
  noCoverage,
  hasDefaultCoverage,
  useDefaultCoverage,
  plansByCoverage,
  getPatientById,
  setPatientName,
  setPatientLastName,
  setPatientPhone,
  setPatientEmail,
  setPhoneError,
  setPatientError,
  setCompletedSections,
  setActiveSection,
  setPatientCoverage,
  setPatientPlan,
  setNoCoverage,
  setUseDefaultCoverage,
  setPatientFound,
  setSelectedAssociatedPatient,
  addPatient,
  associatePatientWithUser,
  capitalizeName,
  validatePatientForm,
  getSpanishCountries
}) => {
  return (
    <>
      {/* Only show the form if no patient is selected or we're editing a patient (patientFound is null) */}
      {(!selectedAssociatedPatient || (selectedAssociatedPatient && !patientFound)) && (
        <div className="space-y-4">
          {/* Datos del paciente Section - Only show when DNI is completed */}
          {completedSections.dni.completed && (
            <CollapsibleSection
              title="Datos del paciente"
              isOpen={activeSection === "datos"}
              onOpenChange={(open) => {
                if (open) {
                  setActiveSection("datos");
                }
              }}
              summary={completedSections.datos.value}
              isComplete={completedSections.datos.completed}
            >
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-2 sm:gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="patientName">Nombre</Label>
                    <Input
                      id="patientName"
                      value={patientName}
                      onChange={(e) => {
                        // Only allow letters and spaces
                        const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                        setPatientName(capitalizeName(value));
                      }}
                      placeholder="Ingrese nombre"
                      className={`${patientFound || patientToVerify ? 'bg-gray-100 text-gray-700' : ''}`}
                      required
                      readOnly={!!patientFound || !!patientToVerify}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="patientLastName">Apellido</Label>
                    <Input
                      id="patientLastName"
                      value={patientLastName}
                      onChange={(e) => {
                        // Only allow letters and spaces
                        const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                        setPatientLastName(capitalizeName(value));
                      }}
                      placeholder="Ingrese apellido"
                      className={`${patientFound || patientToVerify ? 'bg-gray-100 text-gray-700' : ''}`}
                      required
                      readOnly={!!patientFound || !!patientToVerify}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="patientDni">DNI</Label>
                  <Input
                    id="patientDni"
                    value={patientDni}
                    onChange={() => {
                      // This is handled by the parent component
                    }}
                    placeholder="Ingrese DNI sin puntos (7-8 dígitos)"
                    className="mt-1 bg-gray-100 text-gray-700"
                    required
                    readOnly={true}
                    inputMode="numeric"
                    pattern="[0-9]*"
                  />
                </div>

                {/* Information about contact details for associated patients */}
                {isPatientUser && currentUser && (
                  <div className="bg-blue-50 p-3 rounded-md mb-4">
                    <p className="text-sm text-blue-700">
                      Los datos de contacto para este paciente serán los mismos que los de tu cuenta.
                    </p>
                  </div>
                )}
                
                {/* Only show phone and email fields for non-patient users or when editing existing patients */}
                {(!isPatientUser || (patientFound && !!patientFound.phone)) && (
                  <div>
                    <Label htmlFor="patientPhone">Teléfono</Label>
                    <div className="custom-phone-input mt-1">
                      <PhoneInput
                        defaultCountry="ar"
                        value={patientPhone}
                        onChange={(phone) => {
                          setPatientPhone(phone);

                          // Don't show error if phone is empty or just contains the area code
                          if (!phone || phone === "+" || phone.length <= 5) {
                            setPhoneError("");
                            return;
                          }

                          try {
                            // Parse the phone number using Google's libphonenumber
                            const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                            const isValid = phoneUtil.isValidNumber(phoneNumber);

                            if (!isValid) {
                              setPhoneError("El número de teléfono no es válido");
                            } else {
                              setPhoneError("");
                            }
                          } catch {
                            setPhoneError("El número de teléfono no es válido");
                          }
                        }}
                        inputStyle={{
                          width: '100%',
                          height: '2.5rem',
                          backgroundColor: patientFound && patientFound.userId ? '#f3f4f6' : 'white'
                        }}
                        className="w-full custom-phone-input with-dial-code-preview"
                        placeholder="Teléfono (opcional)"
                        countrySelectorStyleProps={{
                          buttonStyle: {
                            paddingLeft: '10px',
                            paddingRight: '5px'
                          }
                        }}
                        hideDropdown={false}
                        disableDialCodeAndPrefix={true}
                        showDisabledDialCodeAndPrefix={true}
                        disableFormatting={false}
                        preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                        countries={getSpanishCountries()}
                        disabled={!!patientFound && !!patientFound.userId && !patientToVerify}
                        inputProps={{
                          inputMode: "tel",
                        }}
                      />
                      {phoneError && (
                        <div className="text-[10px] sm:text-xs text-red-600 mt-1">
                          {phoneError}
                        </div>
                      )}
                    </div>
                    {searchPerformed && (!patientPhone || patientPhone === "+" || patientPhone.length <= 5) && isPatientUser && currentUser && (
                      <p className="text-xs text-blue-600 mt-1">
                        Se usará el teléfono del usuario actual ({getPatientById(currentUser.defaultPatientId || '')?.phone || 'No disponible'}) para recordatorios.
                      </p>
                    )}
                  </div>
                )}

                {(!isPatientUser || (patientFound && !!patientFound.email)) && (
                  <div>
                    <Label htmlFor="patientEmail">Email</Label>
                    <Input
                      id="patientEmail"
                      type="email"
                      value={patientEmail}
                      onChange={(e) => setPatientEmail(e.target.value)}
                      placeholder="Ingrese correo electrónico (opcional)"
                      className={`mt-1 ${patientFound && patientEmail && patientFound.userId && !patientToVerify ? 'bg-gray-100 text-gray-700' : ''}`}
                      readOnly={!!patientFound && !!patientEmail && !!patientFound.userId && !patientToVerify}
                    />
                    {patientFound && !patientEmail && (
                      <p className="text-xs text-blue-600 mt-1">
                        No hay email registrado. Puede agregar uno.
                      </p>
                    )}
                    {searchPerformed && !patientEmail && isPatientUser && currentUser && (
                      <p className="text-xs text-blue-600 mt-1">
                        Se usará el email del usuario actual ({getPatientById(currentUser.defaultPatientId || '')?.email || currentUser.email || 'No disponible'}) para recordatorios.
                      </p>
                    )}
                  </div>
                )}

                <Button
                  onClick={() => {
                    // Validate phone if provided
                    if (patientPhone && patientPhone.trim() !== "+" && patientPhone.trim() !== "" && patientPhone.length > 5) {
                      try {
                        const phoneNumber = phoneUtil.parseAndKeepRawInput(patientPhone);
                        const isValid = phoneUtil.isValidNumber(phoneNumber);

                        if (!isValid) {
                          setPatientError("El número de teléfono no es válido");
                          return;
                        }
                      } catch {
                        setPatientError("El número de teléfono no es válido");
                        return;
                      }
                    }

                    // Validate email if provided
                    if (patientEmail && patientEmail.trim() !== "" && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(patientEmail)) {
                      setPatientError("El correo electrónico no es válido");
                      return;
                    }

                    // Capitalize each part separately to handle multiple names/surnames
                    const capitalizedName = capitalizeName(patientName);
                    const capitalizedLastName = capitalizeName(patientLastName);

                    // Combine them into a full name
                    const fullName = `${capitalizedName} ${capitalizedLastName}`.trim();

                    // Mark datos section as completed and move to cobertura section
                    setCompletedSections(prev => ({
                      ...prev,
                      datos: { completed: true, value: fullName }
                    }));
                    setActiveSection("cobertura");
                    setPatientError(""); // Clear any error messages
                  }}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue-600"
                  disabled={!patientName.trim() || !patientLastName.trim()}
                >
                  Continuar
                </Button>
              </div>
            </CollapsibleSection>
          )}
        </div>
      )}

      {/* Coverage section */}
      {(!selectedAssociatedPatient || (selectedAssociatedPatient && !patientFound)) && (
        <div className="space-y-4">
          {/* Cobertura Section - Only show when Datos del paciente is completed */}
          {completedSections.datos.completed && (
            <CollapsibleSection
              title="Cobertura"
              isOpen={activeSection === "cobertura"}
              onOpenChange={(open) => {
                if (open) {
                  setActiveSection("cobertura");
                }
              }}
              summary={completedSections.cobertura.value}
              isComplete={completedSections.cobertura.completed}
            >
              <div className="space-y-4">
                {patientFound && hasDefaultCoverage && (
                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-100 mb-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-blue-800">Cobertura registrada:</p>
                        <p className="text-sm text-blue-700">{patientFound.coverage}</p>
                      </div>
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <Switch
                          id="use-default-coverage"
                          checked={useDefaultCoverage}
                          onCheckedChange={(checked) => {
                            setUseDefaultCoverage(checked);
                            if (checked) {
                              // Restore default coverage
                              const coverageParts = patientFound.coverage.split(' ');
                              if (coverageParts.length > 1) {
                                setPatientCoverage(coverageParts[0]);
                                setPatientPlan(coverageParts.slice(1).join(' '));
                              } else {
                                setPatientCoverage(patientFound.coverage);
                                setPatientPlan('');
                              }
                              setNoCoverage(false);
                            }
                          }}
                        />
                        <Label htmlFor="use-default-coverage" className="text-sm text-blue-800">
                          Usar esta cobertura
                        </Label>
                      </div>
                    </div>
                  </div>
                )}

                {(!patientFound || !useDefaultCoverage) && (
                  <>
                    <div>
                      <Label htmlFor="patientCoverage">Cobertura</Label>
                      <Select
                        value={patientCoverage}
                        onValueChange={setPatientCoverage}
                        disabled={noCoverage || (!!patientFound && useDefaultCoverage)}
                      >
                        <SelectTrigger
                          id="patientCoverage"
                          className={`mt-1 ${patientFound && useDefaultCoverage ? 'bg-gray-100 text-gray-700' : ''}`}
                        >
                          <SelectValue placeholder="Seleccione cobertura" />
                        </SelectTrigger>
                        <SelectContent>
                          {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                            <SelectItem key={cov.id} value={cov.name}>{cov.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {patientCoverage && (
                      <div>
                        <Label htmlFor="patientPlan">Plan</Label>
                        <Select
                          value={patientPlan}
                          onValueChange={setPatientPlan}
                          disabled={noCoverage || (!!patientFound && useDefaultCoverage)}
                        >
                          <SelectTrigger
                            id="patientPlan"
                            className={`mt-1 ${patientFound && useDefaultCoverage ? 'bg-gray-100 text-gray-700' : ''}`}
                          >
                            <SelectValue placeholder="Seleccione plan" />
                          </SelectTrigger>
                          <SelectContent>
                            {plansByCoverage[patientCoverage]?.map((plan) => (
                              <SelectItem key={plan} value={plan}>
                                {plan}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="flex items-center space-x-2 mt-2">
                      <Switch
                        id="no-coverage"
                        checked={noCoverage}
                        onCheckedChange={(checked) => {
                          setNoCoverage(checked);
                          if (checked) {
                            setPatientCoverage('');
                            setPatientPlan('');
                            if (patientFound && hasDefaultCoverage) {
                              setUseDefaultCoverage(false);
                            }
                          }
                        }}
                        disabled={!!patientFound && useDefaultCoverage}
                      />
                      <Label htmlFor="no-coverage">No tengo cobertura</Label>
                    </div>
                  </>
                )}

                <Button
                  onClick={() => {
                    // Format coverage string based on selected options
                    let coverageString = "";
                    if (noCoverage) {
                      coverageString = "Sin Cobertura"; // Ensure proper capitalization
                    } else if (patientCoverage) {
                      coverageString = patientCoverage;
                      if (patientPlan) {
                        coverageString += " " + patientPlan;
                      }
                    } else if (patientFound && patientFound.coverage) {
                      coverageString = patientFound.coverage;
                    }

                    // If no coverage is selected, default to "Sin Cobertura"
                    if (!coverageString) {
                      coverageString = "Sin Cobertura";
                    }

                    // Mark cobertura section as completed
                    setCompletedSections(prev => ({
                      ...prev,
                      cobertura: { completed: true, value: coverageString }
                    }));

                    // Close the cobertura section by setting no active section
                    setActiveSection("");
                  }}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Continuar
                </Button>
              </div>
            </CollapsibleSection>
          )}
        </div>
      )}

      {/* Add "Crear paciente" button for new patients outside the pills */}
      {!patientFound && !selectedAssociatedPatient && completedSections.cobertura.completed && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <Button
            onClick={() => {
              // Validate form before creating patient
              const validationResult = validatePatientForm();
              if (!validationResult.valid) {
                setPatientError(validationResult.error || "Error en el formulario");
                return;
              }

              // Get the coverage string from completed sections
              const coverageString = completedSections.cobertura.value || "Sin Cobertura";

              try {
                // Get the default patient for contact info if needed
                const defaultPatient = isPatientUser && currentUser ?
                  getPatientById(currentUser.defaultPatientId || '') : null;

                // Capitalize each part separately to handle multiple names/surnames
                const capitalizedName = capitalizeName(patientName);
                const capitalizedLastName = capitalizeName(patientLastName);

                // Combine them into a full name
                const fullName = `${capitalizedName} ${capitalizedLastName}`.trim();

                // Create patient object
                const newPatient: Patient = {
                  name: fullName,
                  dni: patientDni,
                  // When creating an associated patient, always use the main user's contact info
                  phone: isPatientUser && defaultPatient ? defaultPatient.phone : 
                         (patientPhone && patientPhone !== "+" && patientPhone.length > 5 ? patientPhone : ''),
                  email: isPatientUser && defaultPatient ? defaultPatient.email : patientEmail || '',
                  coverage: coverageString,
                  defaultCoverage: coverageString,
                  // These fields will be set to false since we're not verifying them
                  phoneVerified: false,
                  emailVerified: false
                };

                // Add patient to context
                const patientId = addPatient(newPatient);

                // Associate with current user but don't set as default
                if (isPatientUser && currentUser) {
                  const updatedPatient = {
                    ...newPatient,
                    id: patientId,
                    userId: currentUser.id,
                    isDefault: false // Not the default patient
                  };

                  // Update the patient with the user association
                  addPatient(updatedPatient);

                  // Set as the selected patient for this appointment
                  setPatientFound(updatedPatient);
                  setSelectedAssociatedPatient(updatedPatient);

                  // Show success message
                  setPatientError("Paciente creado exitosamente. Ahora puede confirmar la reserva.");
                }

                // Clear error message if no user is logged in
                if (!isPatientUser || !currentUser) {
                  setPatientError("");
                }
              } catch (error) {
                // Handle errors from patient creation
                if (error instanceof Error) {
                  setPatientError(error.message);
                } else {
                  setPatientError("Error al crear el paciente. Intente nuevamente.");
                }
              }
            }}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
            disabled={!completedSections.cobertura.completed}
          >
            Crear paciente
          </Button>
        </div>
      )}

      {/* Add "Asociar paciente" button for verified patients outside the pills */}
      {patientFound && completedSections.cobertura.completed && patientFound.id && !patientFound.userId && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <Button
            onClick={() => {
              if (!patientFound || !currentUser || !patientFound.id) return;

              try {
                // Get the coverage string from completed sections
                const coverageString = completedSections.cobertura.value || "Sin Cobertura";

                // Get default patient for contact info if needed
                const defaultPatient = isPatientUser && currentUser ?
                  getPatientById(currentUser.defaultPatientId || '') : null;

                // Capitalize each part separately to handle multiple names/surnames
                const capitalizedName = capitalizeName(patientName);
                const capitalizedLastName = capitalizeName(patientLastName);

                // Combine them into a full name
                const fullName = `${capitalizedName} ${capitalizedLastName}`.trim();

                // First update the patient with new phone, email and coverage data
                const updatedPatient = {
                  ...patientFound,
                  name: fullName,
                  // When associating a patient, always use main user's contact info
                  phone: defaultPatient ? defaultPatient.phone : patientFound.phone || '',
                  email: defaultPatient ? defaultPatient.email : patientFound.email || '',
                  coverage: coverageString
                };

                // Update the patient in the context
                addPatient(updatedPatient);

                // Now associate the patient with the current user (not as default)
                associatePatientWithUser(patientFound.id, currentUser.id, false);

                // Get the updated patient after association
                const associatedPatient = getPatientById(patientFound.id);
                if (associatedPatient) {
                  // Set as the selected patient for this appointment
                  setPatientFound(associatedPatient);
                  setSelectedAssociatedPatient(associatedPatient);
                }

                // Show success message
                setPatientError("Paciente asociado exitosamente. Ahora puede confirmar la reserva.");
              } catch (error) {
                // Handle errors from patient association
                if (error instanceof Error) {
                  setPatientError(error.message);
                } else {
                  setPatientError("Error al asociar el paciente. Intente nuevamente.");
                }
              }
            }}
            className="w-full bg-green-600 hover:bg-green-700 text-white"
            disabled={!completedSections.cobertura.completed}
          >
            Asociar paciente
          </Button>
        </div>
      )}
    </>
  )
}
