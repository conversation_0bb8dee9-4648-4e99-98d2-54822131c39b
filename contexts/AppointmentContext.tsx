"use client"

import type React from "react"
import {createContext, useContext, useEffect, useState} from "react"
import type {Appointment} from "@/types/scheduler"
import {usePatients} from "./PatientContext"
import {handleAppointmentCancellation, handleNewAppointment} from "@/services/notifications"

type AppointmentContextType = {
    appointments: Record<string, Appointment[]>
    addAppointment: (appointment: Appointment) => Promise<void>
    removeAppointment: (appointmentId: string) => void
    cancelAppointment: (appointmentId: string, reason?: string, cancelledBy?: string) => Promise<void>
    updateAppointment: (appointmentId: string, updatedAppointment: Partial<Appointment>) => void
    getPatientNameForAppointment: (appointment: Appointment) => string
    navigationState: { view?: string; date?: string } | null
    setNavigationState: (state: { view?: string; date?: string } | null) => void
    blockedSlots: Record<string, Record<string, Record<string, string[]>>>
    toggleBlockedSlot: (date: string, time: string, doctorId: string, medicalCenterId: string) => void
    isSlotBlocked: (date: string, time: string, doctorId: string, medicalCenterId: string) => boolean
    clearAllBlockedSlots: () => void
}

const AppointmentContext = createContext<AppointmentContextType | undefined>(undefined)

export const useAppointments = () => {
    const context = useContext(AppointmentContext)
    if (!context) {
        throw new Error("useAppointments must be used within an AppointmentProvider")
    }
    return context
}

export const AppointmentProviderWithPatients: React.FC<{ children: React.ReactNode }> = ({children}) => {
    return (
        <AppointmentProvider>
            {children}
        </AppointmentProvider>
    )
}

export const AppointmentProvider: React.FC<{ children: React.ReactNode }> = ({children}) => {
    const {getPatientById, patients} = usePatients()

    console.log('AppointmentProvider - patients from PatientContext:', patients.length)

    const [appointments, setAppointments] = useState<Record<string, Appointment[]>>({})
    const [blockedSlots, setBlockedSlots] = useState<Record<string, Record<string, Record<string, string[]>>>>({})
    const [navigationState, setNavigationState] = useState<{ view?: string; date?: string } | null>(null)

    useEffect(() => {
        console.log('AppointmentProvider - patients changed:', patients.length)
    }, [patients])

    // Function to clean up invalid appointments (e.g., those with null time)
    const cleanupInvalidAppointments = (appointmentsData: Record<string, Appointment[]>): Record<string, Appointment[]> => {
        const cleanedAppointments: Record<string, Appointment[]> = {}

        // Process each date's appointments
        Object.entries(appointmentsData).forEach(([date, appts]) => {
            // Filter out appointments with null time
            const validAppointments = appts.filter(apt => apt.time !== null && apt.time !== undefined)

            // Only add the date if there are valid appointments
            if (validAppointments.length > 0) {
                cleanedAppointments[date] = validAppointments
            }
        })

        return cleanedAppointments
    }

    useEffect(() => {
        const storedAppointments = localStorage.getItem("appointments")
        if (storedAppointments) {
            try {
                const parsedAppointments = JSON.parse(storedAppointments)

                // Clean up invalid appointments before setting state
                const cleanedAppointments = cleanupInvalidAppointments(parsedAppointments)

                // If we removed any appointments, update localStorage
                const originalCount = Object.values(parsedAppointments).flat().length
                const cleanedCount = Object.values(cleanedAppointments).flat().length

                if (originalCount !== cleanedCount) {
                    console.log(`Cleaned up ${originalCount - cleanedCount} invalid appointments`)
                    localStorage.setItem("appointments", JSON.stringify(cleanedAppointments))
                }

                setAppointments(cleanedAppointments)
            } catch (error) {
                console.error("Error parsing appointments from localStorage:", error)
                setAppointments({})
            }
        }

        // Load blocked slots from localStorage
        const storedBlockedSlots = localStorage.getItem("blockedSlots")
        if (storedBlockedSlots) {
            try {
                const parsedBlockedSlots = JSON.parse(storedBlockedSlots)
                // Validate the structure - should be an object
                if (parsedBlockedSlots && typeof parsedBlockedSlots === 'object') {
                    setBlockedSlots(parsedBlockedSlots)
                    console.log("Loaded blocked slots from localStorage")
                } else {
                    console.warn("Invalid blockedSlots format, resetting to empty object")
                    setBlockedSlots({})
                    localStorage.removeItem("blockedSlots")
                }
            } catch (error) {
                console.error("Error parsing blocked slots from localStorage:", error)
                setBlockedSlots({})
                localStorage.removeItem("blockedSlots")
            }
        } else {
            // Ensure blockedSlots is initialized as an empty object
            console.log("No blocked slots found in localStorage, initializing as empty object")
            setBlockedSlots({})
        }
    }, [])

    useEffect(() => {
        const timeoutId = setTimeout(() => {
            const oldValue = localStorage.getItem("appointments")
            localStorage.setItem("appointments", JSON.stringify(appointments))
            triggerStorageEvent("appointments", appointments, oldValue ? JSON.parse(oldValue) : {})
        }, 300)
        return () => clearTimeout(timeoutId)
    }, [appointments])

    // Save blocked slots to localStorage
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            const oldValue = localStorage.getItem("blockedSlots")

            // Store the blocked slots data
            // The structure is: { medicalCenterId: { doctorId: { date: [blocked times] } } }
            localStorage.setItem("blockedSlots", JSON.stringify(blockedSlots))

            // Dispatch a custom event instead of using triggerStorageEvent
            // This avoids type issues with the StorageEventData interface
            if (typeof window !== 'undefined') {
                const event = new StorageEvent('storage', {
                    key: 'blockedSlots',
                    newValue: JSON.stringify(blockedSlots),
                    oldValue: oldValue,
                    storageArea: localStorage,
                    url: window.location.href
                });
                window.dispatchEvent(event);
            }
        }, 300)
        return () => clearTimeout(timeoutId)
    }, [blockedSlots])

    useEffect(() => {
        const handleStorageChange = (event: StorageEvent) => {
            if (event.key === "appointments" && event.newValue) {
                try {
                    const newAppointments = JSON.parse(event.newValue)
                    setAppointments(prev => {
                        // Only update if different to prevent infinite loops
                        const prevString = JSON.stringify(prev)
                        const newString = JSON.stringify(newAppointments)
                        return prevString === newString ? prev : newAppointments
                    })
                } catch (error) {
                    console.error("Error parsing appointments from storage event:", error)
                }
            }

            // Handle blocked slots storage events
            if (event.key === "blockedSlots") {
                try {
                    if (event.newValue) {
                        const newBlockedSlots = JSON.parse(event.newValue)
                        setBlockedSlots(prev => {
                            // Only update if different to prevent infinite loops
                            const prevString = JSON.stringify(prev)
                            const newString = JSON.stringify(newBlockedSlots)
                            return prevString === newString ? prev : newBlockedSlots
                        })
                        console.log("Updated blocked slots from storage event")
                    } else {
                        // If blockedSlots was removed, set to empty object
                        setBlockedSlots(prev => {
                            return Object.keys(prev).length === 0 ? prev : {}
                        })
                        console.log("Cleared blocked slots from storage event")
                    }
                } catch (error) {
                    console.error("Error parsing blocked slots from storage event:", error)
                    // On error, reset to empty object
                    setBlockedSlots({})
                }
            }
        }

        window.addEventListener("storage", handleStorageChange)
        return () => window.removeEventListener("storage", handleStorageChange)
    }, [])

    // Function to get patient name for an appointment
    const getPatientNameForAppointment = (appointment: Appointment): string => {
        // Get the patient by ID from PatientContext
        const patient = getPatientById(appointment.patient);
        if (patient) {
            return patient.name;
        }

        // If patient not found, return a placeholder
        return "Unknown Patient";
    }

    const addAppointment = async (appointment: Appointment) => {
        // Validate appointment has required fields
        if (!appointment.time) {
            console.error('Cannot add appointment with null time:', appointment)
            return // Don't add invalid appointments
        }

        if (!appointment.date) {
            console.error('Cannot add appointment with null date:', appointment)
            return // Don't add invalid appointments
        }

        if (!appointment.patient) {
            console.error('Cannot add appointment with null patient:', appointment)
            return // Don't add invalid appointments
        }

        // Add the valid appointment
        setAppointments(prev => {
            const existing = prev[appointment.date] || []
            if (existing.some(apt => apt.id === appointment.id)) return prev
            const newAppointments = {
                ...prev,
                [appointment.date]: [...existing, appointment]
            }
            return prev[appointment.date]?.length === newAppointments[appointment.date]?.length
                ? prev
                : newAppointments
        })

        // Trigger notifications for new appointments (only if status is not "Cancelado")
        if (appointment.status !== "Cancelado") {
            try {
                // Get patient data
                const patient = getPatientById(appointment.patient)
                if (!patient) {
                    console.warn(`Patient not found for appointment ${appointment.id}`)
                    return
                }

                // Get doctor and medical center data from storage

                // Get doctor data
                let doctor = null
                if (appointment.doctorId) {
                    const allDoctors = storage.getAllDoctorsInSystem()
                    doctor = allDoctors.find(d => d.id === appointment.doctorId)
                }

                // Get medical center data
                let medicalCenter = null
                if (appointment.medicalCenterId) {
                    const medicalCenters = storage.getMedicalCenters()
                    medicalCenter = medicalCenters.find(mc => mc.id === appointment.medicalCenterId)
                }

                // Only send notifications if we have all required data
                if (patient && doctor && medicalCenter) {
                    await handleNewAppointment({
                        appointment,
                        patient,
                        doctor,
                        medicalCenter
                    })
                } else {
                    console.warn(`Missing data for notifications - Patient: ${!!patient}, Doctor: ${!!doctor}, Medical Center: ${!!medicalCenter}`)
                }
            } catch (error) {
                console.error('Error sending appointment notifications:', error)
            }
        }
    }

    const removeAppointment = (appointmentId: string) => {
        setAppointments(prev => {
            let modified = false
            const newAppointments = {...prev}
            for (const date in newAppointments) {
                const originalLength = newAppointments[date].length
                newAppointments[date] = newAppointments[date].filter(apt => apt.id !== appointmentId)
                if (newAppointments[date].length !== originalLength) {
                    modified = true
                    if (newAppointments[date].length === 0) {
                        delete newAppointments[date]
                    }
                }
            }
            return modified ? newAppointments : prev
        })
    }

    const cancelAppointment = async (appointmentId: string, reason?: string, cancelledBy?: string) => {
        // First, find the appointment to get its data before cancelling
        let appointmentToCancel: Appointment | null = null
        for (const date in appointments) {
            const appointment = appointments[date].find(apt => apt.id === appointmentId)
            if (appointment) {
                appointmentToCancel = appointment
                break
            }
        }

        setAppointments(prev => {
            let modified = false
            const newAppointments = {...prev}

            // Find the appointment to cancel
            for (const date in newAppointments) {
                const appointmentIndex = newAppointments[date].findIndex(apt => apt.id === appointmentId)

                if (appointmentIndex !== -1) {
                    // Update the appointment with cancelled status and metadata
                    const updatedAppointment: Appointment = {
                        ...newAppointments[date][appointmentIndex],
                        status: "Cancelado",
                        cancellationReason: reason || "",
                        cancelledAt: new Date().toISOString(),
                        cancelledBy: cancelledBy || ""
                    }

                    // Replace the appointment with the updated version
                    newAppointments[date][appointmentIndex] = updatedAppointment
                    modified = true
                    break
                }
            }

            return modified ? newAppointments : prev
        })

        // Trigger cancellation notifications
        if (appointmentToCancel) {
            try {
                // Get patient data
                const patient = getPatientById(appointmentToCancel.patient)
                if (!patient) {
                    console.warn(`Patient not found for cancelled appointment ${appointmentId}`)
                    return
                }

                // Get doctor and medical center data from storage

                // Get doctor data
                let doctor = null
                if (appointmentToCancel.doctorId) {
                    const allDoctors = storage.getAllDoctorsInSystem()
                    doctor = allDoctors.find(d => d.id === appointmentToCancel.doctorId)
                }

                // Get medical center data
                let medicalCenter = null
                if (appointmentToCancel.medicalCenterId) {
                    const medicalCenters = storage.getMedicalCenters()
                    medicalCenter = medicalCenters.find(mc => mc.id === appointmentToCancel.medicalCenterId)
                }

                // Only send notifications if we have all required data
                if (patient && doctor && medicalCenter) {
                    await handleAppointmentCancellation({
                        appointment: appointmentToCancel,
                        patient,
                        doctor,
                        medicalCenter
                    }, reason)
                } else {
                    console.warn(`Missing data for cancellation notifications - Patient: ${!!patient}, Doctor: ${!!doctor}, Medical Center: ${!!medicalCenter}`)
                }
            } catch (error) {
                console.error('Error sending cancellation notifications:', error)
            }
        }
    }

    const updateAppointment = (appointmentId: string, updatedAppointment: Partial<Appointment>) => {
        setAppointments(prev => {
            let modified = false
            const newAppointments = {...prev}

            // Find and remove the appointment from its original date
            for (const date in newAppointments) {
                const appointmentIndex = newAppointments[date].findIndex(apt => apt.id === appointmentId)
                if (appointmentIndex !== -1) {
                    const [existingAppointment] = newAppointments[date].splice(appointmentIndex, 1)
                    if (newAppointments[date].length === 0) {
                        delete newAppointments[date]
                    }
                    modified = true

                    // Update the appointment with new data
                    const updated = {...existingAppointment, ...updatedAppointment}
                    const newDate = updated.date // Use the updated date if provided, else original

                    // Add it back to the appropriate date bucket
                    const targetDateAppointments = newAppointments[newDate] || []
                    newAppointments[newDate] = [...targetDateAppointments, updated]
                    break
                }
            }

            return modified ? newAppointments : prev
        })
    }

    // Patient-related functions have been moved to PatientContext

    /**
     * Function to toggle a slot's blocked status
     *
     * The blockedSlots data structure is organized as follows:
     * {
     *   medicalCenterId: {
     *     doctorId: {
     *       date: [array of blocked times]
     *     }
     *   }
     * }
     *
     * This allows us to efficiently look up blocked slots by medical center, doctor, and date.
     */
    const toggleBlockedSlot = (date: string, time: string, doctorId: string, medicalCenterId: string) => {
        console.log(`AppointmentContext - toggleBlockedSlot called with:`, {date, time, doctorId, medicalCenterId});
        console.log(`Current blockedSlots state:`, blockedSlots);

        setBlockedSlots(prev => {
            console.log(`Previous blockedSlots state in setter:`, prev);

            // Create a deep copy of the previous state to avoid mutation issues
            const newBlockedSlots = JSON.parse(JSON.stringify(prev));

            // Initialize nested structure if it doesn't exist
            if (!newBlockedSlots[medicalCenterId]) newBlockedSlots[medicalCenterId] = {};
            if (!newBlockedSlots[medicalCenterId][doctorId]) newBlockedSlots[medicalCenterId][doctorId] = {};
            if (!newBlockedSlots[medicalCenterId][doctorId][date]) newBlockedSlots[medicalCenterId][doctorId][date] = [];

            const dateSlots = newBlockedSlots[medicalCenterId][doctorId][date];
            console.log(`Current dateSlots for this combination:`, dateSlots);

            if (dateSlots.includes(time)) {
                console.log(`Removing time ${time} from blocked slots`);
                // Remove the time from blocked slots
                newBlockedSlots[medicalCenterId][doctorId][date] = dateSlots.filter((t: string) => t !== time);
                // Clean up empty entries
                if (newBlockedSlots[medicalCenterId][doctorId][date].length === 0) {
                    delete newBlockedSlots[medicalCenterId][doctorId][date];
                    if (Object.keys(newBlockedSlots[medicalCenterId][doctorId]).length === 0) {
                        delete newBlockedSlots[medicalCenterId][doctorId];
                        if (Object.keys(newBlockedSlots[medicalCenterId]).length === 0) {
                            delete newBlockedSlots[medicalCenterId];
                        }
                    }
                }
            } else {
                console.log(`Adding time ${time} to blocked slots`);
                // Add the time to blocked slots
                newBlockedSlots[medicalCenterId][doctorId][date] = [...dateSlots, time];
            }

            console.log(`New blockedSlots state:`, newBlockedSlots);
            return newBlockedSlots;
        });
    };

    // Function to check if a slot is blocked
    const isSlotBlocked = (date: string, time: string, doctorId: string, medicalCenterId: string): boolean => {
        // Get the blocked slots for this combination
        const isBlocked = Boolean(blockedSlots[medicalCenterId]?.[doctorId]?.[date]?.includes(time));

        // Only log when a slot is blocked to avoid console spam
        if (isBlocked) {
            console.log(`Slot is blocked: ${date} ${time} ${doctorId} ${medicalCenterId}`);
        }

        return isBlocked;
    };

    // Function to clear all blocked slots
    const clearAllBlockedSlots = () => {
        console.log('Clearing all blocked slots');

        // First set to empty object
        setBlockedSlots({});

        // Remove from localStorage
        localStorage.removeItem('blockedSlots');

        // Also try setting to empty object in localStorage
        try {
            localStorage.setItem('blockedSlots', JSON.stringify({}));
            // Then remove it again to ensure it's gone
            localStorage.removeItem('blockedSlots');
        } catch (error) {
            console.error('Error while clearing blockedSlots:', error);
        }

        // Dispatch a custom event to notify other components
        if (typeof window !== 'undefined') {
            const event = new StorageEvent('storage', {
                key: 'blockedSlots',
                newValue: null,
                oldValue: JSON.stringify(blockedSlots),
                storageArea: localStorage,
                url: window.location.href
            });
            window.dispatchEvent(event);

            // Also dispatch an event with empty object as newValue
            const emptyEvent = new StorageEvent('storage', {
                key: 'blockedSlots',
                newValue: JSON.stringify({}),
                oldValue: JSON.stringify(blockedSlots),
                storageArea: localStorage,
                url: window.location.href
            });
            window.dispatchEvent(emptyEvent);
        }
    };

    return (
        <AppointmentContext.Provider value={{
            // Appointment management
            appointments,
            addAppointment,
            removeAppointment,
            cancelAppointment,
            updateAppointment,
            getPatientNameForAppointment,

            // Navigation state
            navigationState,
            setNavigationState,

            // Blocked slots management
            blockedSlots,
            toggleBlockedSlot,
            isSlotBlocked,
            clearAllBlockedSlots
        }}>
            {children}
        </AppointmentContext.Provider>
    )
}