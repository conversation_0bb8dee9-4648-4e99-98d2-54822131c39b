"use client"

import {useEffect} from "react"
import {usePathname, useRouter} from "next/navigation"
import {useAuth} from "@/hooks/useAuth"
import {UserRole} from "@/types/users"

export default function ProfessionalLayout({
                                               children,
                                           }: {
    children: React.ReactNode
}) {
    const pathname = usePathname()
    const router = useRouter()
    const {currentUser, isAuthenticated, isLoading} = useAuth()

    // Authentication check
    useEffect(() => {
        // Skip authentication check for login, registration, and reset-password pages
        if (
            pathname === "/plataforma/profesional/login" ||
            pathname === "/plataforma/profesional/registro" ||
            pathname.includes("/plataforma/profesional/reset-password")
        ) {
            console.log('Layout - Skipping auth check for special page:', pathname);
            return
        }

        if (!isLoading && !isAuthenticated) {
            // Not authenticated, redirect to login
            router.push('/plataforma/login')
            return
        }

        if (!isLoading && isAuthenticated && currentUser) {
            // Extract doctorId from the URL if present to check for doctor-specific pages
            const matches = pathname.match(/\/plataforma\/profesional\/([^\/]+)/)
            const urlDoctorId = matches && matches[1];

            // If this is a doctor-specific page, we'll check access in the [doctorId] layout
            // For general professional pages, check if user has doctor role anywhere
            if (!urlDoctorId) {
                // Check if user has doctor role in any medical center
                const hasDoctorRole = currentUser.roles === UserRole.DOCTOR ||
                    (currentUser.medicalCenterRoles &&
                        currentUser.medicalCenterRoles.some(mcr => mcr.role === UserRole.DOCTOR));

                if (!hasDoctorRole) {
                    console.warn(`User ${currentUser.name} with role ${currentUser.roles} attempted to access professional page`)
                    router.push('/plataforma/unauthorized')
                    return
                }
            }

            // For doctor users, check if they're accessing their own pages
            if (matches && matches[1]) {
                const urlDoctorId = matches[1]

                // Skip this check for special pages like reset-password
                if (pathname.includes('/reset-password')) {
                    console.log('Layout - Allowing access to reset-password page');
                    return;
                }

                // Check if doctor is accessing their own page
                if (currentUser.doctorId !== urlDoctorId) {
                    console.warn(`Doctor ${currentUser.name} attempted to access page for doctor ${urlDoctorId}`)
                    // Redirect to their own page
                    router.push(`/plataforma/profesional/${currentUser.doctorId}`)
                    return
                }
            }
        }
    }, [isAuthenticated, isLoading, currentUser, router, pathname])

    return (
        <>
            {children}
        </>
    )
}
