'use client';

import React from 'react';
import {useAuth} from '@/contexts/AuthContext';
import {Button} from '@/components/ui/button';

interface Auth0RegisterButtonProps {
  className?: string;
  children?: React.ReactNode;
}

export const Auth0RegisterButton: React.FC<Auth0RegisterButtonProps> = ({
  className = "",
  children = "Registrarse con Auth0",
}) => {
  const {signupWithAuth0, isLoading} = useAuth();

  return (
    <Button
      onClick={signupWithAuth0}
      disabled={isLoading}
      className={`w-full ${className}`}
      variant="outline"
    >
      {isLoading ? 'Cargando...' : children}
    </Button>
  );
};

export default Auth0RegisterButton;

