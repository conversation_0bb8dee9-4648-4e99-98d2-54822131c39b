"use client"

import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, usePathname } from "next/navigation"
import { useContext, useEffect, useRef, useMemo, useState } from "react"
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext"
import { useAppointments } from "@/contexts/AppointmentContext"
import { usePatients } from "@/contexts/PatientContext"
import { useDoctors } from "@/hooks/useDoctors"
import AnalyticsDashboard from "@/components/dashboard/establishment-dashboard"
import { useAuth } from "@/hooks/useAuth"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { LoadingButton } from "@/components/ui/loading-button"
import { LoadingIconButton } from "@/components/ui/loading-icon-button"
import { MedicalCenterUserPill } from "@/components/ui/MedicalCenterUserPill"
import { BarChart2, Calendar, Check, Search, Settings, Share2 } from "lucide-react"

export default function AnalyticsPage() {
  const router = useRouter()
  const pathname = usePathname()
  const params = useParams()
  const medicalCenterIdParam = params.medicalCenterId as string
  const medicalCenterIdNum = Number.parseInt(medicalCenterIdParam)

  // Get data from contexts
  const { medicalCenters, setActiveMedicalCenterId } = useContext(MedicalCenterContext)
  const { doctors } = useDoctors(medicalCenterIdParam)
  const { appointments } = useAppointments()
  const { patients } = usePatients()
  const { currentUser, logout } = useAuth()

  // Local header UI state (to match establishment page)
  const [patientSearchTerm, setPatientSearchTerm] = useState("")
  const [isPatientSearchFocused, setIsPatientSearchFocused] = useState(false)
  const [filteredPatients, setFilteredPatients] = useState<typeof patients>([])
  const [patientsLoading, setPatientsLoading] = useState(false)
  const [isAnalyticsLoading, setIsAnalyticsLoading] = useState(false)
  const [isConfigLoading, setIsConfigLoading] = useState(false)
  const [isShareCopied, setIsShareCopied] = useState(false)

  // Use useRef to track if we've already set the active medical center
  const hasInitializedRef = useRef(false)

  // Find the medical center
  const medicalCenter = useMemo(() =>
    medicalCenters.find((mc) => mc.id === medicalCenterIdParam),
    [medicalCenters, medicalCenterIdParam]
  )

  // Set the active medical center ID when the component mounts
  useEffect(() => {
    if (medicalCenterIdParam && !hasInitializedRef.current) {
      hasInitializedRef.current = true
      setActiveMedicalCenterId(medicalCenterIdParam)
    }
  }, [medicalCenterIdParam, setActiveMedicalCenterId])

  // Patient search filtering (simple match by name or DNI)
  useEffect(() => {
    if (!patientSearchTerm.trim()) {
      setFilteredPatients([])
      return
    }
    setPatientsLoading(true)
    const normalizedQuery = patientSearchTerm.toLowerCase().trim()
    const data = patients.filter(p =>
      p.name.toLowerCase().includes(normalizedQuery) ||
      p.dni.toLowerCase().includes(normalizedQuery)
    )
    setFilteredPatients(data)
    setPatientsLoading(false)
  }, [patientSearchTerm, patients])

  const handleAgendasClick = () => {
    setIsAnalyticsLoading(false)
    router.push(`/plataforma/establecimiento/${medicalCenterIdParam}`)
  }

  const handleAnalyticsClick = () => {
    setIsAnalyticsLoading(true)
    router.push(`/plataforma/establecimiento/${medicalCenterIdParam}/analytics`)
  }

  const handleConfigClick = () => {
    setIsConfigLoading(true)
    router.push(`/plataforma/establecimiento/${medicalCenterIdParam}/configuration`)
  }

  const copyMedicalCenterBookingUrl = () => {
    const url = `${window.location.origin}/plataforma/reservar/cm/${medicalCenterIdParam}`
    navigator.clipboard.writeText(url)
      .then(() => {
        setIsShareCopied(true)
        setTimeout(() => setIsShareCopied(false), 2000)
      })
      .catch(err => console.error('Failed to copy URL: ', err))
  }

  if (!medicalCenter) {
    return <div>Medical Center not found</div>
  }

  return (
    <div className="bg-blue-50 min-h-screen flex flex-col relative overflow-hidden">
      <header className="border-b bg-white shadow-sm z-10">
        <div className="container mx-auto px-[6rem] py-[0.75rem] flex items-center justify-between">
          <div className="flex items-center">
            <Link href={`/plataforma/establecimiento/${medicalCenterIdParam}`} className="flex items-center">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={40}
                className="h-[1.8rem] w-auto"
                priority
              />
            </Link>
            <span className="mx-4 h-6 w-px bg-blue-100" aria-hidden="true"></span>
            <div className="flex flex-col justify-center">
              <span className="text-xl font-semibold text-slate-700 leading-none">
                {medicalCenter?.name}
              </span>
            </div>
          </div>

          <div className="flex-1 max-w-[31.25rem] mx-[2rem] relative">
            <div className="relative group">
              <Search
                className="absolute left-[0.75rem] top-1/2 h-[1rem] w-[1rem] -translate-y-1/2 text-gray-400 transition-colors group-focus-within:text-blue-500"/>
              <Input
                className="pl-[2.5rem] border-blue-300/50 shadow-sm focus:border-blue-400 focus:ring-1 focus:ring-blue-100 rounded-lg transition-all"
                placeholder="Buscar un paciente"
                value={patientSearchTerm}
                onChange={(e) => setPatientSearchTerm(e.target.value)}
                onFocus={() => setIsPatientSearchFocused(true)}
                onBlur={() => {
                  setTimeout(() => {
                    setIsPatientSearchFocused(false)
                    setPatientSearchTerm("")
                  }, 200)
                }}
              />
            </div>

            {patientSearchTerm && isPatientSearchFocused && (
              <div
                className="absolute top-full mt-1 w-full border rounded-lg max-h-[15rem] overflow-y-auto bg-white border-black shadow-lg z-8">
                {patientsLoading ? (
                  <div className="px-4 py-2 text-sm">Buscando...</div>
                ) : filteredPatients.length > 0 ? (
                  filteredPatients.map((patient) => (
                    <Button
                      key={patient.id}
                      variant="ghost"
                      className="w-full justify-start text-left px-4 py-2 hover:bg-gray-100"
                      onClick={() => {
                        setPatientSearchTerm(patient.name)
                        setIsPatientSearchFocused(false)
                      }}
                    >
                      {patient.name} - {patient.dni}
                    </Button>
                  ))
                ) : (
                  <div className="px-4 py-2 text-sm">No se encontraron pacientes</div>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center gap-[0.5rem]">
            {/* Agendas / Estadísticas - icon segmented pill */}
            <div className="flex items-center rounded-full bg-white border border-blue-300/50 shadow-sm h-9">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={pathname === `/plataforma/establecimiento/${medicalCenterIdParam}` ? "default" : "ghost"}
                      size="icon"
                      className={`h-9 w-9 rounded-full ${
                        pathname === `/plataforma/establecimiento/${medicalCenterIdParam}`
                          ? "bg-blue-500 text-white hover:bg-blue-600"
                          : "text-blue-700 hover:bg-blue-50"
                      }`}
                      onClick={handleAgendasClick}
                    >
                      <Calendar className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">Agendas</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <LoadingButton
                      variant={pathname === `/plataforma/establecimiento/${medicalCenterIdParam}/analytics` ? "default" : "ghost"}
                      size="icon"
                      className={`h-9 w-9 rounded-full ${
                        pathname === `/plataforma/establecimiento/${medicalCenterIdParam}/analytics`
                          ? "bg-blue-500 text-white hover:bg-blue-600"
                          : "text-blue-700 hover:bg-blue-50"
                      }`}
                      onClick={handleAnalyticsClick}
                      isLoading={isAnalyticsLoading}
                    >
                      <BarChart2 className="h-4 w-4" />
                    </LoadingButton>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">Estadísticas</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Share + Settings combined pill */}
            <div className="flex items-center rounded-full bg-white border border-blue-300/50 shadow-sm h-9">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      aria-label="Compartir enlace de reserva del establecimiento"
                      className="h-9 w-9 rounded-full text-blue-700 hover:bg-blue-50"
                      onClick={copyMedicalCenterBookingUrl}
                    >
                      {isShareCopied ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Share2 className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">{isShareCopied ? '¡URL copiada!' : 'Copiar URL de reserva para pacientes'}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <span className="h-6 w-px bg-blue-100" />
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <LoadingIconButton
                      variant="ghost"
                      size="icon"
                      aria-label="Configuración del establecimiento"
                      className="h-9 w-9 rounded-full text-blue-700 hover:bg-blue-50 bg-transparent border-0 shadow-none"
                      onClick={handleConfigClick}
                      isLoading={isConfigLoading}
                    >
                      <Settings className="h-4 w-4"/>
                    </LoadingIconButton>
                  </TooltipTrigger>
                  <TooltipContent side="bottom">Configuración</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {medicalCenter && (
              <MedicalCenterUserPill
                medicalCenter={{
                  id: medicalCenterIdNum,
                  address: medicalCenter.address || "",
                  name: medicalCenter.name,
                  phoneNumber: medicalCenter.phone || "",
                  doctorsCount: medicalCenter.doctors?.length || 0,
                  role: 0 as any,
                  workingDays: []
                }}
                currentUser={currentUser}
                medicalCenterId={medicalCenterIdNum}
                logout={logout}
                showShare={false}
              />
            )}
          </div>
        </div>
      </header>

      <div className="bg-blue-50">
        <AnalyticsDashboard
          appointments={appointments}
          patients={patients}
          doctors={doctors}
          medicalCenterId={medicalCenterIdParam}
          medicalCenterName={medicalCenter.name}
          router={router}
          showHeader={false}
        />
      </div>
    </div>
  )
}