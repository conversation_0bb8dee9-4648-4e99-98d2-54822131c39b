import axios from 'axios';
import { API_CONFIG, API_ENDPOINTS } from '../config/api';
import {
  MedicalCenter,
  CreateMedicalCenterRequest,
  UpdateMedicalCenterRequest,
  Doctor,
  CreateDoctorRequest,
  UpdateDoctorRequest,
  Appointment,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  HealthInsurance,
  CreateHealthInsuranceRequest,
  UpdateHealthInsuranceRequest,
  Schedule,
  User
} from '../types/api';

// Create axios instance with default config
const axiosInstance = axios.create(API_CONFIG);

// Add request interceptor for authentication
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // Handle unauthorized
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          // Handle forbidden
          console.error('Access forbidden');
          break;
        case 404:
          // Handle not found
          console.error('Resource not found');
          break;
        default:
          console.error('API error:', error.response.data);
      }
    }
    return Promise.reject(error);
  }
);

// API endpoints
const api = {
  medicalCenters: {
    getAll: () => axiosInstance.get<MedicalCenter[]>(API_ENDPOINTS.MEDICAL_CENTERS),
    get: (id: string) => axiosInstance.get<MedicalCenter>(API_ENDPOINTS.MEDICAL_CENTER(id)),
    create: (data: CreateMedicalCenterRequest) => axiosInstance.post<MedicalCenter>(API_ENDPOINTS.MEDICAL_CENTERS, data),
    update: (id: string, data: UpdateMedicalCenterRequest) => axiosInstance.put<MedicalCenter>(API_ENDPOINTS.MEDICAL_CENTER(id), data),
    delete: (id: string) => axiosInstance.delete(API_ENDPOINTS.MEDICAL_CENTER(id)),
  },

  doctors: {
    getAll: (medicalCenterId: string) => axiosInstance.get<Doctor[]>(API_ENDPOINTS.DOCTORS(medicalCenterId)),
    get: (id: string) => axiosInstance.get<Doctor>(API_ENDPOINTS.DOCTOR(id)),
    create: (data: CreateDoctorRequest) => axiosInstance.post<Doctor>(API_ENDPOINTS.DOCTORS(data.medicalCenterId), data),
    update: (id: string, data: UpdateDoctorRequest) => axiosInstance.put<Doctor>(API_ENDPOINTS.DOCTOR(id), data),
    delete: (id: string) => axiosInstance.delete(API_ENDPOINTS.DOCTOR(id)),
  },

  appointments: {
    getAll: (medicalCenterId: string) => axiosInstance.get<Appointment[]>(API_ENDPOINTS.APPOINTMENTS(medicalCenterId)),
    get: (id: string) => axiosInstance.get<Appointment>(API_ENDPOINTS.APPOINTMENT(id)),
    create: (data: CreateAppointmentRequest) => axiosInstance.post<Appointment>(API_ENDPOINTS.APPOINTMENTS(data.medicalCenterId), data),
    update: (id: string, data: UpdateAppointmentRequest) => axiosInstance.put<Appointment>(API_ENDPOINTS.APPOINTMENT(id), data),
    delete: (id: string) => axiosInstance.delete(API_ENDPOINTS.APPOINTMENT(id)),
  },

  healthInsurance: {
    getAll: () => axiosInstance.get<HealthInsurance[]>(API_ENDPOINTS.HEALTH_INSURANCE),
    get: (id: string) => axiosInstance.get<HealthInsurance>(API_ENDPOINTS.HEALTH_INSURANCE_PLAN(id)),
    create: (data: CreateHealthInsuranceRequest) => axiosInstance.post<HealthInsurance>(API_ENDPOINTS.HEALTH_INSURANCE, data),
    update: (id: string, data: UpdateHealthInsuranceRequest) => axiosInstance.put<HealthInsurance>(API_ENDPOINTS.HEALTH_INSURANCE_PLAN(id), data),
    delete: (id: string) => axiosInstance.delete(API_ENDPOINTS.HEALTH_INSURANCE_PLAN(id)),
  },

  schedule: {
    get: (doctorId: string) => axiosInstance.get<Schedule>(API_ENDPOINTS.DOCTOR_SCHEDULE(doctorId)),
    update: (doctorId: string, data: Schedule) => axiosInstance.put<Schedule>(API_ENDPOINTS.DOCTOR_SCHEDULE(doctorId), data),
  },

  users: {
    getAll: () => axiosInstance.get<User[]>(API_ENDPOINTS.USERS),
    get: (id: string) => axiosInstance.get<User>(API_ENDPOINTS.USER(id)),
    create: (data: User) => axiosInstance.post<User>(API_ENDPOINTS.USERS, data),
    update: (id: string, data: Partial<User>) => axiosInstance.put<User>(API_ENDPOINTS.USER(id), data),
    delete: (id: string) => axiosInstance.delete(API_ENDPOINTS.USER(id)),
  },
};

export default api; 