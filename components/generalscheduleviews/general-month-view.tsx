"use client"

import { useMemo } from "react"
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isPast, subMonths, addMonths } from "date-fns"
import { es } from "date-fns/locale"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import type { MedicalCenter } from "@/types/medical-center"
import type { Doctor } from "@/types/doctor"
import type { Appointment } from "@/types/scheduler"
import { getWeeksDifference, REFERENCE_DATE, getActiveHoursForDate } from '@/utils/scheduleUtils'

interface GeneralMonthViewProps {
  selectedDate: Date
  medicalCenter: MedicalCenter
  doctors: Doctor[]
  appointments: Record<string, Appointment[]>
  doctor?: Doctor
  onDateChange?: (newDate: Date) => void
}

export function GeneralMonthView({
  selectedDate,
  medicalCenter,
  doctors,
  appointments,
  doctor,
  onDateChange,
}: GeneralMonthViewProps) {
  const monthStart = startOfMonth(selectedDate)
  const monthEnd = endOfMonth(selectedDate)
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd })

  const workingDoctors = useMemo(() => {
    return doctor ? [doctor] : doctors.filter((d) => medicalCenter.doctors.includes(d.id))
  }, [doctor, medicalCenter.doctors, doctors])

  const calendarDays = useMemo(() => {
    const firstDayOfMonth = monthStart.getDay()
    const lastDayOfMonth = monthEnd.getDay()
    const prevMonthDays = Array.from(
      { length: (firstDayOfMonth + 6) % 7 },
      (_, i) => new Date(monthStart.getFullYear(), monthStart.getMonth(), -i)
    ).reverse()
    const nextMonthDays = Array.from(
      { length: (7 - lastDayOfMonth) % 7 },
      (_, i) => new Date(monthEnd.getFullYear(), monthEnd.getMonth() + 1, i + 1)
    )
    return [...prevMonthDays, ...monthDays, ...nextMonthDays]
  }, [monthStart, monthEnd, monthDays])

  const monthlyStats = useMemo(() => {
    let totalAppointments = 0
    let pendingCount = 0
    let absentCount = 0
    let peakDay = { date: 0, count: 0 }

    monthDays.forEach((day) => {
      const dateStr = format(day, "yyyy-MM-dd")
      const dayAppointments =
        appointments[dateStr]?.filter((apt) =>
          workingDoctors.some((doctor) => doctor.id === apt.doctorId) &&
          (!apt.medicalCenterId || apt.medicalCenterId === medicalCenter.id) &&
          apt.status !== "Cancelado"
        ) || []

      totalAppointments += dayAppointments.length
      pendingCount += dayAppointments.filter((apt) => apt.status === "Agendado").length
      absentCount += dayAppointments.filter((apt) => apt.status === "Ausente").length

      const dayOfMonth = day.getDate()
      if (dayAppointments.length > peakDay.count) {
        peakDay = { date: dayOfMonth, count: dayAppointments.length }
      }
    })

    return { totalAppointments, pendingCount, absentCount, peakDay }
  }, [appointments, monthDays, workingDoctors, medicalCenter.id])

  const getDayStatus = (date: Date): "unavailable" | "free" | "partial" | "full" => {
    const dateStr = format(date, "yyyy-MM-dd")
    const dayAppointments =
      appointments[dateStr]?.filter((apt) =>
        workingDoctors.some((doctor) => doctor.id === apt.doctorId) &&
        (!apt.medicalCenterId || apt.medicalCenterId === medicalCenter.id) &&
        apt.status !== "Cancelado"
      ) || []
    const dayOfWeek = date.getDay().toString()

    // Check medical center date exceptions first
    const mcDateExceptions = medicalCenter.dateExceptions || {}
    if (dateStr in mcDateExceptions) {
      if (!mcDateExceptions[dateStr].enabled) return "unavailable"
    } else {
      const mcWorkingDay = medicalCenter.workingDays[dayOfWeek]
      if (!mcWorkingDay?.enabled) return "unavailable"

      // Add week frequency check for medical center
      const frequency = mcWorkingDay.weeksFrequency || 1
      const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE)
      if (weeksSinceReference % frequency !== 0) return "unavailable"
    }

    let totalSlots = 0
    let isAnyDoctorAvailable = false

    workingDoctors.forEach((doctor) => {
      const docDateExceptions = doctor.dateExceptions || {}

      // Check for date exceptions first
      if (dateStr in docDateExceptions) {
        if (docDateExceptions[dateStr].enabled) {
          isAnyDoctorAvailable = true
          const hours = docDateExceptions[dateStr].hours || []
          const slots = hours.reduce((sum, { start, end }) => {
            const [startH, startM] = start.split(":").map(Number)
            const [endH, endM] = end.split(":").map(Number)
            return sum + ((endH * 60 + endM) - (startH * 60 + startM)) / 15
          }, 0)
          totalSlots += slots
        }
        return; // Skip normal day processing if we have an exception
      }

      // Check normal working day
      const workingDay = doctor.workingDays[dayOfWeek];
      if (!workingDay?.enabled) return;

      // Check week frequency
      const frequency = workingDay.weeksFrequency || 1;
      const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE);
      if (weeksSinceReference % frequency !== 0) return;

      // Get active hours based on date ranges
      const activeHours = getActiveHoursForDate(workingDay.hours, date);
      if (activeHours.length > 0) {
        isAnyDoctorAvailable = true;
        const slots = activeHours.reduce((sum, { start, end }) => {
          const [startH, startM] = start.split(":").map(Number);
          const [endH, endM] = end.split(":").map(Number);
          return sum + ((endH * 60 + endM) - (startH * 60 + startM)) / 15;
        }, 0);
        totalSlots += slots;
      }
    })

    if (!isAnyDoctorAvailable) return "unavailable"
    if (dayAppointments.length === 0) return "free"
    if (dayAppointments.length >= totalSlots) return "full"
    return "partial"
  }

  const getWorkingDoctorsForDate = (date: Date) => {
    const dateStr = format(date, "yyyy-MM-dd")
    const dayOfWeek = date.getDay().toString()

    return workingDoctors.filter((doctor) => {
      const docDateExceptions = doctor.dateExceptions || {}

      // Check date exceptions first
      if (dateStr in docDateExceptions) {
        return docDateExceptions[dateStr].enabled
      }

      // Check working days with week frequency
      const workingDay = doctor.workingDays[dayOfWeek]
      if (!workingDay?.enabled) return false

      // Check week frequency
      const frequency = workingDay.weeksFrequency || 1
      const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE)
      if (weeksSinceReference % frequency !== 0) return false

      // Check if doctor has active hours for this date based on date ranges
      const activeHours = getActiveHoursForDate(workingDay.hours, date)
      return activeHours.length > 0
    })
  }

  const handlePrevMonth = () => {
    const newDate = subMonths(selectedDate, 1)
    onDateChange?.(newDate)
  }

  const handleNextMonth = () => {
    const newDate = addMonths(selectedDate, 1)
    onDateChange?.(newDate)
  }

  return (
    <div className="bg-white rounded-lg border border-black">
      <div className="flex items-center justify-between p-4 border-b">
        <Button onClick={handlePrevMonth} variant="outline" size="sm">
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <div className="flex flex-col items-center">
          <h2 className="text-lg font-semibold text-center">
            {format(selectedDate, "MMMM yyyy", { locale: es })}
          </h2>
          <div className="text-xs text-gray-500 mt-1">
            {monthlyStats.totalAppointments} turnos | {monthlyStats.pendingCount} pendientes |{" "}
            {monthlyStats.absentCount} ausentes
            {monthlyStats.peakDay.count > 0 && (
              <span>
                {" "}
                | Pico: {monthlyStats.peakDay.count} el {monthlyStats.peakDay.date}
              </span>
            )}
          </div>
        </div>
        <Button onClick={handleNextMonth} variant="outline" size="sm">
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-7 text-sm font-medium">
        {["Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado", "Domingo"].map((day) => (
          <div key={day} className="p-4 text-center border-b">
            {day}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 text-sm">
        {calendarDays.map((date) => {
          const dateStr = format(date, "yyyy-MM-dd")
          const isCurrentMonth = isSameMonth(date, selectedDate)
          const dayAppointments =
            appointments[dateStr]?.filter((apt) =>
              workingDoctors.some((doctor) => doctor.id === apt.doctorId) &&
              (!apt.medicalCenterId || apt.medicalCenterId === medicalCenter.id) &&
              apt.status !== "Cancelado"
            ) || []

          const isPastDay = isPast(date) && !isSameMonth(date, new Date())
          const status = !isPastDay ? getDayStatus(date) : null
          const workingDoctorsCount = getWorkingDoctorsForDate(date).length

          const statusBadges = {
            free: { text: "Disponible", className: "bg-green-100 text-green-700" },
            partial: { text: "Hay turnos", className: "bg-blue-100 text-blue-700" },
            full: { text: "Completo", className: "bg-red-100 text-red-700" },
          }
          const statusBadge = status && status !== "unavailable" ? statusBadges[status] : null

          return (
            <div
              key={date.toISOString()}
              className={`pt-7 px-4 pb-4 border-b border-r min-h-[120px] text-left ${
                !isCurrentMonth ? "text-gray-400 bg-gray-100 cursor-not-allowed" : ""
              }`}
            >
              <div className="flex items-center gap-2">
                <div className={`font-medium ${isCurrentMonth ? "" : "text-gray-400"}`}>{format(date, "d")}</div>
                {statusBadge && (
                  <div className={`inline-block px-2 py-0.5 rounded-full text-xs ${statusBadge.className}`}>
                    {statusBadge.text}
                  </div>
                )}
              </div>
              <div className="space-y-1 mt-2">
                {dayAppointments.length > 0 && (
                  <div className="text-xs text-gray-500">
                    {dayAppointments.length} {dayAppointments.length === 1 ? "turno" : "turnos"}
                  </div>
                )}
                {isCurrentMonth && workingDoctorsCount > 0 && (
                  <div className="text-xs text-gray-500">
                    {workingDoctorsCount} {workingDoctorsCount === 1 ? "profesional" : "profesionales"}
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}