import { MedicalCenter, OpeningHours } from "@/types/medical-center";

// Map between numeric day keys and named days
const dayMapping: Record<string, keyof OpeningHours> = {
  "0": "sunday",
  "1": "monday",
  "2": "tuesday",
  "3": "wednesday",
  "4": "thursday",
  "5": "friday",
  "6": "saturday"
};

// Convert numeric day keys to named days
export function workingDaysToOpeningHours(workingDays: MedicalCenter['workingDays']): OpeningHours {
  const openingHours: OpeningHours = {
    monday: { start: "", end: "", enabled: false },
    tuesday: { start: "", end: "", enabled: false },
    wednesday: { start: "", end: "", enabled: false },
    thursday: { start: "", end: "", enabled: false },
    friday: { start: "", end: "", enabled: false },
    saturday: { start: "", end: "", enabled: false },
    sunday: { start: "", end: "", enabled: false }
  };

  Object.entries(workingDays).forEach(([dayNum, config]) => {
    const namedDay = dayMapping[dayNum];
    if (namedDay) {
      const firstHours = config.hours[0] || { start: "09:00", end: "17:00" };
      openingHours[namedDay] = {
        start: firstHours.start,
        end: firstHours.end,
        enabled: config.enabled
      };
    }
  });

  return openingHours;
}

// Convert named days to numeric day keys
export function openingHoursToWorkingDays(openingHours: OpeningHours): MedicalCenter['workingDays'] {
  const workingDays: MedicalCenter['workingDays'] = {};
  
  Object.entries(dayMapping).forEach(([dayNum, namedDay]) => {
    const dayConfig = openingHours[namedDay];
    workingDays[dayNum] = {
      enabled: dayConfig.enabled,
      hours: dayConfig.enabled ? [{ start: dayConfig.start, end: dayConfig.end }] : []
    };
  });
  
  return workingDays;
}

// Update both formats at once when saving a medical center
export function syncMedicalCenterSchedules(medicalCenter: MedicalCenter): MedicalCenter {
  const updatedCenter = { ...medicalCenter };
  
  // If openingHours exists, sync from openingHours to workingDays
  if (updatedCenter.openingHours) {
    updatedCenter.workingDays = openingHoursToWorkingDays(updatedCenter.openingHours);
  } 
  // If only workingDays exists, sync from workingDays to openingHours
  else if (updatedCenter.workingDays) {
    updatedCenter.openingHours = workingDaysToOpeningHours(updatedCenter.workingDays);
  }
  
  return updatedCenter;
} 