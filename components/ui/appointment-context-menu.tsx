"use client"

import { useEffect, useRef } from "react"

interface AppointmentContextMenuProps {
  x: number
  y: number
  onAction: (action: string) => void
  onClose: () => void
}

export const AppointmentContextMenu = ({ x, y, onAction, onClose }: AppointmentContextMenuProps) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    console.log("AppointmentContextMenu mounted at:", { x, y });
    if (menuRef.current) {
      console.log("DOM element present:", menuRef.current.getBoundingClientRect());
    }
    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        console.log("Clicked outside, closing context menu");
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose, x, y]);

  const adjustedX = Math.min(x, window.innerWidth - 150);
  const adjustedY = Math.min(y, window.innerHeight - 100);

  return (
    <div
      ref={menuRef}
      className="fixed bg-white border rounded-md shadow-lg z-[10000] py-1 text-[0.75rem]"
      style={{ left: `${adjustedX}px`, top: `${adjustedY}px`, display: "block" }}
    >
      <div
        className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
        onClick={(e) => {
          e.stopPropagation();
          onAction("status");
        }}
      >
        Estado de turno
      </div>
      <div
        className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
        onClick={(e) => {
          e.stopPropagation();
          onAction("observations");
        }}
      >
        Detalles de turno
      </div>
      <div
        className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
        onClick={(e) => {
          e.stopPropagation();
          onAction("modify");
        }}
      >
        Modificar turno
      </div>
      <div
        className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer text-red-600"
        onClick={(e) => {
          e.stopPropagation();
          onAction("cancel");
        }}
      >
        Cancelar turno
      </div>
    </div>
  );
};