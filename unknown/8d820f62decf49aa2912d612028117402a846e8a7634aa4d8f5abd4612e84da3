"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { PatientAuthForm } from "@/components/auth/patient-auth-form"
import { useAuth } from "@/contexts/AuthContext"
import Image from "next/image"
import { ArrowLeft, ShieldCheck } from "lucide-react"
import { Button } from "@/components/ui/button"
import Link from 'next/link'

export default function RegistroPage() {
  const router = useRouter()
  const { isAuthenticated, currentUser } = useAuth()
  
  // If already authenticated, redirect to patient dashboard
  useEffect(() => {
    if (isAuthenticated && currentUser?.role === "patient") {
      router.push("/plataforma/paciente")
    }
  }, [isAuthenticated, currentUser, router])
  
  // Handler for successful registration
  const handleSuccess = () => {
    router.push("/plataforma/buscar")
  }

  return (
    <div className="min-h-screen bg-blue-50 flex flex-col">
      <header className="py-3">
          <div className="container mx-auto flex justify-center items-center">
            <Link href="/">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-8 w-auto"
                priority
              />
            </Link>
          </div>
        </header>
      
      <main className="flex-1 flex flex-col items-start pt-4 px-4">
        <div className="w-full max-w-xl mx-auto">
          <Button
            variant="outline"
            className="mb-3 flex items-center gap-[0.375rem] transition-all duration-300 bg-white border-blue-300/50 hover:bg-blue-100/70 hover:border-blue-400 px-[0.75rem] py-[0.375rem] rounded-lg shadow-sm hover:shadow-md"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-[1rem] h-[1rem] text-blue-600 group-hover:text-blue-800 transition-colors" />
            <span className="text-blue-700 group-hover:text-blue-900 font-medium text-[0.875rem]">Volver</span>
          </Button>

          <PatientAuthForm 
            onSuccess={handleSuccess} 
            mode="registerOnly" 
            showPatientBadge={true}
          />
        </div>
      </main>

      <footer className="bg-white border-t border-gray-200 py-8 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0 flex flex-col items-center md:items-start">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-7 w-auto mb-3"
              />
              <p className="text-gray-500 text-sm text-center md:text-left">
                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
              </p>
            </div>

            <div className="flex flex-col items-center md:items-end">
              <div className="flex items-center mb-3">
                <ShieldCheck className="h-4 w-4 text-[#1cd8e1] mr-1.5" />
                <span className="text-sm text-[#1c2533] font-medium">Reservá turnos médicos 24/7</span>
              </div>
              <div className="flex space-x-4">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                  Términos y Condiciones
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                  Privacidad
                </Button>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
} 