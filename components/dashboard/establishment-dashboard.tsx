"use client"

import React, { useState, use<PERSON><PERSON>back, useMem<PERSON> } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { MedicalCenterUserPill } from "@/components/ui/MedicalCenterUserPill"
import { useAuth } from "@/hooks/useAuth"
import { Calendar, ChevronDown, Search, Settings } from "lucide-react"
import { LoadingButton } from "@/components/ui/loading-button"
import { LoadingIconButton } from "@/components/ui/loading-icon-button"
import Link from "next/link"
import Image from "next/image"
import { format, subDays, startOfDay, startOfWeek, startOfMonth, startOfYear, parseISO, addDays, endOfMonth, endOfYear, eachDayOfInterval } from "date-fns"
import { es } from "date-fns/locale"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend,
} from "recharts"
// Removed PatientDetailsDialog to avoid type mismatch with analytics data shapes
import { usePathname } from "next/navigation"
import type { Appointment } from "@/types/scheduler"
import type { Patient } from "@/types/patient"
import type { Doctor } from "@/types/doctor"
import type { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime"
import { Checkbox } from "@/components/ui/checkbox"
import { MedicalCenterPermission } from "@/types/MedicalCenter/medicalCenterPermission"

type Period = "day" | "week" | "month" | "year" | "custom"

interface AnalyticsDashboardProps {
  appointments: Record<string, Appointment[]>
  patients: Patient[]
  doctors: Doctor[]
  medicalCenterId: string
  medicalCenterName: string
  router: AppRouterInstance
  showHeader?: boolean
}

const COLORS = ['#3b82f6', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#10b981', '#facc15', '#06b6d4']

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  appointments,
  patients,
  doctors,
  medicalCenterId,
  medicalCenterName,
  router,
  showHeader = true,
}) => {
  const pathname = usePathname()
  const { currentUser, logout } = useAuth()
  const [currentDate] = useState(() => startOfDay(new Date()))
  const [period, setPeriod] = useState<Period>("month")
  const [dateRange, setDateRange] = useState<{
    from: Date
    to: Date
  }>({
    from: subDays(new Date(), 30),
    to: new Date(),
  })
  const [patientSearchTerm, setPatientSearchTerm] = useState("")
  const [isPatientSearchFocused, setIsPatientSearchFocused] = useState(false)
  const [selectedDoctorIds, setSelectedDoctorIds] = useState<string[]>([]) // Default to empty (all doctors)
  const [isAgendasLoading, setIsAgendasLoading] = useState(false)
  const [isConfigLoading, setIsConfigLoading] = useState(false)

  // Filter patients who have at least one appointment at this medical center
  const patientsWithAppointmentsAtCenter = useMemo(() => {
    const patientNames = new Set()
    Object.values(appointments).flat().forEach(apt => {
      if (apt.medicalCenterId === medicalCenterId) {
        patientNames.add(apt.patient)
      }
    })

    return patients.filter(patient => patientNames.has(patient.name))
  }, [patients, appointments, medicalCenterId])

  const filteredPatients = patientsWithAppointmentsAtCenter.filter((patient) =>
    patient.name.toLowerCase().includes(patientSearchTerm.toLowerCase()) ||
    patient.dni.toLowerCase().includes(patientSearchTerm.toLowerCase())
  )

  const getStartDate = useCallback(() => {
    switch (period) {
      case "day": return startOfDay(currentDate)
      case "week": return startOfWeek(currentDate, { weekStartsOn: 1 })
      case "month": return startOfMonth(currentDate)
      case "year": return startOfYear(currentDate)
      case "custom": return startOfDay(dateRange.from)
      default: return startOfMonth(currentDate)
    }
  }, [period, currentDate, dateRange])

  const getEndDate = useCallback(() => {
    switch (period) {
      case "day": return startOfDay(currentDate)
      case "week": return addDays(startOfWeek(currentDate, { weekStartsOn: 1 }), 6)
      case "month": return endOfMonth(currentDate)
      case "year": return endOfYear(currentDate)
      case "custom": return dateRange.to
      default: return endOfMonth(currentDate)
    }
  }, [period, currentDate, dateRange])

  const filteredAppointments = useMemo(() => {
    const startDate = getStartDate()
    const endDate = getEndDate()

    return Object.entries(appointments)
      .filter(([date]) => {
        const appointmentDate = parseISO(date)
        return appointmentDate >= startDate && appointmentDate <= endDate
      })
      .flatMap(([, appts]) => appts)
      .filter(apt => selectedDoctorIds.length === 0 || selectedDoctorIds.includes(apt.doctorId))
  }, [appointments, getStartDate, getEndDate, selectedDoctorIds])

  const totalAppointments = filteredAppointments.length

  const attendedAppointments = filteredAppointments.filter(
    (apt) => apt.status === "Atendido" || apt.status === "En Atención"
  ).length

  const attendanceRate = totalAppointments > 0
    ? ((attendedAppointments / totalAppointments) * 100).toFixed(1)
    : "0"

  const uniquePatientIds = new Set(filteredAppointments.map(apt => apt.patient))
  const activePatientsCount = uniquePatientIds.size

  const calculateFillRate = () => {
    if (period === "year") return "N/A"

    const doctorCount = selectedDoctorIds.length === 0 ? doctors.length : selectedDoctorIds.length
    const slotsPerDoctorPerDay = 12
    const daysInPeriod = period === "day" ? 1 : period === "week" ? 7 : period === "month" ? format(endOfMonth(currentDate), "d", { locale: es }) : 30

    const totalPossibleSlots = doctorCount * slotsPerDoctorPerDay * Number(daysInPeriod)
    return totalPossibleSlots > 0
      ? ((totalAppointments / totalPossibleSlots) * 100).toFixed(1)
      : "0"
  }

  const fillRate = calculateFillRate()

  const calculatePatientTypes = () => {
    const startDate = getStartDate()
    const previousAppointments = Object.entries(appointments)
      .filter(([date]) => parseISO(date) < startDate)
      .flatMap(([, appts]) => appts)
      .filter(apt => selectedDoctorIds.length === 0 || selectedDoctorIds.includes(apt.doctorId))

    const previousPatientIds = new Set(previousAppointments.map(apt => apt.patient))

    let newPatients = 0
    let recurringPatients = 0

    uniquePatientIds.forEach(patientId => {
      if (previousPatientIds.has(patientId)) {
        recurringPatients++
      } else {
        newPatients++
      }
    })

    return { newPatients, recurringPatients }
  }

  const { newPatients, recurringPatients } = calculatePatientTypes()

  const doctorStats = useMemo(() => {
    const doctorAppointments = new Map<string, number>()

    filteredAppointments.forEach(apt => {
      const count = doctorAppointments.get(apt.doctorId) || 0
      doctorAppointments.set(apt.doctorId, count + 1)
    })

    const stats = Array.from(doctorAppointments.entries())
      .map(([doctorId, count]) => {
        const doctor = doctors.find(d => d.id === doctorId)
        if (!doctor) {
          console.warn(`Unknown doctor ID: ${doctorId} with ${count} appointment(s)`);
          return null;
        }
        return {
          name: doctor.name,
          appointments: count,
          id: doctorId
        }
      })
      .filter((stat): stat is { name: string; appointments: number; id: string } => stat !== null)
      .sort((a, b) => b.appointments - a.appointments)

    return stats
  }, [filteredAppointments, doctors])

  const specialtyStats = useMemo(() => {
    const specialtyCounts = new Map<string, number>()

    doctorStats.forEach(doctor => {
      const doctorObj = doctors.find(d => d.id === doctor.id)
      if (doctorObj) {
        doctorObj.specialties.forEach((specialty: string) => {
          const count = specialtyCounts.get(specialty) || 0
          specialtyCounts.set(specialty, count + doctor.appointments)
        })
      }
    })

    return Array.from(specialtyCounts.entries())
      .map(([specialty, count]) => ({
        name: specialty,
        value: count
      }))
      .sort((a, b) => b.value - a.value)
  }, [doctorStats, doctors])

  const appointmentTrend = useMemo(() => {
    const trend: { name: string; appointments: number }[] = []

    if (period === "day") {
      const hours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
      hours.forEach(hour => {
        const count = filteredAppointments.filter(apt => {
          const [h] = apt.time.split(":").map(Number)
          return h === hour
        }).length

        trend.push({
          name: `${hour}:00`,
          appointments: count
        })
      })
    } else if (period === "week") {
      const days = ["Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado", "Domingo"]
      const startOfWeekDate = startOfWeek(currentDate, { weekStartsOn: 1 })

      days.forEach((day, index) => {
        const currentDateInWeek = new Date(startOfWeekDate)
        currentDateInWeek.setDate(startOfWeekDate.getDate() + index)
        const formattedDate = format(currentDateInWeek, "yyyy-MM-dd")

        const count = (appointments[formattedDate] || []).filter(apt =>
          selectedDoctorIds.length === 0 || selectedDoctorIds.includes(apt.doctorId)
        ).length
        trend.push({
          name: day,
          appointments: count
        })
      })
    } else if (period === "month") {
      const start = startOfMonth(currentDate)
      const end = endOfMonth(currentDate)
      const daysInMonth = eachDayOfInterval({ start, end })
      daysInMonth.forEach(day => {
        const formattedDate = format(day, "yyyy-MM-dd")
        const count = (appointments[formattedDate] || []).filter(apt =>
          selectedDoctorIds.length === 0 || selectedDoctorIds.includes(apt.doctorId)
        ).length
        trend.push({
          name: format(day, "d"),
          appointments: count
        })
      })
    } else if (period === "year") {
      const start = startOfYear(currentDate)
      const end = endOfYear(currentDate)
      const monthlyData = new Map<string, number>()

      eachDayOfInterval({ start, end }).forEach(day => {
        const monthYear = format(day, "MMM yyyy", { locale: es })
        const formattedDate = format(day, "yyyy-MM-dd")
        const count = monthlyData.get(monthYear) || 0
        monthlyData.set(monthYear, count + (
          (appointments[formattedDate] || []).filter(apt =>
            selectedDoctorIds.length === 0 || selectedDoctorIds.includes(apt.doctorId)
          ).length
        ))
      })

      Array.from(monthlyData.entries()).forEach(([month, count]) => {
        trend.push({
          name: month,
          appointments: count
        })
      })
    } else { // Custom
      const monthlyData = new Map<string, number>()

      Object.entries(appointments)
        .filter(([date]) => {
          const appointmentDate = parseISO(date)
          return appointmentDate >= getStartDate() && appointmentDate <= getEndDate()
        })
        .forEach(([date, appts]) => {
          const filteredAppts = appts.filter(apt =>
            selectedDoctorIds.length === 0 || selectedDoctorIds.includes(apt.doctorId)
          )
          const monthYear = format(parseISO(date), "MMM yyyy", { locale: es })
          const count = monthlyData.get(monthYear) || 0
          monthlyData.set(monthYear, count + filteredAppts.length)
        })

      Array.from(monthlyData.entries())
        .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())
        .forEach(([month, count]) => {
          trend.push({
            name: month,
            appointments: count
          })
        })
    }

    return trend
  }, [filteredAppointments, appointments, period, getStartDate, getEndDate, currentDate, selectedDoctorIds])

  const periodLabels: Record<Period, string> = {
    day: "Hoy",
    week: "Esta semana",
    month: "Este mes",
    year: "Este año",
    custom: "Personalizado"
  }

  // On analytics we only show a quick list; no dialog
  const handlePatientSelect = (patient: Patient) => {
    setPatientSearchTerm(patient.name)
    setIsPatientSearchFocused(false)
  }

  const handleAppointmentSelect = (
    appointmentOrDate: Appointment | Date | null,
    appointment?: Appointment
  ) => {
    let selectedAppointment: Appointment | undefined

    if (appointmentOrDate instanceof Date && appointment) {
      selectedAppointment = appointment
    } else if (appointmentOrDate && "id" in appointmentOrDate) {
      selectedAppointment = appointmentOrDate as Appointment
    }

    if (selectedAppointment) {
      const { doctorId, date } = selectedAppointment
      const formattedDate = date.includes("T") ? date.split("T")[0] : date
      router.push(`/plataforma/profesional/${doctorId}?date=${formattedDate}`)
    }
  }

  const handleDoctorToggle = (doctorId: string) => {
    setSelectedDoctorIds(prev =>
      prev.includes(doctorId)
        ? prev.filter(id => id !== doctorId)
        : [...prev, doctorId]
    )
  }

  const handleSelectAllDoctors = () => {
    setSelectedDoctorIds(doctors.map(d => d.id))
  }

  const handleClearDoctors = () => {
    setSelectedDoctorIds([])
  }

  const handleAgendasClick = () => {
    setIsAgendasLoading(true)
    router.push(`/plataforma/establecimiento/${medicalCenterId}`)
  }

  const handleConfigClick = () => {
    setIsConfigLoading(true)
    router.push(`/plataforma/establecimiento/${medicalCenterId}/configuration`)
  }

  return (
    <>
      <style jsx global>{`
        html {
          font-size: 13px;
        }
        @media (min-width: 1500px) {
          html {
            font-size: 15px;
          }
        }
        @media (min-width: 2560px) {
          html {
            font-size: 17px;
          }
        }
        .container {
          max-width: 1400px;
        }
      `}</style>

      {showHeader && (
      <header className="bg-white text-gray-800 shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-[1.5rem] py-[0.75rem] flex items-center justify-between">
          <Link href={`/plataforma/establecimiento/${medicalCenterId}`} className="flex items-center">
            <Image
              src="/images/turnera-logo.svg"
              alt="Turnera Logo"
              width={120}
              height={40}
              className="h-[1.8rem] w-auto"
              priority
            />
          </Link>

          <div className="flex-1 max-w-[31.25rem] mx-[2rem] relative">
            <div className="relative group">
              <Search className="absolute left-[0.75rem] top-1/2 h-[1rem] w-[1rem] -translate-y-1/2 text-gray-400 transition-colors group-focus-within:text-blue-500" />
              <Input
                className="pl-[2.5rem] border-gray-300 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 rounded-lg transition-all"
                placeholder="Buscar un paciente"
                value={patientSearchTerm}
                onChange={(e) => setPatientSearchTerm(e.target.value)}
                onFocus={() => setIsPatientSearchFocused(true)}
                onBlur={() => {
                  setTimeout(() => setIsPatientSearchFocused(false), 100)
                  setPatientSearchTerm("")
                }}
              />
            </div>
            {patientSearchTerm && isPatientSearchFocused && (
              <div className="absolute top-full mt-1 w-full bg-white border border-gray-200 rounded-lg max-h-60 overflow-y-auto shadow-xl z-10">
                {filteredPatients.length > 0 ? (
                  filteredPatients.map((patient) => (
                    <Button
                      key={patient.id}
                      variant="ghost"
                      className="w-full text-left px-4 py-2 text-gray-800 hover:bg-blue-50 hover:text-blue-600 transition-colors duration-150"
                      onClick={() => handlePatientSelect(patient)}
                    >
                      {patient.name} - {patient.dni}
                    </Button>
                  ))
                ) : (
                  <div className="px-4 py-2 text-sm text-gray-500">No se encontraron pacientes</div>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center gap-[0.5rem]">
            <div className="flex rounded-lg bg-white border border-blue-200 shadow-sm p-1 gap-1">
            <LoadingButton
              variant={pathname === `/plataforma/establecimiento/${medicalCenterId}` ? "default" : "ghost"}
              className={`px-3 py-1 h-8 text-sm font-medium rounded-md transition-all duration-200 ${
                pathname === `/plataforma/establecimiento/${medicalCenterId}`
                  ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                  : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
              }`}
              onClick={handleAgendasClick}
              isLoading={isAgendasLoading}
            >
              Agendas
            </LoadingButton>
            <Button
              variant={pathname === `/plataforma/establecimiento/${medicalCenterId}/analytics` ? "default" : "ghost"}
              className={`px-3 py-1 h-8 text-sm font-medium rounded-md transition-all duration-200 ${
                pathname === `/plataforma/establecimiento/${medicalCenterId}/analytics`
                  ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                  : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
              }`}
              onClick={() => router.push(`/plataforma/establecimiento/${medicalCenterId}/analytics`)}
            >
              Estadísticas
            </Button>
            </div>
            <LoadingIconButton
              variant="ghost"
              size="icon"
              className="text-gray-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
              onClick={handleConfigClick}
              isLoading={isConfigLoading}
            >
              <Settings className="h-[1.25rem] w-[1.25rem]" />
            </LoadingIconButton>
            <MedicalCenterUserPill
              medicalCenter={{
                id: Number(medicalCenterId),
                address: "",
                name: medicalCenterName,
                phoneNumber: "",
                doctorsCount: 0,
                role: MedicalCenterPermission.ADMIN,
                workingDays: []
              }}
              currentUser={currentUser}
              medicalCenterId={Number(medicalCenterId)}
              logout={logout}
            />
          </div>
        </div>
      </header>
      )}

      <main className="container mx-auto px-6 py-8">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-bold text-gray-800">
            Análisis y Estadísticas
          </h2>

          <div className="flex items-center space-x-4">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2 bg-white border-gray-200 rounded-full shadow-sm hover:bg-gray-50 transition-colors duration-200">
                  <span className="text-sm font-medium text-gray-700">
                    {selectedDoctorIds.length === 0 ? "Todos los profesionales" :
                     selectedDoctorIds.length === 1 ? doctors.find(d => d.id === selectedDoctorIds[0])?.name :
                     `${selectedDoctorIds.length} seleccionados`}
                  </span>
                  <ChevronDown className="h-5 w-5 text-gray-400" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-64 p-4 bg-white border border-gray-200 rounded-lg shadow-xl">
                <div className="space-y-2">
                  <div className="flex justify-between mb-2">
                    <Button variant="ghost" size="sm" onClick={handleSelectAllDoctors}>
                      Seleccionar todos
                    </Button>
                    <Button variant="ghost" size="sm" onClick={handleClearDoctors}>
                      Limpiar
                    </Button>
                  </div>
                  {doctors.map(doctor => (
                    <div key={doctor.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={doctor.id}
                        checked={selectedDoctorIds.includes(doctor.id)}
                        onCheckedChange={() => handleDoctorToggle(doctor.id)}
                      />
                      <label htmlFor={doctor.id} className="text-sm text-gray-700 cursor-pointer">
                        {doctor.name}
                      </label>
                    </div>
                  ))}
                </div>
              </PopoverContent>
            </Popover>

            <div className="flex rounded-lg bg-white border border-blue-200 shadow-sm p-1 gap-1">
              <Button
                variant={period === "day" ? "default" : "ghost"}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  period === "day"
                    ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                    : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                }`}
                onClick={() => setPeriod("day")}
              >
                Hoy
              </Button>
              <Button
                variant={period === "week" ? "default" : "ghost"}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  period === "week"
                    ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                    : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                }`}
                onClick={() => setPeriod("week")}
              >
                Esta semana
              </Button>
              <Button
                variant={period === "month" ? "default" : "ghost"}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  period === "month"
                    ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                    : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                }`}
                onClick={() => setPeriod("month")}
              >
                Este mes
              </Button>
              <Button
                variant={period === "year" ? "default" : "ghost"}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  period === "year"
                    ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                    : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                }`}
                onClick={() => setPeriod("year")}
              >
                Este año
              </Button>
              <Button
                variant={period === "custom" ? "default" : "ghost"}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                  period === "custom"
                    ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                    : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                }`}
                onClick={() => setPeriod("custom")}
              >
                Personalizado
              </Button>
            </div>

            {period === "custom" && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2 bg-white border-gray-200 rounded-full shadow-sm hover:bg-gray-50 transition-colors duration-200">
                    <Calendar className="h-5 w-5 text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">
                      {format(dateRange.from, "dd/MM/yyyy")} -{" "}
                      {format(dateRange.to, "dd/MM/yyyy")}
                    </span>
                    <ChevronDown className="h-5 w-5 text-gray-400" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-4 bg-white border border-gray-200 rounded-lg shadow-xl">
                  <div className="space-y-3">
                    <Input
                      type="date"
                      value={format(dateRange.from, "yyyy-MM-dd")}
                      onChange={(e) => setDateRange({ ...dateRange, from: parseISO(e.target.value) })}
                      className="border-gray-200 rounded-md focus:ring-blue-300"
                    />
                    <Input
                      type="date"
                      value={format(dateRange.to, "yyyy-MM-dd")}
                      onChange={(e) => setDateRange({ ...dateRange, to: parseISO(e.target.value) })}
                      className="border-gray-200 rounded-md focus:ring-blue-300"
                    />
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total de Turnos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600">{totalAppointments}</div>
              <p className="text-sm text-gray-500 mt-1">{periodLabels[period]}</p>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Porcentaje de Ocupación</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-600">
                {fillRate === "N/A" ? "N/A" : `${fillRate}%`}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                {period === "year" ? "No aplicable en este periodo" : periodLabels[period]}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Tasa de Presencialidad</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-600">{attendanceRate}%</div>
              <p className="text-sm text-gray-500 mt-1">
                {attendedAppointments} de {totalAppointments} {totalAppointments === 1 ? "turno" : "turnos"}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total de Pacientes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-orange-600">{patients.length}</div>
              <p className="text-sm text-gray-500 mt-1">
                {activePatientsCount} activos en este periodo
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          <Card className="bg-white border border-gray-100 shadow-lg rounded-xl overflow-hidden">
            <CardHeader className="bg-gray-50 border-b border-gray-200">
              <CardTitle className="text-xl font-bold text-gray-800">Tendencia de Turnos</CardTitle>
            </CardHeader>
            <CardContent className="p-6 h-[24rem]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={appointmentTrend}
                  margin={{ top: 10, right: 30, left: 0, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                  <XAxis dataKey="name" stroke="#6b7280" />
                  <YAxis stroke="#6b7280" />
                  <Tooltip
                    formatter={(value: number) => [`${value} ${value === 1 ? "turno" : "turnos"}`, ""]}
                    contentStyle={{ backgroundColor: "#fff", border: "1px solid #e5e7eb", borderRadius: "8px" }}
                  />
                  <Legend formatter={() => totalAppointments === 1 ? "Turno" : "Turnos"} />
                  <Line
                    type="monotone"
                    dataKey="appointments"
                    name={totalAppointments === 1 ? "Turno" : "Turnos"}
                    stroke="#3b82f6"
                    strokeWidth={2}
                    activeDot={{ r: 8 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-100 shadow-lg rounded-xl overflow-hidden">
            <CardHeader className="bg-gray-50 border-b border-gray-200">
              <CardTitle className="text-xl font-bold text-gray-800">Nuevos vs. Recurrentes</CardTitle>
            </CardHeader>
            <CardContent className="p-6 h-[24rem] flex flex-col">
              <div className="flex-1 flex items-center justify-center">
                <ResponsiveContainer width="100%" height="80%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: "Pacientes nuevos", value: newPatients },
                        { name: "Pacientes recurrentes", value: recurringPatients },
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={90}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      <Cell fill="#3b82f6" />
                      <Cell fill="#22c55e" />
                    </Pie>
                    <Tooltip formatter={(value: number) => [`${value} ${value === 1 ? "paciente" : "pacientes"}`, ""]} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="flex justify-center space-x-8 mt-4">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-blue-600 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-gray-700">Nuevos: {newPatients}</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-gray-700">Recurrentes: {recurringPatients}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          <Card className="bg-white border border-gray-100 shadow-lg rounded-xl overflow-hidden">
            <CardHeader className="bg-gray-50 border-b border-gray-200">
              <CardTitle className="text-xl font-bold text-gray-800">Profesionales Más Solicitados</CardTitle>
            </CardHeader>
            <CardContent className="p-6 h-[24rem]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={doctorStats.slice(0, 10)}
                  layout="vertical"
                  margin={{ top: 10, right: 30, left: 120, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                  <XAxis type="number" stroke="#6b7280" />
                  <YAxis type="category" dataKey="name" width={110} stroke="#6b7280" />
                  <Tooltip
                    formatter={(value: number) => [`${value} ${value === 1 ? "turno" : "turnos"}`, ""]}
                    contentStyle={{ backgroundColor: "#fff", border: "1px solid #e5e7eb", borderRadius: "8px" }}
                  />
                  <Bar dataKey="appointments" name={totalAppointments === 1 ? "Turno" : "Turnos"} fill="#3b82f6" barSize={20} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card className="bg-white border border-gray-100 shadow-lg rounded-xl overflow-hidden">
            <CardHeader className="bg-gray-50 border-b border-gray-200">
              <CardTitle className="text-xl font-bold text-gray-800">Especialidades Más Solicitadas</CardTitle>
            </CardHeader>
            <CardContent className="p-6 h-[24rem]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={specialtyStats.slice(0, 8)}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={90}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {specialtyStats.slice(0, 8).map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value: number) => [`${value} ${value === 1 ? "turno" : "turnos"}`, ""]} />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Patient details dialog removed */}
    </>
  )
}

export default AnalyticsDashboard