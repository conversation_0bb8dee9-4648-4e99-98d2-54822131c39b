"use client"

import {use<PERSON>ontex<PERSON>, useEffect, useState} from "react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {<PERSON>, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {Plus, Save, Trash2, X} from "lucide-react"
import {Badge} from "@/components/ui/badge"
import {DoctorContext} from "@/contexts/DoctorContext"
import {PatientContext} from "@/contexts/PatientContext"
import {Doctor} from "@/types/doctor"
import {sendDoctorInvitationEmail} from "@/services/email"
import {toast} from "react-toastify"
import {SPECIALTIES, Specialty} from "@/data/specialties"
import {defaultDoctorPermissions, User, UserRole} from "@/types/users"
import {generateCustomId, generateUserId} from "@/utils/idGenerator"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"

interface DoctorFormData {
    id: string
    name: string
    mn: string
    email: string
    phone: string
    dni: string
    specialties: string[]
    currentSpecialty: string
    isValid: boolean
}

// Simple table components
const Table = ({className, children}: { className?: string, children: React.ReactNode }) => (
    <table className={`w-full ${className || ''}`}>{children}</table>
)

const TableHeader = ({children}: { children: React.ReactNode }) => (
    <thead className="bg-gray-50">{children}</thead>
)

const TableBody = ({children}: { children: React.ReactNode }) => (
    <tbody>{children}</tbody>
)

const TableRow = ({className, children}: { className?: string, children: React.ReactNode }) => (
    <tr className={`border-b hover:bg-gray-50 ${className || ''}`}>{children}</tr>
)

const TableHead = ({className, children}: { className?: string, children: React.ReactNode }) => (
    <th className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${className || ''}`}>{children}</th>
)

const TableCell = ({className, children}: { className?: string, children: React.ReactNode }) => (
    <td className={`px-4 py-3 ${className || ''}`}>{children}</td>
)

export function MultiDoctorForm() {
    const {addDoctor} = useContext(DoctorContext) || {addDoctor: () => ({id: ""} as Doctor)}
    const patientContext = useContext(PatientContext)

    // Log the availability of the context at component mount
    useEffect(() => {
        console.log("MultiDoctorForm: DoctorContext available:", !!addDoctor);
        console.log("MultiDoctorForm: Checking for any active medical center:", storage.getActiveMedicalCenterId() || "None");

        // Also check unassigned doctors
        const unassignedDoctors = storage.getUnassignedDoctors();
        console.log(`MultiDoctorForm: Current unassigned doctors: ${unassignedDoctors.length}`);
    }, [addDoctor]);

    const [doctors, setDoctors] = useState<DoctorFormData[]>([
        {
            id: generateCustomId('form'),
            name: "Rene Favaloro",
            mn: "38555",
            email: "<EMAIL>",
            phone: "+541122334567",
            dni: "00000000",
            specialties: ["Cardiología"],
            currentSpecialty: "",
            isValid: true,
        },
        {
            id: generateCustomId('form'),
            name: "Cecilia Grierson",
            mn: "20444",
            email: "<EMAIL>",
            phone: "+541123456789",
            dni: "00000000",
            specialties: ["Obstetricia"],
            currentSpecialty: "",
            isValid: true,
        },
        {
            id: generateCustomId('form'),
            name: "Hector Saenz",
            mn: "45678",
            email: "<EMAIL>",
            phone: "+541134567890",
            dni: "00000000",
            specialties: ["Clínica Médica"],
            currentSpecialty: "",
            isValid: true,
        },
        {
            id: generateCustomId('form'),
            name: "Erica Paluco",
            mn: "40555",
            email: "<EMAIL>",
            phone: "+541145678901",
            dni: "00000000",
            specialties: ["Dermatología"],
            currentSpecialty: "",
            isValid: true,
        },
        {
            id: generateCustomId('form'),
            name: "Dino Dinares",
            mn: "34889",
            email: "<EMAIL>",
            phone: "+541156789012",
            dni: "00000000",
            specialties: ["Oftalmología"],
            currentSpecialty: "",
            isValid: true,
        },
    ])
    const [isSubmitting, setIsSubmitting] = useState(false)

    function createEmptyDoctorForm(): DoctorFormData {
        return {
            id: generateCustomId('form'),
            name: "",
            mn: "",
            email: "",
            phone: "",
            dni: "",
            specialties: [],
            currentSpecialty: "",
            isValid: false,
        }
    }

    // Calculate valid doctors - no useEffect needed as this is derived during render
    const validDoctors = doctors.filter(doctor =>
        doctor.name.trim() !== "" && doctor.mn.trim() !== "" && doctor.email.trim() !== "" && doctor.specialties.length > 0
    )

    // Add a new row
    const handleAddRow = () => {
        setDoctors([...doctors, createEmptyDoctorForm()])
    }

    // Remove a row
    const handleRemoveRow = (idToRemove: string) => {
        setDoctors(doctors.filter(doctor => doctor.id !== idToRemove))
    }

    // Update doctor field
    const handleDoctorChange = (id: string, field: keyof DoctorFormData, value: string | string[]) => {
        setDoctors(
            doctors.map(doctor => {
                if (doctor.id !== id) return doctor

                const updatedDoctor = {...doctor, [field]: value}

                // Update the isValid flag right when the doctor changes
                updatedDoctor.isValid =
                    updatedDoctor.name.trim() !== "" &&
                    updatedDoctor.mn.trim() !== "" &&
                    updatedDoctor.email.trim() !== "" &&
                    updatedDoctor.specialties.length > 0

                return updatedDoctor
            })
        )
    }

    // Add specialty to a doctor
    const handleAddSpecialty = (doctorId: string, specialty: Specialty) => {
        const doctor = doctors.find(d => d.id === doctorId)
        if (doctor && !doctor.specialties.includes(specialty)) {
            setDoctors(
                doctors.map(d => {
                    if (d.id !== doctorId) return d

                    const updatedSpecialties = [...d.specialties, specialty]

                    return {
                        ...d,
                        specialties: updatedSpecialties,
                        currentSpecialty: "",
                        isValid: d.name.trim() !== "" && d.mn.trim() !== "" && d.email.trim() !== "" && updatedSpecialties.length > 0
                    }
                })
            )
        }
    }

    // Remove specialty from a doctor
    const handleRemoveSpecialty = (doctorId: string, specialty: string) => {
        setDoctors(
            doctors.map(d => {
                if (d.id !== doctorId) return d

                const updatedSpecialties = d.specialties.filter(s => s !== specialty)

                return {
                    ...d,
                    specialties: updatedSpecialties,
                    isValid: d.name.trim() !== "" && d.mn.trim() !== "" && d.email.trim() !== "" && updatedSpecialties.length > 0
                }
            })
        )
    }


    // Validate specialty - check if it exists in SPECIALTIES
    const isValidSpecialty = (specialty: string): specialty is Specialty => {
        return SPECIALTIES.includes(specialty as Specialty)
    }

    // Convert string specialties to valid Specialty types
    const convertToValidSpecialties = (specialties: string[]): Specialty[] => {
        return specialties.filter(isValidSpecialty)
    }

    // Submit all valid doctors
    const handleSubmit = async () => {
        // Log the submission attempt
        console.log("MultiDoctorForm: handleSubmit called with valid doctors:", validDoctors.length);
        console.log("MultiDoctorForm: Current unassigned doctors before creating:", storage.getUnassignedDoctors().length);

        if (validDoctors.length === 0) {
            toast.error("No hay profesionales válidos para crear")
            return
        }

        setIsSubmitting(true)

        try {
            // Track how many doctors were successfully created
            let successCount = 0;

            // Create each doctor sequentially with delay to avoid timestamp collisions
            for (let i = 0; i < validDoctors.length; i++) {
                const doctorForm = validDoctors[i];

                // Convert specialties to valid Specialty types
                const validSpecialties = convertToValidSpecialties(doctorForm.specialties)

                // If no valid specialties were found, use a default one
                const specialtiesToUse = validSpecialties.length > 0
                    ? validSpecialties
                    : ["Medicina General" as Specialty]

                // Create the doctor with default values
                const newDoctor = {
                    name: doctorForm.name,
                    mn: doctorForm.mn,
                    specialties: specialtiesToUse,
                    consultationTypes: [
                        {
                            name: "Consulta General",
                            requiresMedicalOrder: false,
                            duration: "default" as const,
                            dailyLimit: "unlimited" as const,
                            basePrice: 0,
                            availableOnline: true,
                            acceptsPrivatePay: true
                        }
                    ],
                    workingDays: {
                        "1": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
                        "2": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
                        "3": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
                        "4": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
                        "5": {enabled: true, hours: [{start: "09:00", end: "17:00"}]},
                        "0": {enabled: false, hours: []},
                        "6": {enabled: false, hours: []}
                    },
                    onlineBookingAdvanceDays: 30,
                    onlineBookingMinHours: 1,
                    appointmentSlotDuration: 15,
                    // Calculate initial from name
                    initial: doctorForm.name.split(' ').map(part => part[0]).join(''),
                    title: doctorForm.name.toLowerCase().includes('dra.') ? "Dra" : "Dr",
                    email: doctorForm.email,
                    phone: doctorForm.phone,
                    dni: doctorForm.dni,
                }

                console.log(`Creating doctor ${i + 1}/${validDoctors.length} with name: ${newDoctor.name}, MN: ${newDoctor.mn}`)

                try {
                    // Add the doctor through context and get the result with ID
                    const doctorWithId = addDoctor(newDoctor)

                    console.log(`Created doctor: ${doctorWithId.id} - ${doctorWithId.name}`)
                    console.log(`Doctor assigned to medical center: ${storage.getActiveMedicalCenterId() || "None (will be unassigned)"}`)

                    // Make sure doctor is registered in ID map
                    if (doctorWithId.id && doctorWithId.mn) {
                        storage.registerDoctorId(doctorWithId.mn, doctorWithId.id)
                        console.log(`Registered doctor ${doctorWithId.id} with MN ${doctorWithId.mn} in ID map`)
                    }

                    // Create a doctor user account if email is provided
                    if (doctorWithId.email) {
                        // Check if a user with this email already exists
                        const existingUser = storage.getUserByEmail(doctorWithId.email);

                        if (!existingUser) {
                            // Generate a temporary password
                            const tempPassword = `temp${Math.floor(100000 + Math.random() * 900000)}`;

                            // Create the doctor user
                            const doctorUser: User = {
                                id: generateUserId(),
                                name: doctorWithId.name,
                                email: doctorWithId.email,
                                password: tempPassword,
                                roles: UserRole.DOCTOR,
                                medicalCenterId: storage.getActiveMedicalCenterId() || "",
                                doctorId: doctorWithId.id,
                                phone: doctorWithId.phone,
                                dni: doctorWithId.dni,
                                permissions: {...defaultDoctorPermissions}
                            };

                            // Save the doctor user
                            storage.saveUser(doctorUser);

                            // Create patient profile if phone and DNI are provided
                            if (doctorWithId.phone && doctorWithId.dni && patientContext) {
                                const patientId = patientContext.createPatientForProfessional(doctorUser);
                                if (patientId) {
                                    // Update the user with the default patient ID
                                    const updatedUser = {...doctorUser, defaultPatientId: patientId};
                                    storage.saveUser(updatedUser);
                                    console.log(`Created patient profile ${patientId} for doctor ${doctorUser.name}`);
                                }
                            }

                            // Send email invitation to the doctor
                            const emailSent = await sendDoctorInvitationEmail(doctorUser.email, tempPassword, doctorUser.name);
                            console.log(`Email invitation ${emailSent ? 'sent' : 'failed'} to ${doctorUser.email}`);
                            console.log(`Created user account for doctor ${doctorWithId.name} with email ${doctorWithId.email} and temporary password ${tempPassword}`);
                        } else {
                            // If user exists but is not a doctor, update their role
                            if (existingUser.roles !== UserRole.DOCTOR) {
                                const updatedUser: User = {
                                    ...existingUser,
                                    roles: UserRole.DOCTOR,
                                    doctorId: doctorWithId.id,
                                    permissions: {...defaultDoctorPermissions}
                                };

                                // Save the updated user
                                storage.saveUser(updatedUser);

                                console.log(`Updated existing user to doctor role for ${doctorWithId.name}`);
                            } else {
                                console.log(`User already exists as a doctor for ${doctorWithId.name}`);
                            }
                        }
                    }

                    successCount++;

                    // Check unassigned doctors after each doctor creation
                    const currentUnassigned = storage.getUnassignedDoctors();
                    console.log(`Unassigned doctors after creating doctor ${i + 1}/${validDoctors.length}: ${currentUnassigned.length}`);

                    // Wait 50ms between doctor creations to ensure unique timestamp IDs
                    if (i < validDoctors.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }
                } catch (error) {
                    console.error(`Error creating doctor ${newDoctor.name}:`, error);
                }
            }

            // Final check of unassigned doctors
            console.log("Final unassigned doctors count:", storage.getUnassignedDoctors().length);

            if (successCount > 0) {
                toast.success(`${successCount} ${successCount === 1 ? 'doctor creado' : 'profesionales creados'} correctamente`)

                // Reset the form after successful submission
                setDoctors([
                    createEmptyDoctorForm()
                ])
            } else {
                toast.error("No se pudo crear ningún doctor")
            }

            setIsSubmitting(false)
        } catch (error) {
            console.error("Error creating doctors:", error)
            toast.error("Ha ocurrido un error al crear los profesionales")
            setIsSubmitting(false)
        }
    }

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle>Crear Múltiples profesionales</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="mb-4">
                    <Button variant="outline" size="sm" onClick={handleAddRow} className="mr-2">
                        <Plus className="h-4 w-4 mr-2"/>
                        Añadir Fila
                    </Button>
                    <Button
                        size="sm"
                        onClick={handleSubmit}
                        disabled={isSubmitting || doctors.every(d => !d.isValid)}
                    >
                        <Save className="h-4 w-4 mr-2"/>
                        {isSubmitting ? "Guardando..." : "Guardar profesionales"}
                    </Button>
                </div>

                <div className="border rounded-md">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[150px]">Nombre</TableHead>
                                <TableHead className="w-[80px]">MN</TableHead>
                                <TableHead className="w-[180px]">Email</TableHead>
                                <TableHead className="w-[120px]">Teléfono</TableHead>
                                <TableHead className="w-[80px]">DNI</TableHead>
                                <TableHead>Especialidades</TableHead>
                                <TableHead className="w-[80px]">Acciones</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {doctors.map((doctor) => (
                                <TableRow key={doctor.id}>
                                    <TableCell>
                                        <Input
                                            placeholder="Nombre del doctor"
                                            value={doctor.name}
                                            onChange={(e) => handleDoctorChange(doctor.id, "name", e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Input
                                            placeholder="Matrícula"
                                            value={doctor.mn}
                                            onChange={(e) => handleDoctorChange(doctor.id, "mn", e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Input
                                            placeholder="Email"
                                            type="email"
                                            value={doctor.email}
                                            onChange={(e) => handleDoctorChange(doctor.id, "email", e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Input
                                            placeholder="+541112345678"
                                            type="tel"
                                            value={doctor.phone}
                                            onChange={(e) => handleDoctorChange(doctor.id, "phone", e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Input
                                            placeholder="DNI"
                                            type="text"
                                            value={doctor.dni}
                                            onChange={(e) => handleDoctorChange(doctor.id, "dni", e.target.value)}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <div className="space-y-2">
                                            <div className="flex flex-wrap gap-1 mb-1">
                                                {doctor.specialties.map((specialty) => (
                                                    <Badge key={specialty} className="flex items-center gap-1">
                                                        {specialty}
                                                        <X
                                                            className="h-3 w-3 cursor-pointer"
                                                            onClick={() => handleRemoveSpecialty(doctor.id, specialty)}
                                                        />
                                                    </Badge>
                                                ))}
                                            </div>
                                            <div className="flex">
                                                <Select
                                                    onValueChange={(value) => handleAddSpecialty(doctor.id, value as Specialty)}
                                                >
                                                    <SelectTrigger className="w-full mr-2">
                                                        <SelectValue placeholder="Seleccionar especialidad"/>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {SPECIALTIES
                                                            .filter(specialty => !doctor.specialties.includes(specialty))
                                                            .map((specialty) => (
                                                                <SelectItem key={specialty} value={specialty}>
                                                                    {specialty}
                                                                </SelectItem>
                                                            ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <Button
                                            variant="destructive"
                                            size="sm"
                                            onClick={() => handleRemoveRow(doctor.id)}
                                            disabled={doctors.length === 1}
                                        >
                                            <Trash2 className="h-4 w-4"/>
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
    )
}