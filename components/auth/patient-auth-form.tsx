"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useAuth } from "@/contexts/AuthContext"
import { usePatients } from "@/contexts/PatientContext"
import { Patient } from "@/types/patient"
import { sendPatientAssociationSMS } from "@/services/phone"
import { AlertCircle, CreditCard, X, Phone, CheckCircle, Mail } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { DEFAULT_COVERAGES, findCoverageByName } from "@/data/coverages"
import { PhoneInput } from "react-international-phone"
import "react-international-phone/style.css"
import "@/styles/phone-input.css"
import { getSpanishCountries } from "@/data/phoneCountries"
import { PhoneNumberUtil } from 'google-libphonenumber'
import { useRouter } from "next/navigation"

interface PatientAuthFormProps {
  onSuccess: () => void;
  title?: string;
  description?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  showPatientBadge?: boolean;
}

// Add a minimalistic step indicator component to show progress
interface StepIndicatorProps {
  currentStep: number;
  completedSteps: {
    personal: boolean;
    coverage: boolean;
    phone: boolean;
  };
}

function StepIndicator({ currentStep, completedSteps }: StepIndicatorProps) {
  const steps = ['personal', 'coverage', 'phone'];
  
  return (
    <div className="mb-8">
      <div className="flex justify-between gap-2">
        {steps.map((step, index) => (
          <div key={step} className="flex-1">
             <div className={`h-1 rounded-full transition-all duration-300 ease-in-out ${
              completedSteps[step as keyof typeof completedSteps]
                ? 'bg-blue-600'
                : currentStep === index + 1
                  ? 'bg-blue-400'
                  : 'bg-gray-200'
            }`} />
          </div>
        ))}
      </div>
    </div>
  )
}

// Helper function to capitalize names
const capitalizeName = (name: string) => {
  return name
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ")
}

// Verification Code Input Component
interface VerificationCodeInputProps {
  value: string;
  onChange: (value: string) => void;
  length?: number;
  onComplete?: (value: string) => void;
}

function VerificationCodeInput({ value, onChange, length = 6, onComplete }: VerificationCodeInputProps) {
  const [inputs, setInputs] = useState<string[]>(Array(length).fill(''));

  useEffect(() => {
    // Split the value into individual digits
    const digits = value.split('').slice(0, length);
    const newInputs = [...Array(length)].map((_, i) => digits[i] || '');
    setInputs(newInputs);
  }, [value, length]);

  const handleInputChange = (index: number, inputValue: string) => {
    // Only allow numeric input
    const numericValue = inputValue.replace(/[^0-9]/g, '');
    
    if (numericValue.length <= 1) {
      const newInputs = [...inputs];
      newInputs[index] = numericValue;
      setInputs(newInputs);
      
      const newValue = newInputs.join('');
      onChange(newValue);
      
      // Auto-focus next input
      if (numericValue && index < length - 1) {
        const nextInput = document.getElementById(`verification-input-${index + 1}`);
        nextInput?.focus();
      }
      
      // Call onComplete when all fields are filled
      if (newValue.length === length && onComplete) {
        onComplete(newValue);
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    // Handle backspace
    if (e.key === 'Backspace' && !inputs[index] && index > 0) {
      const prevInput = document.getElementById(`verification-input-${index - 1}`);
      prevInput?.focus();
    }
    
    // Handle paste
    if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      navigator.clipboard.readText().then(text => {
        const numericText = text.replace(/[^0-9]/g, '').slice(0, length);
        onChange(numericText);
      });
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');
    const numericData = pasteData.replace(/[^0-9]/g, '').slice(0, length);
    onChange(numericData);
    
    // Focus the last filled input or the first empty one
    const focusIndex = Math.min(numericData.length, length - 1);
    const targetInput = document.getElementById(`verification-input-${focusIndex}`);
    targetInput?.focus();
  };

  return (
    <div className="flex gap-2 justify-center">
      {inputs.map((digit, index) => (
        <input
          key={index}
          id={`verification-input-${index}`}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          value={digit}
          onChange={(e) => handleInputChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          className="w-12 h-12 text-center text-lg font-semibold border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all"
          maxLength={1}
          autoComplete="one-time-code"
        />
      ))}
    </div>
  );
}

// Verification Status Component
interface VerificationStatusProps {
  type: 'email' | 'phone' | 'patient';
  contact: string;
  isVisible: boolean;
}

function VerificationStatus({ type, contact, isVisible }: VerificationStatusProps) {
  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case 'email':
        return <Mail className="h-5 w-5" />;
      case 'phone':
        return <Phone className="h-5 w-5" />;
      case 'patient':
        return <CheckCircle className="h-5 w-5" />;
      default:
        return <CheckCircle className="h-5 w-5" />;
    }
  };

  const getMessage = () => {
    switch (type) {
      case 'email':
        return `Código enviado a ${contact}`;
      case 'phone':
        return `Código enviado por SMS a ${contact}`;
      case 'patient':
        return `Código enviado al teléfono registrado`;
      default:
        return 'Código enviado';
    }
  };

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
      <div className="flex items-center space-x-3">
        <div className="bg-green-100 rounded-full p-2">
          {getIcon()}
        </div>
        <div>
          <p className="font-medium text-green-800 text-sm">
            {getMessage()}
          </p>
        </div>
      </div>
    </div>
  );
}

export function PatientAuthForm({
  onSuccess,
  title: propTitle,
  description: propDescription,
  titleClassName,
  descriptionClassName,
  showPatientBadge
}: PatientAuthFormProps) {
  const router = useRouter()
  const { currentUser, isAuthenticated, isLoading, auth0Token } = useAuth()
  const { addPatient, getPatientByDni, getPatientByPhone } = usePatients()
  // Simplified flow: no tabs

  // Initialize Google's libphonenumber utility
  const phoneUtil = PhoneNumberUtil.getInstance()

  // No login state needed

  // Register state - Changed to match new step progression
  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = 3 // personal, coverage, phone

  // Removed email step

  // Personal info step (Step 2)
  const [registerName, setRegisterName] = useState("")
  const [registerLastName, setRegisterLastName] = useState("")
  const [registerDni, setRegisterDni] = useState("")
  const [foundPatient, setFoundPatient] = useState<Patient | null>(null)
  const [patientVerified, setPatientVerified] = useState(false)
  const [verificationCode, setVerificationCode] = useState("")
  const [showVerificationInput, setShowVerificationInput] = useState(false)
  const [actualVerificationCode, setActualVerificationCode] = useState("")

  // Coverage step (Step 3)
  const [registerCoverage, setRegisterCoverage] = useState("")
  const [registerPlan, setRegisterPlan] = useState("")
  const [registerNoCoverage, setRegisterNoCoverage] = useState(false)
  const [isCoverageOpen, setIsCoverageOpen] = useState(false)
  const [isPlanOpen, setIsPlanOpen] = useState(false)

  // Phone step (Step 4)
  const [registerPhone, setRegisterPhone] = useState("+54") // Default to Argentina
  const [phoneVerificationCode, setPhoneVerificationCode] = useState("")
  const [actualPhoneCode, setActualPhoneCode] = useState("")
  const [isPhoneVerifying, setIsPhoneVerifying] = useState(false)
  const [originalVerifiedPhone, setOriginalVerifiedPhone] = useState("")
  const [phoneExists, setPhoneExists] = useState(false)

  // Removed password step

  // General registration state
  const [registerError, setRegisterError] = useState("")
  const [phoneError, setPhoneError] = useState("")
  const [verificationError, setVerificationError] = useState("")

  const [isSubmitting, setIsSubmitting] = useState(false)

  // State for handling DNI conflicts and creating new accounts
  const [showCreateNewAccountOption, setShowCreateNewAccountOption] = useState(false)
  const [isCreatingNewAccount, setIsCreatingNewAccount] = useState(false)

  // Registration success state
  const [registrationSuccess, setRegistrationSuccess] = useState(false)

  // Completed steps tracking
  const [completedSteps, setCompletedSteps] = useState({
    personal: false,
    coverage: false,
    phone: false,
  })

  // Dynamically create plansByCoverage from DEFAULT_COVERAGES
  const plansByCoverage = React.useMemo(() => {
    return DEFAULT_COVERAGES.reduce((acc, coverage) => {
      acc[coverage.name] = coverage.plans || [];
      return acc;
    }, {} as Record<string, string[]>);
  }, []);

  // Function to mark a step as completed
  const markStepCompleted = (step: keyof typeof completedSteps, isCompleted: boolean = true) => {
    setCompletedSteps(prev => ({
      ...prev,
      [step]: isCompleted
    }))
  }

  // Function to navigate to next step
  const goToNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1)
    }
  }

  // Function to navigate to previous step
  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1)
    }
  }

  // Function to reset the form
  const resetForm = () => {
    // Reset all form fields
    setRegisterDni("");
    setRegisterName("");
    setRegisterLastName("");
    setRegisterCoverage("");
    setRegisterPlan("");
    setRegisterNoCoverage(false);
    setRegisterPhone("+54");
    // removed email/password fields
    setRegisterError("");
    setPhoneError("");
    setFoundPatient(null);
    setPatientVerified(false);
    setVerificationCode("");
    setShowVerificationInput(false);
    setVerificationError("");
    setIsCoverageOpen(false);
    setIsPlanOpen(false);
    setActualVerificationCode("");
    setPhoneVerificationCode("");
    // removed email verification
    setActualPhoneCode("");
    // removed email code state
    setIsPhoneVerifying(false);
    // removed email exists state
    setPhoneExists(false);
    setOriginalVerifiedPhone("");
    setShowCreateNewAccountOption(false);
    setIsCreatingNewAccount(false);

    // Reset completed steps
    setCompletedSteps({
      personal: false,
      coverage: false,
      phone: false,
    });

    // Reset to first step
    setCurrentStep(1);
  }

  // Effect: redirect if already has any role (handled by parent routes too)
  useEffect(() => {
    if (!isLoading && isAuthenticated && currentUser && currentUser.roles && currentUser.roles.length > 0) {
      router.push("/plataforma");
    }
  }, [isLoading, isAuthenticated, currentUser, router])

  // Effect to check if patient exists when DNI is entered
  useEffect(() => {
    if (currentStep === 1 && registerDni.length >= 7 && registerDni.length <= 8) {
      // Check if patient exists
      const patient = getPatientByDni(registerDni);
      if (patient) {
        // Store the patient internally but don't show it to the user
        setFoundPatient(patient);
      } else {
        setFoundPatient(null);
      }
    }
  }, [registerDni, currentStep, getPatientByDni]);

  // Removed email submit handler

  // Handle personal info submission (Step 2)
  const handlePersonalInfoSubmit = () => {
    setRegisterError("");

    // Validate name and last name
    if (!registerName.trim()) {
      setRegisterError("Por favor, ingrese su nombre");
      return;
    }

    if (!registerLastName.trim()) {
      setRegisterError("Por favor, ingrese su apellido");
      return;
    }

    // Validate DNI
    if (!registerDni) {
      setRegisterError("Por favor, ingrese su DNI");
      return;
    }

    // Validate DNI length
    if (registerDni.length < 7 || registerDni.length > 8) {
      setRegisterError("El DNI debe tener entre 7 y 8 dígitos");
      return;
    }

    // If foundPatient exists and needs verification, handle it
    if (foundPatient && (!foundPatient.userId || (foundPatient.userId && foundPatient.isDefault === false)) && !patientVerified) {
      // If not yet showing verification input, send code
      if (!showVerificationInput) {
        try {
          // Generate a verification code
          const newVerificationCode = Math.floor(100000 + Math.random() * 900000).toString();
          setActualVerificationCode(newVerificationCode);

          // For testing purposes, log the code to console
          console.log("Verification code:", newVerificationCode);

          // Send the verification SMS
          sendPatientAssociationSMS(foundPatient.phone, foundPatient.name, newVerificationCode);

          setShowVerificationInput(true);
          // Also show the create new account option immediately
          setShowCreateNewAccountOption(true);
          return;
        } catch (error) {
          if (error instanceof Error) {
            setRegisterError(error.message);
          } else {
            setRegisterError("Error al enviar el código de verificación. Intente nuevamente.");
          }
          return;
        }
      } else {
        // Verify the patient with the entered code
        if (!verificationCode) {
          setRegisterError("Por favor, ingrese el código de verificación");
          return;
        }

        if (verificationCode !== actualVerificationCode) {
          setRegisterError("El código de verificación no es válido o ha expirado");
          // Show the create new account option after failed verification
          setShowCreateNewAccountOption(true);
          return;
        }

        // Mark patient as verified
        setPatientVerified(true);
        setShowVerificationInput(false);

        // Pre-fill data if a patient was found and verified
        if (foundPatient) {
          // Pre-fill coverage information
          if (foundPatient.coverage) {
            if (foundPatient.coverage === "Sin Cobertura") {
              setRegisterNoCoverage(true);
              setRegisterCoverage("");
              setRegisterPlan("");
            } else {
              // Check if coverage has a plan format (e.g., "OSDE 310")
              const coverageParts = foundPatient.coverage.split(' ');
              if (coverageParts.length > 1) {
                setRegisterCoverage(coverageParts[0]);
                setRegisterPlan(coverageParts.slice(1).join(' '));
              } else {
                setRegisterCoverage(foundPatient.coverage);
                setRegisterPlan("");
              }
              setRegisterNoCoverage(false);
            }
          }

          // Store the original phone but don't pre-fill it - user must enter a different one
          if (foundPatient.phone) {
            setOriginalVerifiedPhone(foundPatient.phone);
            setRegisterPhone("+54"); // Reset to default instead of pre-filling
          }
        }
      }
    }

    // Mark step as completed
    markStepCompleted('personal');

    // Move to next step
    goToNextStep();
  }

  // Handle coverage submission (Step 2)
  const handleCoverageSubmit = () => {
    setRegisterError("");

    // If no coverage is selected and no-coverage option is not checked,
    // show error and prevent continuing
    if (!registerNoCoverage && !registerCoverage) {
      setRegisterError("Por favor, seleccione una cobertura o marque 'No tengo cobertura'");
      return;
    }

    // Mark step as completed
    markStepCompleted('coverage');

    // Move to next step
    goToNextStep();
  }

  // Handle phone submission and verification (Step 3)
  const handlePhoneSubmit = () => {
    setRegisterError("");
    setPhoneError("");
    setPhoneExists(false);

    // Validate phone
    if (!registerPhone || registerPhone === "+" || registerPhone.length <= 5) {
      setPhoneError("Por favor, ingrese su número de teléfono");
      return;
    }

    // Validate phone format
    try {
      const phoneNumber = phoneUtil.parseAndKeepRawInput(registerPhone);
      const isValid = phoneUtil.isValidNumber(phoneNumber);

      if (!isValid) {
        setPhoneError("El número de teléfono no es válido");
        return;
      }
    } catch {
      setPhoneError("El número de teléfono no es válido");
      return;
    }

    // If patient is verified and trying to use the same phone as the existing patient
    if (patientVerified && originalVerifiedPhone && registerPhone === originalVerifiedPhone) {
      setRegisterError("Debes usar un número diferente al del paciente existente");
      return;
    }

    // Check if phone already exists in a user with a defaultPatientId
    const patient = getPatientByPhone(registerPhone);
    if (patient) {
      // If this patient has a userId and is their defaultPatientId
      if (patient.userId && patient.isDefault) {
        setRegisterError("Este número de teléfono ya está asociado a una cuenta");
        setPhoneExists(true);
        return;
      }
      // If the patient exists but doesn't have a user account, or isn't their default patient,
      // we can proceed with registration
    }

    // If patient is verified and phone hasn't changed, skip verification and finalize
    if (patientVerified && registerPhone === originalVerifiedPhone) {
      markStepCompleted('phone');
      finalizePatientAssignment();
      return;
    }

    // If not yet verifying, send verification code
    if (!isPhoneVerifying) {
      // Generate a verification code
      const phoneCode = Math.floor(100000 + Math.random() * 900000).toString();
      setActualPhoneCode(phoneCode);

      // Log for development purposes
      console.log(`Phone verification code: ${phoneCode}`);

      // Show verification input
      setIsPhoneVerifying(true);
      return;
    }

    // Verify the code
    if (phoneVerificationCode !== actualPhoneCode) {
      setRegisterError("El código de verificación del teléfono no es válido");
      return;
    }

    // Mark phone step as completed and update original verified phone if needed
    setOriginalVerifiedPhone(registerPhone);
    markStepCompleted('phone');

    // Finalize immediately after phone verification
    finalizePatientAssignment();
  }

  // Removed password submission

  // Removed login handler and UI (Auth0 handles login)

  // Removed forgot password handler

  // Handle creating a new account when DNI verification fails
  const handleCreateNewAccount = () => {
    setIsCreatingNewAccount(true);
    setRegisterError("");
    setVerificationError("");

    // If there was a found patient, mark it as potentially fraudulent
    if (foundPatient && foundPatient.id) {
      const updatedFoundPatient = {
        ...foundPatient,
        notes: (foundPatient.notes || "") +
               (foundPatient.notes ? " | " : "") +
               `POTENTIAL FRAUD: New account created with same DNI on ${new Date().toISOString()}`
      };
      // Update the patient record to flag the potential fraud
      addPatient(updatedFoundPatient);
    }

    // Clear the found patient to proceed as a new patient
    setFoundPatient(null);
    setPatientVerified(false);
    setShowVerificationInput(false);
    setShowCreateNewAccountOption(false);

    // Mark step as completed and move to next step
    markStepCompleted('personal');
    goToNextStep();
  }

  const finalizePatientAssignment = async () => {
    setIsSubmitting(true);
    setRegisterError("");

    // Format the full name
    const fullName = capitalizeName(`${registerName.trim()} ${registerLastName.trim()}`.trim());

    // Determine coverage
    let coverage;
    if (registerNoCoverage) {
      coverage = "Sin Cobertura";
    } else if (registerCoverage) {
      coverage = registerPlan ? `${registerCoverage} ${registerPlan}`.trim() : registerCoverage.trim();
    } else {
      // Default to "Sin Cobertura" if no coverage is selected
      coverage = "Sin Cobertura";
      setRegisterNoCoverage(true);
    }

    try {

      // Map coverage name to healthInsuranceId (numeric)
      let healthInsuranceId: number = 0;
      if (!registerNoCoverage && registerCoverage) {
        const cov = findCoverageByName(registerCoverage);
        if (cov && cov.id && cov.id !== '8') { // '8' is "Sin Cobertura" in DEFAULT_COVERAGES
          const parsed = parseInt(cov.id, 10);
          if (!Number.isNaN(parsed)) healthInsuranceId = parsed;
        }
      }

      // Create patient on backend from current user
      const createPatientPayload = {
        name: registerName.trim(),
        surname: registerLastName.trim(),
        identificationNumber: registerDni.trim(),
        healthInsuranceId,
      };

      const createPatientRes = await fetch(`/api/patients/create-from-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Auth handled server-side via cookies
        },
        body: JSON.stringify(createPatientPayload),
      });

      if (!createPatientRes.ok) {
        const errText = await createPatientRes.text();
        throw new Error(`Error al crear paciente: ${errText}`);
      }

      const createdPatient = await createPatientRes.json();
      const createdPatientId: number | undefined = createdPatient?.id ?? createdPatient?.patientId ?? createdPatient?.data?.id;
      if (!createdPatientId) {
        console.warn('No se pudo determinar el ID del paciente creado. Respuesta:', createdPatient);
      }
      // If patient already exists in local cache, update it
      if (foundPatient) {
        // Update patient with new information
        const updatedPatient = {
          ...foundPatient,
          name: fullName,
          coverage: coverage,
          phone: registerPhone,
          email: "",
          phoneVerified: true
        };

        // Add/update patient
        addPatient(updatedPatient as Patient);
      } else {
        // Add patient locally using server ID when available
        const newLocalPatient: Patient = {
          id: createdPatientId ?? Date.now(),
          name: fullName,
          dni: registerDni,
          coverage: coverage,
          phone: registerPhone,
          email: "",
          phoneVerified: true,
          ...(isCreatingNewAccount ? {
            createdByMedicalCenterId: "SELF_REGISTRATION",
            notes: "Created due to DNI verification conflict - potential fraud case"
          } : {})
        };
        addPatient(newLocalPatient);
      }

      // Assign patient role to the current user in backend
      const auth0Id = currentUser?.auth0Sub;
      if (auth0Id) {
        // Merge existing idFromRole map to plain object
        const idFromRoleObj: Record<string, number> = {};
        if (currentUser?.idFromRole instanceof Map) {
          currentUser.idFromRole.forEach((val, key) => {
            idFromRoleObj[String(key)] = val as unknown as number;
          });
        } else if (currentUser && (currentUser as any).idFromRole) {
          Object.assign(idFromRoleObj, (currentUser as any).idFromRole);
        }
        if (createdPatientId) {
          idFromRoleObj['TURNERA_USER'] = createdPatientId;
        }

        const updatedRoles = Array.from(new Set([...(currentUser?.roles || []), 'TURNERA_USER' as any]));

        const updateUserPayload = {
          idFromRole: idFromRoleObj,
          name: registerName.trim() || currentUser?.name || '',
          surname: registerLastName.trim() || currentUser?.surname || '',
          roles: updatedRoles,
          auth0Sub: auth0Id,
        };

        const updateUserRes = await fetch(`/api/user/${encodeURIComponent(auth0Id)}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            // Auth handled server-side via cookies
          },
          body: JSON.stringify(updateUserPayload),
        });

        if (!updateUserRes.ok) {
          const errText = await updateUserRes.text();
          console.warn('Error actualizando usuario con rol de paciente:', errText);
        } else {
          const updatedUser = await updateUserRes.json();
          try {
            localStorage.setItem('user', JSON.stringify(updatedUser));
          } catch (e) {
            console.warn('No se pudo actualizar localStorage con el usuario actualizado:', e);
          }
        }
      }

      // Success
      setRegistrationSuccess(true);
      setTimeout(() => {
        onSuccess();
      }, 800);
    } catch (error) {
      if (error instanceof Error) {
        setRegisterError(error.message);
      } else {
        setRegisterError("Error al crear el paciente. Intente nuevamente.");
      }
    } finally {
      setIsSubmitting(false);
    }
  }

  // Determine display title and description based on mode
  const displayTitle = propTitle || "Crear perfil de paciente";
  const displayDescription = propDescription || "Completá tus datos para usar Turnera como paciente";

  const renderRegisterContent = () => (
    <>
      {/* Progress indicator */}
      <StepIndicator currentStep={currentStep} completedSteps={completedSteps} />

      {/* Registration success modal */}
      {registrationSuccess && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <div className="space-y-4">
              <div className="bg-green-50 p-3 sm:p-4 rounded-lg border border-green-100 text-center">
                <div className="flex items-center justify-center mb-2">
                  <div className="bg-green-100 rounded-full p-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-base sm:text-lg font-medium text-green-800 mb-1">¡Cuenta creada exitosamente!</h3>
                <p className="text-green-700 text-xs sm:text-sm">Redirigiendo al inicio de sesión...</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 1 is Personal Info now - handled below */}

      {/* Step 1: Personal information */}
      {currentStep === 1 && (
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
              Contanos sobre vos
            </h2>
            <p className="text-gray-600 text-sm sm:text-base">
              Los centros de salud verán estos datos para identificarte
            </p>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-3 sm:gap-4">
              <div className="space-y-2">
                <Input
                  id="registerName"
                  value={registerName}
                  onChange={(e) => {
                    // Only allow letters and spaces
                    const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                    setRegisterName(capitalizeName(value));
                  }}
                  placeholder="Nombre"
                  className="py-3"
                />
              </div>
              <div className="space-y-2">
                <Input
                  id="registerLastName"
                  value={registerLastName}
                  onChange={(e) => {
                    // Only allow letters and spaces
                    const value = e.target.value.replace(/[^a-zA-ZáéíóúÁÉÍÓÚñÑüÜ\s]/g, '');
                    setRegisterLastName(capitalizeName(value));
                  }}
                  placeholder="Apellido"
                  className="py-3"
                />
              </div>
            </div>

            <div className="space-y-2">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <CreditCard className={`h-5 w-5 ${showVerificationInput ? 'text-gray-300' : 'text-gray-400'}`} />
                </div>
                <Input
                  id="registerDni"
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  placeholder="DNI"
                  value={registerDni}
                  className={`pl-10 py-3 ${showVerificationInput ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : ''}`}
                  disabled={showVerificationInput}
                  onChange={(e) => {
                    // Only allow numbers
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    // Prevent DNI from starting with 0
                    if (value === "0" || (value.length > 0 && value[0] === "0")) {
                      return;
                    }
                    setRegisterDni(value);
                  }}
                />
                {registerDni && (
                  <div
                    className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                    onClick={() => {
                      setRegisterDni("");
                      // If verification is showing, reset all verification state
                      if (showVerificationInput) {
                        setShowVerificationInput(false);
                        setFoundPatient(null);
                        setPatientVerified(false);
                        setVerificationCode("");
                        setActualVerificationCode("");
                        setVerificationError("");
                        setShowCreateNewAccountOption(false);
                        setIsCreatingNewAccount(false);
                      }
                    }}
                  >
                    <X className="h-4 w-4 text-gray-400 hover:text-gray-500" />
                  </div>
                )}
              </div>
            </div>

            {/* Patient verification section */}
            {showVerificationInput && (
              <div className="mt-4 p-4 border border-blue-200 rounded-lg bg-blue-50">
                <h3 className="font-medium text-blue-800 mb-2">Verificación de paciente</h3>
                <p className="text-xs sm:text-sm text-blue-700 mb-4">
                  Hemos encontrado un paciente con este DNI.
                  Para asociar este paciente a tu cuenta, necesitamos verificar que eres el propietario.
                </p>

                <VerificationStatus 
                  type="patient" 
                  contact="" 
                  isVisible={true} 
                />

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="verificationCode" className="block text-center text-sm font-medium text-blue-800">
                      Código de verificación
                    </Label>
                    <VerificationCodeInput
                      value={verificationCode}
                      onChange={setVerificationCode}
                      onComplete={(code) => {
                        setVerificationCode(code);
                      }}
                    />
                  </div>
                  {verificationError && (
                    <div className="text-xs text-red-600 text-center">
                      {verificationError}
                    </div>
                  )}
                  {/* Always show the verification code for testing */}
                  <p className="text-[10px] sm:text-xs text-blue-600 font-bold text-center">
                    Código de desarrollo: {actualVerificationCode}
                  </p>
                </div>

                {/* Show create new account option */}
                {showCreateNewAccountOption && (
                  <div className="mt-4 pt-4 border-t border-blue-200">
                    <p className="text-xs text-blue-700 mb-3">
                      ¿No tenés acceso al teléfono registrado? No hay problema, podés crear una nueva cuenta con este DNI.
                    </p>
                    <Button
                      onClick={handleCreateNewAccount}
                      variant="outline"
                      className="w-full text-blue-600 border-blue-600 hover:bg-blue-50"
                      disabled={isCreatingNewAccount}
                    >
                      {isCreatingNewAccount ? "Creando nueva cuenta..." : "Crear nueva cuenta con este DNI"}
                    </Button>
                  </div>
                )}
              </div>
            )}

            {registerError && (
              <div className="bg-red-50 p-3 rounded-md flex items-start text-red-600 text-sm">
                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>{registerError}</span>
              </div>
            )}

            <div className="flex gap-3 sm:gap-4">
              <Button
                onClick={handlePersonalInfoSubmit}
                className={`w-full bg-blue-600 hover:bg-blue-700 py-3`}
                disabled={!registerName || !registerLastName || !registerDni || (showVerificationInput && !verificationCode)}
              >
                {showVerificationInput ? "Verificar y continuar" : "Continuar"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Step 2: Coverage */}
      {currentStep === 2 && (
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
              ¿Tenés cobertura médica?
            </h2>
            <p className="text-gray-600 text-sm sm:text-base">
              Esto nos ayuda a entender qué profesionales pueden atenderte
            </p>
          </div>

          <div className="space-y-4">
            {patientVerified && foundPatient?.coverage && (
              <div className="bg-blue-50 p-2 sm:p-3 rounded-md text-blue-700 text-xs sm:text-sm mb-3 sm:mb-4">
                <p className="font-medium text-xs sm:text-sm">Cobertura actual: {foundPatient.coverage}</p>
                <p className="text-xs sm:text-sm">Podés mantener esta cobertura o seleccionar una diferente.</p>
              </div>
            )}

            <Select
              open={isCoverageOpen}
              onOpenChange={setIsCoverageOpen}
              value={registerCoverage}
              onValueChange={(value) => {
                setRegisterCoverage(value);
                setRegisterPlan("");
              }}
              disabled={registerNoCoverage}
            >
              <SelectTrigger id="coverage">
                <SelectValue placeholder="Seleccionar Cobertura" />
              </SelectTrigger>
              <SelectContent>
                {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                  <SelectItem key={cov.id} value={cov.name}>{cov.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            {registerCoverage && (
              <Select
                open={isPlanOpen}
                onOpenChange={setIsPlanOpen}
                value={registerPlan}
                onValueChange={(value) => setRegisterPlan(value)}
                disabled={registerNoCoverage}
              >
                <SelectTrigger id="plan">
                  <SelectValue placeholder="Seleccionar Plan" />
                </SelectTrigger>
                <SelectContent>
                  {plansByCoverage[registerCoverage]?.map((plan) => (
                    <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="no-coverage"
                  checked={registerNoCoverage}
                  onCheckedChange={(checked) => {
                    setRegisterNoCoverage(checked);
                    if (checked) {
                      setRegisterCoverage("");
                      setRegisterPlan("");
                    }
                  }}
                />
                <Label htmlFor="no-coverage">No tengo cobertura</Label>
              </div>
            </div>

            {registerError && (
              <div className="bg-red-50 p-3 rounded-md flex items-start text-red-600 text-sm">
                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>{registerError}</span>
              </div>
            )}

            <div className="flex flex-col-reverse sm:flex-row gap-3 sm:gap-4">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                className="w-full sm:w-1/3 py-3"
              >
                Atrás
              </Button>
              <Button
                onClick={handleCoverageSubmit}
                className="w-full sm:w-2/3 bg-blue-600 hover:bg-blue-700 py-3"
              >
                Continuar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Phone */}
      {currentStep === 3 && (
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
              {isPhoneVerifying ? "¡Revisá tu teléfono!" : "¿Cuál es tu número?"}
            </h2>
            {!isPhoneVerifying && (
              <p className="text-gray-600 text-sm sm:text-base">
                Te enviaremos recordatorios y actualizaciones de tus turnos
              </p>
            )}
          </div>

          <div className="space-y-4">
            {patientVerified && originalVerifiedPhone && (
              <div className="bg-amber-50 p-2 sm:p-3 rounded-md text-amber-700 text-xs sm:text-sm mb-3 sm:mb-4 border border-amber-200">
                <p className="font-medium">Teléfono del paciente existente: {originalVerifiedPhone}</p>
                <p>Debes ingresar un número diferente para tu nueva cuenta de usuario.</p>
              </div>
            )}

            {!isPhoneVerifying ? (
              <div className="space-y-2">
                <div className="custom-phone-input">
                  <PhoneInput
                    defaultCountry="ar"
                    value={registerPhone}
                    onChange={(phone) => {
                      setRegisterPhone(phone);
                      setPhoneError("");
                      setPhoneExists(false);

                      try {
                        // Parse the phone number using Google's libphonenumber
                        const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                        const isValid = phoneUtil.isValidNumber(phoneNumber);

                        if (!isValid && phone !== "+" && phone.length > 5) {
                          setPhoneError("El número de teléfono no es válido");
                        }
                      } catch {
                        if (phone !== "+" && phone.length > 5) {
                          setPhoneError("El número de teléfono no es válido");
                        }
                      }
                    }}
                    inputStyle={{
                      width: '100%',
                      height: '3rem'
                    }}
                    className={`w-full custom-phone-input with-dial-code-preview ${phoneExists ? "border-red-500" : ""}`}
                    placeholder="Tu número de teléfono"
                    countrySelectorStyleProps={{
                      buttonStyle: {
                        paddingLeft: '10px',
                        paddingRight: '5px'
                      }
                    }}
                    hideDropdown={false}
                    disableDialCodeAndPrefix={true}
                    showDisabledDialCodeAndPrefix={true}
                    disableFormatting={false}
                    preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                    countries={getSpanishCountries()}
                  />
                  {phoneError && (
                    <div className="text-xs text-red-600 text-center mt-2">
                      {phoneError}
                    </div>
                  )}
                  {phoneExists && (
                    <div className="text-xs text-red-600 text-center mt-2">
                      Este número ya está asociado a una cuenta existente
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <>
                <VerificationStatus 
                  type="phone" 
                  contact={registerPhone} 
                  isVisible={true} 
                />

                                  <div className="space-y-4">
                    <div className="space-y-2">
                      <p className="text-center text-gray-700 font-medium">
                        Ingresá el código de 6 dígitos que te enviamos
                      </p>
                      <VerificationCodeInput
                        value={phoneVerificationCode}
                        onChange={setPhoneVerificationCode}
                        onComplete={(code) => {
                          setPhoneVerificationCode(code);
                        }}
                      />
                    </div>
                    <p className="text-[10px] sm:text-xs text-blue-600 font-bold text-center">
                      Código de desarrollo: {actualPhoneCode}
                    </p>
                  </div>
              </>
            )}

            {registerError && (
              <div className="bg-red-50 p-3 rounded-md flex items-start text-red-600 text-sm">
                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>{registerError}</span>
              </div>
            )}

            <div className="flex flex-col-reverse sm:flex-row gap-3 sm:gap-4">
              <Button
                onClick={goToPreviousStep}
                variant="outline"
                className="w-full sm:w-1/3 py-3"
              >
                Atrás
              </Button>
              <Button
                onClick={isPhoneVerifying || completedSteps.phone ? finalizePatientAssignment : handlePhoneSubmit}
                className="w-full sm:w-2/3 bg-blue-600 hover:bg-blue-700 py-3"
                disabled={!registerPhone || phoneError !== "" || phoneExists || (isPhoneVerifying && !phoneVerificationCode) || isSubmitting}
              >
                {isSubmitting ? "Guardando..." : isPhoneVerifying ? "Verificar y continuar" : completedSteps.phone ? "Finalizar" : "Continuar"}
              </Button>
            </div>
          </div>
        </div>
      )}

    </>
  );

  return (
    <Card className="w-full shadow-md overflow-hidden">
      {(displayTitle || displayDescription || showPatientBadge) && (
        <CardHeader className="space-y-1 p-4 sm:p-6 text-center">
          {showPatientBadge && (
            <div className="mb-2 sm:mb-3">
              <span className="inline-block bg-blue-100 text-blue-700 text-xs sm:text-sm font-medium px-3 py-1.5 rounded-lg shadow-sm border border-blue-200">
                Cuenta de paciente
              </span>
            </div>
          )}
          {displayTitle && (
            <CardTitle className={`text-lg sm:text-xl font-bold ${titleClassName || ""}`}>{displayTitle}</CardTitle>
          )}
          {displayDescription && (
            <CardDescription className={`text-sm sm:text-base ${descriptionClassName || ""}`}>
              {displayDescription}
            </CardDescription>
          )}
        </CardHeader>
      )}
      <CardContent className="p-4 sm:p-6 pt-2 sm:pt-3">
        {renderRegisterContent()}
      </CardContent>
    </Card>
  )
}
