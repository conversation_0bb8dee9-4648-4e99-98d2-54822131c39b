"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import { ArrowLeft, Trash2, FileDown } from "lucide-react"
import * as XLSX from 'xlsx'

// Create a simple table component since we don't have the shadcn table component
const Table = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <table className={`w-full border-collapse ${className}`}>{children}</table>
)

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead className="bg-gray-100">{children}</thead>
)

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody>{children}</tbody>
)

const TableRow = ({ children }: { children: React.ReactNode }) => (
  <tr className="border-b border-gray-200 hover:bg-gray-50">{children}</tr>
)

const TableHead = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <th className={`py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${className}`}>{children}</th>
)

const TableCell = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <td className={`py-3 px-4 text-sm ${className}`}>{children}</td>
)

const TableCaption = ({ children }: { children: React.ReactNode }) => (
  <caption className="py-2 text-sm text-gray-500">{children}</caption>
)

interface ProfessionalFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  establishmentName?: string
  locationArea: string
  locationId: string
  officeCount?: string
  professionalCount?: string
  monthlyAppointments: string
  formType: "establishment" | "individual"
  submittedAt: string
}

export default function ProfessionalRegistrationsPage() {
  const [registrations, setRegistrations] = useState<ProfessionalFormData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Load data from localStorage
    try {
      const storedData = localStorage.getItem("professional-registrations")
      if (storedData) {
        const parsedData = JSON.parse(storedData)
        // Sort registrations by submittedAt date, newest first
        const sortedData = [...parsedData].sort((a, b) =>
          new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()
        )
        setRegistrations(sortedData)
      }
    } catch (error) {
      console.error("Error loading registrations:", error)
    } finally {
      setLoading(false)
    }
  }, [])

  const handleClearAll = () => {
    if (window.confirm("¿Está seguro que desea eliminar todos los registros?")) {
      localStorage.removeItem("professional-registrations")
      setRegistrations([])
    }
  }

  // Export to Excel function
  const exportToExcel = () => {
    // Create worksheet data
    const headers = [
      "Fecha",
      "Tipo",
      "Nombre",
      "Email",
      "Teléfono",
      "Establecimiento",
      "Localidad",
      "Barrio/Partido",
      "Consultorios",
      "Profesionales",
      "Turnos Mensuales"
    ]

    // Create data rows
    const data = registrations.map(reg => [
      formatDate(reg.submittedAt),
      reg.formType === "establishment" ? "Establecimiento" : "Consultorio Individual",
      `${reg.firstName} ${reg.lastName}`,
      reg.email,
      reg.phone,
      reg.establishmentName || "-",
      reg.locationArea === "caba" ? "CABA" : reg.locationArea === "gba" ? "GBA" : reg.locationArea,
      reg.locationId
        ? reg.locationId.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')
        : "-",
      reg.officeCount || "-",
      reg.professionalCount || "-",
      reg.monthlyAppointments
    ])

    // Add headers to the beginning of the data array
    const wsData = [headers, ...data]

    // Create a worksheet
    const ws = XLSX.utils.aoa_to_sheet(wsData)

    // Create a workbook
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, "Registros Profesionales")

    // Generate the Excel file and trigger download
    XLSX.writeFile(wb, `registros-profesionales_${new Date().toISOString().split("T")[0]}.xlsx`)
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return new Intl.DateTimeFormat('es-AR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date)
    } catch {
      return dateString
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href="/dev" passHref>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dev Tools
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Registros de Profesionales</h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={exportToExcel}
            disabled={registrations.length === 0}
            className="flex items-center gap-1"
          >
            <FileDown className="h-4 w-4 mr-1" />
            Exportar a Excel
          </Button>
          <Button variant="destructive" size="sm" onClick={handleClearAll} disabled={registrations.length === 0}>
            <Trash2 className="h-4 w-4 mr-2" />
            Limpiar Todo
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Solicitudes de Registro</CardTitle>
          <CardDescription>
            Lista de todas las solicitudes de registro enviadas desde los formularios de profesionales.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Cargando...</div>
          ) : registrations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No hay registros para mostrar. Complete el formulario de registro para ver los datos aquí.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableCaption>Total de registros: {registrations.length}</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Fecha</TableHead>
                    <TableHead>Tipo</TableHead>
                    <TableHead>Nombre</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Teléfono</TableHead>
                    <TableHead>Establecimiento</TableHead>
                    <TableHead>Localidad</TableHead>
                    <TableHead>Barrio/Partido</TableHead>
                    <TableHead>Consultorios</TableHead>
                    <TableHead>Profesionales</TableHead>
                    <TableHead>Turnos Mensuales</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {registrations.map((registration, index) => (
                    <TableRow key={index}>
                      <TableCell className="whitespace-nowrap">{formatDate(registration.submittedAt)}</TableCell>
                      <TableCell>
                        {registration.formType === "establishment" ? "Establecimiento" : "Consultorio Individual"}
                      </TableCell>
                      <TableCell>{`${registration.firstName} ${registration.lastName}`}</TableCell>
                      <TableCell>{registration.email}</TableCell>
                      <TableCell>{registration.phone}</TableCell>
                      <TableCell>{registration.establishmentName || "-"}</TableCell>
                      <TableCell>
                        {registration.locationArea === "caba" ? "CABA" :
                         registration.locationArea === "gba" ? "GBA" : registration.locationArea}
                      </TableCell>
                      <TableCell>
                        {registration.locationId
                          ? registration.locationId.split('_').map(word =>
                              word.charAt(0).toUpperCase() + word.slice(1)
                            ).join(' ')
                          : "-"}
                      </TableCell>
                      <TableCell>{registration.officeCount || "-"}</TableCell>
                      <TableCell>{registration.professionalCount || "-"}</TableCell>
                      <TableCell>{registration.monthlyAppointments}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
