"use client"

import { useContext, useEffect, useRef, useState } from "react"
import { DoctorContext } from "@/contexts/DoctorContext"
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext"
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { DAYS } from "@/utils/constants"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "react-toastify"
import { ConsultationTypeCollapsible } from "./ConsultationTypeCollapsible"

export default function DoctorBookingPoliciesTab() {
  const { editedConfig, setEditedConfig, activeMedicalCenterId, refreshDoctorsFromStorage } = useContext(DoctorContext)
  const { medicalCenters } = useContext(MedicalCenterContext)

  // Get selectedCenter from activeMedicalCenterId
  const selectedCenter = medicalCenters.find(mc => mc.id === activeMedicalCenterId) || null

  // Track if we've already initialized the component
  const hasInitializedRef = useRef(false)

  // Track open sections
  const [openSections, setOpenSections] = useState({
    generalSettings: false,
    consecutiveBookings: false,
    overbooking: false,
    lastToFirst: false
  })

  // Log when component renders with new props
  useEffect(() => {
    console.log("DoctorBookingPoliciesTab: Rendering with", {
      doctorId: editedConfig?.id,
      medicalCenterId: activeMedicalCenterId,
      hasCenter: !!selectedCenter
    });
  }, [editedConfig?.id, activeMedicalCenterId, selectedCenter]);

  // Initialize configurations when editedConfig changes
  useEffect(() => {
    if (!editedConfig || !activeMedicalCenterId || !selectedCenter) return;

    // Skip if we've already initialized
    if (hasInitializedRef.current) return;

    console.log("DoctorBookingPoliciesTab: Initializing configurations for", {
      doctorId: editedConfig.id,
      medicalCenterId: activeMedicalCenterId
    });

    let needsUpdate = false;
    const updatedConfig = { ...editedConfig };

    // Initialize lastToFirstRangesByDay if not present
    if (!updatedConfig.lastToFirstRangesByDay) {
      const initialLastToFirstRanges: { [dayId: string]: { start: string; end: string; enabled: boolean }[] } = {};

      Object.keys(updatedConfig.workingDays || {}).forEach(dayId => {
        const workingHours = updatedConfig.workingDays[dayId]?.hours || [];
        initialLastToFirstRanges[dayId] = workingHours.map(range => ({
          start: range.start,
          end: range.end,
          enabled: false // Default to disabled
        }));
      });

      updatedConfig.lastToFirstRangesByDay = initialLastToFirstRanges;
      needsUpdate = true;
    }

    // Initialize overbookingConfig if not present
    if (!updatedConfig.overbookingConfig) {
      // Create overbooking ranges based on working days
      const overbookingRanges: Array<{ start: string; end: string; ratio: "none" | "2per1" | "1per1" | "1per2" | "1per3" }> = [];

      // Collect all unique time ranges from all working days
      const allTimeRanges = new Set<string>();

      Object.values(updatedConfig.workingDays || {}).forEach(day => {
        if (day.enabled && day.hours.length > 0) {
          day.hours.forEach(range => {
            allTimeRanges.add(`${range.start}-${range.end}`);
          });
        }
      });

      // Create an overbooking configuration for each unique time range
      Array.from(allTimeRanges).forEach(timeRange => {
        const [start, end] = timeRange.split('-');
        overbookingRanges.push({ start, end, ratio: "none" });
      });

      // If no working days are configured yet, add a default range
      if (overbookingRanges.length === 0) {
        overbookingRanges.push({ start: "08:00", end: "20:00", ratio: "none" });
      }

      updatedConfig.overbookingConfig = { ranges: overbookingRanges };
      needsUpdate = true;
    }

    // Initialize consecutive bookings options if not present
    if (updatedConfig.onlyConsecutiveBookings === undefined) {
      updatedConfig.onlyConsecutiveBookings = false;
      needsUpdate = true;
    }

    if (updatedConfig.maxConsecutiveBookingsVisible === undefined) {
      updatedConfig.maxConsecutiveBookingsVisible = 2;
      needsUpdate = true;
    }

    if (needsUpdate) {
      console.log("DoctorBookingPoliciesTab: Updating editedConfig with initialized values");
      setEditedConfig(updatedConfig);
      hasInitializedRef.current = true;
    }
  }, [editedConfig, setEditedConfig, activeMedicalCenterId, selectedCenter]);

  // Force refresh when activeMedicalCenterId changes
  useEffect(() => {
    if (!activeMedicalCenterId || !editedConfig) return;

    // Reset initialization flag when medical center changes
    hasInitializedRef.current = false;

    console.log("DoctorBookingPoliciesTab: Medical center changed, refreshing data");

    // Force a refresh of the data
    refreshDoctorsFromStorage();
  }, [activeMedicalCenterId, refreshDoctorsFromStorage, editedConfig]);

  // Sync lastToFirstRangesByDay when workingDays changes
  useEffect(() => {
    if (!editedConfig || !editedConfig.lastToFirstRangesByDay || !activeMedicalCenterId) return;

    console.log("DoctorBookingPoliciesTab: Syncing lastToFirstRangesByDay with workingDays for", {
      doctorId: editedConfig.id,
      medicalCenterId: activeMedicalCenterId
    });

    const updatedRanges: { [dayId: string]: { start: string; end: string; enabled: boolean }[] } = {};
    let needsUpdate = false;

    DAYS.forEach(day => {
      const workingHours = editedConfig.workingDays[day.id]?.hours || [];
      const currentRanges = editedConfig.lastToFirstRangesByDay?.[day.id] || [];

      if (workingHours.length === 0) {
        // If no working hours, remove any ranges for that day
        updatedRanges[day.id] = [];
        if (currentRanges.length > 0) needsUpdate = true;
      } else {
        // Sync with working hours while preserving enabled status
        const newRanges = workingHours.map(wsRange => {
          // Find a matching range that is within this working hour range
          const matchingRange = currentRanges.find((range: { start: string; end: string; enabled: boolean }) => {
            const wsStart = new Date(`2000-01-01T${wsRange.start}:00`).getTime();
            const wsEnd = new Date(`2000-01-01T${wsRange.end}:00`).getTime();
            const rangeStart = new Date(`2000-01-01T${range.start}:00`).getTime();
            const rangeEnd = new Date(`2000-01-01T${range.end}:00`).getTime();
            return rangeStart >= wsStart && rangeEnd <= wsEnd;
          });
          return matchingRange ? matchingRange : { start: wsRange.start, end: wsRange.end, enabled: false };
        });

        updatedRanges[day.id] = newRanges;
        if (JSON.stringify(newRanges) !== JSON.stringify(currentRanges)) needsUpdate = true;
      }
    });

    if (needsUpdate) {
      setEditedConfig(prev => prev ? {
        ...prev,
        lastToFirstRangesByDay: updatedRanges
      } : null);
    }
  }, [editedConfig?.workingDays, editedConfig, setEditedConfig, activeMedicalCenterId]);

  // Sync overbookingByDay when workingDays changes
  useEffect(() => {
    if (!editedConfig || !editedConfig.workingDays || !activeMedicalCenterId) return;

    console.log("DoctorBookingPoliciesTab: Syncing overbookingByDay with workingDays for", {
      doctorId: editedConfig.id,
      medicalCenterId: activeMedicalCenterId
    });

    // Initialize overbookingByDay if it doesn't exist
    if (!editedConfig.overbookingByDay) {
      setEditedConfig(prev => {
        if (!prev) return null;
        return {
          ...prev,
          overbookingByDay: {}
        };
      });
      return;
    }

    let needsUpdate = false;
    const updatedOverbookingByDay = { ...editedConfig.overbookingByDay };

    // Process each working day
    Object.entries(editedConfig.workingDays).forEach(([dayId, day]) => {
      if (!day.enabled || !day.hours.length) {
        // Skip disabled days or days without hours
        return;
      }

      // Get current ranges for this day
      const currentRanges = updatedOverbookingByDay[dayId] || [];
      const currentRangesSet = new Set(currentRanges.map(r => `${r.start}-${r.end}`));

      // Collect time ranges for this day
      const dayTimeRanges = new Set<string>();
      day.hours.forEach(range => {
        dayTimeRanges.add(`${range.start}-${range.end}`);
      });

      // Create updated ranges for this day
      let dayRanges = [...currentRanges];

      // Add missing ranges
      Array.from(dayTimeRanges).forEach(timeRange => {
        if (!currentRangesSet.has(timeRange)) {
          const [start, end] = timeRange.split('-');
          dayRanges.push({ start, end, ratio: "none" });
          needsUpdate = true;
        }
      });

      // Remove ranges that no longer exist in this day
      const rangesToKeep = dayRanges.filter(range => {
        const key = `${range.start}-${range.end}`;
        // Keep ranges that exist in this day's working hours or ones that have a non-default ratio
        return dayTimeRanges.has(key) || range.ratio !== "none";
      });

      if (rangesToKeep.length !== dayRanges.length) {
        needsUpdate = true;
        dayRanges = rangesToKeep;
      }

      // Update the day's ranges
      updatedOverbookingByDay[dayId] = dayRanges;
    });

    if (needsUpdate) {
      setEditedConfig(prev => {
        if (!prev) return null;
        return {
          ...prev,
          overbookingByDay: updatedOverbookingByDay
        };
      });
    }

    // For backward compatibility, also update the global overbookingConfig
    // This can be removed in the future when all code uses overbookingByDay
    if (!editedConfig.overbookingConfig) {
      setEditedConfig(prev => {
        if (!prev) return null;
        return {
          ...prev,
          overbookingConfig: {
            ranges: []
          }
        };
      });
    }
  }, [editedConfig?.workingDays, editedConfig, setEditedConfig, activeMedicalCenterId]);

  const toggleLastToFirstRange = (dayId: string, rangeIndex: number, enabled: boolean) => {
    if (!editedConfig || !activeMedicalCenterId) {
      toast.error("No se puede guardar la configuración sin un establecimiento seleccionado");
      return;
    }

    console.log(`DoctorBookingPoliciesTab: Toggling lastToFirstRange for day ${dayId} at index ${rangeIndex} to ${enabled} for medical center ${activeMedicalCenterId}`);

    const currentRanges = editedConfig.lastToFirstRangesByDay?.[dayId] || [];
    const updatedRanges = [...currentRanges];

    if (updatedRanges[rangeIndex]) {
      updatedRanges[rangeIndex] = { ...updatedRanges[rangeIndex], enabled };
    }

    setEditedConfig(prev => prev ? {
      ...prev,
      lastToFirstRangesByDay: {
        ...prev.lastToFirstRangesByDay,
        [dayId]: updatedRanges
      }
    } : null);
  };

  type OverbookingRatio = "none" | "2per1" | "1per1" | "1per2" | "1per3";

  const handleOverbookingRangeChange = (dayId: string, index: number, field: 'start' | 'end' | 'ratio', value: string) => {
    if (!editedConfig || !activeMedicalCenterId) {
      toast.error("No se puede guardar la configuración sin un establecimiento seleccionado");
      return;
    }

    console.log(`DoctorBookingPoliciesTab: Changing overbooking range for day ${dayId} at index ${index}, field ${field} to ${value} for medical center ${activeMedicalCenterId}`);

    // Initialize overbookingByDay if it doesn't exist
    const overbookingByDay = editedConfig.overbookingByDay || {};
    const dayRanges = overbookingByDay[dayId] || [];
    const updatedRanges = [...dayRanges];

    if (updatedRanges[index]) {
      updatedRanges[index] = {
        ...updatedRanges[index],
        [field]: field === 'ratio' ? value as OverbookingRatio : value
      };
    }

    setEditedConfig(prev => {
      if (!prev) return null;
      return {
        ...prev,
        overbookingByDay: {
          ...prev.overbookingByDay || {},
          [dayId]: updatedRanges
        }
      };
    });
  };

  // Toggle for consecutive bookings
  const toggleConsecutiveBookings = (enabled: boolean) => {
    if (!editedConfig || !activeMedicalCenterId) {
      toast.error("No se puede guardar la configuración sin un establecimiento seleccionado");
      return;
    }

    console.log(`DoctorBookingPoliciesTab: Toggling consecutive bookings to ${enabled} for medical center ${activeMedicalCenterId}`);

    setEditedConfig(prev => {
      if (!prev) return null;
      return {
        ...prev,
        onlyConsecutiveBookings: enabled
      };
    });
  };

  // Handle max consecutive bookings change
  const handleMaxConsecutiveBookingsChange = (value: string) => {
    if (!editedConfig || !activeMedicalCenterId) {
      toast.error("No se puede guardar la configuración sin un establecimiento seleccionado");
      return;
    }

    console.log(`DoctorBookingPoliciesTab: Changing max consecutive bookings to ${value} for medical center ${activeMedicalCenterId}`);

    setEditedConfig(prev => {
      if (!prev) return null;
      return {
        ...prev,
        maxConsecutiveBookingsVisible: parseInt(value)
      };
    });
  };

  // Toggle section open/closed state
  const toggleSection = (section: 'generalSettings' | 'consecutiveBookings' | 'overbooking' | 'lastToFirst') => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Generate summary for general settings section
  const getGeneralSettingsSummary = () => {
    if (!editedConfig) return "No configurado";

    return `Anticipación: ${editedConfig.onlineBookingAdvanceDays} ${editedConfig.onlineBookingAdvanceDays === 1 ? "día" : "días"}, Mínimo: ${editedConfig.onlineBookingMinHours} ${editedConfig.onlineBookingMinHours === 1 ? "hora" : "horas"}`;
  };

  // Generate summary for consecutive bookings section
  const getConsecutiveBookingsSummary = () => {
    if (!editedConfig) return "No configurado";

    if (!editedConfig.onlyConsecutiveBookings) {
      return "Desactivado";
    }

    return `Activado, máximo ${editedConfig.maxConsecutiveBookingsVisible} turnos`;
  };

  // Generate summary for overbooking section
  const getOverbookingSummary = () => {
    if (!editedConfig || !editedConfig.overbookingByDay) return "No configurado";

    // Count days with overbooking enabled
    const daysWithOverbooking = DAYS.filter(day => {
      const dayRanges = editedConfig.overbookingByDay?.[day.id] || [];
      return dayRanges.some(range => range.ratio !== "none");
    }).length;

    if (daysWithOverbooking === 0) {
      return "Sin sobreturnos";
    }

    return `Configurado en ${daysWithOverbooking} ${daysWithOverbooking === 1 ? 'día' : 'días'}`;
  };

  // Generate summary for last-to-first section
  const getLastToFirstSummary = () => {
    if (!editedConfig || !editedConfig.lastToFirstRangesByDay) return "No configurado";

    // Count days with last-to-first enabled
    const daysWithLastToFirst = DAYS.filter(day => {
      const dayRanges = editedConfig.lastToFirstRangesByDay?.[day.id] || [];
      return dayRanges.some(range => range.enabled);
    }).length;

    if (daysWithLastToFirst === 0) {
      return "Desactivado";
    }

    return `Activado en ${daysWithLastToFirst} ${daysWithLastToFirst === 1 ? 'día' : 'días'}`;
  };

  if (!editedConfig || !activeMedicalCenterId || !selectedCenter) {
    return (
      <Card className="border-0 shadow-none">
        <CardContent className="p-4">
          <p className="text-gray-500">Seleccione un establecimiento para configurar las políticas de reserva.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-none">
      {/* Add global styles to hide number input arrows */}
      <style jsx global>{`
        /* Hide arrows for Chrome, Safari, Edge, Opera */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
        /* Hide arrows for Firefox */
        input[type=number] {
          -moz-appearance: textfield;
        }
      `}</style>
      <CardHeader className="p-0 pb-4">
        <CardTitle className="text-lg text-gray-900">Políticas de Reserva Online</CardTitle>
      </CardHeader>
      <CardContent className="p-0 space-y-6">
        <div className="p-3 border border-yellow-100 rounded-lg bg-yellow-50 mb-4">
          <p className="text-sm text-gray-700">
            <strong>Nota:</strong> Estas configuraciones afectan únicamente a las reservas realizadas por los pacientes de forma online.
            El personal médico podrá seguir agendando tantos turnos o sobreturnos como sea necesario.
          </p>
        </div>

        {/* General Settings */}
        <ConsultationTypeCollapsible
          title="Configuración General"
          isOpen={openSections.generalSettings}
          onOpenChange={() => toggleSection('generalSettings')}
          summary={getGeneralSettingsSummary()}
          isComplete={true}
        >
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="w-48">Anticipación Máxima</Label>
              <div className="flex items-center gap-4">
                <Input
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  value={editedConfig.onlineBookingAdvanceDays}
                  onChange={(e) => {
                    const numericString = e.target.value.replace(/[^0-9]/g, "");
                    setEditedConfig({
                      ...editedConfig,
                      onlineBookingAdvanceDays: numericString === "" ? 0 : parseInt(numericString, 10)
                    });
                  }}
                  onClick={(e) => {
                    // When clicking on the field with value 0, select all text to make it easy to replace
                    if (editedConfig.onlineBookingAdvanceDays === 0) {
                      (e.target as HTMLInputElement).select();
                    }
                  }}
                  placeholder="0"
                  className="w-20"
                />
                <span className="text-gray-600">
                  {editedConfig.onlineBookingAdvanceDays === 1 ? "día" : "días"}
                </span>
              </div>
              <p className="text-sm text-gray-600">
                Con cuántos días de anticipación los pacientes pueden reservar turnos online.
              </p>
            </div>
            <div className="space-y-2">
              <Label className="w-48">Anticipación Mínima</Label>
              <div className="flex items-center gap-4">
                <Input
                  type="text"
                  inputMode="numeric"
                  pattern="[0-9]*"
                  value={editedConfig.onlineBookingMinHours}
                  onChange={(e) => {
                    const numericString = e.target.value.replace(/[^0-9]/g, "");
                    setEditedConfig({
                      ...editedConfig,
                      onlineBookingMinHours: numericString === "" ? 0 : parseInt(numericString, 10)
                    });
                  }}
                  onClick={(e) => {
                    // When clicking on the field with value 0, select all text to make it easy to replace
                    if (editedConfig.onlineBookingMinHours === 0) {
                      (e.target as HTMLInputElement).select();
                    }
                  }}
                  placeholder="0"
                  className="w-20"
                />
                <span className="text-gray-600">
                  {editedConfig.onlineBookingMinHours === 1 ? "hora" : "horas"}
                </span>
              </div>
              <p className="text-sm text-gray-600">Horas mínimas antes del turno para permitir reservas online.</p>
            </div>
          </div>
        </ConsultationTypeCollapsible>

        {/* Consecutive Booking Settings */}
        <ConsultationTypeCollapsible
          title="Configuración de Turnos Consecutivos"
          isOpen={openSections.consecutiveBookings}
          onOpenChange={() => toggleSection('consecutiveBookings')}
          summary={getConsecutiveBookingsSummary()}
          isComplete={true}
        >
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Switch
                checked={editedConfig.onlyConsecutiveBookings}
                onCheckedChange={toggleConsecutiveBookings}
              />
              <Label className="cursor-pointer">Sólo permitir reservas online de turnos consecutivos</Label>
            </div>

            {editedConfig.onlyConsecutiveBookings && (
              <div className="pl-10 space-y-2">
                <Label className="text-sm">Máximos turnos consecutivos visibles</Label>
                <Select
                  value={editedConfig.maxConsecutiveBookingsVisible?.toString() || "2"}
                  onValueChange={handleMaxConsecutiveBookingsChange}
                >
                  <SelectTrigger className="w-60">
                    <SelectValue placeholder="Seleccionar máximo" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[300px] overflow-y-auto">
                    {Array.from({ length: 48 }, (_, i) => i + 1).map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} {num === 1 ? 'turno' : 'turnos'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-600">
                  Define cuántos turnos consecutivos serán visibles para el paciente al realizar una reserva online.
                </p>
              </div>
            )}
          </div>
        </ConsultationTypeCollapsible>

        {/* Overbooking Configuration */}
        <ConsultationTypeCollapsible
          title="Configuración de Sobreturnos Online"
          isOpen={openSections.overbooking}
          onOpenChange={() => toggleSection('overbooking')}
          summary={getOverbookingSummary()}
          isComplete={true}
        >
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Configura cuántos sobreturnos podrán agendar los pacientes según el horario del día.
            </p>

            <div className="space-y-3">
              {DAYS.map(day => {
                const dayRanges = editedConfig.workingDays[day.id]?.hours || [];
                const hasWorkingHours = dayRanges.length > 0;

                return (
                  <div key={day.id} className={`space-y-2 ${!hasWorkingHours ? "opacity-50" : ""}`}>
                    <Label className="w-24 font-medium">{day.name}</Label>
                    {hasWorkingHours ? (
                      dayRanges.map((range, index) => {
                        // Find matching overbooking configuration for this specific day
                        const dayOverbookingRanges = editedConfig.overbookingByDay?.[day.id] || [];
                        const overbookingRange = dayOverbookingRanges.find(
                          r => r.start === range.start && r.end === range.end
                        );
                        const ratio = overbookingRange?.ratio || "none";

                        return (
                          <div key={index} className="flex items-center gap-2 pl-10">
                            <span className="min-w-24">{range.start} a {range.end}</span>
                            <Select
                              value={ratio}
                              onValueChange={(value) => {
                                // Find the index in the day-specific overbooking ranges
                                const dayRanges = editedConfig.overbookingByDay?.[day.id] || [];
                                const rangeIndex = dayRanges.findIndex(
                                  r => r.start === range.start && r.end === range.end
                                );

                                if (rangeIndex >= 0) {
                                  // Update existing range
                                  handleOverbookingRangeChange(day.id, rangeIndex, 'ratio', value);
                                } else {
                                  // Create new range for this day
                                  const newRanges = [...(editedConfig.overbookingByDay?.[day.id] || [])];
                                  newRanges.push({ start: range.start, end: range.end, ratio: value as OverbookingRatio });

                                  setEditedConfig(prev => {
                                    if (!prev) return null;
                                    return {
                                      ...prev,
                                      overbookingByDay: {
                                        ...(prev.overbookingByDay || {}),
                                        [day.id]: newRanges
                                      }
                                    };
                                  });
                                }
                              }}
                            >
                              <SelectTrigger className="ml-2 w-60">
                                <SelectValue placeholder="Seleccionar proporción" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Sin sobreturnos</SelectItem>
                                <SelectItem value="2per1">2 sobreturnos por cada turno</SelectItem>
                                <SelectItem value="1per1">1 sobreturno por cada turno</SelectItem>
                                <SelectItem value="1per2">1 sobreturno por cada 2 turnos</SelectItem>
                                <SelectItem value="1per3">1 sobreturno por cada 3 turnos</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        );
                      })
                    ) : (
                      <div className="pl-10 text-gray-500">
                        {hasWorkingHours ? "Sin configuración" : "Sin horarios definidos"}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </ConsultationTypeCollapsible>

        {/* Last-to-First Booking Settings */}
        <ConsultationTypeCollapsible
          title="Horarios Reservas de Último a Primero"
          isOpen={openSections.lastToFirst}
          onOpenChange={() => toggleSection('lastToFirst')}
          summary={getLastToFirstSummary()}
          isComplete={true}
        >
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Configurá en qué días los pacientes podrán agendar turnos de último a primero (el más tardío disponible). Sólo disponible cuando la opción de turnos consecutivos está activada.
            </p>

            <div className="space-y-3">
              {DAYS.map(day => {
                const dayRanges = editedConfig.lastToFirstRangesByDay?.[day.id] || [];
                const hasWorkingHours = editedConfig.workingDays[day.id]?.hours?.length > 0;

                return (
                  <div key={day.id} className={`space-y-2 ${!hasWorkingHours ? "opacity-50" : ""}`}>
                    <Label className="w-24 font-medium">{day.name}</Label>
                    {hasWorkingHours && dayRanges.length > 0 ? (
                      dayRanges.map((range: { start: string; end: string; enabled: boolean }, index: number) => (
                        <div key={index} className="flex items-center gap-2 pl-10">
                          <Switch
                            checked={range.enabled}
                            onCheckedChange={(checked) => toggleLastToFirstRange(day.id, index, checked)}
                            disabled={!hasWorkingHours}
                          />
                          <span>{range.start} a {range.end}</span>
                          {range.enabled &&
                            <span className="text-sm text-blue-700">Último a primero</span>
                          }
                        </div>
                      ))
                    ) : (
                      <div className="pl-10 text-gray-500">
                        {hasWorkingHours ? "Sin configuración" : "Sin horarios definidos"}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </ConsultationTypeCollapsible>
      </CardContent>
    </Card>
  );
}