// Auth0 route handler for Next.js App Router
import {NextRequest, NextResponse} from 'next/server';
import {UserRole} from '@/types/users';

// Helper function to get professional user ID from user data
function getProfessionalUserId(user: any): number | undefined {
    if (!user?.idFromRole) return undefined;

    // Handle both Map object and plain object formats
    if (user.idFromRole.get && typeof user.idFromRole.get === 'function') {
        return user.idFromRole.get(UserRole.PROFESSIONAL_USER);
    } else {
        return user.idFromRole[UserRole.PROFESSIONAL_USER];
    }
}

// Helper function to determine redirect URL based on user roles
function getRedirectUrlForUser(user: any, baseUrl: string): string {
    const roles = user?.roles || [];

    // No roles -> send to patient registration
    if (roles.length === 0) {
        return `${baseUrl}/plataforma/paciente/registro`;
    }

    const hasProfessional = roles.includes(UserRole.PROFESSIONAL_USER);
    const hasEmployee = roles.includes(UserRole.EMPLOYEE_USER);
    const hasTurnera = roles.includes(UserRole.TURNERA_USER);

    // TURNERA only -> main page
    if (hasTurnera && !hasProfessional && !hasEmployee) {
        return `${baseUrl}/`;
    }

    // PROFESSIONAL only -> [doctorId] page
    if (hasProfessional && !hasEmployee) {
        const doctorId = getProfessionalUserId(user);
        return doctorId ? `${baseUrl}/plataforma/profesional/${doctorId}` : `${baseUrl}/plataforma/user`;
    }

    // Any combination with EMPLOYEE -> establecimiento page
    if (hasEmployee) {
        return `${baseUrl}/plataforma/establecimiento`;
    }

    // Fallback to user page
    return `${baseUrl}/plataforma/user`;
}

export async function GET(request: NextRequest) {
    const {pathname, searchParams} = new URL(request.url);
    const authType = pathname.split('/').pop();

    switch (authType) {
        case 'login':
            // Redirect to Auth0 login
            const loginUrl = new URL(`${process.env.AUTH0_ISSUER_BASE_URL}/authorize`);
            loginUrl.searchParams.set('response_type', 'code');
            loginUrl.searchParams.set('client_id', process.env.AUTH0_CLIENT_ID || '');
            loginUrl.searchParams.set('redirect_uri', `${process.env.AUTH0_BASE_URL}/api/auth/callback`);
            loginUrl.searchParams.set('scope', 'openid profile email');
            loginUrl.searchParams.set('prompt', 'login');

            return NextResponse.redirect(loginUrl);

        case 'signup':
            // Redirect to Auth0 signup (Universal Login with signup screen)
            const signupUrl = new URL(`${process.env.AUTH0_ISSUER_BASE_URL}/authorize`);
            signupUrl.searchParams.set('response_type', 'code');
            signupUrl.searchParams.set('client_id', process.env.AUTH0_CLIENT_ID || '');
            signupUrl.searchParams.set('redirect_uri', `${process.env.AUTH0_BASE_URL}/api/auth/callback`);
            signupUrl.searchParams.set('scope', 'openid profile email');
            signupUrl.searchParams.set('screen_hint', 'signup');

            return NextResponse.redirect(signupUrl);

        case 'logout':
            // Build the Auth0 logout URL
            const logoutUrl = new URL(`${process.env.AUTH0_ISSUER_BASE_URL}/v2/logout`);
            logoutUrl.searchParams.set('client_id', process.env.AUTH0_CLIENT_ID || '');

            // Set returnTo to the main page - this is where Auth0 will redirect after logout
            const baseUrl = process.env.AUTH0_BASE_URL || new URL(request.url).origin;
            logoutUrl.searchParams.set('returnTo', baseUrl);

            console.log('Auth0 logout URL:', logoutUrl.toString());
            console.log('Return to URL:', baseUrl);

            // Create response that redirects to Auth0 logout and clears cookies
            const response = NextResponse.redirect(logoutUrl);

            // Clear all authentication cookies
            response.cookies.set('auth0_jwt', '', {
                expires: new Date(0),
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production'
            });
            response.cookies.set('auth0_access_token', '', {
                expires: new Date(0),
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production'
            });
            response.cookies.set('auth0_user', '', {
                expires: new Date(0),
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production'
            });
            response.cookies.set('user', '', {
                expires: new Date(0),
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production'
            });
            response.cookies.set('authenticatedUserId', '', {
                expires: new Date(0),
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production'
            });

            return response;

        case 'callback':
            // Handle Auth0 callback and exchange code for JWT
            const code = searchParams.get('code');
            const error = searchParams.get('error');

            if (error) {
                console.error('Auth0 callback error:', error);
                return NextResponse.redirect(new URL(`/plataforma/login?error=${encodeURIComponent(error)}`, request.url));
            }

            if (!code) {
                console.error('No authorization code received');
                return NextResponse.redirect(new URL('/plataforma/login?error=no_code', request.url));
            }

            try {
                // Exchange authorization code for tokens
                const tokenResponse = await fetch(`${process.env.AUTH0_ISSUER_BASE_URL}/oauth/token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        grant_type: 'authorization_code',
                        client_id: process.env.AUTH0_CLIENT_ID,
                        client_secret: process.env.AUTH0_CLIENT_SECRET,
                        code: code,
                        redirect_uri: `${process.env.AUTH0_BASE_URL}/api/auth/callback`,
                    }),
                });

                if (!tokenResponse.ok) {
                    const errorData = await tokenResponse.text();
                    console.error('Token exchange failed:', errorData);
                    return NextResponse.redirect(new URL('/plataforma/login?error=token_exchange_failed', request.url));
                }

                const tokens = await tokenResponse.json();
                const {access_token, id_token, refresh_token} = tokens;

                console.log('Received tokens:', {
                    access_token: access_token ? 'present' : 'missing',
                    id_token: id_token ? 'present' : 'missing',
                    refresh_token: refresh_token ? 'present' : 'missing'
                });

                // Get user info using the access token
                const userResponse = await fetch(`${process.env.AUTH0_ISSUER_BASE_URL}/userinfo`, {
                    headers: {
                        Authorization: `Bearer ${access_token}`,
                    },
                });

                if (!userResponse.ok) {
                    console.error('Failed to fetch user info');
                    return NextResponse.redirect(new URL('/plataforma/login?error=user_fetch_failed', request.url));
                }

                const user = await userResponse.json();

                // Extract Auth0 account ID (sub field)
                const auth0AccountId = user.sub;
                console.log('Auth0 Account ID:', auth0AccountId);

                // Fetch employee user data from the API
                let apiUserResponse = null;
                try {
                    const apiResponse = await fetch(`${process.env.BACKEND_URL}/user/${encodeURIComponent(auth0AccountId)}`, {
                        headers: {
                            'Authorization': `Bearer ${access_token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (apiResponse.ok) {
                        apiUserResponse = await apiResponse.json();
                        console.log('user data fetched:', apiUserResponse);
                    } else {
                        console.warn('user not found or error fetching data:', apiResponse.status);
                    }
                } catch (employeeError) {
                    console.error('Error fetching user:', employeeError);
                }

                const jwtToken = access_token;

                // Determine redirect URL based on user roles
                const baseUrl = process.env.AUTH0_BASE_URL || request.url.split('/api')[0];
                const redirectUrl = getRedirectUrlForUser(apiUserResponse, baseUrl);



                // Create response with HTML that sets localStorage and redirects
                // We use HTML response instead of server redirect to ensure localStorage is set
                const script = `
          <script>
            localStorage.setItem('auth0_jwt', '${jwtToken}');
            localStorage.setItem('auth0_access_token', '${access_token}');
            localStorage.setItem('auth0_user', '${JSON.stringify(user).replace(/'/g, "\\'")}');
            localStorage.setItem('user', '${JSON.stringify(apiUserResponse).replace(/'/g, "\\'")}');
            localStorage.setItem('auth0_authenticated', 'true');

            window.location.href = '${redirectUrl}';
          </script>
        `;

                const htmlResponse = new Response(`
          <!DOCTYPE html>
          <html lang="es">
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Iniciando sesión - Turnera</title>
              <style>
                * {
                  margin: 0;
                  padding: 0;
                  box-sizing: border-box;
                }
                
                body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  background: linear-gradient(to bottom, #eff6ff, #ffffff);
                  min-height: 100vh;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 24px;
                }
                
                .loading-container {
                  background: white;
                  border-radius: 16px;
                  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                  padding: 40px;
                  text-align: center;
                  max-width: 448px;
                  width: 100%;
                  border: 1px solid #e5e7eb;
                }
                
                .spinner-container {
                  width: 64px;
                  height: 64px;
                  background: #f1f5f9;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 0 auto 24px;
                }
                
                .spinner {
                  width: 32px;
                  height: 32px;
                  border: 4px solid #475569;
                  border-top: 4px solid transparent;
                  border-radius: 50%;
                  animation: spin 1s linear infinite;
                }
                
                @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
                }
                
                .loading-title {
                  font-size: 22px;
                  font-weight: 600;
                  color: #1e293b;
                  margin-bottom: 8px;
                }
                
                .loading-subtitle {
                  font-size: 16px;
                  color: #64748b;
                }
              </style>
            </head>
            <body>
              <div class="loading-container">
                <div class="spinner-container">
                  <div class="spinner"></div>
                </div>
                <h3 class="loading-title">Iniciando sesión</h3>
                <p class="loading-subtitle">Preparando tu cuenta...</p>
              </div>
              ${script}
            </body>
          </html>
        `, {
                    headers: {
                        'Content-Type': 'text/html; charset=utf-8',
                    },
                });

                // Set secure HTTP-only cookies for tokens
                htmlResponse.headers.append('Set-Cookie', `auth0_jwt=${jwtToken}; HttpOnly; Secure=${process.env.NODE_ENV === 'production'}; SameSite=Lax; Max-Age=${60 * 60 * 24 * 7}; Path=/`);
                htmlResponse.headers.append('Set-Cookie', `auth0_access_token=${access_token}; HttpOnly; Secure=${process.env.NODE_ENV === 'production'}; SameSite=Lax; Max-Age=${60 * 60 * 24 * 7}; Path=/`);
                htmlResponse.headers.append('Set-Cookie', `auth0_user=${encodeURIComponent(JSON.stringify(user))}; HttpOnly; Secure=${process.env.NODE_ENV === 'production'}; SameSite=Lax; Max-Age=${60 * 60 * 24 * 7}; Path=/`);
                htmlResponse.headers.append('Set-Cookie', `user=${encodeURIComponent(JSON.stringify(apiUserResponse))}; HttpOnly; Secure=${process.env.NODE_ENV === 'production'}; SameSite=Lax; Max-Age=${60 * 60 * 24 * 7}; Path=/`);

                return htmlResponse;

            } catch (error) {
                console.error('Callback processing error:', error);
                return NextResponse.redirect(new URL('/plataforma/login?error=callback_error', request.url));
            }

        default:
            return NextResponse.json({error: 'Invalid auth endpoint'}, {status: 404});
    }
}
