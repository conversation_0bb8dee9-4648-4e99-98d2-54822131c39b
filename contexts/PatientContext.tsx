"use client"

import React, {createContext, useContext, useEffect, useState} from "react"
import type {Patient} from "@/types/patient"
import type {Appointment} from "@/types/scheduler"
import {User, UserRole} from "@/types/users"
import {generatePatientId, generateUserId} from "@/utils/idGenerator"
import {sendPatientAssociationSMS} from "@/services/phone"
import {sendVerificationEmail} from "@/services/email"

type PatientContextType = {
    patients: Patient[]
    getPatientById: (patientId: string) => Patient | undefined
    getPatientByDni: (dni: string) => Patient | undefined
    getPatientByName: (name: string) => Patient | undefined
    getPatientByEmail: (email: string) => Patient | undefined
    getPatientByPhone: (phone: string) => Patient | undefined
    getPatientsByUserId: (userId: string) => Patient[] // Get all patients associated with a user
    getDefaultPatientByUserId: (userId: string) => Patient | undefined // Get the default patient for a user
    getPatientsByMedicalCenter: (medicalCenterId: string, appointments: { [date: string]: Appointment[] }) => Patient[] // Get patients created by or with appointments at a medical center
    addPatient: (patient: Patient) => string // Returns the patient ID
    updatePatient: (patientId: string, updatedPatient: Partial<Patient>) => void
    searchPatients: (query: string, medicalCenterId?: string, appointments?: {
        [date: string]: Appointment[]
    }) => Patient[] // Search by name or DNI, optionally filtered by medical center

    // User association functions
    createPatientUser: (patient: Patient, password: string) => Promise<{ userId: string, patientId: string }> // Create a new user for a patient
    createPatientForProfessional: (user: User) => string | null // Create a patient profile for a professional user
    associatePatientWithUser: (patientId: string, userId: string, isDefault?: boolean) => void // Associate a patient with an existing user
    sendPatientVerificationCode: (patientId: string) => Promise<boolean> // Send a verification code to the patient's phone
    verifyPatientPhone: (patientId: string, code: string) => Promise<boolean> // Verify a patient's phone number with a code
}

// Create the context
export const PatientContext = createContext<PatientContextType | undefined>(undefined)

// Custom hook to use the context
export const usePatients = () => {
    const context = useContext(PatientContext)
    if (!context) {
        throw new Error("usePatients must be used within a PatientProvider")
    }
    return context
}

// Provider component
export const PatientProvider: React.FC<{ children: React.ReactNode }> = ({children}) => {
    const [patients, setPatients] = useState<Patient[]>([])
    const [users, setUsers] = useState<User[]>([])

    // Load patients from localStorage on mount
    useEffect(() => {
        const storedPatients = localStorage.getItem("patients")
        console.log('Loading patients from localStorage')

        if (storedPatients) {
            try {
                const parsedPatients = JSON.parse(storedPatients)
                console.log('Loaded patients from localStorage:', parsedPatients.length)
                console.log('Sample patient:', parsedPatients.length > 0 ? parsedPatients[0] : 'No patients')
                setPatients(parsedPatients)
            } catch (error) {
                console.error("Error parsing patients from localStorage:", error)
            }
        } else {
            console.log('No patients found in localStorage')
        }

        // Load users from localStorage
        const storedUsers = localStorage.getItem("medical-scheduler-users")
        if (storedUsers) {
            try {
                const parsedUsers = JSON.parse(storedUsers)
                setUsers(parsedUsers)
            } catch (error) {
                console.error("Error parsing users from localStorage:", error)
            }
        }
    }, [])

    // Save patients to localStorage whenever they change
    useEffect(() => {
        if (patients.length > 0) {
            console.log('Saving patients to localStorage:', patients.length)
            localStorage.setItem("patients", JSON.stringify(patients))

            // Dispatch a custom event for other components that might be using their own patient state
            const event = new CustomEvent('patientsUpdated', {detail: {patients}})
            window.dispatchEvent(event)

            // Verify the save worked by reading it back
            const savedPatients = localStorage.getItem("patients")
            if (savedPatients) {
                const parsedPatients = JSON.parse(savedPatients)
                console.log('Verified patients in localStorage:', parsedPatients.length)
            } else {
                console.error('Failed to save patients to localStorage!')
            }
        }
    }, [patients])

    // Get patient by ID
    const getPatientById = (patientId: string): Patient | undefined => {
        if (!patientId) return undefined;
        return patients.find(p => p.id === patientId);
    }

    // Get patient by DNI
    const getPatientByDni = (dni: string): Patient | undefined => {
        if (!dni) return undefined;
        return patients.find(p => p.dni === dni);
    }

    // Get patient by name
    const getPatientByName = (name: string): Patient | undefined => {
        if (!name) return undefined;
        return patients.find(p => p.name === name);
    }

    // Get patient by email
    const getPatientByEmail = (email: string): Patient | undefined => {
        if (!email) return undefined;
        return patients.find(p => p.email === email);
    }

    // Get patient by phone
    const getPatientByPhone = (phone: string): Patient | undefined => {
        if (!phone) return undefined;
        return patients.find(p => p.phone === phone);
    }

    // Get patients by user ID
    const getPatientsByUserId = (userId: string): Patient[] => {
        if (!userId) return [];
        return patients.filter(p => p.userId === userId);
    }

    // Get default patient by user ID
    const getDefaultPatientByUserId = (userId: string): Patient | undefined => {
        if (!userId) return undefined;
        return patients.find(p => p.userId === userId && p.isDefault === true);
    }

    // Add or update a patient
    const addPatient = (patient: Patient): string => {
        console.log("PatientContext.addPatient called with:", {
            name: patient.name,
            phone: patient.phone,
            dni: patient.dni
        });

        // Validate patient object
        if (!patient.name || !patient.dni) {
            console.error("Invalid patient object - missing required fields:", patient)
            throw new Error("Invalid patient object - missing required fields")
        }

        // Ensure patient has an ID
        const patientId = patient.id || generatePatientId()
        console.log("Generated/using patient ID:", patientId);

        // Ensure coverage is always set
        const coverage = patient.coverage || "Sin Cobertura"

        // Create a complete patient object with all required fields
        const patientWithId = {
            ...patient,
            id: patientId,
            coverage: coverage,
            // Set defaultCoverage to coverage if it's not already set
            defaultCoverage: patient.defaultCoverage || coverage
        }

        console.log("Complete patient object to be added:", patientWithId);

        setPatients(prev => {
            console.log("Current patients count before adding:", prev.length);

            // Check if patient with same DNI already exists (DNI is the unique identifier for a person)
            const existingDniIndex = prev.findIndex(p => p.dni === patient.dni)

            if (existingDniIndex >= 0 && patient.id) {
                // Only update if this is an existing patient with an ID
                // Update existing patient, preserving the original ID
                console.log("Updating existing patient at index:", existingDniIndex);
                const updatedPatients = [...prev]
                updatedPatients[existingDniIndex] = {
                    ...updatedPatients[existingDniIndex],
                    ...patientWithId,
                    id: updatedPatients[existingDniIndex].id // Keep the original ID
                }
                return updatedPatients
            }

            // Add new patient (even if DNI already exists)
            console.log("Adding new patient to list");
            const newPatients = [...prev, patientWithId];
            console.log("New patients count after adding:", newPatients.length);
            return newPatients;
        })

        console.log("PatientContext.addPatient returning ID:", patientId);
        return patientId
    }

    // Update an existing patient
    const updatePatient = (patientId: string, updatedPatient: Partial<Patient>): void => {
        setPatients(prev => {
            const existingPatientIndex = prev.findIndex(p => p.id === patientId)

            if (existingPatientIndex === -1) {
                console.error(`Patient with ID ${patientId} not found`)
                return prev
            }

            const updatedPatients = [...prev]
            updatedPatients[existingPatientIndex] = {
                ...updatedPatients[existingPatientIndex],
                ...updatedPatient,
                id: patientId // Ensure ID doesn't change
            }

            console.log("Updated patient:", updatedPatients[existingPatientIndex])
            return updatedPatients
        })
    }

    // Get patients by medical center (created by or with appointments at)
    const getPatientsByMedicalCenter = (medicalCenterId: string, appointments: {
        [date: string]: Appointment[]
    }): Patient[] => {
        if (!medicalCenterId) return []

        // Create a set of patient IDs who have appointments at this center
        // Include all appointments, even cancelled ones
        const patientIdsWithAppointments = new Set<string>()
        Object.values(appointments).flat().forEach(apt => {
            if (apt.medicalCenterId === medicalCenterId) {
                patientIdsWithAppointments.add(apt.patient)
            }
        })

        // Return patients who were created by this medical center or have/had appointments at this center
        return patients.filter(patient =>
            patient.createdByMedicalCenterId === medicalCenterId ||
            patientIdsWithAppointments.has(patient.id || '')
        )
    }

    // Search patients by name or DNI, optionally filtered by medical center
    const searchPatients = (query: string, medicalCenterId?: string, appointments?: {
        [date: string]: Appointment[]
    }): Patient[] => {
        if (!query.trim()) return []

        const normalizedQuery = query.toLowerCase().trim()

        // First filter by search query
        const searchResults = patients.filter(patient =>
            patient.name.toLowerCase().includes(normalizedQuery) ||
            patient.dni.includes(normalizedQuery)
        )

        // If medicalCenterId is provided, further filter by medical center
        if (medicalCenterId && appointments) {
            // Get patients associated with this medical center
            const medicalCenterPatients = getPatientsByMedicalCenter(medicalCenterId, appointments)
            const medicalCenterPatientIds = new Set(medicalCenterPatients.map(p => p.id))

            // Return only patients that match both the search query and are associated with the medical center
            return searchResults.filter(patient => medicalCenterPatientIds.has(patient.id))
        }

        return searchResults
    }

    // Create a new user for a patient
    const createPatientUser = async (patient: Patient, password: string): Promise<{
        userId: string,
        patientId: string
    }> => {
        // Generate a verification code for email only if email is not already verified
        const emailVerificationCode = patient.emailVerified ? undefined : Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
        const verificationCodeExpiry = patient.emailVerified ? undefined : new Date(Date.now() + 10 * 60 * 1000).toISOString(); // 10 minutes from now

        // Create a new user with a formatted ID
        const userId = generateUserId();
        const newUser: User = {
            id: userId,
            name: patient.name,
            email: patient.email,
            password: password,
            roles: UserRole.PATIENT,
            medicalCenterId: "", // Patients don't have a medical center
            permissions: {...defaultPatientPermissions},
            phoneVerified: patient.phoneVerified || false, // Use the phoneVerified status from the patient
            emailVerified: patient.emailVerified || false, // Use the emailVerified status from the patient
            verificationCode: emailVerificationCode,
            verificationCodeExpiry: verificationCodeExpiry,
            defaultPatientId: patient.id // Set the default patient ID
        };

        // Update the patient with the user ID and set as default
        const patientId = patient.id || generatePatientId();
        const updatedPatient: Patient = {
            ...patient,
            id: patientId,
            userId: userId,
            isDefault: true
        };

        // Save the user
        const updatedUsers = [...users, newUser];
        setUsers(updatedUsers);
        localStorage.setItem("medical-scheduler-users", JSON.stringify(updatedUsers));

        // Save or update the patient
        if (patient.id) {
            updatePatient(patient.id, updatedPatient);
        } else {
            addPatient(updatedPatient);
        }

        // Send email verification code if email is provided and not already verified
        if (patient.email && !patient.emailVerified && emailVerificationCode) {
            await sendVerificationEmail(patient.email, patient.name, emailVerificationCode);
        }

        return {userId, patientId};
    };

    // Create a patient profile for a professional user
    const createPatientForProfessional = (user: User): string | null => {
        // Only create patient profiles for professionals who have phone and DNI
        if (!user.phone || !user.dni) {
            console.log(`Cannot create patient profile for user ${user.name}: missing phone or DNI`);
            return null;
        }

        // Check if a patient with this phone or DNI already exists
        const existingPatientByPhone = patients.find(p => p.phone === user.phone);
        const existingPatientByDni = patients.find(p => p.dni === user.dni);

        if (existingPatientByPhone || existingPatientByDni) {
            console.log(`Patient profile already exists for user ${user.name} (phone: ${user.phone}, DNI: ${user.dni})`);
            return existingPatientByPhone?.id || existingPatientByDni?.id || null;
        }

        // Create a new patient profile
        const newPatient: Patient = {
            name: user.name,
            dni: user.dni,
            phone: user.phone,
            email: user.email,
            coverage: "Sin Cobertura", // Default coverage, will be updated when they first login as patient
            defaultCoverage: "Sin Cobertura",
            userId: user.id,
            isDefault: true,
            phoneVerified: false, // Will be verified when they first login as patient
            emailVerified: user.emailVerified || false,
            createdByMedicalCenterId: "PROFESSIONAL_AUTO_CREATED", // Mark as auto-created for professional
            notes: `Auto-created patient profile for ${user.roles} user`
        };

        console.log(`Creating patient profile for professional user ${user.name} with phone: ${user.phone}, DNI: ${user.dni}`);

        // Add the patient and get the ID
        const patientId = addPatient(newPatient);
        console.log(`Patient ID returned from addPatient: ${patientId}`);

        // Force immediate save to localStorage to ensure it's available for login
        setTimeout(() => {
            const currentPatients = JSON.parse(localStorage.getItem("patients") || "[]");
            console.log(`Total patients in localStorage after creation: ${currentPatients.length}`);
            const patientExists = currentPatients.find((p: Patient) => p.id === patientId);
            if (patientExists) {
                console.log(`✅ Patient profile ${patientId} successfully saved to localStorage for user ${user.name}`);
                console.log(`Patient phone in localStorage: ${patientExists.phone}`);
            } else {
                console.error(`❌ Patient profile ${patientId} NOT found in localStorage for user ${user.name}`);
                console.error(`Available patient IDs in localStorage:`, currentPatients.map((p: Patient) => p.id));
            }
        }, 100);

        console.log(`Created patient profile ${patientId} for professional user ${user.name}`);
        return patientId;
    };

    // Associate a patient with an existing user
    const associatePatientWithUser = (patientId: string, userId: string, isDefault: boolean = false): void => {
        const patient = getPatientById(patientId);
        if (!patient) {
            throw new Error(`Patient with ID ${patientId} not found`);
        }

        const user = users.find(u => u.id === userId);
        if (!user) {
            throw new Error(`User with ID ${userId} not found`);
        }

        // Update the patient with the user ID
        updatePatient(patientId, {
            userId: userId,
            isDefault: isDefault
        });

        // If this is the default patient, update the user
        if (isDefault) {
            const updatedUser = {
                ...user,
                defaultPatientId: patientId
            };

            const updatedUsers = users.map(u => u.id === userId ? updatedUser : u);
            setUsers(updatedUsers);
            localStorage.setItem("medical-scheduler-users", JSON.stringify(updatedUsers));
        }
    };

    // Send a verification code to the patient's phone
    const sendPatientVerificationCode = async (patientId: string): Promise<boolean> => {
        const patient = getPatientById(patientId);
        if (!patient) {
            throw new Error(`Patient with ID ${patientId} not found`);
        }

        // Generate a verification code
        const verificationCode = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
        const verificationCodeExpiry = new Date(Date.now() + 10 * 60 * 1000).toISOString(); // 10 minutes from now

        // If the patient has a user, update the user with the verification code
        if (patient.userId) {
            const user = users.find(u => u.id === patient.userId);
            if (user) {
                const updatedUser = {
                    ...user,
                    verificationCode: verificationCode,
                    verificationCodeExpiry: verificationCodeExpiry
                };

                const updatedUsers = users.map(u => u.id === patient.userId ? updatedUser : u);
                setUsers(updatedUsers);
                localStorage.setItem("medical-scheduler-users", JSON.stringify(updatedUsers));
            }
        }

        // Send the verification SMS
        return await sendPatientAssociationSMS(patient.phone, patient.name, verificationCode);
    };

    // Verify a patient's phone number with a code
    const verifyPatientPhone = async (patientId: string, code: string): Promise<boolean> => {
        const patient = getPatientById(patientId);
        if (!patient) {
            throw new Error(`Patient with ID ${patientId} not found`);
        }

        // If the patient has a user, check the verification code
        if (patient.userId) {
            const user = users.find(u => u.id === patient.userId);
            if (user && user.verificationCode === code) {
                // Check if the code is expired
                if (user.verificationCodeExpiry) {
                    const expiryTime = new Date(user.verificationCodeExpiry).getTime();
                    if (Date.now() > expiryTime) {
                        return false; // Code expired
                    }
                }

                // Update the user with verified phone
                const updatedUser = {
                    ...user,
                    phoneVerified: true,
                    verificationCode: undefined,
                    verificationCodeExpiry: undefined
                };

                const updatedUsers = users.map(u => u.id === patient.userId ? updatedUser : u);
                setUsers(updatedUsers);
                localStorage.setItem("medical-scheduler-users", JSON.stringify(updatedUsers));

                return true;
            }
        }

        return false;
    };

    return (
        <PatientContext.Provider value={{
            patients,
            getPatientById,
            getPatientByDni,
            getPatientByName,
            getPatientByEmail,
            getPatientByPhone,
            getPatientsByUserId,
            getDefaultPatientByUserId,
            getPatientsByMedicalCenter,
            addPatient,
            updatePatient,
            searchPatients,
            createPatientUser,
            createPatientForProfessional,
            associatePatientWithUser,
            sendPatientVerificationCode,
            verifyPatientPhone
        }}>
            {children}
        </PatientContext.Provider>
    )
}
