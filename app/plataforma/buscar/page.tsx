"use client"

import Header from "@/components/landingpage/Header"
import UnifiedSearch from "@/components/landingpage/UnifiedSearch"
import Footer from "@/components/searchresults/Footer"

export default function SearchPage() {
  return (
    <div className="bg-blue-50 min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow relative">
        {/* Subtle background accent */}
        <div
          aria-hidden
          className="pointer-events-none absolute inset-0 -z-10"
          style={{
            background:
              "radial-gradient(60rem 60rem at 50% -10%, rgba(59,130,246,0.12), transparent 60%), radial-gradient(40rem 40rem at 90% 10%, rgba(59,130,246,0.08), transparent 60%)",
          }}
        />

        {/* Intro */}
        <section className="mt-8 md:mt-36 mb-6">
          <div className="max-w-6xl mx-auto px-10 md:px-6 text-center">
            <h1 className="text-4xl md:text-4xl font-normal font-recoleta tracking-tight -mb-3">
              Encontrá tu <span className="font-bold">próximo turno</span> médico:
            </h1>
          </div>
        </section>

        <UnifiedSearch />

        {/* Subtle guidance below search */}
        <div className="max-w-6xl mx-auto px-4 md:px-6 mt-1 mb-10">
          <p className="text-[12px] text-blue-900/60 text-center">
            Consejo: seleccioná tu cobertura y plan para ver resultados más relevantes.
          </p>
        </div>
      </main>
      <Footer />
    </div>
  )
}
