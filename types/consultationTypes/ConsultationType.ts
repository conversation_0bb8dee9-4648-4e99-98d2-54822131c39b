import {ConsultationHealthInsuranceExtraProperties} from './ConsultationHealthInsuranceExtraProperties';
import {ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";


export class AppointmentConsultationType {
    constructor(
        public consultationTypeId: number,
        public name: string,
    ) {
    }

    static fromJSON(json: Record<string, unknown>): AppointmentConsultationType {
        return new AppointmentConsultationType(
            json.consultationTypeId as number,
            json.name as string
        );
    }
}


export class ConsultationType {
    constructor(
        public consultationTypeId: number,
        public name: string,
        public price: number,
        public availableOnline: boolean,
        public acceptsSelfPaidPatient: boolean,
        public requiresMedicalOrder: boolean,
        public appointmentIntervalAmount: number,
        public instructions: string,
        public dailyLimit: number | null,
        public healthInsuranceExtraProperties: ConsultationHealthInsuranceExtraProperties[],
    ) {
    }

    static fromJSON(json: Record<string, unknown>): ConsultationType {
        const healthInsuranceExtraProperties = Array.isArray(json.healthInsuranceExtraProperties)
            ? (json.healthInsuranceExtraProperties as Record<string, unknown>[]).map((prop) => ConsultationHealthInsuranceExtraProperties.fromJSON(prop))
            : [];

        return new ConsultationType(
            json.consultationTypeId as number,
            json.name as string,
            json.price as number,
            json.availableOnline as boolean,
            json.acceptsSelfPaidPatient as boolean,
            json.requiresMedicalOrder as boolean,
            json.appointmentIntervalAmount as number,
            json.instructions as string,
            json.dailyLimit as number | null,
            healthInsuranceExtraProperties
        );
    }

    hasInstructions() {
        return this.instructions != null && this.instructions.trim().length > 0;
    }

    hasConsultationTypeInfo(healthInsuranceId: number | null): boolean {
        if (this.hasInstructions() || this.requiresMedicalOrder || !healthInsuranceId) {
            return true;
        }
        const healthInsuranceExtraProperty = this.healthInsuranceExtraProperties.find(
            (property) => property.healthInsuranceId === healthInsuranceId
        );
        return !!healthInsuranceExtraProperty;


    }

    getHealthInsurancePropertyForMaybeHealthInsuranceId(healthInsuranceId: number | null): ConsultationHealthInsuranceExtraProperties | null {
        if (!healthInsuranceId) {
            return null;
        }
        return this.healthInsuranceExtraProperties.find(
            (property) => property.healthInsuranceId === healthInsuranceId) || null;
    }

    toAppointmentConsultationType() {
        return new AppointmentConsultationType(this.consultationTypeId, this.name);
    }
}


export function getConsultationTypesInfo(types: AppointmentConsultationType[], healthInsuranceId: number | null, consultations: ConsultationType[]): ConsultationTypeInfo[] {
    const matchingConsultations = consultations.filter(consultation => types.some(consultationType => consultationType.consultationTypeId === consultation.consultationTypeId));
    return matchingConsultations.map(consultationType => {
        const healthInsuranceProperty = consultationType.getHealthInsurancePropertyForMaybeHealthInsuranceId(healthInsuranceId)
        const isExcluded = healthInsuranceProperty?.isExcluded || false;
        const copay = healthInsuranceProperty?.coPaymentPrice || null;
        return new ConsultationTypeInfo(consultationType.name,
            consultationType.requiresMedicalOrder,
            consultationType.hasInstructions() ? consultationType.instructions || "" : "",
            isExcluded,
            copay,
            consultationType.price || 0,
            consultationType.acceptsSelfPaidPatient
        );
    });
}