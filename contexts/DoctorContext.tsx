"use client"

import {createContext, ReactNode, useCallback, useContext, useEffect, useRef, useState} from "react"
import {CoverageEx<PERSON>, Doctor, DoctorConfiguration} from "@/types/doctor"
import {MedicalCenter} from "@/types/medical-center"
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import {useDoctors} from "@/hooks/useDoctors"
import {AppStateContext} from "./AppStateContext"

type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>

interface DoctorContextProps {
    doctors: Doctor[]
    setDoctors: React.Dispatch<React.SetStateAction<Doctor[]>>
    selectedDoctor: Doctor | null
    setSelectedDoctor: (doctor: Doctor | null) => void
    isDialogOpen: boolean
    setIsDialogOpen: (open: boolean) => void
    editedConfig: DoctorConfiguration | null
    setEditedConfig: React.Dispatch<React.SetStateAction<DoctorConfiguration | null>>
    addDoctor: (doctor: Omit<Doctor, "id">) => Doctor
    editDoctor: (doctor: Doctor) => void
    removeDoctor: (doctorId: string) => void
    handleSaveConfig: () => void
    loadDoctorById: (doctorId: string, medicalCenterId: string) => Promise<Doctor | null>
    refreshDoctorsFromStorage: () => void
    activeMedicalCenterId: string | null
    pendingDoctorCoverageExceptions: CoverageException[]
    setPendingDoctorCoverageExceptions: React.Dispatch<React.SetStateAction<CoverageException[]>>
}

export const DoctorContext = createContext<DoctorContextProps>({} as DoctorContextProps)

export function DoctorProvider({children}: { children: ReactNode }) {
    const {activeMedicalCenterId, setMedicalCenters} = useContext(MedicalCenterContext)
    const {setActiveConfigTab} = useContext(AppStateContext)
    const {
        doctors: initialDoctors,
        addDoctor: addDoctorHook,
        editDoctor: editDoctorHook,
        removeDoctor,
        refreshDoctors: refreshDoctorsHook
    } = useDoctors(activeMedicalCenterId || "")
    const [doctors, setDoctors] = useState<Doctor[]>([])

    const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null)
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [editedConfig, setEditedConfig] = useState<DoctorConfiguration | null>(null)
    const [pendingDoctorCoverageExceptions, setPendingDoctorCoverageExceptions] = useState<CoverageException[]>([])

    // Track if we're currently updating to prevent circular updates
    const isUpdatingRef = useRef(false)

    // Track if we're currently refreshing to prevent infinite loops
    const isRefreshingRef = useRef(false);

    // Function to refresh doctors from storage
    const refreshDoctorsFromStorage = useCallback(() => {
        // Prevent concurrent refreshes
        if (isRefreshingRef.current) {
            console.log("DoctorContext: Already refreshing, skipping")
            return;
        }

        console.log("DoctorContext: Refreshing doctors from storage")
        isRefreshingRef.current = true;

        try {
            if (activeMedicalCenterId) {
                refreshDoctorsHook();
            }
        } finally {
            // Reset the flag after a short delay to allow state updates to settle
            setTimeout(() => {
                isRefreshingRef.current = false;
            }, 100);
        }
    }, [activeMedicalCenterId, refreshDoctorsHook])

    // Sync doctors with useDoctors whenever initialDoctors changes
    useEffect(() => {
        // Skip if we're already in the middle of an update
        if (isUpdatingRef.current) return;

        // Check if the doctors array actually changed
        if (initialDoctors.length > 0) {
            console.log("DoctorProvider: Syncing doctors from useDoctors:", initialDoctors)
            isUpdatingRef.current = true;
            setDoctors(initialDoctors);

            // Reset the flag after a small delay to ensure all state updates complete
            setTimeout(() => {
                isUpdatingRef.current = false;
            }, 0);
        }
    }, [initialDoctors]);

    // Subscribe to storage events for real-time updates
    useEffect(() => {
        if (!activeMedicalCenterId) return;

        console.log(`DoctorContext: Subscribing to doctor updates for medical center ${activeMedicalCenterId}`);

        const unsubscribeDoctors = subscribeToStorageEvent(
            StorageEventType.DOCTORS_UPDATED,
            (newValue) => {
                // Only update if the event is for our current medical center
                if (newValue && newValue.medicalCenterId === activeMedicalCenterId) {
                    console.log('Doctors updated from another tab:', newValue.doctors);
                    if (!isUpdatingRef.current) {
                        isUpdatingRef.current = true;
                        setDoctors(newValue.doctors);
                        setTimeout(() => {
                            isUpdatingRef.current = false;
                        }, 0);
                    }
                }
            }
        );

        // Also subscribe to unassigned doctors updates if needed
        const unsubscribeUnassignedDoctors = subscribeToStorageEvent(
            StorageEventType.UNASSIGNED_DOCTORS_UPDATED,
            (newValue) => {
                // Only update if we're viewing unassigned doctors (no active medical center)
                if (!activeMedicalCenterId) {
                    console.log('Unassigned doctors updated from another tab:', newValue);
                    if (!isUpdatingRef.current) {
                        isUpdatingRef.current = true;
                        setDoctors(newValue);
                        setTimeout(() => {
                            isUpdatingRef.current = false;
                        }, 0);
                    }
                }
            }
        );

        // Cleanup subscriptions when component unmounts or medical center changes
        return () => {
            unsubscribeDoctors();
            unsubscribeUnassignedDoctors();
        };
    }, [activeMedicalCenterId]);

    const editDoctorContext = (doctor: Doctor) => {
        console.log("DoctorContext - editDoctor called with:", doctor.id)
        setSelectedDoctor(doctor)
        setEditedConfig({...doctor})

        // Initialize pending doctor coverage exceptions with current exceptions
        // We'll get the current exceptions from the CoverageContext in the component
        setIsDialogOpen(true)
        setActiveConfigTab("info")
    }

    const handleSaveConfig = () => {
        if (editedConfig && selectedDoctor && activeMedicalCenterId) {
            // Create a deep copy of the editedConfig to ensure all nested objects are properly saved
            const editedConfigCopy = JSON.parse(JSON.stringify(editedConfig));

            // Create the updated doctor by merging the selected doctor with the edited config
            const updatedDoctor: Doctor = {...selectedDoctor, ...editedConfigCopy}

            console.log("Saving Doctor:", updatedDoctor)

            // The CoverageContext will handle applying the pending exceptions

            // Save to storage using the hook
            editDoctorHook(updatedDoctor)

            // Update the local state
            setDoctors(prev => {
                const newDoctors = prev.map(d => (d.id === updatedDoctor.id ? updatedDoctor : d))
                console.log("Updated doctors in context:", newDoctors)
                return newDoctors
            })

            // Force a direct update to localStorage to ensure persistence
            try {
                // Get the current doctors from localStorage
                const storageKey = `${STORAGE_KEYS.DOCTORS_PREFIX}${activeMedicalCenterId}`;
                const doctorsJson = localStorage.getItem(storageKey);
                if (doctorsJson) {
                    const storedDoctors = JSON.parse(doctorsJson);
                    // Update the doctor in the array
                    const updatedDoctors = storedDoctors.map((d: Doctor) =>
                        d.id === updatedDoctor.id ? updatedDoctor : d
                    );
                    // Save back to localStorage
                    localStorage.setItem(storageKey, JSON.stringify(updatedDoctors));
                    console.log(`Directly updated doctor in localStorage for ${activeMedicalCenterId}`);

                    // Dispatch custom events to notify components that a doctor has been updated
                    if (typeof window !== 'undefined') {
                        // First, dispatch the standard doctor-updated event
                        const updateEvent = new CustomEvent('doctor-updated', {
                            detail: {
                                doctorId: updatedDoctor.id,
                                medicalCenterId: activeMedicalCenterId,
                                doctor: updatedDoctor
                            }
                        });
                        window.dispatchEvent(updateEvent);
                        console.log(`Dispatched doctor-updated event for doctor ${updatedDoctor.id}`);

                        // Then, dispatch a storage-changed event to notify other tabs
                        const storageEvent = new CustomEvent('storage-changed', {
                            detail: {
                                type: 'doctor-updated',
                                doctorId: updatedDoctor.id,
                                medicalCenterId: activeMedicalCenterId
                            }
                        });
                        window.dispatchEvent(storageEvent);
                        console.log(`Dispatched storage-changed event for doctor ${updatedDoctor.id}`);
                    }
                }
            } catch (error) {
                console.error("Error directly updating localStorage:", error);
            }

            // Close the dialog and reset state
            setIsDialogOpen(false)
            setSelectedDoctor(null)
            setEditedConfig(null)
        }
    }

    const handleAddDoctor = (newDoctor: Omit<Doctor, "id">) => {
        console.log("DoctorContext.handleAddDoctor: Adding doctor", newDoctor.name);
        console.log("DoctorContext.handleAddDoctor: Active medical center ID:", activeMedicalCenterId || "None");

        // Call the hook to add the doctor - will work even without an active medical center
        const doctor = addDoctorHook(newDoctor)
        console.log("DoctorContext.handleAddDoctor: Doctor created with ID:", doctor.id);

        // NOTE: We've removed automatic assignment to the active medical center
        // Doctors are now created as unassigned, and can be added to medical centers later
        console.log(`DoctorContext: Doctor ${doctor.id} created as unassigned`);

        // Make sure the doctor is added to our state
        setDoctors(prev => {
            // Check if this doctor already exists in our state
            if (prev.some(d => d.id === doctor.id)) {
                console.log(`DoctorContext: Doctor ${doctor.id} already in state, not adding again`);
                return prev; // No change needed
            }
            // Add the new doctor
            console.log(`DoctorContext: Adding doctor ${doctor.id} to state`);
            return [...prev, doctor];
        });

        return doctor
    }

    const handleRemoveDoctor = (doctorId: string) => {
        if (activeMedicalCenterId) {
            removeDoctor(doctorId)
            setMedicalCenters((prev: MedicalCenter[]) =>
                prev.map((mc: MedicalCenter) =>
                    mc.id === activeMedicalCenterId
                        ? {...mc, doctors: mc.doctors.filter((id: string) => id !== doctorId)}
                        : mc
                )
            )
            setDoctors(prev => {
                const newDoctors = prev.filter(d => d.id !== doctorId)
                console.log("Removed doctor, new doctors:", newDoctors)
                return newDoctors
            })
            setIsDialogOpen(false)
            setSelectedDoctor(null)
            setEditedConfig(null)
        }
    }

    const handleDialogClose = (open: boolean) => {
        setIsDialogOpen(open)
        if (!open) {
            console.log("DoctorContext - Dialog closing, resetting state")
            setSelectedDoctor(null)
            setEditedConfig(null)
            // Discard pending doctor coverage exceptions
            setPendingDoctorCoverageExceptions([])
            console.log("DoctorContext: Discarded pending doctor coverage exceptions")
        }
    }

    // Add a function to explicitly load a doctor by ID
    const loadDoctorById = async (doctorId: string, medicalCenterId: string): Promise<Doctor | null> => {
        console.log(`DoctorContext: Loading doctor ${doctorId} for medical center ${medicalCenterId}`);

        try {
            // Import storage

            // First check if the doctor exists in the current context state
            const existingDoctor = doctors.find(d => d.id === doctorId);
            if (existingDoctor) {
                console.log(`DoctorContext: Found doctor ${doctorId} in current context state`);
                return existingDoctor;
            }

            // Next, check if the doctor exists in storage for the specified medical center
            const storedDoctors = storage.getDoctors(medicalCenterId);
            const storedDoctor = storedDoctors.find(d => d.id === doctorId);

            if (storedDoctor) {
                console.log(`DoctorContext: Found doctor ${doctorId} in storage for center ${medicalCenterId}`);
                return storedDoctor;
            }

            // If not found in the specified medical center, check unassigned doctors
            const unassignedDoctors = storage.getUnassignedDoctors();
            console.log(`DoctorContext: Checking among ${unassignedDoctors.length} unassigned doctors`);

            const unassignedDoctor = unassignedDoctors.find(d => d.id === doctorId);
            if (unassignedDoctor) {
                console.log(`DoctorContext: Found doctor ${doctorId} in unassigned doctors`);

                // If doctor is in unassigned doctors, we should add it to the specified medical center
                console.log(`DoctorContext: Adding previously unassigned doctor ${doctorId} to medical center ${medicalCenterId}`);
                storage.addDoctorToMedicalCenter(doctorId, medicalCenterId);

                return unassignedDoctor;
            }

            // As a last resort, check all doctors in the system
            const allDoctors = storage.getAllDoctorsInSystem();
            console.log(`DoctorContext: Checking among ${allDoctors.length} total doctors in the system`);

            const doctorFromSystem = allDoctors.find(d => d.id === doctorId);
            if (doctorFromSystem) {
                console.log(`DoctorContext: Found doctor ${doctorId} in the overall system`);

                // Add doctor to the specified medical center
                storage.addDoctorToMedicalCenter(doctorId, medicalCenterId);

                return doctorFromSystem;
            }

            console.warn(`DoctorContext: Doctor ${doctorId} not found anywhere in the system`);
            return null;
        } catch (error) {
            console.error(`Error loading doctor ${doctorId}:`, error);
            return null;
        }
    };

    return (
        <DoctorContext.Provider
            value={{
                doctors,
                setDoctors,
                selectedDoctor,
                setSelectedDoctor,
                isDialogOpen,
                setIsDialogOpen: handleDialogClose,
                editedConfig,
                setEditedConfig,
                addDoctor: handleAddDoctor,
                editDoctor: editDoctorContext,
                removeDoctor: handleRemoveDoctor,
                handleSaveConfig,
                loadDoctorById,
                refreshDoctorsFromStorage,
                activeMedicalCenterId,
                pendingDoctorCoverageExceptions,
                setPendingDoctorCoverageExceptions,
            }}
        >
            {children}
        </DoctorContext.Provider>
    )
}