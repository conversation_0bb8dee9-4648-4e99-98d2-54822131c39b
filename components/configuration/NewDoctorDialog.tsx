"use client"

import {use<PERSON>ontext, useEffect, useRef, useState} from "react"
import {NewDoctorContext} from "@/contexts/NewDoctorContext"
import {Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle} from "@/components/ui/dialog"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Button} from "@/components/ui/button"
import {Badge} from "@/components/ui/badge"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Plus, Search, X, UserPlus, Check, Stethoscope, ChevronLeft, ChevronRight, User} from "lucide-react"
import {SPECIALTIES, Specialty} from "@/data/specialties"
import {toast} from "sonner"
import {PhoneInput} from "react-international-phone"
import "react-international-phone/style.css"
import "@/styles/phone-input.css"
import {getSpanishCountries} from "@/data/phoneCountries"
import {PhoneNumberUtil} from 'google-libphonenumber'
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import type { CreateProfessionalRequest, CreateProfessionalResponse } from "@/app/api/professional/route"
import type { CreateProfessionalMedicalCenterRelationshipRequest, CreateProfessionalMedicalCenterRelationshipResponse } from "@/app/api/professional-medical-center-relationship/route"
import type { CreateSpecialtyProfessionalMedicalCenterRelationshipRequest, CreateSpecialtyProfessionalMedicalCenterRelationshipResponse } from "@/app/api/specialty-professional-medical-center-relationship/route"

    // Initialize phone number utility
    const phoneUtil = PhoneNumberUtil.getInstance()

    // Shared styles aligned with platform/landing pages
    const inputClassNames =
        'rounded-xl border-[#e5e9f2] bg-[#f7f8fb] text-[#2d2f46] placeholder:text-slate-400 shadow-inner focus:border-blue-400 focus:ring-2 focus:ring-blue-100';
    const labelClassNames = 'text-slate-700 text-sm font-medium';

type Step = 1 | 2;

// Helper function to capitalize names properly
const capitalizeName = (name: string) => {
    return name
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(" ")
}

export default function NewDoctorDialog() {
    const {
        isNewDoctorDialogOpen,
        setIsNewDoctorDialogOpen,
        newDoctor,
        setNewDoctor,
        removeSpecialty,
    } = useContext(NewDoctorContext)

    const {activeMedicalCenterId} = useContext(MedicalCenterContext)

    const [currentStep, setCurrentStep] = useState<Step>(1)
    const [mnSearched, setMnSearched] = useState(false)
    const [searchResult, setSearchResult] = useState<"found" | "not-found" | "duplicate" | null>(null)
    const [specialtySearch, setSpecialtySearch] = useState("")
    const [isProcessing, setIsProcessing] = useState(false)
    const [phoneError, setPhoneError] = useState("")

    // Simplified search function - just marks as searched
    const handleSearchByMN = () => {
        if (!newDoctor.mn.trim()) {
            toast.error('Por favor ingrese la matrícula nacional');
            return;
        }
        setMnSearched(true);
        setSearchResult("not-found"); // For now, always treat as new professional
    }

    const handleMNChange = (value: string) => {
        setNewDoctor({...newDoctor, mn: value})
        setMnSearched(false)
        setSearchResult(null)
        setSpecialtySearch("")
    }

    useEffect(() => {
        if (isNewDoctorDialogOpen) {
            // Reset all dialog state when opening
            setNewDoctor({
                firstName: "",
                lastName: "",
                mn: "",
                specialties: [] as Specialty[],
                email: "",
                phone: "",
                dni: ""
            })
            setCurrentStep(1)
            setMnSearched(false)
            setSearchResult(null)
            setSpecialtySearch("")
            setIsProcessing(false)
            setPhoneError("")
        }
    }, [isNewDoctorDialogOpen, setNewDoctor])

    const handleSpecialtyToggle = (specialty: string) => {
        const isSelected = newDoctor.specialties.includes(specialty as Specialty)
        if (isSelected) {
            // Remove specialty
            setNewDoctor({
                ...newDoctor,
                specialties: newDoctor.specialties.filter(s => s !== specialty)
            })
        } else {
            // Add specialty
            setNewDoctor({
                ...newDoctor,
                specialties: [...newDoctor.specialties, specialty as Specialty]
            })
        }
    }

    // Filter specialties based on search
    const filteredSpecialties = SPECIALTIES.filter(specialty =>
        specialty.toLowerCase().includes(specialtySearch.toLowerCase())
    )

    if (!newDoctor) {
        return null
    }

    // Validation helpers
    const isMNValid = (): boolean => newDoctor.mn.trim().length > 0;
    const isNameValid = (): boolean => newDoctor.firstName.trim().length >= 2;
    const isLastNameValid = (): boolean => newDoctor.lastName.trim().length >= 2;
    const isEmailValid = (): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return newDoctor.email.trim().length > 0 && emailRegex.test(newDoctor.email);
    };
    const isSpecialtiesValid = (): boolean => newDoctor.specialties.length > 0;

    // Step validation
    const isStepValid = (step: Step): boolean => {
        switch (step) {
            case 1:
                return mnSearched && isNameValid() && isLastNameValid() && isEmailValid();
            case 2:
                return isSpecialtiesValid();
            default:
                return false;
        }
    };

    const validateStep = (step: Step): boolean => {
        switch (step) {
            case 1:
                if (!mnSearched) {
                    toast.error('Por favor busque por M.N. primero');
                    return false;
                }
                if (!isNameValid()) {
                    toast.error('El nombre es requerido (mínimo 2 caracteres)');
                    return false;
                }
                if (!isLastNameValid()) {
                    toast.error('El apellido es requerido (mínimo 2 caracteres)');
                    return false;
                }
                if (!isEmailValid()) {
                    toast.error('El email es requerido y debe tener un formato válido');
                    return false;
                }
                return true;
            case 2:
                if (!isSpecialtiesValid()) {
                    toast.error('Debe seleccionar al menos una especialidad');
                    return false;
                }
                return true;
            default:
                return false;
        }
    };

    const handleNext = () => {
        if (validateStep(currentStep)) {
            setCurrentStep(2);
        }
    };

    const handlePrevious = () => {
        setCurrentStep(1);
    };

    const handleClose = () => {
        if (!isProcessing) {
            setCurrentStep(1);
            setIsNewDoctorDialogOpen(false);
        }
    };

    // Since we're now treating all as new professionals, no duplicates

    const handleCreateNewDoctor = async () => {
        if (!activeMedicalCenterId || isProcessing) {
            return;
        }

        setIsProcessing(true);
        console.log("Processing doctor creation...");

        try {
            // Step 1: Create the professional using the /professional endpoint
            const professionalRequest: CreateProfessionalRequest = {
                name: capitalizeName(newDoctor.firstName.trim()),
                surname: capitalizeName(newDoctor.lastName.trim()),
                identificationNumber: newDoctor.dni || "",
                medicalLicense: newDoctor.mn,
                createdBy: 1 // TODO: Get actual user ID from auth context
            };

            console.log("Creating professional with data:", professionalRequest);

            const professionalResponse = await fetch('/api/professional', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(professionalRequest),
            });

            if (!professionalResponse.ok) {
                const errorData = await professionalResponse.json();
                throw new Error(errorData.error || 'Failed to create professional');
            }

            const professionalResult: CreateProfessionalResponse = await professionalResponse.json();
            const professionalId = professionalResult.id;

            console.log(`Professional created with ID: ${professionalId}`);

            // Step 2: Add the professional to the medical center
            const relationshipRequest: CreateProfessionalMedicalCenterRelationshipRequest = {
                email: newDoctor.email,
                overlappedAppointmentLimit: "ONE_PER_ONE",
                maximumAnticipationAppointmentTimeLimit: "60", // 60 days
                minimumAnticipationAppointmentTimeLimit: "2", // 2 hours
                appointmentIntervalTime: "15", // 15 minutes
                medicalCenterId: Number(activeMedicalCenterId),
                createdById: 1, // TODO: Get actual user ID from auth context
            };

            console.log("Adding professional to medical center with data:", relationshipRequest);

            const relationshipResponse = await fetch('/api/professional-medical-center-relationship', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(relationshipRequest),
            });

            if (!relationshipResponse.ok) {
                const errorData = await relationshipResponse.json();
                throw new Error(errorData.error || 'Failed to add professional to medical center');
            }

            const relationshipResult: CreateProfessionalMedicalCenterRelationshipResponse = await relationshipResponse.json();

            console.log(`Professional added to medical center:`, relationshipResult);

            // Step 3: Create specialty relationships for each selected specialty
            if (newDoctor.specialties.length > 0) {
                console.log(`Creating specialty relationships for ${newDoctor.specialties.length} specialties`);

                const specialtyPromises = newDoctor.specialties.map(async (specialty) => {
                    const specialtyRequest: CreateSpecialtyProfessionalMedicalCenterRelationshipRequest = {
                        specialtyName: specialty,
                        professionalId: professionalId,
                        medicalCenterId: Number(activeMedicalCenterId),
                        createdById: 1 // TODO: Get actual user ID from auth context
                    };

                    console.log(`Creating specialty relationship for ${specialty}:`, specialtyRequest);

                    const specialtyResponse = await fetch('/api/specialty-professional-medical-center-relationship', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(specialtyRequest),
                    });

                    if (!specialtyResponse.ok) {
                        const errorData = await specialtyResponse.json();
                        console.error(`Failed to create specialty relationship for ${specialty}:`, errorData);
                        // Don't throw here to avoid failing the entire process for one specialty
                        return { specialty, success: false, error: errorData.error };
                    }

                    const specialtyResult: CreateSpecialtyProfessionalMedicalCenterRelationshipResponse = await specialtyResponse.json();
                    console.log(`Specialty relationship created for ${specialty}:`, specialtyResult);
                    return { specialty, success: true, result: specialtyResult };
                });

                const specialtyResults = await Promise.all(specialtyPromises);
                const successfulSpecialties = specialtyResults.filter(r => r.success);
                const failedSpecialties = specialtyResults.filter(r => !r.success);

                if (failedSpecialties.length > 0) {
                    console.warn(`Failed to create ${failedSpecialties.length} specialty relationships:`, failedSpecialties);
                    toast.warning(`Profesional creado pero ${failedSpecialties.length} especialidades no se pudieron asociar`);
                } else {
                    console.log(`All ${successfulSpecialties.length} specialty relationships created successfully`);
                }
            }

            // Success - show message and close dialog
            toast.success('Profesional creado y agregado al establecimiento exitosamente');

            // Reset form and close dialog
            setNewDoctor({
                firstName: "",
                lastName: "",
                mn: "",
                specialties: [] as Specialty[],
                email: "",
                phone: "",
                dni: ""
            });
            setCurrentStep(1);
            setMnSearched(false);
            setSearchResult(null);
            setSpecialtySearch("");

            // Dispatch success event
            if (typeof window !== 'undefined') {
                const savedEvent = new CustomEvent('doctor-config-saved', {
                    detail: {
                        professionalId: professionalId,
                        medicalCenterId: activeMedicalCenterId,
                        timestamp: Date.now()
                    }
                });
                window.dispatchEvent(savedEvent);

                // Show loading overlay and refresh
                const loadingEvent = new CustomEvent('show-loading-overlay');
                window.dispatchEvent(loadingEvent);

                setTimeout(() => {
                    window.location.reload();
                }, 100);
            }

            setIsNewDoctorDialogOpen(false);

        } catch (error) {
            console.error("Error creating/adding professional:", error);
            toast.error(error instanceof Error ? error.message : 'Error al crear el profesional');
        } finally {
            setIsProcessing(false);
        }
    };

    // Step Components
    const renderStep1 = () => (
        <div className="space-y-4 md:space-y-6">
            <div className="text-center space-y-2">
                <div className="flex items-center justify-center gap-2 md:gap-3">
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/20 border border-blue-300">
                        <User className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />
                    </div>
                    <h3 className="text-lg md:text-xl font-semibold text-slate-700">Información del Profesional</h3>
                </div>
                <p className="text-sm md:text-base text-slate-500">Complete los datos básicos del profesional.</p>
            </div>

            <div className="space-y-4 md:space-y-6 max-w-2xl mx-auto">
                <div className="flex gap-3 sm:gap-4 items-end">
                    <div className="flex-1 space-y-2">
                        <Label htmlFor="mn" className={`${labelClassNames} flex items-center gap-2`}>
                            M.N. (Matrícula Nacional)
                            <span className="text-red-500">*</span>
                            {isMNValid() && <Check className="w-4 h-4 text-blue-600" />}
                        </Label>
                        <Input
                            id="mn"
                            value={newDoctor.mn}
                            onChange={(e) => {
                                // Only allow numbers
                                const numericValue = e.target.value.replace(/[^0-9]/g, '');
                                handleMNChange(numericValue);
                            }}
                            placeholder="Ej: 12345"
                            className={`${inputClassNames} ${isMNValid() ? 'border-blue-400 focus:border-blue-500 shadow-inner' : ''}`}
                            pattern="[0-9]*"
                            inputMode="numeric"
                        />
                    </div>
                    <Button
                        type="button"
                        onClick={handleSearchByMN}
                        className="rounded-xl bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 sm:py-2 whitespace-nowrap"
                    >
                        <Search className="h-4 w-4 mr-2"/>
                        Buscar
                    </Button>
                </div>

                {searchResult === "found" && (
                    <div className="bg-green-50 border border-green-200 text-green-700 p-3 rounded-xl text-sm">
                        Profesional encontrado. Seleccione las especialidades que ejercerá en este establecimiento.
                    </div>
                )}
                {searchResult === "not-found" && (
                    <div className="bg-amber-50 border border-amber-200 text-amber-700 p-3 rounded-xl text-sm">
                        Profesional no encontrado. Complete los datos para crearlo.
                    </div>
                )}

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="firstName" className={`${labelClassNames} flex items-center gap-2`}>
                            Nombre
                            <span className="text-red-500">*</span>
                            {isNameValid() && <Check className="w-4 h-4 text-blue-600" />}
                        </Label>
                        <Input
                            id="firstName"
                            value={newDoctor.firstName || ""}
                            onChange={(e) => setNewDoctor({
                                ...newDoctor,
                                firstName: capitalizeName(e.target.value)
                            })}
                            placeholder="Ej: Juan"
                            className={`${inputClassNames} ${isNameValid() ? 'border-blue-400 focus:border-blue-500 shadow-inner' : ''}`}
                            disabled={!mnSearched}
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="lastName" className={`${labelClassNames} flex items-center gap-2`}>
                            Apellido
                            <span className="text-red-500">*</span>
                            {isLastNameValid() && <Check className="w-4 h-4 text-blue-600" />}
                        </Label>
                        <Input
                            id="lastName"
                            value={newDoctor.lastName || ""}
                            onChange={(e) => setNewDoctor({...newDoctor, lastName: capitalizeName(e.target.value)})}
                            placeholder="Ej: Pérez"
                            className={`${inputClassNames} ${isLastNameValid() ? 'border-blue-400 focus:border-blue-500 shadow-inner' : ''}`}
                            disabled={!mnSearched}
                        />
                    </div>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="email" className={`${labelClassNames} flex items-center gap-2`}>
                        Email
                        <span className="text-red-500">*</span>
                        {isEmailValid() && <Check className="w-4 h-4 text-blue-600" />}
                    </Label>
                    <Input
                        id="email"
                        type="email"
                        value={newDoctor.email}
                        onChange={(e) => setNewDoctor({...newDoctor, email: e.target.value})}
                        placeholder="Ej: <EMAIL>"
                        className={`${inputClassNames} ${isEmailValid() ? 'border-blue-400 focus:border-blue-500 shadow-inner' : ''}`}
                        disabled={!mnSearched}
                    />
                </div>

                {/* Phone and DNI - Stack vertically on mobile, side by side on desktop */}
                <div className="space-y-4 md:space-y-0 md:grid md:grid-cols-2 md:gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="phone" className={labelClassNames}>Teléfono (Opcional)</Label>
                        <div className="custom-phone-input">
                            <PhoneInput
                                defaultCountry="ar"
                                value={newDoctor.phone}
                                onChange={(phone) => {
                                    setNewDoctor({...newDoctor, phone});
                                    setPhoneError("");

                                    try {
                                        // Parse the phone number using Google's libphonenumber
                                        const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                                        const isValid = phoneUtil.isValidNumber(phoneNumber);

                                        if (!isValid && phone !== "+" && phone.length > 5) {
                                            setPhoneError("El número de teléfono no es válido");
                                        }
                                    } catch {
                                        if (phone !== "+" && phone.length > 5) {
                                            setPhoneError("El número de teléfono no es válido");
                                        }
                                    }
                                }}
                                inputStyle={{
                                    width: '100%',
                                    height: '2.5rem',
                                    borderRadius: '0 0.75rem 0.75rem 0',
                                    border: '1px solid #e5e9f2',
                                    borderLeft: 'none',
                                    backgroundColor: '#f7f8fb',
                                    color: '#2d2f46',
                                    fontSize: '0.875rem',
                                    padding: '0.5rem 0.75rem',
                                    boxShadow: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
                                    opacity: !mnSearched ? '0.5' : '0.9',
                                    pointerEvents: !mnSearched ? 'none' : 'auto'
                                }}
                                className={`w-full custom-phone-input unified-phone-input ${!mnSearched ? 'disabled' : ''}`}
                                placeholder="Teléfono"
                                countrySelectorStyleProps={{
                                    buttonStyle: {
                                        height: '2.5rem',
                                        borderRadius: '0.75rem 0 0 0.75rem',
                                        border: '1px solid #e5e9f2',
                                        borderRight: 'none',
                                        backgroundColor: '#f7f8fb',
                                        color: '#2d2f46',
                                        fontSize: '0.875rem',
                                        padding: '0.5rem 0.75rem',
                                        boxShadow: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
                                        opacity: !mnSearched ? '0.5' : '0.9',
                                        pointerEvents: !mnSearched ? 'none' : 'auto',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '0.5rem'
                                    }
                                }}
                                hideDropdown={false}
                                disableDialCodeAndPrefix={false}
                                showDisabledDialCodeAndPrefix={false}
                                disableFormatting={false}
                                preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                                countries={getSpanishCountries()}
                                disabled={!mnSearched}
                                // Prevent direct editing of country code part
                                inputProps={{
                                    onKeyDown: (e) => {
                                        // Allow normal typing but prevent backspace/delete when cursor is in country code area
                                        const input = e.target as HTMLInputElement;
                                        const cursorPosition = input.selectionStart || 0;
                                        const countryCodeLength = newDoctor.phone.match(/^\+\d+/)?.[0]?.length || 0;

                                        if (cursorPosition <= countryCodeLength && (e.key === 'Backspace' || e.key === 'Delete')) {
                                            e.preventDefault();
                                        }
                                    },
                                    onFocus: (e) => {
                                        // When focusing, place cursor after country code
                                        const input = e.target as HTMLInputElement;
                                        const countryCodeLength = newDoctor.phone.match(/^\+\d+/)?.[0]?.length || 0;
                                        if (input.selectionStart && input.selectionStart < countryCodeLength) {
                                            setTimeout(() => {
                                                input.setSelectionRange(countryCodeLength, countryCodeLength);
                                            }, 0);
                                        }
                                    }
                                }}
                            />
                            {phoneError && (
                                <div className="text-xs text-red-600 mt-1 bg-red-50 border border-red-200 rounded-lg p-2">
                                    {phoneError}
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="dni" className={labelClassNames}>DNI (Opcional)</Label>
                        <Input
                            id="dni"
                            type="text"
                            value={newDoctor.dni}
                            onChange={(e) => setNewDoctor({...newDoctor, dni: e.target.value})}
                            placeholder="Ej: 12345678"
                            className={inputClassNames}
                            disabled={!mnSearched}
                        />
                    </div>
                </div>
            </div>
        </div>
    );

    const renderStep2 = () => (
        <div className="space-y-4 md:space-y-6">
            <div className="text-center space-y-2">
                <div className="flex items-center justify-center gap-2 md:gap-3">
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center shadow-lg shadow-blue-500/20 border border-blue-300">
                        <Stethoscope className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />
                    </div>
                    <h3 className="text-lg md:text-xl font-semibold text-slate-700">Especialidades</h3>
                </div>
                <p className="text-sm md:text-base text-slate-500">Seleccione las especialidades que ejercerá en este establecimiento.</p>
            </div>

            <div className="space-y-4 md:space-y-6 max-w-4xl mx-auto">
                {/* Search Input */}
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <Input
                        placeholder="Buscar especialidades..."
                        value={specialtySearch}
                        onChange={(e) => setSpecialtySearch(e.target.value)}
                        className="pl-10 pr-10 rounded-xl border-slate-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-100"
                    />
                    {specialtySearch && (
                        <button
                            onClick={() => setSpecialtySearch("")}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 hover:text-slate-600 transition-colors"
                        >
                            <X className="h-4 w-4" />
                        </button>
                    )}
                </div>

                {/* Specialties Selection Area */}
                <div className="bg-white border border-slate-200 rounded-xl shadow-sm overflow-hidden">
                    <div className="max-h-48 md:max-h-64 overflow-y-auto">
                        {filteredSpecialties.length === 0 ? (
                            <div className="text-center py-6 md:py-8 text-slate-500">
                                <Search className="h-6 w-6 md:h-8 md:w-8 mx-auto mb-2 opacity-50" />
                                <p className="text-sm md:text-base">No se encontraron especialidades</p>
                                <p className="text-xs md:text-sm">Intente con otros términos de búsqueda</p>
                            </div>
                        ) : (
                            <div className="p-3 md:p-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {filteredSpecialties.map((specialty) => {
                                        const isSelected = newDoctor.specialties.includes(specialty)
                                        return (
                                            <div
                                                key={specialty}
                                                onClick={() => handleSpecialtyToggle(specialty)}
                                                className={`group relative flex items-center p-2.5 md:p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                                                    isSelected
                                                        ? 'bg-blue-50 border-blue-300 hover:bg-blue-100'
                                                        : 'bg-white border-slate-200 hover:border-slate-300 hover:bg-slate-50'
                                                }`}
                                            >
                                                <div className={`flex-shrink-0 w-4 h-4 md:w-5 md:h-5 rounded border-2 flex items-center justify-center mr-2 md:mr-3 transition-colors ${
                                                    isSelected
                                                        ? 'bg-blue-600 border-blue-600'
                                                        : 'border-slate-300 group-hover:border-slate-400'
                                                }`}>
                                                    {isSelected && (
                                                        <Check className="h-2.5 w-2.5 md:h-3 md:w-3 text-white" />
                                                    )}
                                                </div>
                                                <span className={`text-sm font-medium ${
                                                    isSelected ? 'text-blue-900' : 'text-slate-700'
                                                }`}>
                                                    {specialty}
                                                </span>
                                            </div>
                                        )
                                    })}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Selected Specialties Summary */}
                {newDoctor.specialties.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-3 md:p-4">
                        <div className="flex items-center justify-between mb-3">
                            <h4 className="text-xs md:text-sm font-medium text-blue-900 flex items-center gap-2">
                                Especialidades Seleccionadas ({newDoctor.specialties.length})
                                {isSpecialtiesValid() && <Check className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />}
                            </h4>
                            <Button
                                onClick={() => setNewDoctor({...newDoctor, specialties: []})}
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50 text-xs md:text-sm"
                            >
                                <X className="h-3 w-3 mr-1" />
                                Limpiar todas
                            </Button>
                        </div>
                        <div className="flex flex-wrap gap-1.5 md:gap-2">
                            {newDoctor.specialties.map((specialty) => (
                                <Badge
                                    key={specialty}
                                    className="px-2 md:px-3 py-1 md:py-1.5 bg-white text-blue-700 hover:bg-blue-50 border border-blue-200 rounded-lg flex items-center gap-1 md:gap-1.5 shadow-sm text-xs md:text-sm"
                                >
                                    <Stethoscope className="h-2.5 w-2.5 md:h-3 md:w-3" />
                                    <span>{specialty}</span>
                                    <button
                                        onClick={() => handleSpecialtyToggle(specialty)}
                                        className="ml-1 text-blue-500 hover:text-blue-700 rounded-full w-3 h-3 md:w-4 md:h-4 inline-flex items-center justify-center hover:bg-blue-100 transition-colors"
                                    >
                                        <X className="h-2 w-2 md:h-2.5 md:w-2.5" />
                                    </button>
                                </Badge>
                            ))}
                        </div>
                    </div>
                )}

                {/* Empty State */}
                {newDoctor.specialties.length === 0 && (
                    <div className="bg-slate-50 border border-slate-200 rounded-xl p-4 text-center">
                        <Stethoscope className="h-6 w-6 md:h-8 md:w-8 mx-auto mb-2 text-slate-400" />
                        <p className="text-sm md:text-base text-slate-600">No hay especialidades seleccionadas</p>
                        <p className="text-xs md:text-sm text-slate-500">Seleccione las especialidades de la lista arriba</p>
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <Dialog open={isNewDoctorDialogOpen} onOpenChange={handleClose}>
            <DialogContent className="w-[95vw] md:w-[92vw] max-w-4xl max-h-[95vh] md:max-h-[90vh] overflow-y-auto p-4 md:p-6 lg:p-8 rounded-xl md:rounded-2xl border border-slate-200 shadow-2xl shadow-slate-400/20 bg-white">
                <DialogHeader>
                    {/* Step Progress Indicator */}
                    <div className="flex items-center justify-center space-x-2 md:space-x-4 mb-4">
                        {[1, 2].map((step) => (
                            <div key={step} className="flex items-center">
                                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-full flex items-center justify-center text-xs md:text-sm font-semibold transition-all duration-300 ${
                                    step === currentStep
                                        ? 'bg-gradient-to-br from-blue-500 to-sky-600 text-white shadow-lg shadow-blue-500/30 border border-blue-400'
                                        : step < currentStep
                                            ? 'bg-gradient-to-br from-slate-100 to-slate-200 text-slate-700 shadow-md shadow-slate-400/20 border border-slate-300'
                                            : 'bg-gradient-to-br from-slate-50 to-slate-100 text-slate-400 shadow-sm shadow-slate-300/20 border border-slate-200'
                                }`}>
                                    {step < currentStep ? <Check className="w-3 h-3 md:w-4 md:h-4" /> : step}
                                </div>
                                {step < 2 && (
                                    <div className={`w-8 md:w-16 h-0.5 mx-2 md:mx-3 rounded-full transition-all duration-300 ${
                                        step < currentStep
                                            ? 'bg-gradient-to-r from-slate-300 to-slate-400 shadow-sm'
                                            : 'bg-gradient-to-r from-slate-200 to-slate-300'
                                    }`} />
                                )}
                            </div>
                        ))}
                    </div>
                </DialogHeader>

                {/* Step Content */}
                <div className="py-4 md:py-6">
                    {currentStep === 1 && renderStep1()}
                    {currentStep === 2 && renderStep2()}
                </div>

                {/* Navigation Buttons */}
                <div className="flex flex-col gap-3 pt-4 md:pt-6 border-t border-slate-200">
                    {/* Anterior button - always on top when visible */}
                    {currentStep > 1 && (
                        <div className="flex justify-start">
                            <Button
                                variant="outline"
                                onClick={handlePrevious}
                                disabled={isProcessing}
                                className="w-full sm:w-auto rounded-xl text-slate-600 border-slate-300 hover:border-slate-400 hover:bg-slate-50"
                            >
                                <ChevronLeft className="w-4 h-4 mr-2" />
                                Anterior
                            </Button>
                        </div>
                    )}

                    {/* Cancelar and Action buttons - side by side */}
                    <div className="flex gap-2 sm:gap-3">
                        <Button
                            variant="outline"
                            onClick={handleClose}
                            disabled={isProcessing}
                            className="flex-1 sm:flex-none rounded-xl text-blue-600 border-blue-300 hover:border-blue-600 hover:bg-blue-50"
                        >
                            Cancelar
                        </Button>

                        {currentStep < 2 ? (
                            <Button
                                onClick={handleNext}
                                disabled={isProcessing || !isStepValid(currentStep)}
                                className="flex-1 sm:flex-none rounded-xl bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Seleccionar especialidades
                                <ChevronRight className="w-4 h-4 ml-2" />
                            </Button>
                        ) : (
                            <Button
                                onClick={handleCreateNewDoctor}
                                disabled={isProcessing || !isStepValid(currentStep)}
                                className="flex-1 sm:flex-none rounded-xl bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isProcessing ? 'Procesando...' : (
                                    <>
                                        <Plus className="h-4 w-4 mr-2"/>
                                        Crear Profesional
                                    </>
                                )}
                            </Button>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    )
}