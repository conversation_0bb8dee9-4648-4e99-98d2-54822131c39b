"use client"

import Link from "next/link"
import Image from "next/image"
import { useAuth } from "@/contexts/AuthContext"
import { PatientUserPill } from "@/components/ui/PatientUserPill"
import { usePatients } from "@/contexts/PatientContext"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useState } from "react"
import { Menu, X } from "lucide-react"
import { Auth0LoginButton } from "@/components/auth/Auth0LoginButton"

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { currentUser, logout, loginWithAuth0 } = useAuth()
  const { getPatientById } = usePatients()

  return (
    <>
      {/* Fixed height placeholder to prevent content jumping */}
      <div className="h-[60px] w-full" />

      {/* Fixed frosted header */}
      <header className="fixed top-0 left-0 right-0 z-50">
        {/* Frosty backdrop */}
        <div className="fixed top-0 left-0 right-0 w-full z-40 backdrop-blur-sm bg-blue-50/70 h-[60px]" />

        {/* Header content */}
        <div className="max-w-6xl mx-auto px-7 flex justify-between items-center h-[60px] relative z-50">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera"
                width={100}
                height={30}
                priority
                className="h-8 md:h-7 w-auto"
              />
            </Link>
          </div>

          {/* Auth buttons/user pill */}
          <div className="flex items-center gap-2">
            {currentUser ? (
              <>
                <Button variant="ghost" asChild className="hidden md:inline-flex text-sm hover:bg-blue-50 hover:text-blue-600">
                  <Link href="/plataforma/paciente">Mis turnos</Link>
                </Button>
                <PatientUserPill
                  currentUser={currentUser}
                  currentPatient={null} //TODO FACU sacar patient id
                  logout={logout}
                />
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={loginWithAuth0}
                  className="hidden md:inline-flex text-sm hover:bg-transparent hover:text-blue-600"
                >
                  Iniciar sesión
                </Button>
                <Button
                  variant="default"
                  className="hidden md:inline-flex rounded-full bg-blue-600 hover:bg-blue-700 text-white border border-blue-600 px-4 py-2 text-sm shadow-md hover:shadow-lg transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/60 focus-visible:ring-offset-1"
                  size="sm"
                  asChild
                >
                  <Link href="/plataforma/registro">Registrarme</Link>
                </Button>
              </>
            )}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 md:hidden ml-2"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>

          {/* Mobile menu dropdown */}
          {isMenuOpen && (
            <div className="absolute top-full left-0 right-0 mx-4 bg-white shadow-lg rounded-xl md:hidden z-50 mt-2">
              <div className="flex flex-col p-4 space-y-4">
                {currentUser && (
                  <Link href="/plataforma/paciente" className="mx-3 text-slate-800 hover:text-blue-600">
                    Mis turnos
                  </Link>
                )}
                {!currentUser && (
                  <>
                    <div className="mx-2 border-t border-slate-200/80" />
                    <div className="px-3 text-xs uppercase tracking-wide text-slate-500/90">Tu cuenta</div>
                    <div className="flex flex-col gap-2 px-2">
                      <Auth0LoginButton
                        className="w-full rounded-full bg-white/70 backdrop-blur border border-blue-600 text-blue-700 hover:bg-white/90 hover:border-blue-600 hover:text-blue-700 px-4 py-3 text-base shadow-sm transition-all duration-200 group focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500/50 focus-visible:ring-offset-1"
                      >
                        <span className="font-medium">Iniciar sesión</span>
                      </Auth0LoginButton>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </header>
    </>
  )
}