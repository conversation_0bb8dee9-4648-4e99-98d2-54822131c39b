"use client"

import React, {Suspense, useEffect, useState} from "react"
import {useRouter, useSearchParams} from "next/navigation"
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Button} from "@/components/ui/button"
import {Label} from "@/components/ui/label"
import {toast} from "sonner"
import {User, UserRole} from "@/types/users"
import {AlertCircle, Eye, EyeOff, LogOut} from "lucide-react"
import {useAuth} from "@/hooks/useAuth"

// Wrapper component that doesn't use useSearchParams directly
export default function ResetPasswordPageWrapper() {
    return (
        <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Cargando...</div>}>
            <ResetPasswordPage/>
        </Suspense>
    )
}

// Main component that uses useSearchParams
function ResetPasswordPage() {
    const searchParams = useSearchParams()
    const router = useRouter()
    const email = searchParams.get("email") || ""
    const {isAuthenticated, currentUser, logout} = useAuth()

    const [password, setPassword] = useState("")
    const [confirmPassword, setConfirmPassword] = useState("")
    const [isLoading, setIsLoading] = useState(false)
    const [showPassword, setShowPassword] = useState(false)
    const [user, setUser] = useState<User | null>(null)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        // Clear any stored redirect URL since we're now on the reset password page
        localStorage.removeItem('loginRedirectUrl');

        // If user is not authenticated, redirect to login with this page as redirect target
        if (!isAuthenticated || !currentUser) {
            // Store the current URL as the redirect target
            const currentPath = `/plataforma/profesional/reset-password?email=${encodeURIComponent(email)}`;
            localStorage.setItem('loginRedirectUrl', currentPath);

            // Redirect to login page
            console.log('Reset password page - User not authenticated, redirecting to login');
            router.replace(`/plataforma/profesional/login?redirect=${encodeURIComponent(currentPath)}`);
            return;
        }

        // If user is authenticated and the email matches the current user
        if (email === currentUser.email) {
            if (currentUser.roles === UserRole.DOCTOR) {
                setUser(currentUser);
            } else {
                setError("Esta página es solo para profesionales médicos");
            }
        }
        // If user is authenticated but email doesn't match
        else {
            setError(`No puede cambiar la contraseña de ${email}. Solo puede cambiar su propia contraseña (${currentUser.email}).`);
        }
    }, [email, isAuthenticated, currentUser, router])

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        setError(null)

        if (!user) {
            setError("Usuario no encontrado")
            return
        }

        if (password.length < 6) {
            setError("La contraseña debe tener al menos 6 caracteres")
            return
        }

        if (password !== confirmPassword) {
            setError("Las contraseñas no coinciden")
            return
        }

        setIsLoading(true)

        try {
            // Update the user's password
            const updatedUser = {
                ...user,
                password
            }

            storage.saveUser(updatedUser)

            toast.success("Contraseña actualizada correctamente")

            // Redirect to login page after successful password reset
            setTimeout(() => {
                router.push("/plataforma/profesional/login")
            }, 2000)
        } catch (error) {
            console.error("Error updating password:", error)
            setError("Error al actualizar la contraseña")
        } finally {
            setIsLoading(false)
        }
    }

    // Handle logout and redirect to the same page
    const handleLogout = () => {
        logout()
        // Redirect to the same page after logout
        router.push(`/plataforma/profesional/reset-password?email=${encodeURIComponent(email)}`)
    }

    if (!email) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-100">
                <Card className="w-full max-w-md">
                    <CardHeader>
                        <CardTitle>Restablecer Contraseña</CardTitle>
                        <CardDescription>No se proporcionó un correo electrónico válido</CardDescription>
                    </CardHeader>
                    <CardFooter>
                        <Button onClick={() => router.push("/plataforma/profesional/login")} className="w-full">
                            Volver al inicio de sesión
                        </Button>
                    </CardFooter>
                </Card>
            </div>
        )
    }

    // If user is authenticated but with a different email
    if (isAuthenticated && currentUser && email !== currentUser.email) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-100">
                <Card className="w-full max-w-md">
                    <CardHeader>
                        <CardTitle>Acceso denegado</CardTitle>
                        <CardDescription>No puede cambiar la contraseña de otro usuario</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <AlertCircle className="h-5 w-5 text-red-500"/>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-red-700">Solo puede cambiar su propia contraseña.</p>
                                </div>
                            </div>
                        </div>

                        <div className="text-center py-4">
                            <p className="text-gray-700 mb-4">Actualmente ha iniciado sesión
                                como <strong>{currentUser.email}</strong></p>
                            <p className="text-gray-700 mb-4">No tiene permiso para cambiar la contraseña
                                de <strong>{email}</strong>.</p>

                            <div className="flex flex-col space-y-2 mt-6">
                                <Button
                                    onClick={() => router.push(`/plataforma/profesional/reset-password?email=${encodeURIComponent(currentUser.email)}`)}
                                    className="bg-blue-600 hover:bg-blue-700">
                                    Cambiar mi contraseña
                                </Button>

                                <Button onClick={handleLogout} variant="outline" className="flex items-center gap-2">
                                    <LogOut className="h-4 w-4"/>
                                    Cerrar sesión
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
            <Card className="w-full max-w-md">
                <CardHeader>
                    <CardTitle>Restablecer Contraseña</CardTitle>
                    <CardDescription>Cree una nueva contraseña para su cuenta</CardDescription>
                </CardHeader>
                <CardContent>
                    {error && (
                        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <AlertCircle className="h-5 w-5 text-red-500"/>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-red-700">{error}</p>
                                </div>
                            </div>
                        </div>
                    )}

                    {!user ? (
                        <div className="text-center py-4">
                            <p className="text-gray-500">No se encontró ningún usuario con el correo electrónico
                                proporcionado</p>
                            <Button onClick={() => router.push("/plataforma/profesional/login")} className="mt-4">
                                Volver al inicio de sesión
                            </Button>
                        </div>
                    ) : (
                        <form onSubmit={handleSubmit}>
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="email">Correo electrónico</Label>
                                    <Input id="email" value={email} disabled className="bg-gray-50"/>
                                </div>

                                <div>
                                    <Label htmlFor="password">Nueva contraseña</Label>
                                    <div className="relative">
                                        <Input
                                            id="password"
                                            type={showPassword ? "text" : "password"}
                                            value={password}
                                            onChange={(e) => setPassword(e.target.value)}
                                            className="pr-10"
                                        />
                                        <button
                                            type="button"
                                            className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                            onClick={() => setShowPassword(!showPassword)}
                                        >
                                            {showPassword ? <EyeOff className="h-4 w-4"/> : <Eye className="h-4 w-4"/>}
                                        </button>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="confirmPassword">Confirmar contraseña</Label>
                                    <div className="relative">
                                        <Input
                                            id="confirmPassword"
                                            type={showPassword ? "text" : "password"}
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            className="pr-10"
                                        />
                                    </div>
                                </div>

                                <Button type="submit" className="w-full" disabled={isLoading}>
                                    {isLoading ? "Actualizando..." : "Actualizar contraseña"}
                                </Button>
                            </div>
                        </form>
                    )}
                </CardContent>
            </Card>
        </div>
    )
}
