/* Custom styles for react-international-phone */
.react-international-phone-input-container {
  height: 100%;
  border: none !important;
  position: relative; /* Add this to ensure the focus effect works properly */
}

.react-international-phone-country-selector-button {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
  background-color: hsl(var(--background));
  height: 100%;
  display: flex;
  align-items: center;
  border: 1px solid hsl(var(--border)) !important;
  border-right: 1px solid hsl(var(--border)) !important;
}

.react-international-phone-input {
  height: 2.5rem; /* h-10 */
  border-radius: 0.375rem; /* rounded-md */
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border: 1px solid hsl(var(--border)) !important;
  background-color: hsl(var(--background));
  font-size: 0.875rem; /* text-sm */
}

.react-international-phone-input input {
  height: 100%;
  border: none;
  outline: none;
  padding-left: 0.75rem;
  font-size: 0.875rem;
  background-color: transparent;
}

/* Focus styles that match the Input component */
.react-international-phone-input-container:focus-within {
  z-index: 1;
}

/* This creates the focus ring effect that matches your Input component */
.react-international-phone-input-container:focus-within::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 0.5rem; /* var(--radius) */
  box-shadow: 0 0 0 2px hsl(var(--ring));
  pointer-events: none;
}

/* Remove any existing focus styling */
.custom-phone-input .react-international-phone-input:focus-within {
  outline: none;
  box-shadow: none;
  border-color: hsl(var(--border)) !important;
}

.custom-phone-input .react-international-phone-input-container:focus-within .react-international-phone-country-selector-button {
  outline: none;
  box-shadow: none;
  border-color: hsl(var(--border)) !important;
}

/* Ensure the input doesn't have its own focus styling */
.react-international-phone-input input:focus {
  outline: none;
  box-shadow: none;
}

/* Ensure the dropdown maintains theme consistency */
.react-international-phone-country-selector-dropdown {
  border: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Disable any conflicting focus styles */
.react-international-phone-input:focus-within {
  box-shadow: none !important;
}

.custom-phone-input .react-international-phone-input-container:focus-within .react-international-phone-country-selector-button {
  border-right-color: hsl(var(--border)) !important;
}

/* Styles for the dial code preview component */
.react-international-phone-dial-code-preview {
  height: 2.5rem !important; /* Match the height of the input (h-10) */
  min-height: 100% !important;
  display: flex;
  align-items: center; /* Center vertically */
  justify-content: center;
  padding: 0 0.25rem 0 0.125rem; /* Reduced padding, especially on the left side */
  margin-left: -0.125rem; /* Negative margin to bring it closer to the flag */
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border)) !important;
  border-left: none !important;
  border-right: none !important;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  box-sizing: border-box;
  min-width: 3rem; /* Ensure there's enough space for the dial code */
  line-height: normal; /* Use normal line height for better text alignment */
  overflow: visible;
}

/* Adjust the country selector button to not have right border when dial code preview is shown */
.react-international-phone-input-container.with-dial-code-preview .react-international-phone-country-selector-button {
  border-right: none !important;
}

/* Adjust the input field to have proper border radius when dial code preview is shown */
.react-international-phone-input-container.with-dial-code-preview .react-international-phone-input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  height: 2.5rem !important; /* Ensure consistent height */
}

/* Ensure the input field inside maintains the correct height */
.react-international-phone-input-container.with-dial-code-preview .react-international-phone-input input {
  height: 100% !important;
}

/* Ensure the container maintains consistent height */
.react-international-phone-input-container.with-dial-code-preview {
  display: flex;
  align-items: center; /* Changed from stretch to center for better alignment */
  height: 2.5rem; /* Match the height of the input (h-10) */
}

/* Ensure the country selector button maintains the same height */
.react-international-phone-input-container.with-dial-code-preview .react-international-phone-country-selector-button {
  height: 2.5rem !important; /* Match the height of the input (h-10) */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Improve flag vertical alignment */
.react-international-phone-country-selector-button img {
  vertical-align: middle;
  margin-top: 0;
  margin-bottom: 0;
  display: inline-flex;
  position: relative;
  top: 0; /* Reset vertical adjustment */
}

/* Ensure the flag container is properly centered */
.react-international-phone-country-selector-button .react-international-phone-country-flag {
  display: flex;
  align-items: center;
  justify-content: center;
}