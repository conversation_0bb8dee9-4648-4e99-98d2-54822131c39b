"use client"

import React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Check, Search, Pencil, AlertCircle, User, Users, Plus } from "lucide-react"
import { CollapsibleSection } from "@/components/ui/collapsible-section"
import { Patient } from "@/types/patient"
import { User as UserType } from "@/types/users"
import type { CountryData } from 'react-international-phone'

interface PatientSelectionStepProps {
  currentUser: UserType | null
  isPatientUser: boolean
  searchPerformed: boolean
  selectedAssociatedPatient: Patient | null
  patientDniSearch: string
  patientFound: Patient | null
  patientNotFound: boolean
  patientError: string
  patientToVerify: Patient | null
  showVerificationSection: boolean
  verificationCode: string
  actualVerificationCode: string
  patientDni: string
  patientName: string
  patientLastName: string
  patientPhone: string
  patientEmail: string
  phoneError: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  phoneUtil: any
  searchingPatient: boolean
  activeSection: "dni" | "datos" | "cobertura" | ""
  completedSections: {
    dni: { completed: boolean, value: string }
    datos: { completed: boolean, value: string }
    cobertura: { completed: boolean, value: string }
  }
  getPatientById: (id: string) => Patient | null | undefined
  getAssociatedPatients: () => Patient[]
  setPatientDniSearch: (value: string) => void
  setPatientFound: (patient: Patient | null) => void
  setPatientNotFound: (value: boolean) => void
  setSearchPerformed: (value: boolean) => void
  setSelectedAssociatedPatient: (patient: Patient | null) => void
  setPatientError: (error: string) => void
  setCompletedSections: (sections: {
    dni: { completed: boolean, value: string }
    datos: { completed: boolean, value: string }
    cobertura: { completed: boolean, value: string }
  }) => void
  setActiveSection: (section: "dni" | "datos" | "cobertura" | "") => void
  setPatientDni: (value: string) => void
  setPatientName: (value: string) => void
  setPatientLastName: (value: string) => void
  setPatientPhone: (value: string) => void
  setPatientEmail: (value: string) => void
  setPatientCoverage: (value: string) => void
  setPatientPlan: (value: string) => void
  setNoCoverage: (value: boolean) => void
  setHasDefaultCoverage: (value: boolean) => void
  setPatientToEdit: (patient: Patient) => void
  setShowEditDialog: (show: boolean) => void
  setVerificationCode: (code: string) => void
  searchPatient: () => void
  handleVerifyPatient: () => void
  capitalizeName: (name: string) => string
  getSpanishCountries: () => CountryData[]
  isModifying?: boolean
}

export function PatientSelectionStep({
  currentUser,
  // isPatientUser,
  searchPerformed,
  selectedAssociatedPatient,
  patientDniSearch,
  patientFound,
  patientNotFound,
  patientError,
  patientToVerify,
  showVerificationSection,
  verificationCode,
  actualVerificationCode,
  searchingPatient,
  activeSection,
  completedSections,
  getPatientById,
  getAssociatedPatients,
  setPatientDniSearch,
  setPatientFound,
  setPatientNotFound,
  setSearchPerformed,
  setSelectedAssociatedPatient,
  setPatientError,
  setCompletedSections,
  setActiveSection,
  setPatientDni,
  setPatientName,
  setPatientLastName,
  setPatientPhone,
  setPatientEmail,
  setPatientCoverage,
  setPatientPlan,
  setNoCoverage,
  setHasDefaultCoverage,
  setPatientToEdit,
  setShowEditDialog,
  setVerificationCode,
  searchPatient,
  handleVerifyPatient,
  isModifying = false
}: PatientSelectionStepProps) {
  
  const defaultPatient = currentUser?.defaultPatientId ? getPatientById(currentUser.defaultPatientId) : null;
  const associatedPatients = getAssociatedPatients().filter((p: Patient) => p.id !== currentUser?.defaultPatientId);

  // If modifying, show locked patient info
  if (isModifying) {
    const patientInfo = selectedAssociatedPatient || defaultPatient;
    
    if (patientInfo) {
      return (
        <div className="bg-amber-50 border border-amber-200 rounded-xl p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
              <AlertCircle className="h-5 w-5 text-amber-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-amber-900 mb-2">Modificando turno existente</h3>
              <p className="text-amber-800 text-sm mb-4">
                No es posible cambiar el paciente al modificar un turno. Si necesitas reservar para otro paciente, 
                cancela esta modificación y crea una nueva reserva.
              </p>
              <div className="bg-white rounded-lg p-4 border border-amber-200">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <User className="h-4 w-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{patientInfo.name}</p>
                    <p className="text-sm text-gray-500">DNI: {patientInfo.dni}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    return null;
  }

  // Main patient selection UI
  return (
    <div className="space-y-6">
      {/* Patient Selection Header */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">¿Para quién es el turno?</h2>
        <p className="text-gray-600">Selecciona el paciente para la reserva</p>
      </div>

      {/* Patient Options */}
      <div className="space-y-4">
        {/* Option 1: Book for yourself (if user has default patient) */}
        {currentUser?.defaultPatientId && defaultPatient && (
          <div 
            className={`border-2 rounded-xl p-4 cursor-pointer transition-all hover:shadow-md ${
              !searchPerformed && !selectedAssociatedPatient
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
            onClick={() => {
              setSearchPerformed(false);
              setSelectedAssociatedPatient(null);
              setPatientFound(defaultPatient);
              setPatientError("");
              
              // Set default patient data
              setPatientDni(defaultPatient.dni);
              const nameParts = defaultPatient.name.split(' ');
              if (nameParts.length > 1) {
                setPatientName(nameParts[0]);
                setPatientLastName(nameParts.slice(1).join(' '));
              } else {
                setPatientName(defaultPatient.name);
                setPatientLastName('');
              }
              setPatientPhone(defaultPatient.phone);
              setPatientEmail(defaultPatient.email || '');

              // Handle coverage
              if (defaultPatient.coverage) {
                const defaultCoverage = defaultPatient.defaultCoverage || defaultPatient.coverage;
                setHasDefaultCoverage(true);
                const coverageParts = defaultCoverage.split(' ');
                if (coverageParts.length > 1) {
                  setPatientCoverage(coverageParts[0]);
                  setPatientPlan(coverageParts.slice(1).join(' '));
                  setNoCoverage(false);
                } else if (defaultCoverage === "Sin Cobertura") {
                  setPatientCoverage('');
                  setPatientPlan('');
                  setNoCoverage(true);
                } else {
                  setPatientCoverage(defaultCoverage);
                  setPatientPlan('');
                  setNoCoverage(false);
                }
              } else {
                setPatientCoverage('');
                setPatientPlan('');
                setNoCoverage(true);
                setHasDefaultCoverage(false);
              }

              setCompletedSections({
                dni: { completed: false, value: "" },
                datos: { completed: false, value: "" },
                cobertura: { completed: false, value: "" }
              });
            }}
          >
            <div className="flex items-center space-x-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                !searchPerformed && !selectedAssociatedPatient
                  ? 'bg-blue-100 text-blue-600' 
                  : 'bg-gray-100 text-gray-600'
              }`}>
                <User className="h-6 w-6" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">Reservar para mí</h3>
                <p className="text-sm text-gray-600">{defaultPatient.name} • DNI: {defaultPatient.dni}</p>
              </div>
              {!searchPerformed && !selectedAssociatedPatient && (
                <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Option 2: Book for associated patients */}
        {associatedPatients.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900 flex items-center">
              <Users className="h-5 w-5 mr-2 text-gray-600" />
              Pacientes asociados
            </h4>
            {associatedPatients.map((patient: Patient) => (
              <div
                key={patient.id}
                className={`border-2 rounded-xl p-4 cursor-pointer transition-all hover:shadow-md ${
                  selectedAssociatedPatient?.id === patient.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
                onClick={() => {
                  setSelectedAssociatedPatient(patient);
                  setSearchPerformed(true);
                  setPatientFound(patient);
                  setPatientDni(patient.dni);
                  setPatientError("");

                  // Split the name into first and last name
                  const nameParts = patient.name.split(' ');
                  if (nameParts.length > 1) {
                    setPatientName(nameParts[0]);
                    setPatientLastName(nameParts.slice(1).join(' '));
                  } else {
                    setPatientName(patient.name);
                    setPatientLastName('');
                  }

                  setPatientPhone(patient.phone);
                  setPatientEmail(patient.email || '');

                  // Mark all sections as completed
                  setCompletedSections({
                    dni: { completed: true, value: `DNI: ${patient.dni}` },
                    datos: { completed: true, value: patient.name },
                    cobertura: { completed: true, value: patient.coverage || "Sin Cobertura" }
                  });

                  // Handle coverage
                  if (patient.coverage) {
                    const defaultCoverage = patient.defaultCoverage || patient.coverage;
                    setHasDefaultCoverage(true);
                    const coverageParts = defaultCoverage.split(' ');
                    if (coverageParts.length > 1) {
                      setPatientCoverage(coverageParts[0]);
                      setPatientPlan(coverageParts.slice(1).join(' '));
                      setNoCoverage(false);
                    } else if (defaultCoverage === "Sin Cobertura") {
                      setPatientCoverage('');
                      setPatientPlan('');
                      setNoCoverage(true);
                    } else {
                      setPatientCoverage(defaultCoverage);
                      setPatientPlan('');
                      setNoCoverage(false);
                    }
                  } else {
                    setPatientCoverage('');
                    setPatientPlan('');
                    setNoCoverage(true);
                    setHasDefaultCoverage(false);
                  }
                }}
              >
                <div className="flex items-center space-x-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    selectedAssociatedPatient?.id === patient.id
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    <User className="h-6 w-6" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{patient.name}</h3>
                    <p className="text-sm text-gray-600">DNI: {patient.dni}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {selectedAssociatedPatient?.id === patient.id && (
                      <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                        <Check className="h-4 w-4 text-white" />
                      </div>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-2 h-8 w-8 text-gray-500 hover:text-blue-600"
                      onClick={(e) => {
                        e.stopPropagation();
                        setPatientToEdit(patient);
                        setShowEditDialog(true);
                      }}
                      title="Editar información"
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Option 3: Add new patient */}
        <div 
          className={`border-2 border-dashed rounded-xl p-4 cursor-pointer transition-all hover:shadow-md ${
            searchPerformed && !selectedAssociatedPatient
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100'
          }`}
          onClick={() => {
            setSelectedAssociatedPatient(null);
            setSearchPerformed(true);
            setPatientFound(null);
            setPatientNotFound(false);
            setPatientDni('');
            setPatientName('');
            setPatientLastName('');
            setPatientPhone('');
            setPatientEmail('');
            setPatientCoverage('');
            setPatientPlan('');
            setNoCoverage(false);
            setHasDefaultCoverage(false);
            setPatientDniSearch('');
            setPatientError('');

            setCompletedSections({
              dni: { completed: false, value: "" },
              datos: { completed: false, value: "" },
              cobertura: { completed: false, value: "" }
            });
            setActiveSection("dni");
          }}
        >
          <div className="flex items-center space-x-4">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
              searchPerformed && !selectedAssociatedPatient
                ? 'bg-blue-100 text-blue-600'
                : 'bg-gray-200 text-gray-600'
            }`}>
              <Plus className="h-6 w-6" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900">Agregar nuevo paciente</h3>
              <p className="text-sm text-gray-600">Buscar por DNI o crear nuevo paciente</p>
            </div>
            {searchPerformed && !selectedAssociatedPatient && (
              <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                <Check className="h-4 w-4 text-white" />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* New Patient Form - Only show when "Add new patient" is selected */}
      {searchPerformed && !selectedAssociatedPatient && (
        <div className="bg-white border border-gray-200 rounded-xl p-6 space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">Nuevo paciente</h3>
            <p className="text-sm text-gray-600">Busca por DNI o completa los datos para crear un nuevo paciente</p>
          </div>

          {/* DNI Section */}
          <CollapsibleSection
            title="DNI"
            isOpen={activeSection === "dni"}
            onOpenChange={(open) => {
              if (open) {
                setActiveSection("dni");
              }
            }}
            summary={completedSections.dni.value}
            isComplete={completedSections.dni.completed}
          >
            <div className="space-y-4">
              <Label htmlFor="patientDniSearch">Buscar paciente por DNI</Label>
              <div className="flex gap-2">
                <Input
                  id="patientDniSearch"
                  value={patientDniSearch}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^0-9]/g, '');
                    if (value === "0" || (value.length > 0 && value[0] === "0")) {
                      return;
                    }
                    if (value.length <= 8) {
                      setPatientDniSearch(value);
                    }
                  }}
                  placeholder="DNI sin puntos (7-8 dígitos)"
                  className="flex-1"
                  inputMode="numeric"
                  pattern="[0-9]*"
                />
                <Button
                  onClick={() => {
                    if (patientDniSearch.length < 7 || patientDniSearch.length > 8) {
                      setPatientError("El DNI debe tener entre 7 y 8 dígitos");
                      return;
                    }
                    setPatientError("");
                    searchPatient();
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                  disabled={searchingPatient}
                >
                  {searchingPatient ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                  ) : (
                    <Search className="h-4 w-4 mr-2" />
                  )}
                  Buscar
                </Button>
              </div>

              {/* Search Status Messages */}
              {searchingPatient && (
                <div className="flex items-center text-blue-600 text-sm">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-200 border-t-blue-600 mr-2"></div>
                  Buscando paciente...
                </div>
              )}

              {patientFound && searchPerformed && (
                <div className="flex items-center text-green-600 text-sm bg-green-50 p-3 rounded-lg">
                  <Check className="h-4 w-4 mr-2" />
                  Paciente encontrado. Los datos han sido completados automáticamente.
                </div>
              )}

              {patientNotFound && searchPerformed && (
                <div className="flex items-center text-green-600 text-sm bg-green-50 p-3 rounded-lg">
                  <Check className="h-4 w-4 mr-2" />
                  Paciente no encontrado. Complete los datos para crear un nuevo paciente.
                </div>
              )}

              {/* Verification Section */}
              {showVerificationSection && patientToVerify && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">Verificación de paciente</h4>
                  <p className="text-sm text-blue-700 mb-4">
                    Encontramos un paciente con este DNI. Para asociarlo a tu cuenta, 
                    ingresa el código enviado a su teléfono.
                  </p>
                  <div className="space-y-3">
                    <Label htmlFor="verificationCode">Código de verificación</Label>
                    <Input
                      id="verificationCode"
                      type="text"
                      placeholder="Ingrese el código de 6 dígitos"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                    />
                    <Button
                      onClick={handleVerifyPatient}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                      Verificar
                    </Button>
                    <p className="text-xs text-blue-600">
                      Código de desarrollo: <span className="font-bold">{actualVerificationCode}</span>
                    </p>
                  </div>
                </div>
              )}

              {/* Error Messages */}
              {patientError && !patientError.includes("verificación") && (
                <div className={`flex items-center text-sm p-3 rounded-lg ${
                  patientError.includes("verificado") || patientError.includes("exitosamente")
                    ? 'text-green-700 bg-green-50'
                    : 'text-red-700 bg-red-50'
                }`}>
                  {patientError.includes("verificado") || patientError.includes("exitosamente") ? (
                    <Check className="h-4 w-4 mr-2" />
                  ) : (
                    <AlertCircle className="h-4 w-4 mr-2" />
                  )}
                  {patientError}
                </div>
              )}
            </div>
          </CollapsibleSection>
        </div>
      )}
    </div>
  )
}
