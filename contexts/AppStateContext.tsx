"use client"

import { createContext, useState, ReactNode } from "react"
import { toast } from "react-toastify"

interface AppStateContextProps {
  activeConfigTab: string
  setActiveConfigTab: (tab: string) => void
  hasUnsavedChanges: boolean
  setHasUnsavedChanges: React.Dispatch<React.SetStateAction<boolean>>
}

export const AppStateContext = createContext<AppStateContextProps>({} as AppStateContextProps)

export function AppStateProvider({ children }: { children: ReactNode }) {
  const [activeConfigTab, setActiveConfigTabState] = useState("info")
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  const setActiveConfigTab = (tab: string) => {
    if (hasUnsavedChanges) {
      toast.error("Por favor, guarda o cancela los cambios antes de cambiar de pestaña.", {
        position: "top-right",
        autoClose: 3000,
        toastId: "unsaved-changes" // Prevents duplicate toasts
      })
      return
    }
    setActiveConfigTabState(tab)
  }

  return (
    <AppStateContext.Provider
      value={{
        activeConfigTab,
        setActiveConfigTab,
        hasUnsavedChanges,
        setHasUnsavedChanges,
      }}
    >
      {children}
    </AppStateContext.Provider>
  )
}