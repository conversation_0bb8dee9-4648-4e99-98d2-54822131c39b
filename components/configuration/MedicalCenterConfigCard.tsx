"use client"

import { useState, use<PERSON>ontext, useRef, useEffect } from "react"
import Image from "next/image"
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Building, MapPin, Phone, Mail, CreditCard, Clock, AlertCircle, Camera, X, Save, Download, Users, AlertTriangle, Loader2, LocateFixed } from "lucide-react"
import { useToast } from "@/components/ui/toast"
import { OpeningHours } from "@/types/medical-center"
import { syncMedicalCenterSchedules } from "@/utils/medicalCenterUtils"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { usePatients } from "@/contexts/PatientContext"
import * as XLSX from 'xlsx'
import L from "leaflet"
import "leaflet/dist/leaflet.css"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel } from "@/components/ui/select"
import { CABA_LOCATIONS, GBA_LOCATIONS, getLocationByName, getLocationById } from "@/data/locations"

// Helper function to geocode an address
async function geocodeAddress(address: string): Promise<{ lat: number, lon: number, display_name: string } | null> {
  try {
    // Using Nominatim OpenStreetMap service for geocoding with Spanish language preference
    const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&accept-language=es,es-AR,en&addressdetails=1&countrycodes=ar`);
    const data = await response.json();

    if (data && data.length > 0) {
      return {
        lat: parseFloat(data[0].lat),
        lon: parseFloat(data[0].lon),
        display_name: data[0].display_name
      };
    }
    return null;
  } catch (error) {
    console.error("Geocoding error:", error);
    return null;
  }
}

// Backup geocoding service
async function geocodeAddressBackup(address: string): Promise<{ lat: number, lon: number, display_name: string } | null> {
  try {
    // Using OpenCage as a backup geocoding service (free tier available)
    // You'll need to add your API key here if you want to use this in production
    const response = await fetch(`https://api.opencagedata.com/geocode/v1/json?q=${encodeURIComponent(address)}&no_annotations=1&key=DEMO_KEY_FOR_NOW&language=es&countrycode=ar`);
    const data = await response.json();

    if (data && data.results && data.results.length > 0) {
      const result = data.results[0];
      return {
        lat: result.geometry.lat,
        lon: result.geometry.lng,
        display_name: result.formatted
      };
    }
    return null;
  } catch (error) {
    console.error("Backup geocoding error:", error);
    return null;
  }
}

// Combined verification function that tries multiple services
async function verifyAddressWithBackup(address: string): Promise<{ lat: number, lon: number, display_name: string } | null> {
  try {
    // Try primary service first
    const primaryResult = await geocodeAddress(address);
    if (primaryResult) return primaryResult;

    // Fall back to backup service
    console.info("Primary geocoding service failed, trying backup service");
    const backupResult = await geocodeAddressBackup(address);
    return backupResult;
  } catch (error) {
    console.error("Address verification error:", error);
    return null;
  }
}

// Helper function to format address in Argentine style
function formatArgentineAddress(
  streetNumber: string, 
  route: string, 
  locality: string, 
  administrativeArea: string, 
  fallbackStreetAddress?: string
): string {
  const parts = [];
  
  // Street name and number in Argentine format: "Street Number"
  if (route && streetNumber) {
    parts.push(`${route} ${streetNumber}`);
  } else if (route) {
    parts.push(route);
  } else if (fallbackStreetAddress) {
    // Use the user's input if geocoding didn't provide route/number
    parts.push(fallbackStreetAddress);
  }
  
  // Add neighborhood/locality
  if (locality) {
    parts.push(locality);
  }
  
  // Add administrative area (Ciudad Autónoma de Buenos Aires or Buenos Aires)
  if (administrativeArea) {
    parts.push(administrativeArea);
  }
  
  return parts.join(', ');
}

export default function MedicalCenterConfigCard() {
  const { activeMedicalCenterId, medicalCenters, updateMedicalCenter } = useContext(MedicalCenterContext)
  const { toast } = useToast()
  const { addPatient, getPatientByDni } = usePatients()

  const activeMedicalCenter = medicalCenters.find(mc => mc.id === activeMedicalCenterId) || null

  const defaultOpeningHours: OpeningHours = {
    monday: { start: '08:00', end: '18:00', enabled: true },
    tuesday: { start: '08:00', end: '18:00', enabled: true },
    wednesday: { start: '08:00', end: '18:00', enabled: true },
    thursday: { start: '08:00', end: '18:00', enabled: true },
    friday: { start: '08:00', end: '18:00', enabled: true },
    saturday: { start: '09:00', end: '13:00', enabled: false },
    sunday: { start: '09:00', end: '13:00', enabled: false }
  };

  const [isEditing, setIsEditing] = useState(false)

  // Determine initial region and location based on medical center data
  const determineInitialRegion = (): "none" | "caba" | "gba" => {
    // First check if we have a saved locationRegion property
    if (activeMedicalCenter?.locationRegion) {
      if (activeMedicalCenter.locationRegion === "caba" ||
          activeMedicalCenter.locationRegion === "gba") {
        return activeMedicalCenter.locationRegion;
      }
    }

    // If no saved region, try to determine from locality
    const locality = activeMedicalCenter?.location?.locality || '';
    if (locality) {
      // Check if it's in CABA
      const inCaba = CABA_LOCATIONS.some(loc =>
        loc.name.toLowerCase() === locality.toLowerCase()
      );
      if (inCaba) return "caba";

      // Check if it's in GBA
      const inGba = GBA_LOCATIONS.some(loc =>
        loc.name.toLowerCase() === locality.toLowerCase()
      );
      if (inGba) return "gba";
    }

    // If we have coordinates but no region, try to determine from administrative area
    if (activeMedicalCenter?.location?.administrativeArea) {
      const adminArea = activeMedicalCenter.location.administrativeArea.toLowerCase();
      if (adminArea.includes("ciudad") || adminArea.includes("capital")) {
        return "caba";
      } else if (adminArea.includes("buenos aires") && !adminArea.includes("ciudad")) {
        return "gba";
      }
    }

    return "none"; // Default to "none" if not found
  };

  const determineInitialLocationId = (): string => {
    // First check if we have a saved locationId property
    if (activeMedicalCenter?.locationId) {
      // Verify that the locationId actually exists in our predefined locations
      const location = getLocationById(activeMedicalCenter.locationId);
      if (location) {
        return activeMedicalCenter.locationId;
      }
    }

    // If no saved locationId or it's invalid, try to determine from locality
    const locality = activeMedicalCenter?.location?.locality || '';
    if (!locality) return "";

    // Try to find the location by exact name match only
    const location = getLocationByName(locality);
    return location?.id || "";
  };

  // Determine initial values for region and location
  const initialRegion = determineInitialRegion();
  const initialLocationId = determineInitialLocationId();

  // Log the initial values for debugging
  console.log("Initial region:", initialRegion);
  console.log("Initial locationId:", initialLocationId);
  console.log("Medical center locationRegion:", activeMedicalCenter?.locationRegion);
  console.log("Medical center locationId:", activeMedicalCenter?.locationId);

  // State for region and location selection
  const [selectedRegion, setSelectedRegion] = useState<"none" | "caba" | "gba">(initialRegion);
  const [selectedLocationId, setSelectedLocationId] = useState<string>(initialLocationId);

  // Initialize form data with values from the active medical center
  const initializeFormData = () => {
    // Create a base form data object
    const formData = {
      name: activeMedicalCenter?.name || '',
      address: activeMedicalCenter?.address || '',
      floor: activeMedicalCenter?.floor || '',
      apartment: activeMedicalCenter?.apartment || '',
      phone: activeMedicalCenter?.phone || '',
      email: activeMedicalCenter?.email || '',
      website: activeMedicalCenter?.website || '',

      logoUrl: activeMedicalCenter?.logoUrl || '',
      openingHours: activeMedicalCenter?.openingHours || defaultOpeningHours,
      billing: {
        businessName: activeMedicalCenter?.billing?.businessName || '',
        taxId: activeMedicalCenter?.billing?.taxId || '',
        address: activeMedicalCenter?.billing?.address || '',
        businessType: activeMedicalCenter?.billing?.businessType || 'individual',
      },
      location: {
        latitude: activeMedicalCenter?.location?.latitude,
        longitude: activeMedicalCenter?.location?.longitude,
        formattedAddress: activeMedicalCenter?.location?.formattedAddress || '',
        placeId: activeMedicalCenter?.location?.placeId || '',
        streetNumber: activeMedicalCenter?.location?.streetNumber || '',
        route: activeMedicalCenter?.location?.route || '',
        locality: activeMedicalCenter?.location?.locality || '',
        administrativeArea: activeMedicalCenter?.location?.administrativeArea || '',
        postalCode: activeMedicalCenter?.location?.postalCode || '',
        country: activeMedicalCenter?.location?.country || 'Argentina',
      },
      streetAddress: activeMedicalCenter?.streetAddress || '',
      // Add temporary UI-only fields for form handling
      city: activeMedicalCenter?.location?.locality || '',
      state: activeMedicalCenter?.location?.administrativeArea || '',
      postalCode: activeMedicalCenter?.location?.postalCode || '',
      country: activeMedicalCenter?.location?.country || 'Argentina',
    };

    return formData;
  };

  const [formData, setFormData] = useState(initializeFormData())

  // Simplify state management for address verification
  const [isVerifyingAddress, setIsVerifyingAddress] = useState(false)
  const [addressVerified, setAddressVerified] = useState(
    !!(activeMedicalCenter?.location?.latitude && activeMedicalCenter?.location?.longitude)
  )
  const [isAdjustingPin, setIsAdjustingPin] = useState(false)
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<L.Map | null>(null)
  const markerRef = useRef<L.Marker | null>(null)

  // Clean up map on unmount
  useEffect(() => {
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
        markerRef.current = null
      }
    }
  }, [])

  // Create or update the full address when structured fields change
  useEffect(() => {
    if (!isEditing) return;

    // Only update if we have at least street address and city
    if (formData.streetAddress && formData.city) {
      const addressParts = [
        formData.streetAddress,
        formData.city,
        formData.state,
        formData.postalCode,
        formData.country || 'Argentina' // default country
      ].filter(Boolean); // Remove empty parts

      const fullAddress = addressParts.join(', ');

      // Only update if different from current address to avoid endless loops
      if (fullAddress !== formData.address) {
        setFormData(prev => ({
          ...prev,
          address: fullAddress
        }));
      }
    }
  }, [formData.streetAddress, formData.city, formData.state, formData.postalCode, formData.country, isEditing, formData.address]);

  // Update region, location selectors, and form data when medical center changes
  // We use a ref to store the medical center ID to avoid including all dependencies
  const prevMedicalCenterIdRef = useRef<string | undefined>(activeMedicalCenter?.id);

  useEffect(() => {
    // Only update if the medical center ID has changed
    if (activeMedicalCenter?.id !== prevMedicalCenterIdRef.current) {
      prevMedicalCenterIdRef.current = activeMedicalCenter?.id;

      if (activeMedicalCenter) {
        // These functions are recreated on each render, but their logic depends only on activeMedicalCenter
        const newRegion = determineInitialRegion();
        const newLocationId = determineInitialLocationId();

        console.log("Medical center changed, updating selectors and form data:", {
          newRegion,
          newLocationId,
          postalCode: activeMedicalCenter?.location?.postalCode
        });

        // Update the selectors
        setSelectedRegion(newRegion);
        setSelectedLocationId(newLocationId);

        // Update the form data
        setFormData(initializeFormData());
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeMedicalCenter]);

  // Single effect to manage map lifecycle - handles both creation and cleanup
  useEffect(() => {
    // Function to create the map
    const createMap = () => {
      if (!mapContainerRef.current) return;

      // Create custom marker icon
      const customIcon = L.divIcon({
        className: 'custom-map-marker',
        html: `
          <div class="marker-container">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="marker-svg">
              <path fill="#0070F3" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z"/>
              <path fill="white" d="M12 4.5C9.51 4.5 7.5 6.51 7.5 9c0 2.49 2.01 4.5 4.5 4.5s4.5-2.01 4.5-4.5c0-2.49-2.01-4.5-4.5-4.5zm-1 7V10H9.5V8H11V6.5h2V8h1.5v2H13v1.5h-2z"/>
            </svg>
          </div>
        `,
        iconSize: [36, 42],
        iconAnchor: [18, 38],
        popupAnchor: [0, -38]
      });

      // Initialize map
      const coords: [number, number] = [formData.location.latitude!, formData.location.longitude!];
      mapInstanceRef.current = L.map(mapContainerRef.current, {
        zoomControl: false,
        attributionControl: false
      }).setView(coords, 15);

      // Add tile layer
      L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
        subdomains: 'abcd',
        maxZoom: 19
      }).addTo(mapInstanceRef.current);

      // Add zoom control
      L.control.zoom({
        position: 'bottomright'
      }).addTo(mapInstanceRef.current);

      // Add marker
      markerRef.current = L.marker(coords, {
        icon: customIcon,
        draggable: isAdjustingPin
      })
        .addTo(mapInstanceRef.current)
        .bindPopup(`
          <div class="text-sm p-1">
            <p class="font-semibold text-[#0070F3]">${formData.name}</p>
            <p class="text-gray-600 mt-1">${formData.location.formattedAddress || formData.address}</p>
          </div>
        `, {
          className: 'modern-popup',
          closeButton: false,
          offset: [0, -30]
        });

      // If we're adjusting the pin, enable interactions
      if (isAdjustingPin && mapInstanceRef.current && markerRef.current) {
        // Enable map click to move marker
        mapInstanceRef.current.on('click', function(e) {
          if (markerRef.current && mapInstanceRef.current) {
            const latlng = e.latlng;
            markerRef.current.setLatLng(latlng);

            // Update form data with new coordinates
            setFormData(prev => ({
              ...prev,
              location: {
                ...prev.location,
                latitude: latlng.lat,
                longitude: latlng.lng
              }
            }));
          }
        });

        // Handle marker drag end
        markerRef.current.on('dragend', function(e) {
          const marker = e.target;
          const position = marker.getLatLng();

          // Update form data with new coordinates
          setFormData(prev => ({
            ...prev,
            location: {
              ...prev.location,
              latitude: position.lat,
              longitude: position.lng
            }
          }));
        });
      }
    };

         // Function to cleanup the map
     const cleanupMap = () => {
       if (mapInstanceRef.current) {
         try {
           mapInstanceRef.current.remove();
         } catch (error) {
           console.warn('Error removing map instance:', error);
         }
         mapInstanceRef.current = null;
         markerRef.current = null;
       }
       
       // Also clear the container's innerHTML to ensure a clean state
       if (mapContainerRef.current) {
         mapContainerRef.current.innerHTML = '';
       }
     };

    // Determine if we should show the map
    const shouldShowMap = isEditing && 
                         addressVerified && 
                         formData.location.latitude && 
                         formData.location.longitude && 
                         mapContainerRef.current;

    if (shouldShowMap) {
      // Clean up any existing map first
      cleanupMap();

      // Small delay to ensure DOM is ready and previous cleanup is complete
      const timeoutId = setTimeout(() => {
        createMap();
      }, 50);

      return () => {
        clearTimeout(timeoutId);
      };
    } else {
      // If we shouldn't show the map, clean it up
      cleanupMap();
    }

  }, [isEditing, addressVerified, formData.location.latitude, formData.location.longitude, formData.name, formData.address, isAdjustingPin, formData.location.formattedAddress]);

  // Improved function to verify an address
  const verifyAddress = async (address: string) => {
    if (!address || address.length < 5) return false;

    setIsVerifyingAddress(true);
    setAddressVerified(false);

    try {
      const geoResult = await verifyAddressWithBackup(address);

      if (geoResult) {
        console.log("Geocoding result:", geoResult.display_name);

        // Parse the formatted address to extract components
        let streetNumber = '';
        let route = '';
        let locality = '';
        let administrativeArea = '';
        let postalCode = '';
        let country = '';
        let barrio = '';

        // Improved parsing logic for OpenStreetMap results
        const addressParts = geoResult.display_name.split(',').map(part => part.trim());

        // First part is typically street and number
        if (addressParts.length >= 1) {
          const firstPart = addressParts[0];
          const numberMatch = firstPart.match(/\d+/);
          if (numberMatch) {
            streetNumber = numberMatch[0];
            route = firstPart.replace(numberMatch[0], '').trim();
          } else {
            route = firstPart;
          }
        }

        // Debug logging
        console.log("Geocoding parsing results:", {
          firstPart: addressParts[0],
          streetNumber,
          route,
          locality,
          administrativeArea,
          userStreetAddress: formData.streetAddress
        });

        // For addresses in Buenos Aires, the typical format is:
        // "Street Number, Neighborhood, District, City, Province, Postal Code, Country"

        // Try to identify barrio/neighborhood (usually the 2nd or 3rd part)
        if (addressParts.length >= 2) {
          // Check if any part matches a known barrio in CABA
          for (let i = 1; i < Math.min(4, addressParts.length); i++) {
            const part = addressParts[i];
            const matchedBarrio = CABA_LOCATIONS.find(loc =>
              loc.name.toLowerCase() === part.toLowerCase()
            );

            if (matchedBarrio) {
              barrio = matchedBarrio.name;
              break;
            }
          }
        }

        // Try to determine if this is in CABA or GBA based on the full address
        const isInCABA = geoResult.display_name.toLowerCase().includes("ciudad autónoma") ||
                         geoResult.display_name.toLowerCase().includes("capital federal") ||
                         geoResult.display_name.toLowerCase().includes("comuna");

        const isInGBA = !isInCABA &&
                       (geoResult.display_name.toLowerCase().includes("buenos aires") ||
                        geoResult.display_name.toLowerCase().includes("provincia"));

        // Set locality based on barrio or district
        if (barrio) {
          locality = barrio;
        } else if (addressParts.length >= 2) {
          // If no barrio was identified, use the second part as locality
          locality = addressParts[1];
        }

        // Set administrative area
        if (isInCABA) {
          administrativeArea = "Ciudad Autónoma de Buenos Aires";
        } else if (isInGBA) {
          administrativeArea = "Buenos Aires";
        } else if (addressParts.length >= 3) {
          administrativeArea = addressParts[2];
        }

        // Look for postal code pattern - more specific for Argentina
        // Argentine postal codes: CDDDDAAA format (C1426DQG) or simpler 4-digit codes (1426)
        // But we need to avoid matching street numbers, so we look for postal codes in context
        const argPostalCodeMatch = geoResult.display_name.match(/\b[A-Z]\d[A-Z][A-Z]\d[A-Z]\d\b/i); // New format like C1426DQG
        const oldPostalCodeMatch = geoResult.display_name.match(/,\s*(\d{4})\s*,/); // 4 digits between commas
        
        if (argPostalCodeMatch) {
          postalCode = argPostalCodeMatch[0];
        } else if (oldPostalCodeMatch) {
          postalCode = oldPostalCodeMatch[1];
        }

        // Last part is usually country
        if (addressParts.length > 0) {
          country = addressParts[addressParts.length - 1];
        }

        // Create Argentine-style formatted address
        const argentineFormattedAddress = formatArgentineAddress(
          streetNumber, 
          route, 
          locality, 
          administrativeArea, 
          formData.streetAddress // Use user's input as fallback
        );

        // Update form data with coordinates and parsed components
        setFormData(prev => ({
          ...prev,
          location: {
            latitude: geoResult.lat,
            longitude: geoResult.lon,
            formattedAddress: argentineFormattedAddress,
            streetNumber,
            route,
            locality,
            administrativeArea,
            postalCode,
            country,
            placeId: prev.location.placeId || '', // Preserve placeId if exists
          },
          // Also update the structured address fields if they're empty
          streetAddress: prev.streetAddress || `${route} ${streetNumber}`.trim(),
          city: prev.city || locality,
          state: prev.state || administrativeArea,
          postalCode: prev.postalCode || postalCode,
          country: country || prev.country,
        }));

        // First, prioritize determining region based on administrative area or display name
        if (isInCABA) {
          console.log("Determined to be in CABA based on address components");
          setSelectedRegion("caba");

          // Now try to find the specific barrio
          if (barrio) {
            const cabaLocation = CABA_LOCATIONS.find(loc =>
              loc.name.toLowerCase() === barrio.toLowerCase()
            );

            if (cabaLocation) {
              console.log("Found CABA barrio match:", cabaLocation.name);
              setSelectedLocationId(cabaLocation.id);

              // Update form data with the matched location
              setFormData(prev => ({
                ...prev,
                city: cabaLocation.name,
                state: "Ciudad Autónoma de Buenos Aires",
                location: {
                  ...prev.location,
                  locality: cabaLocation.name,
                  administrativeArea: "Ciudad Autónoma de Buenos Aires"
                }
              }));
            } else {
              // Keep the barrio name but don't set a specific ID
              setSelectedLocationId("");
            }
          } else {
            // No specific barrio identified
            setSelectedLocationId("");
          }

          setAddressVerified(true);
          return true;
        } else if (isInGBA) {
          console.log("Determined to be in GBA based on address components");
          setSelectedRegion("gba");

          // Check if any part matches a known municipio in GBA
          let municipio = '';
          for (let i = 1; i < Math.min(4, addressParts.length); i++) {
            const part = addressParts[i];
            // Make sure we're not matching a street name with a municipio
            // by checking if the part is not the same as the route
            if (part.toLowerCase() !== route.toLowerCase()) {
              const matchedMunicipio = GBA_LOCATIONS.find(loc =>
                loc.name.toLowerCase() === part.toLowerCase()
              );

              if (matchedMunicipio) {
                municipio = matchedMunicipio.name;
                console.log("Found GBA municipio match:", matchedMunicipio.name);
                setSelectedLocationId(matchedMunicipio.id);

                // Update form data with the matched location
                setFormData(prev => ({
                  ...prev,
                  city: matchedMunicipio.name,
                  state: "Buenos Aires",
                  location: {
                    ...prev.location,
                    locality: matchedMunicipio.name,
                    administrativeArea: "Buenos Aires"
                  }
                }));
                break;
              }
            }
          }

          if (!municipio) {
            // No specific municipio identified
            setSelectedLocationId("");
          }

          setAddressVerified(true);
          return true;
        }

        // If we couldn't determine the region from the address components,
        // fall back to trying to match the locality with our location data
        if (locality && locality.toLowerCase() !== route.toLowerCase()) {
          // Only use exact match and make sure locality is not the same as the street name
          const cabaLocation = CABA_LOCATIONS.find(loc =>
            loc.name.toLowerCase() === locality.toLowerCase()
          );

          const gbaLocation = GBA_LOCATIONS.find(loc =>
            loc.name.toLowerCase() === locality.toLowerCase()
          );

          if (cabaLocation) {
            console.log("Found CABA location match from locality:", cabaLocation.name);
            setSelectedRegion("caba");
            setSelectedLocationId(cabaLocation.id);

            // Update form data with the matched location
            setFormData(prev => ({
              ...prev,
              city: cabaLocation.name,
              state: "Ciudad Autónoma de Buenos Aires",
              location: {
                ...prev.location,
                locality: cabaLocation.name,
                administrativeArea: "Ciudad Autónoma de Buenos Aires"
              }
            }));

            setAddressVerified(true);
            return true;
          }

          if (gbaLocation) {
            // Extra check to avoid confusing street names with municipios
            // Only accept this match if the locality is not similar to the street name
            if (route.toLowerCase().indexOf(locality.toLowerCase()) === -1 &&
                locality.toLowerCase().indexOf(route.toLowerCase()) === -1) {
              console.log("Found GBA location match from locality:", gbaLocation.name);
              setSelectedRegion("gba");
              setSelectedLocationId(gbaLocation.id);

              // Update form data with the matched location
              setFormData(prev => ({
                ...prev,
                city: gbaLocation.name,
                state: "Buenos Aires",
                location: {
                  ...prev.location,
                  locality: gbaLocation.name,
                  administrativeArea: "Buenos Aires"
                }
              }));

              setAddressVerified(true);
              return true;
            } else {
              console.log("Ignored potential GBA match because locality might be confused with street name");
            }
          }

          // If we couldn't find a match but we have administrativeArea, try to determine region
          if (administrativeArea) {
            if (administrativeArea.toLowerCase().includes("buenos aires") &&
                !administrativeArea.toLowerCase().includes("ciudad")) {
              // It's likely GBA
              console.log("No exact match, but determined to be in GBA based on administrativeArea");
              setSelectedRegion("gba");
              // Keep the locality as is, but don't set a specific municipio
              setSelectedLocationId("");

              setAddressVerified(true);
              return true;
            } else if (administrativeArea.toLowerCase().includes("ciudad") ||
                      administrativeArea.toLowerCase().includes("capital")) {
              // It's likely CABA
              console.log("No exact match, but determined to be in CABA based on administrativeArea");
              setSelectedRegion("caba");
              // Keep the locality as is, but don't set a specific barrio
              setSelectedLocationId("");

              setAddressVerified(true);
              return true;
            }
          }

          // If we still couldn't determine the region, keep the current selection
          // instead of resetting to "none"
          console.log("Could not determine region from geocoding results, keeping current selection");
        }

        // If we get here, we couldn't determine the region from any of the above methods
        // but we still have coordinates, so mark as verified
        setAddressVerified(true);
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error verifying address:", error);
      return false;
    } finally {
      setIsVerifyingAddress(false);
    }
  };

  // Function to create a combined address from separate fields
  const createCombinedAddress = () => {
    // Check if we have at least the street and a region selected
    if (!formData.streetAddress || selectedRegion === "none") {
      return null;
    }

    // Get the location name based on the selected ID or use the city from form data
    let locationName = formData.city;

    if (selectedLocationId) {
      const locations = selectedRegion === "caba" ? CABA_LOCATIONS : GBA_LOCATIONS;
      const location = locations.find(loc => loc.id === selectedLocationId);
      if (location) {
        locationName = location.name;
      }
    }

    // If we still don't have a location name, use a default based on the region
    if (!locationName) {
      locationName = selectedRegion === "caba" ? "Buenos Aires" : "Buenos Aires";
    }

    // For CABA, use "Ciudad Autónoma de Buenos Aires" as the state
    // For GBA, use "Buenos Aires" as the state
    const state = selectedRegion === "caba" ? "Ciudad Autónoma de Buenos Aires" : "Buenos Aires";

    const addressParts = [
      formData.streetAddress,
      locationName,
      state,
      formData.postalCode,
      formData.country || 'Argentina'
    ].filter(Boolean); // Remove empty parts

    return addressParts.join(', ');
  };

  // Define CSV row type
  interface CSVPatientRow {
    Nombre: string;
    Apellido: string;
    DNI: string;
    Teléfono: string;
    Email?: string;
  }

  // Define imported patient type
  interface ImportedPatient {
    name: string;
    dni: string;
    phone: string;
    email: string;
    coverage: string;
    createdByMedicalCenterId?: string; // Track which medical center created this patient
    isDuplicate?: boolean; // Flag to mark duplicate patients
  }

  // Patient import dialog state
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [importedPatients, setImportedPatients] = useState<ImportedPatient[]>([])
  const [importErrors, setImportErrors] = useState<string[]>([])
  const [importSuccess, setImportSuccess] = useState<string | null>(null)
  const [isImporting, setIsImporting] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)
  const csvFileInputRef = useRef<HTMLInputElement>(null)
  const [previewImage, setPreviewImage] = useState<string | null>(formData.logoUrl || null)

  if (!activeMedicalCenter) {
    return <div>No se encuentra el establecimiento activo.</div>
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleBillingChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      billing: {
        ...prev.billing,
        [name]: value
      }
    }))
  }

  const handleOpeningHoursChange = (day: keyof OpeningHours, field: 'start' | 'end', value: string) => {
    setFormData(prev => ({
      ...prev,
      openingHours: {
        ...prev.openingHours,
        [day]: {
          ...prev.openingHours[day],
          [field]: value
        }
      }
    }))
  }

  const toggleDayEnabled = (day: keyof OpeningHours) => {
    setFormData(prev => ({
      ...prev,
      openingHours: {
        ...prev.openingHours,
        [day]: {
          ...prev.openingHours[day],
          enabled: !prev.openingHours[day].enabled
        }
      }
    }))
  }



  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // For this demo, we'll just create a fake URL
    // In a real implementation, you would upload to a server and get a URL back
    const tempUrl = URL.createObjectURL(file)
    setPreviewImage(tempUrl)
    setFormData(prev => ({
      ...prev,
      logoUrl: tempUrl
    }))
  }

  const handleClearLogo = () => {
    setPreviewImage(null)
    setFormData(prev => ({
      ...prev,
      logoUrl: ''
    }))
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleSubmit = () => {
    if (activeMedicalCenter) {
      // Get the location ID and region information
      let locationData = {};

      if (selectedRegion !== "none") {
        // Even if no specific barrio/municipio is selected, save the region
        locationData = {
          locationRegion: selectedRegion
        };

        if (selectedLocationId) {
          // Find the selected location
          const locations = selectedRegion === "caba" ? CABA_LOCATIONS : GBA_LOCATIONS;
          const location = locations.find(loc => loc.id === selectedLocationId);

          if (location) {
            locationData = {
              ...locationData,
              locationId: selectedLocationId,
              locationType: location.type
            };
          }
        }
      }

      // Create a clean version of the form data without UI-only fields
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { city, state, postalCode, country, ...cleanFormData } = formData;

      // First create the updated center by merging current data with form data
      const updatedCenter = {
        ...activeMedicalCenter,
        ...cleanFormData,
        floor: formData.floor,
        apartment: formData.apartment,
        // Make sure we save the structured address fields too
        streetAddress: formData.streetAddress,
        // Update the location object with the latest values
        location: {
          ...formData.location,
          // Ensure these values are updated from the UI fields
          locality: formData.city,
          administrativeArea: formData.state,
          postalCode: formData.postalCode,
          country: formData.country
        },
        // Add location ID and region information
        ...locationData
      };

      // Ensure both schedule formats are properly synced
      const syncedCenter = syncMedicalCenterSchedules(updatedCenter);

      // Update the medical center
      updateMedicalCenter(syncedCenter);

      toast({
        title: "Cambios guardados",
        description: "La configuración del establecimiento ha sido actualizada.",
      });

      setIsEditing(false);
      setIsAdjustingPin(false);

      // Log the saved data to verify both formats
      console.log("Saved medical center with synced schedules:", syncedCenter);
    }
  }

  const handleCancel = () => {
    // Reset form data to original medical center data
    setFormData(initializeFormData());
    setPreviewImage(activeMedicalCenter?.logoUrl || null)

    // Reset region and location selection
    const initialRegion = determineInitialRegion();
    const initialLocationId = determineInitialLocationId();
    console.log("Resetting selectors on cancel:", { initialRegion, initialLocationId });
    setSelectedRegion(initialRegion);
    setSelectedLocationId(initialLocationId);

    // Restore verification status based on whether coordinates exist
    setAddressVerified(
      !!(activeMedicalCenter?.location?.latitude && activeMedicalCenter?.location?.longitude)
    )
    setIsAdjustingPin(false)
    setIsEditing(false)
  }

  // Patient import functions
  const handleOpenImportDialog = () => {
    // Reset import state
    setImportedPatients([])
    setImportErrors([])
    setImportSuccess(null)
    setIsImporting(false)
    setIsImportDialogOpen(true)
  }

  const handleCloseImportDialog = () => {
    setIsImportDialogOpen(false)
  }

  const handleDownloadTemplate = () => {
    // Create a worksheet with headers
    const headers = ["Nombre", "Apellido", "DNI", "Teléfono", "Email"]
    const ws = XLSX.utils.aoa_to_sheet([headers])

    // Create a workbook
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, "Pacientes")

    // Generate the Excel file (xlsx) instead of CSV to maintain column format
    // This will be easier for users to fill out
    XLSX.writeFile(wb, `plantilla-pacientes-${activeMedicalCenter.name.replace(/\s+/g, '-')}.xlsx`)
  }

  const handleImportFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsImporting(true)
    setImportErrors([])
    setImportSuccess(null)

    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = e.target?.result
        if (!data) {
          setImportErrors(["No se pudo leer el archivo"])
          setIsImporting(false)
          return
        }

        // Parse CSV data
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as CSVPatientRow[]

        // Validate and process data
        const errors: string[] = []
        const validPatients: ImportedPatient[] = []

        jsonData.forEach((row, index) => {
          // Check required fields
          if (!row.Nombre || !row.Apellido || !row.DNI || !row.Teléfono) {
            errors.push(`Fila ${index + 2}: Faltan campos obligatorios (Nombre, Apellido, DNI, Teléfono)`)
            return
          }

          // Validate DNI format (7-8 digits)
          const dni = row.DNI.toString().trim()
          if (!/^\d{7,8}$/.test(dni)) {
            errors.push(`Fila ${index + 2}: El DNI debe tener entre 7 y 8 dígitos`)
            return
          }

          // Validate phone format (simple check)
          const phone = row.Teléfono.toString().trim()
          if (phone.length < 8) {
            errors.push(`Fila ${index + 2}: El teléfono no es válido`)
            return
          }

          // Create patient object
          const patient: ImportedPatient = {
            name: `${row.Nombre.trim()} ${row.Apellido.trim()}`,
            dni: dni,
            phone: phone.startsWith('+') ? phone :
                   phone.startsWith('54') ? `+${phone}` :
                   `+54${phone}`, // Format phone number with appropriate country code
            email: row.Email || '',
            coverage: "Sin Cobertura", // Default coverage
            createdByMedicalCenterId: activeMedicalCenterId // Track which medical center created this patient
          }

          validPatients.push(patient)
        })

        if (errors.length > 0) {
          setImportErrors(errors)
          setIsImporting(false)
          return
        }

        // Check for duplicate patients by combination of DNI and phone
        const patientsWithDuplicateStatus = validPatients.map(patient => {
          // Check if a patient with this DNI exists
          const existingPatient = getPatientByDni(patient.dni)

          // Consider it a duplicate only if both DNI and phone match
          const isDuplicate = existingPatient ?
            existingPatient.phone === patient.phone :
            false

          return {
            ...patient,
            isDuplicate: isDuplicate
          }
        })

        // Store valid patients for confirmation
        setImportedPatients(patientsWithDuplicateStatus)
        setIsImporting(false)

      } catch (error) {
        setImportErrors([`Error al procesar el archivo: ${error instanceof Error ? error.message : 'Error desconocido'}`])
        setIsImporting(false)
      }
    }

    reader.onerror = () => {
      setImportErrors(["Error al leer el archivo"])
      setIsImporting(false)
    }

    // Use readAsArrayBuffer instead of readAsBinaryString (which is deprecated)
    reader.readAsArrayBuffer(file)
  }

  const handleConfirmImport = () => {
    setIsImporting(true)

    try {
      // Add each patient to the system, skipping duplicates
      const importedCount = importedPatients.length
      let successCount = 0

      // Only add non-duplicate patients
      importedPatients.filter(p => !p.isDuplicate).forEach(patient => {
        try {
          addPatient(patient)
          successCount++
        } catch (error) {
          console.error("Error adding patient:", error)
        }
      })

      // Show success message with duplicate information
      const duplicateCount = importedPatients.filter(p => p.isDuplicate).length
      const successMessage = duplicateCount > 0
        ? `Se importaron ${successCount} pacientes correctamente. Se omitieron ${duplicateCount} pacientes duplicados.`
        : `Se importaron ${successCount} de ${importedCount} pacientes correctamente.`

      setImportSuccess(successMessage)
      setImportedPatients([]) // Clear imported patients

      // Close dialog after a delay
      setTimeout(() => {
        setIsImportDialogOpen(false)
        toast({
          title: "Importación completada",
          description: successMessage,
        })
      }, 2000)

    } catch (error) {
      setImportErrors([`Error al importar pacientes: ${error instanceof Error ? error.message : 'Error desconocido'}`])
    } finally {
      setIsImporting(false)
    }
  }



  const daysOfWeek = [
    { key: 'monday' as keyof OpeningHours, label: 'Lunes' },
    { key: 'tuesday' as keyof OpeningHours, label: 'Martes' },
    { key: 'wednesday' as keyof OpeningHours, label: 'Miércoles' },
    { key: 'thursday' as keyof OpeningHours, label: 'Jueves' },
    { key: 'friday' as keyof OpeningHours, label: 'Viernes' },
    { key: 'saturday' as keyof OpeningHours, label: 'Sábado' },
    { key: 'sunday' as keyof OpeningHours, label: 'Domingo' },
  ]

  // Updated renderAddressField function with location dropdowns
  const renderAddressField = () => (
    <div className="space-y-2">
      <Label htmlFor="address" className="flex items-center gap-1 text-gray-700">
        <MapPin className="h-4 w-4 text-gray-500" />
        Dirección
      </Label>

      {/* Structured address fields */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
        {/* Combined street name and number */}
        <div className="md:col-span-3">
          <Label htmlFor="streetAddress" className="text-xs text-gray-700">
            Calle y número
          </Label>
          <Input
            id="streetAddress"
            name="streetAddress"
            value={formData.streetAddress}
            onChange={(e) => {
              const streetAddress = e.target.value;

              setFormData(prev => ({
                ...prev,
                streetAddress: streetAddress
              }));

              // Reset verification status when field changes
              setAddressVerified(false);
            }}
            disabled={!isEditing}
            placeholder="Av. Corrientes 1234"
            className="text-sm mt-1"
          />
        </div>

        {/* Region Selection (CABA/GBA) */}
        <div>
          <Label htmlFor="region" className="text-xs text-gray-700">
            Región
          </Label>
          <Select
            value={selectedRegion}
            onValueChange={(value: "none" | "caba" | "gba") => {
              setSelectedRegion(value);

              // Reset location when region changes
              setSelectedLocationId("");

              // Update form data with region name
              if (value === "caba") {
                setFormData(prev => ({
                  ...prev,
                  state: "Ciudad Autónoma de Buenos Aires",
                  city: "Buenos Aires",
                  location: {
                    ...prev.location,
                    administrativeArea: "Ciudad Autónoma de Buenos Aires",
                    locality: "Buenos Aires"
                  }
                }));

                // Reset verification status when field changes
                setAddressVerified(false);
              } else if (value === "gba") {
                setFormData(prev => ({
                  ...prev,
                  state: "Buenos Aires",
                  location: {
                    ...prev.location,
                    administrativeArea: "Buenos Aires"
                  }
                }));

                // For GBA, we'll wait for a specific municipio to be selected
              } else {
                // Reset verification status when field changes to none
                setAddressVerified(false);
              }
            }}
            disabled={!isEditing}
          >
            <SelectTrigger className="text-sm mt-1">
              <SelectValue placeholder="Seleccionar región" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Seleccionar</SelectItem>
              <SelectItem value="caba">Capital Federal</SelectItem>
              <SelectItem value="gba">Gran Buenos Aires</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Location Selection (Barrio/Municipio) - Only shown when region is selected */}
      {selectedRegion !== "none" && (
        <div className="mt-2">
          <Label htmlFor="location" className="text-xs text-gray-700">
            {selectedRegion === "caba" ? "Barrio" : "Municipio"}
          </Label>
          <Select
            value={selectedLocationId}
            onValueChange={(value) => {
              setSelectedLocationId(value);

              // Find the selected location
              const locations = selectedRegion === "caba" ? CABA_LOCATIONS : GBA_LOCATIONS;
              const location = locations.find(loc => loc.id === value);

              if (location) {
                // Update form data with location name
                setFormData(prev => ({
                  ...prev,
                  city: location.name,
                  location: {
                    ...prev.location,
                    locality: location.name
                  }
                }));

                // Reset verification status when field changes
                setAddressVerified(false);
              } else {
                // Reset verification status when field changes to empty
                setAddressVerified(false);
              }
            }}
            disabled={!isEditing}
          >
            <SelectTrigger className="text-sm">
              <SelectValue placeholder={`Seleccionar ${selectedRegion === "caba" ? "barrio" : "municipio"}`} />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>{selectedRegion === "caba" ? "Barrios" : "Municipios"}</SelectLabel>
                {(selectedRegion === "caba" ? CABA_LOCATIONS : GBA_LOCATIONS).map((location) => (
                  <SelectItem key={location.id} value={location.id}>
                    {location.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        {/* Postal Code */}
        <div>
          <Label htmlFor="postalCode" className="text-xs text-gray-700">
            Código Postal
          </Label>
          <Input
            id="postalCode"
            name="postalCode"
            value={formData.postalCode}
            onChange={(e) => {
              const newPostalCode = e.target.value;

              // Update both the top-level postalCode and the nested location.postalCode
              setFormData(prev => ({
                ...prev,
                postalCode: newPostalCode,
                location: {
                  ...prev.location,
                  postalCode: newPostalCode
                }
              }));

              // Log the update for debugging
              console.log("Updated postal code:", newPostalCode);

              // Reset verification status when field changes
              setAddressVerified(false);
            }}
            disabled={!isEditing}
            placeholder="C1051"
            className="text-sm mt-1"
          />
        </div>

        {/* Country - Simplified to just Argentina */}
        <div className="md:col-span-2">
          <Label htmlFor="country" className="text-xs text-gray-700">
            País
          </Label>
          <Input
            id="country"
            name="country"
            value={formData.country}
            onChange={(e) => {
              const newCountry = e.target.value;

              setFormData(prev => ({
                ...prev,
                country: newCountry,
                location: {
                  ...prev.location,
                  country: newCountry
                }
              }));

              // Reset verification status when field changes
              setAddressVerified(false);
            }}
            disabled={!isEditing}
            placeholder="Argentina"
            className="text-sm mt-1"
          />
        </div>
      </div>

      {/* Simplified Address Verification - Only shown in edit mode */}
      {isEditing && !addressVerified && (
        <div className="mt-3 flex justify-end">
          <Button
            size="sm"
            className="text-sm bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => {
              const combinedAddress = createCombinedAddress();
              if (combinedAddress) {
                setFormData(prev => ({...prev, address: combinedAddress}));
                verifyAddress(combinedAddress);
              } else {
                // Show a toast if we can't create a combined address
                toast({
                  title: "Información incompleta",
                  description: "Por favor, ingrese al menos la calle y seleccione una región."
                });
              }
            }}
            disabled={isVerifyingAddress || !formData.streetAddress || selectedRegion === "none"}
          >
            {isVerifyingAddress ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Verificando...
              </>
            ) : (
              <>
                <MapPin className="h-4 w-4 mr-2" />
                Buscar dirección
              </>
            )}
          </Button>
        </div>
      )}

      {/* Floor and apartment fields - shown in both edit and view modes */}
      <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="floor" className="text-sm text-gray-700">
            Piso (Opcional)
          </Label>
          <Input
            id="floor"
            name="floor"
            value={formData.floor}
            onChange={handleInputChange}
            disabled={!isEditing}
            className="text-sm mt-1"
            placeholder="Ej: 3, PB, Planta Baja"
          />
        </div>

        <div>
          <Label htmlFor="apartment" className="text-sm text-gray-700">
            Departamento (Opcional)
          </Label>
          <Input
            id="apartment"
            name="apartment"
            value={formData.apartment}
            onChange={handleInputChange}
            disabled={!isEditing}
            className="text-sm mt-1"
            placeholder="Ej: A, 42, Consultorio 3"
          />
        </div>
      </div>

      {/* Show map preview if address is verified */}
      {isEditing && addressVerified && formData.location.latitude && formData.location.longitude && (
        <div className="mt-2 border border-gray-200 rounded-md overflow-hidden">
          <div className="bg-gray-50 px-3 py-2 text-xs text-gray-500 flex justify-between items-center">
            <span>Ubicación verificada</span>
            <a
              href={`https://www.google.com/maps?q=${formData.location.latitude},${formData.location.longitude}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline"
            >
              Ver en Google Maps
            </a>
          </div>

          {/* Existing map and styles */}
          <style jsx global>{`
            .custom-map-marker {
              background: transparent;
              border: none;
            }
            .marker-container {
              position: relative;
              width: 42px;
              height: 42px;
              transform: translateY(-12px);
            }
            .marker-svg {
              width: 36px;
              height: 42px;
              filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.3));
            }
            .modern-popup .leaflet-popup-content-wrapper {
              background: white;
              border-radius: 8px;
              box-shadow: 0 3px 14px rgba(0, 0, 0, 0.15);
              padding: 0;
            }
            .modern-popup .leaflet-popup-content {
              margin: 8px;
              line-height: 1.5;
            }
            .modern-popup .leaflet-popup-tip {
              background: white;
            }
          `}</style>
          <div ref={mapContainerRef} className="h-[200px] w-full"></div>

          {/* Map adjustment controls */}
          <div className="bg-gray-50 px-3 py-2 flex justify-between items-center">
            <div className="text-xs text-gray-500">
              {formData.location.formattedAddress}
            </div>

            <div>
              <Button
                onClick={() => {
                  setIsAdjustingPin(!isAdjustingPin);
                  if (!isAdjustingPin && markerRef.current) {
                    // Make marker draggable
                    markerRef.current.dragging?.enable();
                  } else if (isAdjustingPin && markerRef.current) {
                    // Disable dragging
                    markerRef.current.dragging?.disable();
                  }
                }}
                size="sm"
                variant={isAdjustingPin ? "default" : "outline"}
                className={`text-xs ${isAdjustingPin ? 'bg-blue-100 text-blue-700 border-blue-300' : ''}`}
              >
                <LocateFixed className="h-3 w-3 mr-1" />
                {isAdjustingPin ? "Finalizar ajuste" : "Ajustar ubicación"}
              </Button>
            </div>
          </div>

          {isAdjustingPin && (
            <div className="bg-blue-50 p-3 text-xs">
              <div className="flex items-start">
                <AlertCircle className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                <p className="text-blue-700">
                  Haz clic en el mapa o arrastra el marcador para ajustar la ubicación exacta.
                  Esto es útil cuando la ubicación automática no es precisa.
                </p>
              </div>

              <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center">
                  <span className="font-medium text-gray-700 mr-1">Latitud:</span>
                  <span>{formData.location.latitude?.toFixed(6)}</span>
                </div>
                <div className="flex items-center">
                  <span className="font-medium text-gray-700 mr-1">Longitud:</span>
                  <span>{formData.location.longitude?.toFixed(6)}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Patient Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Importar Pacientes</DialogTitle>
            <DialogDescription>
              Importa pacientes desde un archivo Excel. Los pacientes serán agregados a este establecimiento.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {!importSuccess && (
              <>
                <div className="flex flex-col gap-2">
                  <Label htmlFor="csvFile">Archivo Excel</Label>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleDownloadTemplate}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      Descargar Plantilla
                    </Button>

                    <div className="relative flex-1">
                      <Input
                        id="csvFile"
                        type="file"
                        accept=".xlsx,.xls"
                        onChange={handleImportFile}
                        ref={csvFileInputRef}
                        className="cursor-pointer"
                      />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">
                    Descarga la plantilla Excel, complétala y súbela para importar pacientes.
                  </p>
                </div>

                {importErrors.length > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <h4 className="text-sm font-medium text-red-800 mb-2">Errores de importación:</h4>
                    <ul className="text-xs text-red-700 list-disc pl-5 space-y-1">
                      {importErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {importedPatients.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium">Pacientes a importar: {importedPatients.length}</h4>

                    {/* Show warning about duplicate patients if any exist */}
                    {importedPatients.some(p => p.isDuplicate) && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 text-sm">
                        <div className="flex items-start">
                          <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <p className="font-medium text-yellow-800">Pacientes duplicados detectados</p>
                            <p className="text-yellow-700 mt-1">
                              Los pacientes marcados en amarillo ya existen en la base de datos (mismo DNI y teléfono) y no serán importados nuevamente.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="max-h-[200px] overflow-y-auto border rounded-md">
                      <table className="w-full text-xs">
                        <thead className="bg-gray-50 sticky top-0">
                          <tr>
                            <th className="px-3 py-2 text-left">Nombre</th>
                            <th className="px-3 py-2 text-left">DNI</th>
                            <th className="px-3 py-2 text-left">Teléfono</th>
                            <th className="px-3 py-2 text-left">Email</th>
                          </tr>
                        </thead>
                        <tbody>
                          {importedPatients.map((patient, index) => (
                            <tr
                              key={index}
                              className={
                                patient.isDuplicate
                                  ? 'bg-yellow-50'
                                  : index % 2 === 0
                                    ? 'bg-white'
                                    : 'bg-gray-50'
                              }
                            >
                              <td className="px-3 py-2 border-t">
                                {patient.isDuplicate && (
                                  <span className="inline-block w-2 h-2 bg-yellow-400 rounded-full mr-2"></span>
                                )}
                                {patient.name}
                              </td>
                              <td className="px-3 py-2 border-t">{patient.dni}</td>
                              <td className="px-3 py-2 border-t">{patient.phone}</td>
                              <td className="px-3 py-2 border-t">{patient.email || '-'}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </>
            )}

            {importSuccess && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4 text-center">
                <div className="flex justify-center mb-2">
                  <div className="rounded-full bg-green-100 p-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <h4 className="text-green-800 font-medium mb-1">Importación exitosa</h4>
                <p className="text-green-700 text-sm">{importSuccess}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            {!importSuccess && (
              <>
                <Button variant="outline" onClick={handleCloseImportDialog}>
                  Cancelar
                </Button>

                {importedPatients.length > 0 && (
                  <Button
                    onClick={handleConfirmImport}
                    disabled={isImporting}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isImporting ? 'Importando...' : 'Confirmar Importación'}
                  </Button>
                )}
              </>
            )}

            {importSuccess && (
              <Button onClick={handleCloseImportDialog}>
                Cerrar
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Card className="bg-white shadow-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building className="h-5 w-5 text-blue-600" />
              <div>
                <CardTitle className="text-[1.3rem] font-semibold text-gray-900">
                  Configuración del Establecimiento
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Configure la información básica del establecimiento médico.
                </CardDescription>
              </div>
            </div>
            {!isEditing ? (
              <Button
                onClick={() => setIsEditing(true)}
                variant="outline"
              >
                Editar Información
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  onClick={handleCancel}
                  variant="outline"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleSubmit}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  Guardar Cambios
                </Button>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="general" className="w-full">
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="general">Información General</TabsTrigger>
                <TabsTrigger value="hours">Horarios</TabsTrigger>
                <TabsTrigger value="billing">Datos de Facturación</TabsTrigger>
              </TabsList>

              <Button
                onClick={handleOpenImportDialog}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Users className="h-4 w-4" />
                Importar Pacientes
              </Button>
            </div>

            <TabsContent value="general" className="space-y-6">
              <div className="flex flex-col gap-6">
                {/* Establishment Information - Reorganized to take full width */}
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                  {/* Logo section - Now just 1/4 of the space */}
                  <div className="flex flex-col items-center space-y-3">
                    <div className="relative">
                      <div className="w-28 h-28 rounded-2xl bg-white shadow-lg p-0.5 border border-gray-200 relative">
                        <div className="w-full h-full rounded-xl overflow-hidden">
                          {previewImage ? (
                            <Image 
                              src={previewImage} 
                              alt={formData.name}
                              fill
                              className="object-cover"
                              sizes="112px"
                            />
                          ) : (
                            <>
                              <div className="absolute inset-0 bg-gradient-to-br from-[#0070F3] to-[#0070F3]/80"></div>
                              <div className="absolute inset-0 bg-[url('/images/pattern-dots.png')] bg-repeat opacity-5"></div>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-white text-2xl font-bold">
                                  {formData.name.substring(0, 2).toUpperCase()}
                                </span>
                              </div>
                            </>
                          )}
                        </div>
                        {isEditing && (
                          <div className="absolute -bottom-2 -right-2 flex gap-1">
                            <Button
                              type="button"
                              size="icon"
                              variant="secondary"
                              className="h-8 w-8 rounded-full bg-white border border-gray-200 shadow-sm hover:bg-gray-50"
                              onClick={() => fileInputRef.current?.click()}
                            >
                              <Camera className="h-4 w-4" />
                            </Button>
                            {previewImage && (
                              <Button
                                type="button"
                                size="icon"
                                variant="destructive"
                                className="h-8 w-8 rounded-full"
                                onClick={handleClearLogo}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}
                            <input
                              type="file"
                              className="hidden"
                              ref={fileInputRef}
                              accept="image/*"
                              onChange={handleLogoUpload}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">Logo del establecimiento</p>
                  </div>

                  {/* General Information Form - Now takes 3/4 of the space */}
                  <div className="lg:col-span-3 space-y-5">
                    {/* Establishment name */}
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-gray-700">Nombre del establecimiento</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="w-full"
                      />
                    </div>



                    {/* Location information */}
                    {renderAddressField()}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="flex items-center gap-1 text-gray-700">
                          <Phone className="h-4 w-4 text-gray-500" />
                          Teléfono
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="flex items-center gap-1 text-gray-700">
                          <Mail className="h-4 w-4 text-gray-500" />
                          Email
                        </Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          disabled={!isEditing}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="hours" className="space-y-6">
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertTitle>Horarios de atención</AlertTitle>
                <AlertDescription>
                  Configure los horarios en los que el establecimiento permanece abierto.
                  Esto ayudará a los pacientes a saber cuándo pueden contactarlo.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                {daysOfWeek.map((day) => (
                  <div key={day.key} className="flex items-center space-x-4 p-3 rounded-lg border border-gray-200">
                    <div className="w-32">
                      <Label className="text-gray-700 flex items-center gap-2">
                        <Switch
                          checked={formData.openingHours[day.key].enabled}
                          onCheckedChange={() => toggleDayEnabled(day.key)}
                          disabled={!isEditing}
                        />
                        {day.label}
                      </Label>
                    </div>

                    <div className="flex items-center gap-3 flex-1">
                      <Input
                        type="time"
                        value={formData.openingHours[day.key].start}
                        onChange={(e) => handleOpeningHoursChange(day.key, 'start', e.target.value)}
                        disabled={!isEditing || !formData.openingHours[day.key].enabled}
                        className="w-32"
                      />
                      <span className="text-gray-500">a</span>
                      <Input
                        type="time"
                        value={formData.openingHours[day.key].end}
                        onChange={(e) => handleOpeningHoursChange(day.key, 'end', e.target.value)}
                        disabled={!isEditing || !formData.openingHours[day.key].enabled}
                        className="w-32"
                      />
                    </div>

                    <div className="w-32 text-center text-sm text-gray-600">
                      {formData.openingHours[day.key].enabled ?
                        `${formData.openingHours[day.key].start} - ${formData.openingHours[day.key].end}` :
                        "Cerrado"
                      }
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="billing" className="space-y-6">
              <Alert>
                <CreditCard className="h-4 w-4" />
                <AlertTitle>Datos de Facturación</AlertTitle>
                <AlertDescription>
                  Esta información se utilizará para emitir las facturas por los servicios contratados.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="w-full md:w-1/2">
                    <Label htmlFor="businessType" className="block mb-2 text-gray-700">Tipo de contribuyente</Label>
                    <select
                      id="businessType"
                      name="businessType"
                      value={formData.billing.businessType}
                      onChange={handleBillingChange}
                      disabled={!isEditing}
                      className="w-full rounded-md border border-gray-300 p-2"
                    >
                      <option value="individual">Persona Física</option>
                      <option value="company">Persona Jurídica</option>
                      <option value="selfEmployed">Monotributista</option>
                      <option value="exempt">Exento</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessName" className="text-gray-700">
                    {formData.billing.businessType === 'individual' ?
                      'Nombre y Apellido' : 'Razón Social'}
                  </Label>
                  <Input
                    id="businessName"
                    name="businessName"
                    value={formData.billing.businessName}
                    onChange={handleBillingChange}
                    disabled={!isEditing}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="taxId" className="text-gray-700">
                    {formData.billing.businessType === 'individual' ?
                      'CUIL' : 'CUIT'}
                  </Label>
                  <Input
                    id="taxId"
                    name="taxId"
                    value={formData.billing.taxId}
                    onChange={handleBillingChange}
                    disabled={!isEditing}
                    placeholder="XX-XXXXXXXX-X"
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="billingAddress" className="text-gray-700">Dirección de facturación</Label>
                  <Input
                    id="billingAddress"
                    name="address"
                    value={formData.billing.address}
                    onChange={handleBillingChange}
                    disabled={!isEditing}
                    className="w-full"
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
