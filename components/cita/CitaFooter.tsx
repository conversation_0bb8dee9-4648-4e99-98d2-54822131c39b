"use client"

import React from "react"
import Image from "next/image"

export const CitaFooter: React.FC = () => {
  return (
    <footer className="bg-white border-t border-gray-200 py-8 mt-16">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <Image
              src="/images/turnera-logo.svg"
              alt="Turnera Logo"
              width={120}
              height={36}
              className="h-7 w-auto mb-3"
            />
            <p className="text-gray-500 text-sm">
              © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
            </p>
          </div>
          <div className="text-gray-500 text-sm">
            Reserve sus turnos médicos de forma segura y eficiente.
          </div>
        </div>
      </div>
    </footer>
  )
}
