import Image from 'next/image'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShieldCheck } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200 py-8">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <Image
              src="/images/turnera-logo.svg"
              alt="Turnera Logo"
              width={120}
              height={36}
              className="h-7 w-auto mb-3"
            />
            <p className="text-gray-500 text-sm">
              © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
            </p>
          </div>

          <div className="flex flex-col items-center md:items-end">
            <div className="flex items-center mb-3">
              <ShieldCheck className="h-4 w-4 text-[#1cd8e1] mr-1.5" />
              <span className="text-sm text-[#1c2533] font-medium">Reservá turnos médicos 24/7</span>
            </div>
            <div className="flex space-x-4">
              <a href="/terminos-y-condiciones">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-full">
                  Términos y Condiciones
                </Button>
              </a>
              <a href="/privacidad">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-full">
                  Privacidad
                </Button>
              </a>
              <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-full">
                Ayuda
              </Button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
} 