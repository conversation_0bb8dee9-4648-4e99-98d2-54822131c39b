"use client"

import { useContext, useState, useEffect } from "react"
import { Doctor<PERSON>ontext } from "@/contexts/DoctorContext"
import { CoverageContext } from "@/contexts/CoverageContext"
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp, AlertCircle, PlusCircle, Info } from "lucide-react"
import AddDoctorCoverageDialog from "./AddDoctorCoverageDialog"
import { CoverageException } from "@/types/doctor"

export default function DoctorCoveragesTab() {
  const { editedConfig, doctors, pendingDoctorCoverageExceptions, setPendingDoctorCoverageExceptions } = useContext(DoctorContext)
  const {
    medicalCoverages,
    getAcceptedCoveragesForCenter,
    getDoctorsAcceptingCoverage,
    doctorCoverageExceptions,
    setDoctorCoverageExceptions,
    setMedicalCoverages
  } = useContext(CoverageContext)

  const [expandedCoverages, setExpandedCoverages] = useState<{ [key: string]: boolean }>({})
  const [expandedDoctorCoverages, setExpandedDoctorCoverages] = useState<{ [key: string]: boolean }>({})
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  // Force re-render when doctor-specific coverages change
  const [forceUpdate, setForceUpdate] = useState(0)
  // State to track coverages that are pending removal (visually only)
  const [pendingRemovals, setPendingRemovals] = useState<string[]>([])

  const toggleCoverageExpansion = (coverageId: string) => {
    setExpandedCoverages((prev) => ({
      ...prev,
      [coverageId]: !prev[coverageId],
    }))
  }

  const toggleDoctorCoverageExpansion = (coverageId: string) => {
    setExpandedDoctorCoverages((prev) => ({
      ...prev,
      [coverageId]: !prev[coverageId],
    }))
  }

  // Update when doctor-specific coverages change or when forceUpdate changes
  useEffect(() => {
    // This effect will run when forceUpdate changes, forcing a re-render
    // which will update the doctorSpecificCoverages list
    console.log('DoctorCoveragesTab: Updating doctor-specific coverages')
  }, [forceUpdate])

  // Initialize pending doctor coverage exceptions when the component mounts
  useEffect(() => {
    if (editedConfig) {
      // Initialize pending doctor coverage exceptions with current exceptions
      const currentExceptions = doctorCoverageExceptions[editedConfig.id] || [];
      setPendingDoctorCoverageExceptions(currentExceptions);
      console.log("DoctorCoveragesTab: Initialized pending exceptions:", currentExceptions);
    }
  }, [editedConfig, doctorCoverageExceptions, setPendingDoctorCoverageExceptions])



  // Custom function to check if a coverage is excluded for the current doctor using pending exceptions
  const isPendingDoctorCoverageExcluded = (_doctorId: string, coverageId: string, planId: string | null = null) => {
    // Special case for Sin Cobertura
    const sinCobertura = medicalCoverages.find(c => c.name === "Sin Cobertura");
    const isSinCobertura = sinCobertura && sinCobertura.id === coverageId;

    // Sin Cobertura is accepted by default for all doctors unless there's an explicit exception
    if (isSinCobertura) {
      const coverageException = pendingDoctorCoverageExceptions.find(
        (e) => e.coverageId === coverageId && e.planId === null
      );
      return coverageException?.excluded || false;
    }

    // If coverage is not accepted by the medical center, it's automatically excluded for all doctors
    if (!getAcceptedCoveragesForCenter().includes(coverageId)) return true;

    if (planId !== null) {
      return pendingDoctorCoverageExceptions.some(
        (e) => e.coverageId === coverageId && e.planId === planId && e.excluded
      );
    }

    const coverageException = pendingDoctorCoverageExceptions.find(
      (e) => e.coverageId === coverageId && e.planId === null
    );
    const coveragePlans = medicalCoverages.find((c) => c.id === coverageId)?.plans || [];
    const allPlansExcluded = coveragePlans.every((plan) =>
      pendingDoctorCoverageExceptions.some(
        (e) => e.coverageId === coverageId && e.planId === plan && e.excluded
      )
    );

    return (
      (coverageException?.excluded &&
        !pendingDoctorCoverageExceptions.some(
          (e) => e.coverageId === coverageId && e.planId !== null && !e.excluded
        )) ||
      allPlansExcluded
    );
  }

  // Custom function to toggle doctor coverage exceptions using pending exceptions
  const togglePendingDoctorCoverageException = (doctorId: string, coverageId: string, planId: string | null = null) => {
    // Special case for Sin Cobertura
    const sinCobertura = medicalCoverages.find(c => c.name === "Sin Cobertura");
    const isSinCobertura = sinCobertura && sinCobertura.id === coverageId;

    // If this is Sin Cobertura, or a center-accepted coverage, allow it to be toggled
    const isValidCoverage = getAcceptedCoveragesForCenter().includes(coverageId) || isSinCobertura;
    if (!isValidCoverage) return;

    const newExceptions = [...pendingDoctorCoverageExceptions];
    const coveragePlans = medicalCoverages.find((c) => c.id === coverageId)?.plans || [];
    const isCoverageExcluded = isPendingDoctorCoverageExcluded(doctorId, coverageId);

    if (planId === null) {
      const isCurrentlyExcluded = isCoverageExcluded;
      const coverageExceptionIndex = newExceptions.findIndex(
        (e) => e.coverageId === coverageId && e.planId === null
      );

      if (isCurrentlyExcluded) {
        // Remove all exceptions for this coverage
        const filteredExceptions = newExceptions.filter((e) => e.coverageId !== coverageId);
        setPendingDoctorCoverageExceptions(filteredExceptions);
      } else {
        // Add exceptions for the coverage and all its plans
        if (coverageExceptionIndex === -1) {
          newExceptions.push({ coverageId, planId: null, excluded: true });
        }

        coveragePlans.forEach((plan) => {
          const planExceptionIndex = newExceptions.findIndex(
            (e) => e.coverageId === coverageId && e.planId === plan
          );
          if (planExceptionIndex === -1) {
            newExceptions.push({ coverageId, planId: plan, excluded: true });
          }
        });

        setPendingDoctorCoverageExceptions(newExceptions);
      }
    } else {
      if (!isCoverageExcluded) {
        const exceptionIndex = newExceptions.findIndex(
          (e) => e.coverageId === coverageId && e.planId === planId
        );

        if (exceptionIndex >= 0) {
          // Remove this specific plan exception
          newExceptions.splice(exceptionIndex, 1);
        } else {
          // Add exception for this specific plan
          newExceptions.push({ coverageId, planId, excluded: true });
        }

        setPendingDoctorCoverageExceptions(newExceptions);
      }
    }
  }

  if (!editedConfig) return null

  // Get coverages accepted by the medical center
  const acceptedCoverageIds = getAcceptedCoveragesForCenter();

  // Get the total number of doctors in the current medical center
  const totalDoctorsInCenter = doctors.length;

  // Get doctor-exclusive coverages (coverages that are accepted only by this doctor)
  // For the UI, we'll use the actual doctorCoverageExceptions to determine which coverages are exclusive
  // This ensures we're showing the current state, not the pending state
  const doctorExclusiveCoverages = medicalCoverages.filter(coverage => {
    // Check if this coverage is accepted by the medical center
    if (!acceptedCoverageIds.includes(coverage.id)) return false;

    // Skip coverages that are visually marked for removal
    if (pendingRemovals.includes(coverage.id)) return false;

    // If there's only one doctor in the medical center, no coverages should be considered "exclusive"
    // because all coverages would naturally be accepted by only that doctor
    if (totalDoctorsInCenter <= 1) return false;

    // Get all doctors who accept this coverage
    const doctorsAccepting = getDoctorsAcceptingCoverage(coverage.id);

    // It's exclusive if only this doctor accepts it AND there are multiple doctors in the center
    return doctorsAccepting.length === 1 && doctorsAccepting[0] === editedConfig.id;
  });

  // Get coverages accepted by the medical center, excluding doctor-exclusive coverages
  const centerAcceptedCoverages = medicalCoverages.filter(coverage =>
    acceptedCoverageIds.includes(coverage.id) &&
    !doctorExclusiveCoverages.some(c => c.id === coverage.id) &&
    !pendingRemovals.includes(coverage.id) // Don't show coverages that are pending removal
  );

  // We no longer need to filter available coverages here since we're using the dialog

  return (
    <Card className="border-0 shadow-none">
      <CardContent className="p-0 space-y-8">
        {/* Medical Center Coverages Section */}
        <div>
          <CardHeader className="p-0 pb-4">
            <CardTitle className="text-lg text-gray-900">Coberturas aceptadas por el establecimiento</CardTitle>
          </CardHeader>

          {acceptedCoverageIds.length === 0 ? (
            <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
              <AlertCircle className="mx-auto h-6 w-6 text-yellow-500 mb-2" />
              <p className="text-gray-600">
                El establecimiento no tiene coberturas configuradas.
                Configure las coberturas aceptadas por el establecimiento antes de continuar.
              </p>
            </div>
          ) : centerAcceptedCoverages.length === 0 ? (
            <div className="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">No hay coberturas configuradas</div>
          ) : (
            centerAcceptedCoverages.map((coverage) => {
              const isAccepted = !isPendingDoctorCoverageExcluded(editedConfig.id || "", coverage.id)
              const isExpanded = expandedCoverages[coverage.id]
              const hasPlans = coverage.plans.length > 0

              return (
                <Card key={coverage.id} className="overflow-hidden border border-gray-200 shadow-sm mb-3">
                  <div
                    className={`flex items-center justify-between p-4 ${isAccepted ? "bg-blue-50 border-b border-blue-100" : "bg-gray-50 border-b border-gray-200"} cursor-pointer hover:bg-opacity-80`}
                    onClick={() => hasPlans && toggleCoverageExpansion(coverage.id)}
                  >
                    <div className="flex items-center gap-3">
                      <span className={`font-medium ${isAccepted ? "text-blue-800" : "text-gray-700"}`}>{coverage.name}</span>
                      <Badge
                        variant="outline"
                        className={isAccepted ? "bg-green-50 text-green-700 border-green-200" : "bg-red-50 text-red-700 border-red-200"}
                      >
                        {isAccepted ? "Aceptada" : "No aceptada"}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-3">
                      <Switch
                        checked={isAccepted}
                        onCheckedChange={() => togglePendingDoctorCoverageException(editedConfig.id || "", coverage.id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                      {hasPlans && (isExpanded ? <ChevronUp className="h-5 w-5 text-gray-500" /> : <ChevronDown className="h-5 w-5 text-gray-500" />)}
                    </div>
                  </div>

                  {hasPlans && isExpanded && (
                    <div className="p-4 bg-white space-y-2">
                      <h4 className="text-sm font-medium text-gray-700">Planes</h4>
                      {coverage.plans.map((plan) => {
                        const isPlanAccepted = !isPendingDoctorCoverageExcluded(editedConfig.id || "", coverage.id, plan)

                        return (
                          <div key={plan} className="flex items-center justify-between p-2 border rounded-md">
                            <span className="text-sm">{plan}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-xs text-gray-600">{isPlanAccepted ? "Acepta" : "No acepta"}</span>
                              <Switch
                                checked={isPlanAccepted}
                                onCheckedChange={() => togglePendingDoctorCoverageException(editedConfig.id || "", coverage.id, plan)}
                              />
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  )}
                </Card>
              )
            })
          )}
        </div>

        {/* Show informational message when there's only one doctor */}
        {totalDoctorsInCenter <= 1 && (
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mt-4">
            <div className="flex items-start gap-2">
              <Info className="h-5 w-5 text-gray-600 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-gray-700">
                Como este es el único profesional en el establecimiento, todas las coberturas aceptadas por el establecimiento se aplican automáticamente a este profesional.
              </p>
            </div>
          </div>
        )}

        {/* Only show doctor-exclusive section if there are multiple doctors in the center */}
        {totalDoctorsInCenter > 1 && (
          <>
            <div className="border-b border-gray-200 mb-4"></div>

            {/* Doctor-specific Coverages Section */}
            <div>
              <CardHeader className="p-0 pb-4">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg text-gray-900">Coberturas exclusivas del profesional</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsAddDialogOpen(true)}
                    className="text-sm"
                  >
                    <PlusCircle className="h-4 w-4 mr-1" />
                    Agregar cobertura
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Coberturas que acepta solamente este profesional.
                </p>
              </CardHeader>

              {/* Explanation box */}
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
                <div className="flex items-start gap-2">
                  <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-blue-700">
                    Estas coberturas aplican solamente a este profesional y no están aceptadas por el resto de los profesionales.
                  </p>
                </div>
              </div>

          {/* Add Doctor Coverage Dialog */}
          {editedConfig && (
            <AddDoctorCoverageDialog
              isOpen={isAddDialogOpen}
              onClose={() => {
                setIsAddDialogOpen(false)
                // Force re-render to update the doctor-specific coverages list
                setForceUpdate(prev => prev + 1)
              }}
              doctorId={editedConfig.id || ""}
            />
          )}

          {/* Doctor-specific coverages list */}
          {doctorExclusiveCoverages.length === 0 ? (
            <div className="text-center p-6 bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-gray-600">
                Este profesional no tiene coberturas exclusivas configuradas.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {/* Current doctor-exclusive coverages */}
              {doctorExclusiveCoverages.map((coverage) => {
                const isExpanded = expandedDoctorCoverages[coverage.id];
                const hasPlans = coverage.plans.length > 0;

                return (
                  <Card key={coverage.id} className="overflow-hidden border border-blue-200 shadow-sm">
                    <div
                      className="flex items-center justify-between p-4 bg-blue-50 border-b border-blue-100 cursor-pointer hover:bg-opacity-80"
                      onClick={() => hasPlans && toggleDoctorCoverageExpansion(coverage.id)}
                    >
                      <div className="flex items-center gap-3">
                        <span className="font-medium text-blue-800">{coverage.name}</span>
                        <Badge
                          variant="outline"
                          className="bg-blue-100 text-blue-700 border-blue-200"
                        >
                          Exclusiva del profesional
                        </Badge>
                      </div>
                      <div className="flex items-center gap-3">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:bg-red-100/50 hover:text-red-600 transition-colors px-2 py-1 rounded-md"
                          onClick={(e) => {
                            e.stopPropagation();

                            // Mark this coverage as pending removal (visually only)
                            const newPendingRemovals = [...pendingRemovals, coverage.id];
                            setPendingRemovals(newPendingRemovals);

                            // Store in localStorage
                            if (typeof window !== 'undefined') {
                              window.localStorage.setItem('pendingVisualRemovals', JSON.stringify(newPendingRemovals));
                            }

                            // Remove the coverage from the medical center (this will make it no longer exclusive)
                            const updatedCoverages = medicalCoverages.filter(c => c.id !== coverage.id);
                            setMedicalCoverages(updatedCoverages);

                            // Remove any exceptions for this coverage for all doctors
                            const updatedExceptions = { ...doctorCoverageExceptions };
                            Object.keys(updatedExceptions).forEach(doctorId => {
                              if (updatedExceptions[doctorId]) {
                                updatedExceptions[doctorId] = updatedExceptions[doctorId].filter(
                                  (e: CoverageException) => e.coverageId !== coverage.id
                                );
                                if (updatedExceptions[doctorId].length === 0) {
                                  delete updatedExceptions[doctorId];
                                }
                              }
                            });
                            setDoctorCoverageExceptions(updatedExceptions);

                            console.log(`Removed exclusive coverage ${coverage.name} from medical center`);

                            // Force re-render to update the doctor-specific coverages list
                            setForceUpdate(prev => prev + 1);
                          }}
                        >
                          Eliminar
                        </Button>
                        {hasPlans && (isExpanded ? <ChevronUp className="h-5 w-5 text-gray-500" /> : <ChevronDown className="h-5 w-5 text-gray-500" />)}
                      </div>
                    </div>

                    {hasPlans && isExpanded && (
                      <div className="p-4 bg-white space-y-2">
                        <h4 className="text-sm font-medium text-gray-700">Planes</h4>
                        {coverage.plans.map((plan) => {
                          const isPlanAccepted = !isPendingDoctorCoverageExcluded(editedConfig.id || "", coverage.id, plan);

                          return (
                            <div key={plan} className="flex items-center justify-between p-2 border rounded-md">
                              <span className="text-sm">{plan}</span>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-600">{isPlanAccepted ? "Acepta" : "No acepta"}</span>
                                <Switch
                                  checked={isPlanAccepted}
                                  onCheckedChange={() => togglePendingDoctorCoverageException(editedConfig.id || "", coverage.id, plan)}
                                />
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    )}
                  </Card>
                );
              })}


            </div>
          )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}