import { Doctor } from "@/types/doctor"
import { DAYS } from "@/utils/constants"
import { MedicalCenter } from "@/types/medical-center"

export const REFERENCE_DATE = new Date(2024, 0, 1); // January 1, 2024

export const getWeeksDifference = (date1: Date, date2: Date): number => {
  const timeDiff = date1.getTime() - date2.getTime();
  return Math.floor(timeDiff / (1000 * 60 * 60 * 24 * 7));
};

/**
 * Filters doctor's working hours based on the current date and the startDate/endDate ranges
 */
export const getActiveHoursForDate = (hours: Array<{ 
  start: string; 
  end: string; 
  startDate?: string; 
  endDate?: string;
}>, currentDate: Date): Array<{ start: string; end: string; }> => {
  const dateStr = currentDate.toISOString().split('T')[0];
  
  return hours.filter(hour => {
    // If no start date, or start date is before or equal to current date
    const isAfterStartDate = !hour.startDate || dateStr >= hour.startDate;
    
    // If no end date (indefinite), or end date is after or equal to current date
    const isBeforeEndDate = !hour.endDate || dateStr <= hour.endDate;
    
    return isAfterStartDate && isBeforeEndDate;
  }).map(({ start, end }) => ({ start, end })); // Strip out date fields, return only time
};

export function createEmptySchedule(): Doctor["workingDays"] {
  return DAYS.reduce((acc, day) => ({ ...acc, [day.id]: { enabled: false, hours: [] } }), {})
}

export function toggleDayEnabled(
  schedule: Doctor["workingDays"],
  dayId: string,
  enabled: boolean
): Doctor["workingDays"] {
  return {
    ...schedule,
    [dayId]: {
      enabled,
      hours: enabled && schedule[dayId]?.hours.length === 0 ? [{ start: "09:00", end: "17:00" }] : schedule[dayId]?.hours || [],
    },
  }
}

export function updateWorkingHours(
  schedule: Doctor["workingDays"],
  dayId: string,
  index: number,
  field: "start" | "end",
  value: string
): Doctor["workingDays"] | null {
  const dayHours = [...(schedule[dayId]?.hours || [])]
  dayHours[index] = { ...dayHours[index], [field]: value }

  const startTime = new Date(`2000-01-01T${dayHours[index].start}:00`)
  const endTime = new Date(`2000-01-01T${dayHours[index].end}:00`)
  if (startTime >= endTime) {
    console.warn(`Invalid time range for ${dayId}: ${dayHours[index].start} - ${dayHours[index].end}`)
    return null
  }

  const hasOverlap = dayHours.some((hour, i) => {
    if (i === index) return false
    const otherStart = new Date(`2000-01-01T${hour.start}:00`)
    const otherEnd = new Date(`2000-01-01T${hour.end}:00`)
    return startTime < otherEnd && endTime > otherStart
  })

  if (hasOverlap) {
    console.warn(`Overlapping hours detected for ${dayId}`)
    return null
  }

  return { ...schedule, [dayId]: { ...schedule[dayId], hours: dayHours } }
}

export function addWorkingHours(schedule: Doctor["workingDays"], dayId: string): Doctor["workingDays"] | null {
  const dayHours = [...(schedule[dayId]?.hours || [])]
  const newHours = { start: "09:00", end: "17:00" }
  dayHours.push(newHours)

  const startTime = new Date(`2000-01-01T${newHours.start}:00`)
  const endTime = new Date(`2000-01-01T${newHours.end}:00`)
  if (startTime >= endTime) {
    console.warn(`Invalid default time range for ${dayId}`)
    return null
  }

  const hasOverlap = dayHours.some((hour, i) => {
    if (i === dayHours.length - 1) return false
    const otherStart = new Date(`2000-01-01T${hour.start}:00`)
    const otherEnd = new Date(`2000-01-01T${hour.end}:00`)
    return startTime < otherEnd && endTime > otherStart
  })

  if (hasOverlap) {
    console.warn(`New hours overlap with existing hours for ${dayId}`)
    return null
  }

  return { ...schedule, [dayId]: { ...schedule[dayId], hours: dayHours } }
}

export function removeWorkingHours(
  schedule: Doctor["workingDays"],
  dayId: string,
  index: number
): Doctor["workingDays"] {
  const dayHours = [...(schedule[dayId]?.hours || [])]
  dayHours.splice(index, 1)
  return { ...schedule, [dayId]: { ...schedule[dayId], hours: dayHours } }
}

// Map numeric days to day names
export const dayNumberToName: Record<string, string> = {
  "0": "sunday",
  "1": "monday",
  "2": "tuesday",
  "3": "wednesday",
  "4": "thursday",
  "5": "friday",
  "6": "saturday"
};

// Map day names to numeric days
export const dayNameToNumber: Record<string, string> = {
  "sunday": "0",
  "monday": "1",
  "tuesday": "2",
  "wednesday": "3",
  "thursday": "4",
  "friday": "5",
  "saturday": "6"
};

// Create a compatibility layer to access schedule data consistently
export function getScheduleForDay(medicalCenter: MedicalCenter, dayNumber: string): { enabled: boolean; hours: Array<{ start: string; end: string }> } {
  // First try the old format
  if (medicalCenter.workingDays && medicalCenter.workingDays[dayNumber]) {
    const dayConfig = medicalCenter.workingDays[dayNumber];
    return {
      enabled: dayConfig.enabled,
      hours: dayConfig.hours || []
    };
  }
  
  // Then try the new format
  if (medicalCenter.openingHours) {
    const dayName = dayNumberToName[dayNumber];
    if (dayName && medicalCenter.openingHours[dayName as keyof typeof medicalCenter.openingHours]) {
      const dayConfig = medicalCenter.openingHours[dayName as keyof typeof medicalCenter.openingHours];
      return {
        enabled: dayConfig.enabled,
        hours: dayConfig.enabled ? [{ start: dayConfig.start, end: dayConfig.end }] : []
      };
    }
  }
  
  // Return default if neither format has data
  return { enabled: false, hours: [] };
}