"use client"

import {useEffect} from "react"
import {usePathname, useRouter} from "next/navigation"
import {useAuth} from "@/contexts/AuthContext"
import {UserRole} from "@/types/users"

export default function PatientLayout({
                                          children,
                                      }: {
    children: React.ReactNode
}) {
    const pathname = usePathname()
    const router = useRouter()
    const {currentUser, isAuthenticated, isLoading} = useAuth()

    // Authentication check
    useEffect(() => {
        // Skip authentication check for login and registration pages
        if (pathname === "/plataforma/paciente/login" || pathname === "/plataforma/paciente/registro") {
            console.log('PatientLayout - Skipping auth check for login or registration page')
            return
        }

        if (!isLoading && !isAuthenticated) {
            // Not authenticated, redirect to login
            console.log('PatientLayout - Not authenticated, redirecting to login')
            router.push('/plataforma/login')
            return
        }

        if (!isLoading && isAuthenticated && currentUser) {
            // Check if user has patient role
            if (currentUser.roles !== UserRole.PATIENT) {
                console.warn(`User ${currentUser.name} with role ${currentUser.roles} attempted to access patient page`)

                // Redirect based on role
                if (currentUser.roles === UserRole.DOCTOR && currentUser.doctorId) {
                    router.push(`/plataforma/profesional/${currentUser.doctorId}`)
                } else if (currentUser.medicalCenterId) {
                    router.push(`/plataforma/establecimiento/${currentUser.medicalCenterId}`)
                } else {
                    router.push('/plataforma/unauthorized')
                }
                return
            }
        }
    }, [isAuthenticated, isLoading, currentUser, router, pathname])

    return (
        <>
            {children}
        </>
    )
}
