import { NextRequest, NextResponse } from 'next/server';

export interface CreateProfessionalMedicalCenterRelationshipRequest {
    email: string;
    overlappedAppointmentLimit: "ONE_PER_ONE";
    maximumAnticipationAppointmentTimeLimit: string;
    minimumAnticipationAppointmentTimeLimit: string;
    appointmentIntervalTime: string;
    medicalCenterId: number;
    createdById: number;
}

export interface CreateProfessionalMedicalCenterRelationshipResponse {
    professionalId: number;
    medicalCenterId: number;
    externalId: string;
}

export async function POST(request: NextRequest) {
    try {
        const body: CreateProfessionalMedicalCenterRelationshipRequest = await request.json();

        // Validate required fields
        const requiredFields: (keyof CreateProfessionalMedicalCenterRelationshipRequest)[] = [
            'email',
            'overlappedAppointmentLimit',
            'maximumAnticipationAppointmentTimeLimit',
            'minimumAnticipationAppointmentTimeLimit',
            'appointmentIntervalTime',
            'medicalCenterId',
            'createdById'
        ];

        for (const field of requiredFields) {
            if (body[field] === undefined || body[field] === null) {
                return NextResponse.json(
                    { error: `Missing required field: ${field}` },
                    { status: 400 }
                );
            }
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(body.email)) {
            return NextResponse.json(
                { error: 'Invalid email format' },
                { status: 400 }
            );
        }

        // Validate overlappedAppointmentLimit is the correct value
        if (body.overlappedAppointmentLimit !== "ONE_PER_ONE") {
            return NextResponse.json(
                { error: 'Invalid overlappedAppointmentLimit value' },
                { status: 400 }
            );
        }

        // Validate numeric fields are positive
        if (body.medicalCenterId <= 0) {
            return NextResponse.json(
                { error: 'MedicalCenterId must be a positive number' },
                { status: 400 }
            );
        }

        if (body.createdById <= 0) {
            return NextResponse.json(
                { error: 'CreatedById must be a positive number' },
                { status: 400 }
            );
        }

        // Validate time limits are positive numbers or strings that can be converted
        const maxTimeLimit = parseInt(body.maximumAnticipationAppointmentTimeLimit);
        const minTimeLimit = parseInt(body.minimumAnticipationAppointmentTimeLimit);
        const intervalTime = parseInt(body.appointmentIntervalTime);

        if (isNaN(maxTimeLimit) || maxTimeLimit <= 0) {
            return NextResponse.json(
                { error: 'Maximum anticipation appointment time limit must be a positive number' },
                { status: 400 }
            );
        }

        if (isNaN(minTimeLimit) || minTimeLimit <= 0) {
            return NextResponse.json(
                { error: 'Minimum anticipation appointment time limit must be a positive number' },
                { status: 400 }
            );
        }

        if (isNaN(intervalTime) || intervalTime <= 0) {
            return NextResponse.json(
                { error: 'Appointment interval time must be a positive number' },
                { status: 400 }
            );
        }

        // Make request to external API
        const response = await fetch(
            `${process.env.BACKEND_URL}/professional-medical-center-relationship`,
            {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to create professional-medical-center relationship',
                    details: errorText,
                    status: response.status
                },
                { status: response.status }
            );
        }

        const result = await response.json();
        return NextResponse.json(result, { status: 200 });

    } catch (error) {
        console.error('Error creating professional-medical-center relationship:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
