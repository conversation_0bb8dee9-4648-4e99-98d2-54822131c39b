"use client"

import {ReactNode, useEffect} from "react"
import {MedicalCenterProvider} from "./MedicalCenterContext"
import {DoctorProvider} from "./DoctorContext"
import {ScheduleProvider} from "./ScheduleContext"
import {ConsultationProvider} from "./ConsultationContext"
import {CoverageProvider} from "./CoverageContext"
import {AppStateProvider} from "./AppStateContext"
import {NewDoctorProvider} from "./NewDoctorContext"

export function AppProvider({children}: { children: ReactNode }) {
    useEffect(() => {
        // TODO  : check if this can be deleted
        console.log("Storage and storage events initialized in AppProvider");
    }, []);

    return (
        <AppStateProvider>
            <MedicalCenterProvider>
                <DoctorProvider>
                    <ScheduleProvider>
                        <ConsultationProvider>
                            <CoverageProvider>
                                <NewDoctorProvider>
                                    {children}
                                </NewDoctorProvider>
                            </CoverageProvider>
                        </ConsultationProvider>
                    </ScheduleProvider>
                </DoctorProvider>
            </MedicalCenterProvider>
        </AppStateProvider>
    )
}