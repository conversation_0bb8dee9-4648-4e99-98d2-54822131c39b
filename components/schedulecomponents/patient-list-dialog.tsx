"use client"

import {useEffect, useState} from "react"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle} from "@/components/ui/dialog"
import {Input} from "@/components/ui/input"
import {FileDown, Search} from "lucide-react"
import {PatientDetailsDialog} from "./patient-details-dialog"
import {Button} from "@/components/ui/button"
import * as XLSX from 'xlsx'
import {PatientResponse} from "@/types/patient/patientResponse";

interface PatientListDialogProps {
    isOpen: boolean
    onClose: () => void
    patients: PatientResponse[]
}

export function PatientListDialog({
                                      isOpen,
                                      onClose,
                                      patients: initialPatients
                                  }: PatientListDialogProps) {
    const [searchQuery, setSearchQuery] = useState("")
    const [filteredPatients, setFilteredPatients] = useState<PatientResponse[]>([])
    const [selectedPatient, setSelectedPatient] = useState<PatientResponse | null>(null)
    const [isPatientDetailsOpen, setIsPatientDetailsOpen] = useState(false)

    // Initialize and sort patients when dialog opens or patients change
    useEffect(() => {
        if (isOpen && initialPatients.length > 0) {
            // Sort patients alphabetically by name
            const sortedPatients = [...initialPatients].sort((a, b) =>
                a.name.localeCompare(b.name, undefined, {sensitivity: 'base'})
            )
            setFilteredPatients(sortedPatients)
        } else {
            setFilteredPatients([])
        }
        // Reset search when dialog opens
        setSearchQuery("")
    }, [isOpen, initialPatients])

    // Custom filter function to search across all patient fields
    const filterPatients = (patients: PatientResponse[], query: string): PatientResponse[] => {
        if (!query.trim()) return patients;

        const normalizedQuery = query.toLowerCase().trim();

        return patients.filter(patient =>
            // Search by name
            patient.name.toLowerCase().includes(normalizedQuery) ||
            // Search by identification number (DNI)
            patient.identificationNumber.includes(normalizedQuery) ||
            // Search by phone
            patient.phone.includes(normalizedQuery) ||
            // Search by email
            (patient.email && patient.email.toLowerCase().includes(normalizedQuery)) ||
            // Search by coverage (cobertura)
            (patient.cobertura && patient.cobertura.toLowerCase().includes(normalizedQuery))
        );
    }

    // Filter patients based on search query
    useEffect(() => {
        const sortedPatients = [...initialPatients].sort((a, b) =>
            a.name.localeCompare(b.name, undefined, {sensitivity: 'base'})
        )

        if (!searchQuery.trim()) {
            setFilteredPatients(sortedPatients)
        } else {
            const results = filterPatients(sortedPatients, searchQuery);
            setFilteredPatients(results)
        }
    }, [searchQuery, initialPatients])

    // Handle patient selection
    const handlePatientClick = (patient: PatientResponse) => {
        setSelectedPatient(patient)
        setIsPatientDetailsOpen(true)
    }

    // Handle patient details dialog close
    const handlePatientDetailsClose = () => {
        setIsPatientDetailsOpen(false)
        // No need to refresh - parent component manages patient data
    }

    // Export patients list to Excel
    const exportToExcel = () => {
        // Create worksheet data
        const headers = ["Nombre", "DNI", "Teléfono", "Email", "Cobertura"]

        // Create data rows from filtered patients using PatientResponse fields
        const data = filteredPatients.map(patient => [
            patient.name,
            patient.identificationNumber,
            patient.phone,
            patient.email || "No disponible",
            patient.cobertura || "Sin cobertura"
        ])

        // Add headers to the beginning of the data array
        const wsData = [headers, ...data]

        // Create a worksheet
        const ws = XLSX.utils.aoa_to_sheet(wsData)

        // Create a workbook
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, "Pacientes")

        // Generate the Excel file and trigger download
        XLSX.writeFile(wb, `lista-pacientes_${new Date().toISOString().split("T")[0]}.xlsx`)
    }

    return (
        <>
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="sm:max-w-[90vw] max-h-[85vh] w-full overflow-hidden p-0">
                    <DialogHeader className="px-6 pt-6 pb-2 border-b">
                        <DialogTitle className="text-xl text-gray-900">Lista de Pacientes</DialogTitle>
                    </DialogHeader>

                    <div className="flex flex-col h-full overflow-hidden">
                        {/* Search bar */}
                        <div className="p-4 border-b">
                            <div className="flex gap-2">
                                <div className="relative flex-grow">
                                    <Search
                                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"/>
                                    <Input
                                        type="text"
                                        className="pl-10 pr-4 py-2 border border-blue-300/50 rounded-lg w-full shadow-sm focus:border-blue-400 focus:ring-1 focus:ring-blue-100"
                                        placeholder="Buscar por nombre, DNI, teléfono, email o cobertura"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                    />
                                </div>
                                <Button
                                    variant="outline"
                                    className="flex items-center gap-2 px-4 py-2 h-10 font-medium rounded-lg transition-all duration-200 text-blue-700 border-blue-300/50 shadow-sm hover:bg-blue-50 hover:text-blue-600 bg-white"
                                    onClick={exportToExcel}
                                    disabled={filteredPatients.length === 0}
                                >
                                    <FileDown className="h-4 w-4"/>
                                    Exportar a Excel
                                </Button>
                            </div>
                        </div>

                        {/* Patient list table */}
                        <div className="flex-grow overflow-y-auto p-4">
                            {filteredPatients.length > 0 ? (
                                <table className="w-full border-collapse">
                                    <thead className="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th className="px-4 py-2 text-left text-gray-600 font-medium text-sm border-b">Nombre</th>
                                        <th className="px-4 py-2 text-left text-gray-600 font-medium text-sm border-b">DNI</th>
                                        <th className="px-4 py-2 text-left text-gray-600 font-medium text-sm border-b">Teléfono</th>
                                        <th className="px-4 py-2 text-left text-gray-600 font-medium text-sm border-b">Email</th>
                                        <th className="px-4 py-2 text-left text-gray-600 font-medium text-sm border-b">Cobertura</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {filteredPatients.map((patient) => (
                                        <tr
                                            key={patient.id}
                                            className="hover:bg-blue-50 cursor-pointer transition-colors"
                                            onClick={() => handlePatientClick(patient)}
                                        >
                                            <td className="px-4 py-3 border-b border-gray-100">{patient.name}</td>
                                            <td className="px-4 py-3 border-b border-gray-100">{patient.identificationNumber}</td>
                                            <td className="px-4 py-3 border-b border-gray-100">{patient.phone}</td>
                                            <td className="px-4 py-3 border-b border-gray-100">{patient.email}</td>
                                            <td className="px-4 py-3 border-b border-gray-100">{patient.cobertura}</td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            ) : (
                                <div className="flex items-center justify-center h-full">
                                    <p className="text-gray-500">No se encontraron pacientes</p>
                                </div>
                            )}
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Patient details dialog */}
            {selectedPatient && (
                <PatientDetailsDialog
                    patient={selectedPatient}
                    isOpen={isPatientDetailsOpen}
                    onClose={handlePatientDetailsClose}
                />
            )}
        </>
    )
}
