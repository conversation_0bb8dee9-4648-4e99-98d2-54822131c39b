"use client"

import { useState, use<PERSON>ffect, use<PERSON>onte<PERSON><PERSON>, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Building2, Calendar, MapPin, User, Clock, Search, X,
  ArrowLeft, CheckCircle2, ShieldCheck, Menu, Stethoscope
} from "lucide-react"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { CoverageContext } from "@/contexts/CoverageContext"
import { PatientUserPill } from "@/components/ui/PatientUserPill"
import { useAuth } from "@/contexts/AuthContext"
import { usePatients } from "@/contexts/PatientContext"
import { MedicalCenter } from "@/types/medical-center"
import { Doctor } from "@/types/doctor"
import { useAppointments } from "@/contexts/AppointmentContext"
import { Appointment } from "@/types/scheduler"
import { format, addDays, startOfDay } from "date-fns"
import { es } from "date-fns/locale"
import { MedicalCenterCardLoading } from "@/components/ui/medical-center-card-loading"
import { getAvailableTimeSlots } from "@/utils/appointmentUtils"

// Helper function to get next available appointment for a doctor at a specific medical center
const getNextAvailableAppointment = (
  doctor: Doctor,
  medicalCenter: MedicalCenter,
  existingAppointments: Record<string, Appointment[]>,
  maxDaysToCheck = 30
) => {
  const today = startOfDay(new Date());

  // Check each day for the next 30 days
  for (let i = 0; i < maxDaysToCheck; i++) {
    const checkDate = addDays(today, i);
    const dayOfWeek = checkDate.getDay();

    // Check if the doctor works on this day
    const workingDay = doctor.workingDays[dayOfWeek.toString()];
    if (!workingDay || !workingDay.enabled || !workingDay.hours.length) {
      continue;
    }

    // Check if there are any date exceptions for this day
    const dateStr = format(checkDate, 'yyyy-MM-dd');
    const dateException = doctor.dateExceptions?.[dateStr];

    // If the date is explicitly disabled, skip this day
    if (dateException && !dateException.enabled) {
      continue;
    }

    // Get existing appointments for this date
    const dateAppointments = existingAppointments[dateStr] || [];

    // Convert to array of appointments for this date
    const appointmentsForDate = dateAppointments.filter(apt =>
      apt.doctorId === doctor.id && apt.medicalCenterId === medicalCenter.id
    );

    // Use our utility function to get available time slots for this date
    const availableSlots = getAvailableTimeSlots(
      doctor,
      medicalCenter,
      checkDate,
      appointmentsForDate,
      {}, // No blocked slots data for this simple case
      doctor.id,
      medicalCenter.id
    );

    // If there are available slots, return the first one
    if (availableSlots.length > 0) {
      return { date: checkDate, time: availableSlots[0] };
    }
  }

  return null;
};

export default function DoctorBookingPage() {
  const params = useParams()
  const router = useRouter()
  const doctorId = params.doctorId as string
  const { currentUser, logout } = useAuth()
  const { getPatientById } = usePatients()

  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [medicalCenters, setMedicalCenters] = useState<MedicalCenter[]>([])
  const [loading, setLoading] = useState(true)
  const [doctor, setDoctor] = useState<Doctor | null>(null)
  const [centerNextAppointments, setCenterNextAppointments] = useState<Record<string, { date: Date; time: string } | null>>({})
  const [loadingMedicalCenterId, setLoadingMedicalCenterId] = useState<string | null>(null)
  const [locations, setLocations] = useState<string[]>([])
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  // Coverage filter state
  const [selectedCoverage, setSelectedCoverage] = useState<string>("")
  const [selectedPlan, setSelectedPlan] = useState<string>("")
  const [noCoverage, setNoCoverage] = useState<boolean>(false)
  const [selectedConsultationType, setSelectedConsultationType] = useState<string>("")

  // Get coverage context for filtering
  const {
    medicalCoverages,
    isDoctorCoverageExcluded
  } = useContext(CoverageContext)

  // Get appointments from context
  const { appointments } = useAppointments()

  // Get available coverages for this doctor
  const availableCoveragesForDoctor = useMemo(() => {
    if (!doctor || !medicalCoverages.length) return [];

    // Get all coverages accepted by this doctor
    const availableCoverages = medicalCoverages.filter(coverage => {
      // Skip "Sin Cobertura" as it's handled separately
      if (coverage.name === "Sin Cobertura") return false;

      // Check if doctor accepts this coverage
      // The isDoctorCoverageExcluded function has been updated to handle consultation type exclusions
      return !isDoctorCoverageExcluded(doctor.id, coverage.id);
    });

    return availableCoverages;
  }, [doctor, medicalCoverages, isDoctorCoverageExcluded]);

  // Dynamically create plansByCoverage from available coverages
  const plansByCoverage = useMemo(() => {
    if (!doctor) return {};

    return availableCoveragesForDoctor.reduce((acc, coverage) => {
      // For each coverage, only include plans that are accepted by the doctor
      const acceptedPlans = coverage.plans?.filter(plan => {
        return !isDoctorCoverageExcluded(doctor.id, coverage.id, plan);
      }) || [];

      acc[coverage.name] = acceptedPlans;
      return acc;
    }, {} as Record<string, string[]>);
  }, [availableCoveragesForDoctor, doctor, isDoctorCoverageExcluded]);

  // Filter medical centers based on coverage, consultation type, location, and search query
  const filteredMedicalCenters = useMemo(() => {
    if (!medicalCenters.length || !doctor) return [];

    // If no coverage is selected, or if coverage is selected but no plan (when plans are available), don't show any medical centers
    if ((!selectedCoverage && !noCoverage) || (selectedCoverage && !selectedPlan && (plansByCoverage[selectedCoverage]?.length > 0))) return [];

    // If no consultation type is selected, don't show any medical centers
    if (!selectedConsultationType && (selectedCoverage || noCoverage)) return [];

    // Filter based on location if selected
    let filtered = [...medicalCenters];

    if (selectedLocation) {
      filtered = filtered.filter(center => {
        // Check if the center's location matches the selected location
        return center.locationId === selectedLocation;
      });
    }

    // Filter by search query if one exists
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(center =>
        center.name.toLowerCase().includes(query) ||
        (center.address && center.address.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, [medicalCenters, doctor, selectedCoverage, selectedPlan, noCoverage, plansByCoverage, selectedLocation, searchQuery, selectedConsultationType]);

  // Handle coverage selection
  const handleCoverageChange = (coverage: string) => {
    // If coverage is being deselected or set to "_none", reset everything
    if (coverage === "" || coverage === "_none") {
      setSelectedCoverage("")
      setSelectedPlan("")
      setSelectedConsultationType("")
      setNoCoverage(false)
      return
    }

    setSelectedCoverage(coverage);
    setSelectedPlan(""); // Reset plan when coverage changes
    setSelectedConsultationType(""); // Reset consultation type when coverage changes
    setNoCoverage(false); // Uncheck no coverage option
  };

  // Handle plan selection
  const handlePlanChange = (plan: string) => {
    setSelectedPlan(plan);
    setSelectedConsultationType(""); // Reset consultation type when plan changes
  };

  // Handle no coverage toggle
  const handleNoCoverageChange = (checked: boolean) => {
    setNoCoverage(checked);
    if (checked) {
      // Reset coverage and plan selections when no coverage is checked
      setSelectedCoverage("");
      setSelectedPlan("");
      setSelectedConsultationType("");
    }
  };

  // Handle location selection
  const handleLocationClick = (location: string) => {
    if (selectedLocation === location) {
      // If clicking the already selected location, deselect it
      setSelectedLocation(null);
    } else {
      // Otherwise, select the clicked location
      setSelectedLocation(location);
    }
  };

  // Handle search query change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Get available plans for selected coverage
  const getAvailablePlans = (): string[] => {
    if (!selectedCoverage) return [];
    return plansByCoverage[selectedCoverage] || [];
  };

  // Load doctor and medical centers where the doctor works
  useEffect(() => {
    if (doctorId) {
      setLoading(true)

      // Get all medical centers
      const allMedicalCenters = storage.getMedicalCenters()

      // Filter to only include centers where this doctor works
      const doctorCenters = allMedicalCenters.filter(center =>
        center.doctors.includes(doctorId)
      )

      // Sort centers alphabetically by name
      const sortedCenters = [...doctorCenters].sort((a, b) =>
        a.name.localeCompare(b.name)
      )

      // Get doctor information
      const allDoctors = storage.getAllDoctorsInSystem()
      const foundDoctor = allDoctors.find(d => d.id === doctorId)
      if (foundDoctor) {
        setDoctor(foundDoctor)

        // Get next available appointment for each medical center
        const nextAppointments: Record<string, { date: Date; time: string } | null> = {};

        for (const center of sortedCenters) {
          nextAppointments[center.id] = getNextAvailableAppointment(
            foundDoctor,
            center,
            appointments
          );
        }

        setCenterNextAppointments(nextAppointments);

        // Extract all unique locations from medical centers
        const allLocations = new Set<string>();
        sortedCenters.forEach(center => {
          if (center.locationId) {
            allLocations.add(center.locationId);
          }
        });

        // Convert to array and sort
        setLocations(Array.from(allLocations).sort());
      }

      setMedicalCenters(sortedCenters)
      setLoading(false)
    }
  }, [doctorId, appointments])

  const handleSelectMedicalCenter = (medicalCenterId: string) => {
    // Show loading indicator for this medical center
    setLoadingMedicalCenterId(medicalCenterId)

    // Build coverage parameters
    let coverageParams = '';
    if (noCoverage) {
      coverageParams = '&noCoverage=true';
    } else if (selectedCoverage) {
      coverageParams = `&coverage=${encodeURIComponent(selectedCoverage)}`;
      if (selectedPlan) {
        coverageParams += `&plan=${encodeURIComponent(selectedPlan)}`;
      }
    }

    // Add consultation type parameter if selected
    let consultationTypeParam = '';
    if (selectedConsultationType) {
      consultationTypeParam = `&consultationType=${encodeURIComponent(selectedConsultationType)}`;
    }

    // Navigate to the booking form for this doctor at this medical center
    router.push(`/plataforma/reservar/cita?doctorId=${doctorId}&medicalCenterId=${medicalCenterId}&source=doctor${coverageParams}${consultationTypeParam}`)
  }

  const handleSelectAppointment = (medicalCenterId: string, date: Date, time: string) => {
    // Build coverage parameters
    let coverageParams = '';
    if (noCoverage) {
      coverageParams = '&noCoverage=true';
    } else if (selectedCoverage) {
      coverageParams = `&coverage=${encodeURIComponent(selectedCoverage)}`;
      if (selectedPlan) {
        coverageParams += `&plan=${encodeURIComponent(selectedPlan)}`;
      }
    }

    // Add consultation type parameter if selected
    let consultationTypeParam = '';
    if (selectedConsultationType) {
      consultationTypeParam = `&consultationType=${encodeURIComponent(selectedConsultationType)}`;
    }

    // Navigate to the booking form with pre-selected date and time
    const dateStr = format(date, 'yyyy-MM-dd')
    router.push(`/plataforma/reservar/cita?doctorId=${doctorId}&medicalCenterId=${medicalCenterId}&date=${dateStr}&time=${time}&source=doctor${coverageParams}${consultationTypeParam}`)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#F4F7F9]">
        <div className="text-center bg-white p-8 rounded-xl shadow-md border border-blue-100 max-w-md w-full mx-4">
          <div className="relative mb-6">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-100 border-t-blue-600 mx-auto"></div>
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
              <Building2 className="h-6 w-6 text-blue-600 animate-pulse" />
            </div>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Cargando establecimientos</h3>
          <p className="text-gray-600">Estamos buscando los mejores establecimientos disponibles para ti...</p>

          <div className="mt-6 space-y-2">
            <div className="h-2.5 bg-gray-200 rounded-full w-3/4 mx-auto animate-pulse"></div>
            <div className="h-2.5 bg-gray-200 rounded-full w-1/2 mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!doctor) {
    return (
      <div className="min-h-screen bg-[#F4F7F9] py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md p-8 text-center border border-red-100">
            <div className="bg-red-50 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6 border-4 border-red-100">
              <User className="h-10 w-10 text-red-500" />
            </div>
            <h3 className="text-2xl font-semibold text-[#1c2533] mb-3">Profesional no encontrado</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              No pudimos encontrar información sobre el profesional solicitado. Por favor, intente nuevamente o contacte con el establecimiento.
            </p>
            <Button
              onClick={() => router.push("/")}
              className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white px-6 py-2.5"
              size="lg"
            >
              Volver al inicio
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-blue-50">
      {/* Fixed height placeholder to prevent content jumping */}
      <div className="h-[60px] w-full" />

      {/* Fixed frosted header */}
      <header className="fixed top-0 left-0 right-0 z-50">
        {/* Frosty backdrop */}
        <div className="fixed top-0 left-0 right-0 w-full z-40 backdrop-blur-sm bg-blue-50/70 h-[60px]" />

        {/* Header content */}
        <div className="max-w-6xl mx-auto px-7 flex justify-between items-center h-[60px] relative z-50">
          {/* Logo and mobile menu button */}
          <div className="flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 md:hidden"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
            <Link href="/">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera"
                width={100}
                height={30}
                priority
                className="h-7 w-auto"
              />
            </Link>
          </div>

          {/* Auth buttons/user pill */}
          <div className="flex items-center gap-2">
            {currentUser ? (
              <>
                <Button variant="ghost" asChild className="hidden sm:inline-flex text-sm hover:bg-blue-50 hover:text-blue-600">
                  <Link href="/plataforma/paciente">Mis turnos</Link>
                </Button>
                <PatientUserPill
                  currentUser={currentUser}
                  currentPatient={currentUser?.defaultPatientId ? getPatientById(currentUser.defaultPatientId) || null : null}
                  logout={logout}
                />
              </>
            ) : (
              <>
                <Button variant="outline" className="rounded-[12px]" size="sm" asChild>
                  <Link href="/plataforma/paciente/registro">Registrarme</Link>
                </Button>
                <Button className="rounded-[12px] bg-blue-600 hover:bg-blue-700" size="sm" asChild>
                  <Link href="/plataforma/paciente/login">Iniciar sesión</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu dropdown */}
          {isMenuOpen && (
            <div className="absolute top-full left-0 right-0 mx-4 bg-white shadow-lg rounded-xl md:hidden z-50 mt-2">
              <div className="flex flex-col p-4 space-y-4">
                <Link href="/plataforma/paciente" className="mx-3 text-slate-800 hover:text-blue-600">
                  Mis turnos
                </Link>
              </div>
            </div>
          )}
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 py-6 sm:py-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              className="mr-3 text-[#0070F3] hover:bg-[#0070F3]/5 hover:text-[#0070F3] border-0"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-1.5" />
              Volver
            </Button>

            <div className="border-l border-gray-200 pl-3">
              <h1 className="text-xl font-bold text-[#1c2533]">Reservar turno</h1>
              <p className="text-gray-600 text-sm">Profesional médico</p>
            </div>
          </div>
        </div>

        {/* Doctor Card - Redesigned */}
        <div className="w-full mb-8 overflow-hidden rounded-2xl shadow-sm border border-gray-100">
          {/* Integrated Card with Header and Coverage Section */}
          <div className="bg-white p-6 relative">
            {/* Blue accent dot/gradient on the right */}
            <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-br from-[#0070F3]/5 to-transparent rounded-tl-[120px] pointer-events-none"></div>

            <div className="flex flex-row items-start gap-4 relative z-0">
              {/* Doctor Avatar - Modern Style */}
              <div className="flex-shrink-0 relative">
                <div className="w-20 h-20 rounded-2xl bg-white shadow-lg p-0.5 relative">
                  <div className="w-full h-full rounded-xl overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-[#0070F3] to-[#0070F3]/80"></div>
                    <div className="absolute inset-0 bg-[url('/images/pattern-dots.png')] bg-repeat opacity-5"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-white text-2xl font-bold">
                        {doctor.name.charAt(0)}
                      </span>
                    </div>
                  </div>
                  <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-1 shadow-md">
                    <div className="bg-[#0070F3] rounded-full p-1">
                      <User className="h-3 w-3 text-white" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Doctor Info - Clean Layout - Mobile optimized to be next to avatar */}
              <div className="flex-1">
                <h2 className="text-xl md:text-2xl font-bold text-[#1c2533]">{doctor.name}</h2>

                {/* Specialties */}
                <div className="flex flex-wrap gap-2 mt-3">
                  {doctor.specialties?.map((specialty, index) => (
                    <div key={index} className="flex items-center bg-[#0070F3]/5 px-3 py-1.5 rounded-full">
                      <Stethoscope className="h-4 w-4 mr-1.5 text-[#0070F3]" />
                      <span className="text-sm font-medium text-[#1c2533]">{specialty}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Integrated Coverages Section - No visual separation */}
            <div className="mt-6 pt-5 border-t border-gray-100">
              <div className="flex items-center mb-3">
                <ShieldCheck className="h-4 w-4 mr-1.5 text-[#0a7c82]" />
                <h3 className="text-sm font-semibold text-[#1c2533]">Coberturas aceptadas</h3>
              </div>

              <div className="flex flex-wrap gap-2">
                {availableCoveragesForDoctor.length > 0 ? (
                  availableCoveragesForDoctor.slice(0, 5).map((coverage, index) => (
                    <Badge
                      key={index}
                      className="bg-[#1cd8e1]/20 shadow-sm border border-[#1cd8e1]/30 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium hover:bg-[#1cd8e1]/25 transition-colors"
                    >
                      {coverage.name}
                    </Badge>
                  ))
                ) : (
                  <Badge
                    className="bg-[#1cd8e1]/20 shadow-sm border border-[#1cd8e1]/30 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium hover:bg-[#1cd8e1]/25 transition-colors"
                  >
                    Sin Cobertura
                  </Badge>
                )}

                {availableCoveragesForDoctor.length > 5 && (
                  <Badge
                    className="bg-[#1cd8e1]/20 border border-[#1cd8e1]/30 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium cursor-pointer hover:bg-[#1cd8e1]/25 transition-colors"
                  >
                    +{availableCoveragesForDoctor.length - 5} más
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Coverage Selection - Step 1 */}
        <div className="w-full mb-6 rounded-xl overflow-hidden shadow-sm border border-[#0070F3]/30">
          <div className="bg-gradient-to-r from-[#0070F3]/10 to-[#0070F3]/5 py-3 px-5 border-b border-[#0070F3]/20">
            <div className="flex items-center">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 mr-3 shrink-0">
                <span className="text-[#0a7c82] font-bold text-2xl">1</span>
              </div>
              <h3 className="text-lg font-semibold text-[#1c2533]">Seleccioná tu cobertura médica</h3>
            </div>
          </div>

          <div className="p-5 bg-white">
            {/* Desktop Layout */}
            <div className="hidden md:flex items-center gap-4">
              <div className="flex-1 grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="coverage" className="block mb-2 text-sm font-medium text-gray-700">Cobertura</Label>
                  <Select
                    value={selectedCoverage}
                    onValueChange={handleCoverageChange}
                    disabled={noCoverage}
                  >
                    <SelectTrigger id="coverage" className="w-full bg-white border-gray-200 rounded-lg">
                      <SelectValue placeholder="Seleccionar Cobertura" />
                    </SelectTrigger>
                    <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                      {availableCoveragesForDoctor.map(coverage => (
                        <SelectItem key={coverage.id} value={coverage.name}>
                          {coverage.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="plan" className="block mb-2 text-sm font-medium text-gray-700">Plan</Label>
                  <Select
                    value={selectedPlan}
                    onValueChange={handlePlanChange}
                    disabled={!selectedCoverage || noCoverage}
                  >
                    <SelectTrigger id="plan" className="w-full bg-white border-gray-200 rounded-lg">
                      <SelectValue placeholder="Seleccionar Plan" />
                    </SelectTrigger>
                    <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                      {selectedCoverage &&
                        getAvailablePlans().map((plan) => (
                          <SelectItem key={plan} value={plan}>
                            {plan}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Sin Cobertura toggle - Desktop */}
              <div className="flex items-center py-2 px-3 bg-[#F4F7F9] rounded-lg border border-gray-100 min-w-[200px] h-[38px]">
                <Switch
                  id="no-coverage-desktop"
                  checked={noCoverage}
                  onCheckedChange={handleNoCoverageChange}
                  className="data-[state=checked]:bg-[#1cd8e1]"
                />
                <Label htmlFor="no-coverage-desktop" className="ml-2 font-medium text-[#1c2533]">Sin Cobertura</Label>
              </div>
            </div>

            {/* Mobile Layout */}
            <div className="flex md:hidden flex-col gap-4">
              <div>
                <Label htmlFor="coverage-mobile" className="block mb-2 text-sm font-medium text-gray-700">Cobertura</Label>
                <Select
                  value={selectedCoverage}
                  onValueChange={handleCoverageChange}
                  disabled={noCoverage}
                >
                  <SelectTrigger id="coverage-mobile" className="w-full bg-white border-gray-200 rounded-lg">
                    <SelectValue placeholder="Seleccionar Cobertura" />
                  </SelectTrigger>
                  <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                    {availableCoveragesForDoctor.map(coverage => (
                      <SelectItem key={coverage.id} value={coverage.name}>
                        {coverage.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="plan-mobile" className="block mb-2 text-sm font-medium text-gray-700">Plan</Label>
                <Select
                  value={selectedPlan}
                  onValueChange={handlePlanChange}
                  disabled={!selectedCoverage || noCoverage}
                >
                  <SelectTrigger id="plan-mobile" className="w-full bg-white border-gray-200 rounded-lg">
                    <SelectValue placeholder="Seleccionar Plan" />
                  </SelectTrigger>
                  <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                    {selectedCoverage &&
                      getAvailablePlans().map((plan) => (
                        <SelectItem key={plan} value={plan}>
                          {plan}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Sin Cobertura toggle - Mobile */}
              <div className="flex items-center py-2 px-3 bg-[#F4F7F9] rounded-lg border border-gray-100">
                <Switch
                  id="no-coverage-mobile"
                  checked={noCoverage}
                  onCheckedChange={handleNoCoverageChange}
                  className="data-[state=checked]:bg-[#1cd8e1]"
                />
                <Label htmlFor="no-coverage-mobile" className="ml-2 font-medium text-[#1c2533]">Sin Cobertura</Label>
              </div>
            </div>

            {/* Active coverage filters */}
            {(selectedCoverage || noCoverage) && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex items-center mb-2">
                  <CheckCircle2 className="h-4 w-4 text-[#0a7c82] mr-2" />
                  <h3 className="text-sm font-medium text-[#1c2533]">Cobertura seleccionada</h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {noCoverage ? (
                    <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                      Sin Cobertura
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                        onClick={() => setNoCoverage(false)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <>
                      {selectedCoverage && (
                        <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                          Cobertura: {selectedCoverage}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                            onClick={() => {
                              setSelectedCoverage("");
                              setSelectedPlan("");
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                      {selectedPlan && (
                        <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                          Plan: {selectedPlan}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                            onClick={() => setSelectedPlan("")}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Consultation Type Selection - Step 2 */}
        {(noCoverage || (selectedCoverage && (selectedPlan || getAvailablePlans().length === 0))) && (
          <div className="w-full mb-6 rounded-xl overflow-hidden shadow-sm border border-[#0070F3]/30">
            <div className="bg-gradient-to-r from-[#0070F3]/10 to-[#0070F3]/5 py-3 px-5 border-b border-[#0070F3]/20">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 mr-3 shrink-0">
                  <span className="text-[#0a7c82] font-bold text-2xl">2</span>
                </div>
                <h3 className="text-lg font-semibold text-[#1c2533]">Seleccioná la atención que necesitás</h3>
              </div>
            </div>

            <div className="p-5 bg-white">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="consultation-type" className="block mb-2 text-sm font-medium text-gray-700">Tipo de atención</Label>
                  <Select
                    value={selectedConsultationType}
                    onValueChange={setSelectedConsultationType}
                  >
                    <SelectTrigger id="consultation-type" className="w-full bg-white border-gray-200 rounded-lg">
                      <SelectValue placeholder="Seleccionar tipo de atención" />
                    </SelectTrigger>
                    <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                      {doctor.consultationTypes?.map(type => {
                        // Handle both string and object types
                        const typeString = typeof type === 'object' && type !== null && 'name' in type
                          ? String(type.name)
                          : String(type);

                        // Skip if this consultation type is excluded for the selected coverage
                        if (selectedCoverage) {
                          const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
                          if (coverage && isDoctorCoverageExcluded(doctor.id, coverage.id, selectedPlan)) {
                            return null;
                          }
                        }
                        return (
                          <SelectItem key={typeString} value={typeString}>
                            {typeString}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Active consultation type */}
              {selectedConsultationType && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center mb-2">
                    <CheckCircle2 className="h-4 w-4 text-[#0a7c82] mr-2" />
                    <h3 className="text-sm font-medium text-[#1c2533]">Tipo de atención seleccionada</h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                      {selectedConsultationType}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                        onClick={() => setSelectedConsultationType("")}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Location Filters Section - Step 3 */}
        {selectedConsultationType && (
          <div className="w-full mb-8 rounded-xl overflow-hidden shadow-sm border border-[#0070F3]/30">
            <div className="bg-gradient-to-r from-[#0070F3]/10 to-[#0070F3]/5 py-3 px-5 border-b border-[#0070F3]/20">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 mr-3 shrink-0">
                  <span className="text-[#0a7c82] font-bold text-2xl">3</span>
                </div>
                <div className="flex items-center">
                  <h3 className="text-lg font-semibold text-[#1c2533]">Filtrá por ubicación</h3>
                  <span className="ml-2 text-xs font-medium px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full">Opcional</span>
                </div>
              </div>
            </div>

            <div className="p-5 bg-white">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Search box */}
                <div>
                  <Label htmlFor="search" className="block mb-2 text-sm font-medium text-gray-700">Buscar por nombre o dirección</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="search"
                      type="text"
                      placeholder="Ingrese nombre o dirección..."
                      className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      value={searchQuery}
                      onChange={handleSearchChange}
                    />
                  </div>
                </div>

                {/* Location filter */}
                {locations.length > 0 && (
                  <div>
                    <Label className="block mb-2 text-sm font-medium text-gray-700">Filtrar por localidad</Label>
                    <div className="flex flex-wrap gap-2">
                      {locations.map((location) => (
                        <Button
                          key={location}
                          variant="outline"
                          size="sm"
                          className={`rounded-full text-xs font-medium ${
                            selectedLocation === location
                              ? 'bg-[#1cd8e1]/15 text-[#0a7c82] border-[#1cd8e1]/30 hover:bg-[#1cd8e1]/20'
                              : 'bg-[#F4F7F9] text-[#1c2533] border-gray-100 hover:bg-[#1cd8e1]/10 hover:text-[#0a7c82]'
                          }`}
                          onClick={() => handleLocationClick(location)}
                        >
                          {location}
                          {selectedLocation === location && (
                            <X className="ml-1 h-3 w-3" />
                          )}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Active location filters */}
              {(selectedLocation || searchQuery) && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center mb-2">
                    <CheckCircle2 className="h-4 w-4 text-[#0a7c82] mr-2" />
                    <h3 className="text-sm font-medium text-[#1c2533]">Filtros activos</h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {selectedLocation && (
                      <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                        Localidad: {selectedLocation}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                          onClick={() => setSelectedLocation(null)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}

                    {searchQuery && (
                      <div className="bg-[#1cd8e1]/15 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                        Búsqueda: {searchQuery}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                          onClick={() => setSearchQuery("")}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}

                    {(selectedLocation || searchQuery) && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs border-gray-200 hover:bg-gray-50"
                        onClick={() => {
                          setSelectedLocation(null);
                          setSearchQuery("");
                        }}
                      >
                        Limpiar filtros
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Medical centers list */}
        <section id="medical-centers-results" className="mb-8">
          {(!selectedCoverage && !noCoverage) || !selectedConsultationType ? (
            <div className="w-full overflow-hidden border border-blue-200 shadow-md rounded-xl bg-white">
              <div className="p-8 text-center">
                <div className="bg-[#1cd8e1]/10 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6 border-4 border-[#1cd8e1]/20">
                  <ShieldCheck className="h-10 w-10 text-[#0a7c82]" />
                </div>
                <h3 className="text-xl font-semibold text-[#1c2533] mb-3">
                  {!selectedCoverage && !noCoverage ?
                    "Selecciona tu cobertura médica" :
                    selectedCoverage && !selectedPlan && getAvailablePlans().length > 0 ?
                    "Selecciona el plan de tu cobertura" :
                    "Seleccioná la atención que necesitás"
                  }
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {!selectedCoverage && !noCoverage ?
                    "Por favor, selecciona una cobertura o marca la opción \"Sin Cobertura\" para continuar." :
                    selectedCoverage && !selectedPlan && getAvailablePlans().length > 0 ?
                    "Por favor, selecciona un plan para la cobertura elegida para continuar." :
                    !selectedConsultationType ?
                    "Por favor, seleccioná el tipo de atención médica que necesitás para ver los establecimientos disponibles." :
                    ""
                  }
                </p>
                <Button
                  onClick={() => {
                    if (!selectedCoverage && !noCoverage) {
                      const filterSection = document.getElementById('coverage');
                      if (filterSection) filterSection.scrollIntoView({ behavior: 'smooth' });
                    } else if (selectedCoverage && !selectedPlan && getAvailablePlans().length > 0) {
                      const planSection = document.getElementById('plan');
                      if (planSection) planSection.scrollIntoView({ behavior: 'smooth' });
                    } else if (!selectedConsultationType) {
                      const consultationSection = document.getElementById('consultation-type');
                      if (consultationSection) consultationSection.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white px-6"
                >
                  {!selectedCoverage && !noCoverage ?
                    "Seleccionar cobertura" :
                    !selectedPlan && getAvailablePlans().length > 0 ?
                    "Seleccionar plan" :
                    "Seleccionar tipo de atención"
                  }
                </Button>
              </div>
            </div>
          ) : filteredMedicalCenters.length === 0 ? (
            <div className="w-full overflow-hidden border border-red-200 shadow-md rounded-xl bg-white">
              <div className="p-8 text-center">
                <div className="bg-red-50 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6 border-4 border-red-100">
                  <Building2 className="h-10 w-10 text-red-500" />
                </div>
                <h3 className="text-xl font-semibold text-[#1c2533] mb-3">No hay establecimientos disponibles</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchQuery || selectedLocation ?
                    "No se encontraron establecimientos con los filtros seleccionados. Intenta con otros criterios de búsqueda." :
                    "No hay establecimientos disponibles para este profesional con la cobertura y tipo de consulta seleccionados."
                  }
                </p>
                {(searchQuery || selectedLocation || selectedCoverage || noCoverage || selectedConsultationType) ? (
                  <Button
                    onClick={() => {
                      setSearchQuery("");
                      setSelectedLocation(null);
                    }}
                    className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white px-6"
                  >
                    Limpiar filtros
                  </Button>
                ) : (
                  <Button
                    onClick={() => router.push("/")}
                    className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white px-6"
                  >
                    Volver al inicio
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <>
              {/* Results header */}
              <div className="flex flex-col sm:flex-row justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-[#1c2533] mb-3 sm:mb-0">
                  {filteredMedicalCenters.length} {filteredMedicalCenters.length === 1 ? 'establecimiento disponible' : 'establecimientos disponibles'}
                </h2>
                <p className="text-sm text-gray-600">
                  Seleccioná un establecimiento para ver los horarios disponibles
                </p>
              </div>

              {/* Medical center cards grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                {filteredMedicalCenters.map((center) => (
                  <div
                    key={center.id}
                    className="relative overflow-hidden rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 group bg-white"
                  >
                    {/* Medical center card loading overlay */}
                    <MedicalCenterCardLoading medicalCenterId={center.id} isLoading={loadingMedicalCenterId === center.id} />

                    {/* Card header */}
                    <div className="p-5 border-b border-gray-100">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-full bg-[#1cd8e1]/20 flex items-center justify-center mr-3">
                          <Building2 className="h-5 w-5 text-[#0a7c82]" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-[#1c2533] group-hover:text-[#0a7c82] transition-colors duration-300">
                            {center.name}
                          </h3>
                          {center.address && (
                            <p className="text-sm text-gray-600 mt-0.5 flex items-center">
                              <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                              <span className="truncate">{center.address}</span>
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Card content */}
                    <div className="p-5">
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <Clock className="h-4 w-4 mr-1.5 text-[#0a7c82]" />
                          Próximo turno disponible:
                        </h4>

                        {centerNextAppointments[center.id] ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full text-sm justify-between h-auto py-2.5 border-[#1cd8e1]/20 hover:bg-[#1cd8e1]/5 hover:text-[#0a7c82] rounded-lg"
                            onClick={() => handleSelectAppointment(
                              center.id,
                              centerNextAppointments[center.id]!.date,
                              centerNextAppointments[center.id]!.time
                            )}
                          >
                            <div className="flex items-center">
                              <Calendar className="h-3.5 w-3.5 mr-1.5 text-[#0a7c82]" />
                              <span className="font-medium">
                                {format(centerNextAppointments[center.id]!.date, "EEE d MMM", { locale: es })}
                              </span>
                            </div>
                            <span className="font-semibold text-[#0a7c82]">{centerNextAppointments[center.id]!.time} hs</span>
                          </Button>
                        ) : (
                          <div className="bg-gray-50 rounded-lg p-3 text-center">
                            <p className="text-sm text-gray-500">No hay turnos disponibles</p>
                          </div>
                        )}
                      </div>

                      <Button
                        className="w-full bg-[#0070F3] hover:bg-[#0070F3]/90 text-white"
                        onClick={() => handleSelectMedicalCenter(center.id)}
                      >
                        Ver todos los horarios
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </section>
      </main>
    </div>
  );
}