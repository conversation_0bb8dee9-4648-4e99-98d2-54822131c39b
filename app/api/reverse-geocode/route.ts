import { NextRequest } from 'next/server'
import { getLocationByName } from '@/data/locations'

// Simple in-memory cache and throttle across server instances
const memoryCache = new Map<string, { name: string; region: 'caba' | 'gba' | 'unknown'; address: any; expiresAt: number }>()
let lastOutboundAt = 0

const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

function normalizeString(value: string | undefined | null): string {
  return (value || '').trim()
}

function stripPartidoPrefix(value: string | undefined | null): string {
  const v = normalizeString(value)
  return v.replace(/^Partido de\s+/i, '').replace(/^Municipio de\s+/i, '')
}

function detectRegionFromAddress(address: any): 'caba' | 'gba' | 'unknown' {
  const state = normalizeString(address?.state)
  const city = normalizeString(address?.city)
  const ISO = normalizeString(address?.ISO3166_2_lvl4 || address?.ISO3166_2_lvl6)
  // Common variants for CABA
  if (
    /CABA/i.test(state) ||
    /Autónoma/i.test(state) ||
    /Autonoma/i.test(state) ||
    /Capital Federal/i.test(state) ||
    ISO.includes('AR-C') ||
    city === 'Buenos Aires'
  ) {
    return 'caba'
  }
  // Province of Buenos Aires -> likely GBA for our use-case
  if (/Buenos Aires/i.test(state)) return 'gba'
  return 'unknown'
}

function chooseBestName(address: any): string | null {
  // Prefer granular local names first
  const candidates: Array<string | undefined> = [
    address?.suburb,
    address?.neighbourhood,
    address?.city_district,
    address?.municipality,
    address?.town,
    address?.city,
    address?.village,
    stripPartidoPrefix(address?.county),
  ]
  for (const c of candidates) {
    const n = normalizeString(c)
    if (n) return n
  }
  const display = normalizeString(address?.display_name)
  return display || null
}

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url)
  const lat = parseFloat(searchParams.get('lat') || '')
  const lon = parseFloat(searchParams.get('lon') || '')

  if (!Number.isFinite(lat) || !Number.isFinite(lon)) {
    return new Response(JSON.stringify({ error: 'Invalid coordinates' }), { status: 400, headers: { 'content-type': 'application/json' } })
  }

  const key = `${lat.toFixed(5)},${lon.toFixed(5)}`
  const now = Date.now()
  const cached = memoryCache.get(key)
  if (cached && cached.expiresAt > now) {
    return new Response(JSON.stringify({ name: cached.name, region: cached.region, address: cached.address }), {
      status: 200,
      headers: {
        'content-type': 'application/json',
        'cache-control': 'public, max-age=86400, s-maxage=86400, stale-while-revalidate=604800'
      }
    })
  }

  // Respect Nominatim usage policy: at most 1 req/sec per client
  const elapsed = now - lastOutboundAt
  if (elapsed < 1100) {
    await delay(1100 - elapsed)
  }

  try {
    const url = `https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat=${encodeURIComponent(String(lat))}&lon=${encodeURIComponent(String(lon))}&zoom=14&addressdetails=1`
    const res = await fetch(url, {
      headers: {
        'User-Agent': 'turnera-medical-schedule/1.0 (<EMAIL>)',
        'Accept-Language': 'es-AR,es;q=0.9,en-US;q=0.8,en;q=0.7'
      },
      cache: 'no-store'
    })
    lastOutboundAt = Date.now()

    if (!res.ok) {
      return new Response(JSON.stringify({ error: `Upstream status ${res.status}` }), { status: 502, headers: { 'content-type': 'application/json' } })
    }
    const data = await res.json()
    const address = data?.address || {}
    let name = chooseBestName(address)
    const region = detectRegionFromAddress(address)

    // Normalize to our system's known location names when possible
    if (name) {
      const known = getLocationByName(name)
      if (known?.name) {
        name = known.name
      }
    }
    // Special-case common CABA synonyms
    if (region === 'caba' && name) {
      const n = name.toLowerCase()
      if (n === 'buenos aires' || n.includes('caba') || n.includes('ciudad autónoma') || n.includes('ciudad autonoma')) {
        name = 'Capital Federal'
      }
    }

    if (!name) {
      return new Response(JSON.stringify({ error: 'Not found' }), { status: 404, headers: { 'content-type': 'application/json' } })
    }

    memoryCache.set(key, { name, region, address, expiresAt: Date.now() + 1000 * 60 * 60 * 24 })
    return new Response(JSON.stringify({ name, region, address }), {
      status: 200,
      headers: {
        'content-type': 'application/json',
        'cache-control': 'public, max-age=86400, s-maxage=86400, stale-while-revalidate=604800'
      }
    })
  } catch (err: any) {
    return new Response(JSON.stringify({ error: 'Fetch failed', details: String(err?.message || err) }), { status: 502, headers: { 'content-type': 'application/json' } })
  }
}


