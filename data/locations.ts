// Define the location type structure
export interface Location {
    id: string;
    name: string;
    type: "barrio" | "municipio";
    region: "caba" | "gba";
}

// Capital Federal (CABA) locations
export const CABA_LOCATIONS: Location[] = [
    {id: "agronomia", name: "Agronomía", type: "barrio", region: "caba"},
    {id: "almagro", name: "Almagro", type: "barrio", region: "caba"},
    {id: "balvanera", name: "<PERSON>lvan<PERSON>", type: "barrio", region: "caba"},
    {id: "barracas", name: "Barracas", type: "barrio", region: "caba"},
    {id: "belgrano", name: "Belgrano", type: "barrio", region: "caba"},
    {id: "boedo", name: "Boedo", type: "barrio", region: "caba"},
    {id: "caballito", name: "Caballito", type: "barrio", region: "caba"},
    {id: "chacarita", name: "<PERSON><PERSON><PERSON>", type: "barrio", region: "caba"},
    {id: "coghlan", name: "Coghlan", type: "barrio", region: "caba"},
    {id: "colegiales", name: "Colegiales", type: "barrio", region: "caba"},
    {id: "constitucion", name: "Constitución", type: "barrio", region: "caba"},
    {id: "flores", name: "Flores", type: "barrio", region: "caba"},
    {id: "floresta", name: "Floresta", type: "barrio", region: "caba"},
    {id: "la_boca", name: "La Boca", type: "barrio", region: "caba"},
    {id: "liniers", name: "Liniers", type: "barrio", region: "caba"},
    {id: "mataderos", name: "Mataderos", type: "barrio", region: "caba"},
    {id: "monte_castro", name: "Monte Castro", type: "barrio", region: "caba"},
    {id: "monserrat", name: "Monserrat", type: "barrio", region: "caba"},
    {id: "nueva_pompeya", name: "Nueva Pompeya", type: "barrio", region: "caba"},
    {id: "nunez", name: "Nuñez", type: "barrio", region: "caba"},
    {id: "palermo", name: "Palermo", type: "barrio", region: "caba"},
    {id: "parque_avellaneda", name: "Parque Avellaneda", type: "barrio", region: "caba"},
    {id: "parque_chacabuco", name: "Parque Chacabuco", type: "barrio", region: "caba"},
    {id: "parque_chas", name: "Parque Chas", type: "barrio", region: "caba"},
    {id: "parque_patricios", name: "Parque Patricios", type: "barrio", region: "caba"},
    {id: "paternal", name: "Paternal", type: "barrio", region: "caba"},
    {id: "puerto_madero", name: "Puerto Madero", type: "barrio", region: "caba"},
    {id: "recoleta", name: "Recoleta", type: "barrio", region: "caba"},
    {id: "retiro", name: "Retiro", type: "barrio", region: "caba"},
    {id: "saavedra", name: "Saavedra", type: "barrio", region: "caba"},
    {id: "san_cristobal", name: "San Cristóbal", type: "barrio", region: "caba"},
    {id: "san_nicolas", name: "San Nicolás", type: "barrio", region: "caba"},
    {id: "san_telmo", name: "San Telmo", type: "barrio", region: "caba"},
    {id: "velez_sarsfield", name: "Vélez Sarsfield", type: "barrio", region: "caba"},
    {id: "versalles", name: "Versalles", type: "barrio", region: "caba"},
    {id: "villa_crespo", name: "Villa Crespo", type: "barrio", region: "caba"},
    {id: "villa_del_parque", name: "Villa del Parque", type: "barrio", region: "caba"},
    {id: "villa_devoto", name: "Villa Devoto", type: "barrio", region: "caba"},
    {id: "villa_gral_mitre", name: "Villa Gral. Mitre", type: "barrio", region: "caba"},
    {id: "villa_lugano", name: "Villa Lugano", type: "barrio", region: "caba"},
    {id: "villa_luro", name: "Villa Luro", type: "barrio", region: "caba"},
    {id: "villa_ortuzar", name: "Villa Ortúzar", type: "barrio", region: "caba"},
    {id: "villa_pueyrredon", name: "Villa Pueyrredón", type: "barrio", region: "caba"},
    {id: "villa_real", name: "Villa Real", type: "barrio", region: "caba"},
    {id: "villa_riachuelo", name: "Villa Riachuelo", type: "barrio", region: "caba"},
    {id: "villa_santa_rita", name: "Villa Santa Rita", type: "barrio", region: "caba"},
    {id: "villa_soldati", name: "Villa Soldati", type: "barrio", region: "caba"},
    {id: "villa_urquiza", name: "Villa Urquiza", type: "barrio", region: "caba"}
];

// Gran Buenos Aires (GBA) locations
export const GBA_LOCATIONS: Location[] = [
    {id: "almirante_brown", name: "Almirante Brown", type: "municipio", region: "gba"},
    {id: "avellaneda", name: "Avellaneda", type: "municipio", region: "gba"},
    {id: "berazategui", name: "Berazategui", type: "municipio", region: "gba"},
    {id: "canuelas", name: "Cañuelas", type: "municipio", region: "gba"},
    {id: "escobar", name: "Escobar", type: "municipio", region: "gba"},
    {id: "esteban_echeverria", name: "Esteban Echeverría", type: "municipio", region: "gba"},
    {id: "ezeiza", name: "Ezeiza", type: "municipio", region: "gba"},
    {id: "florencio_varela", name: "Florencio Varela", type: "municipio", region: "gba"},
    {id: "general_rodriguez", name: "General Rodríguez", type: "municipio", region: "gba"},
    {id: "general_san_martin", name: "General San Martín", type: "municipio", region: "gba"},
    {id: "hurlingham", name: "Hurlingham", type: "municipio", region: "gba"},
    {id: "ituzaingo", name: "Ituzaingó", type: "municipio", region: "gba"},
    {id: "jose_c_paz", name: "José C. Paz", type: "municipio", region: "gba"},
    {id: "la_matanza", name: "La Matanza", type: "municipio", region: "gba"},
    {id: "lanus", name: "Lanús", type: "municipio", region: "gba"},
    {id: "la_plata", name: "La Plata", type: "municipio", region: "gba"},
    {id: "lomas_de_zamora", name: "Lomas de Zamora", type: "municipio", region: "gba"},
    {id: "lujan", name: "Luján", type: "municipio", region: "gba"},
    {id: "malvinas_argentinas", name: "Malvinas Argentinas", type: "municipio", region: "gba"},
    {id: "marcos_paz", name: "Marcos Paz", type: "municipio", region: "gba"},
    {id: "merlo", name: "Merlo", type: "municipio", region: "gba"},
    {id: "moreno", name: "Moreno", type: "municipio", region: "gba"},
    {id: "moron", name: "Morón", type: "municipio", region: "gba"},
    {id: "pilar", name: "Pilar", type: "municipio", region: "gba"},
    {id: "presidente_peron", name: "Presidente Perón", type: "municipio", region: "gba"},
    {id: "quilmes", name: "Quilmes", type: "municipio", region: "gba"},
    {id: "san_fernando", name: "San Fernando", type: "municipio", region: "gba"},
    {id: "san_isidro", name: "San Isidro", type: "municipio", region: "gba"},
    {id: "san_miguel", name: "San Miguel", type: "municipio", region: "gba"},
    {id: "san_vicente", name: "San Vicente", type: "municipio", region: "gba"},
    {id: "tigre", name: "Tigre", type: "municipio", region: "gba"},
    {id: "tres_de_febrero", name: "Tres de Febrero", type: "municipio", region: "gba"},
    {id: "vicente_lopez", name: "Vicente López", type: "municipio", region: "gba"}
];

// Combine all locations
export const ALL_LOCATIONS: Location[] = [...CABA_LOCATIONS, ...GBA_LOCATIONS];

// Helper functions
export const getLocationsByRegion = (region: "caba" | "gba"): Location[] => {
    return ALL_LOCATIONS.filter(location => location.region === region);
};

export const getLocationById = (id: string): Location | undefined => {
    return ALL_LOCATIONS.find(location => location.id === id);
};

export const getLocationByName = (name: string): Location | undefined => {
    // Normalize the name for case-insensitive comparison
    const normalizedName = name.trim().toLowerCase();

    // Only do exact match (case-insensitive) to avoid confusing street names with locations
    return ALL_LOCATIONS.find(location => location.name.toLowerCase() === normalizedName);
};

// For search component
export const SEARCH_LOCATION_OPTIONS = [
    {id: "all", name: "Todas las ubicaciones", type: "all", region: "all"},
    {id: "caba", name: "Capital Federal", type: "region", region: "caba"},
    {id: "gba", name: "Gran Buenos Aires", type: "region", region: "gba"},
    ...ALL_LOCATIONS
];

// Get all barrio names for the Hero component
export const getAllBarrioNames = (): string[] => {
    return ALL_LOCATIONS.map(location => location.name);
};


// Get locations used by medical centers - simplified version
export const getLocationsFromMedicalCenters = (): Location[] => {
    // Check if we're on the server side
    if (typeof window === 'undefined') {
        return []; // Return empty array on server side
    }

    try {
        //
        // TODO : FACU get medical center from  api, or recheck to see what this does const medicalCenters = [];
        const locationIds = new Set<string>();

        // Only collect valid location IDs that exist in our predefined lists
        /* medicalCenters.forEach(center => {
             if (center.locationId) {
                 // Check if the location ID exists in our predefined lists
                 const location = getLocationById(center.locationId);
                 if (location) {
                     locationIds.add(center.locationId);
                 }
             }
         });*/

        // Map to location objects and filter out any undefined values
        return Array.from(locationIds)
            .map(id => getLocationById(id))
            .filter((location): location is Location => location !== undefined);
    } catch (error) {
        console.error('Error getting locations from medical centers:', error);
        return []; // Return empty array on error
    }
};

// Get location names used by medical centers
export const getLocationNamesFromMedicalCenters = (): string[] => {
    // Check if we're on the server side
    if (typeof window === 'undefined') {
        return ['Buenos Aires']; // Return default on server side
    }

    try {
        const locations = getLocationsFromMedicalCenters();
        if (!locations || locations.length === 0) {
            return ['Buenos Aires']; // Return default if no locations
        }
        return locations.map(location => location.name);
    } catch (error) {
        console.error('Error getting location names:', error);
        return ['Buenos Aires']; // Return default on error
    }
};

// Get CABA locations used by medical centers
export const getCABALocationsFromMedicalCenters = (): Location[] => {
    // Check if we're on the server side
    if (typeof window === 'undefined') {
        return []; // Return empty array on server side
    }

    try {
        return getLocationsFromMedicalCenters().filter(location => location.region === "caba");
    } catch (error) {
        console.error('Error getting CABA locations:', error);
        return []; // Return empty array on error
    }
};

// Get GBA locations used by medical centers
export const getGBALocationsFromMedicalCenters = (): Location[] => {
    // Check if we're on the server side
    if (typeof window === 'undefined') {
        return []; // Return empty array on server side
    }

    try {
        return getLocationsFromMedicalCenters().filter(location => location.region === "gba");
    } catch (error) {
        console.error('Error getting GBA locations:', error);
        return []; // Return empty array on error
    }
};

// Get search location options from medical centers
export const getSearchLocationOptionsFromMedicalCenters = () => {
    // Check if we're on the server side
    if (typeof window === 'undefined') {
        // Return default options on server side
        return [
            {id: "all", name: "Todas las ubicaciones", type: "all", region: "all"},
            {id: "caba", name: "Capital Federal", type: "region", region: "caba"},
            {id: "gba", name: "Gran Buenos Aires", type: "region", region: "gba"}
        ];
    }

    try {
        const locations = getLocationsFromMedicalCenters();

        // If no locations found, return default options
        if (!locations || locations.length === 0) {
            return [
                {id: "all", name: "Todas las ubicaciones", type: "all", region: "all"},
                {id: "caba", name: "Capital Federal", type: "region", region: "caba"},
                {id: "gba", name: "Gran Buenos Aires", type: "region", region: "gba"}
            ];
        }

        const hasCABA = locations.some(location => location.region === "caba");
        const hasGBA = locations.some(location => location.region === "gba");

        return [
            {id: "all", name: "Todas las ubicaciones", type: "all", region: "all"},
            ...(hasCABA ? [{id: "caba", name: "Capital Federal", type: "region", region: "caba"}] : []),
            ...(hasGBA ? [{id: "gba", name: "Gran Buenos Aires", type: "region", region: "gba"}] : []),
            ...locations
        ];
    } catch (error) {
        console.error('Error getting search location options:', error);
        // Return default options on error
        return [
            {id: "all", name: "Todas las ubicaciones", type: "all", region: "all"},
            {id: "caba", name: "Capital Federal", type: "region", region: "caba"},
            {id: "gba", name: "Gran Buenos Aires", type: "region", region: "gba"}
        ];
    }
};
