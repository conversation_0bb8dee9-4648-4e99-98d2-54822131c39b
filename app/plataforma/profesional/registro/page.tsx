"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { <PERSON><PERSON><PERSON>t, ShieldCheck } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Link from 'next/link'
import { ProfessionalRegisterForm } from "@/components/ui/professional-register-form"

export default function ProfessionalRegistroPage() {
  const router = useRouter()
  const [formType, setFormType] = useState<"selection" | "establishment" | "individual">("selection")

  // Get background color based on selected form type
  const getBgColor = () => {
    switch (formType) {
      case "selection":
        return "bg-[#0fb5bd]/5" // Using teal for establishment initially
      case "individual":
        return "bg-cyan-50" // Using cyan for professional (individual)
      case "establishment":
        return "bg-[#0fb5bd]/5" // Using teal for establishment
      default:
        return "bg-[#0fb5bd]/5"
    }
  }

  // Get button color for back button based on selected form type
  const getButtonAccentColor = () => {
    switch (formType) {
      case "selection":
        // Use the same color as establishment for the initial screen
        return "text-[#0a7c82] text-[#0a7c82] hover:bg-[#0a7c82]/10 hover:border-[#0a7c82] border-[#0a7c82]/50"
      case "individual":
        return "text-[#0fb5bd] text-[#0fb5bd] hover:bg-[#0fb5bd]/10 hover:border-[#0fb5bd] border-[#0fb5bd]/50"
      case "establishment":
        return "text-[#0a7c82] text-[#0a7c82] hover:bg-[#0a7c82]/10 hover:border-[#0a7c82] border-[#0a7c82]/50"
      default:
        return "text-[#0a7c82] text-[#0a7c82] hover:bg-[#0a7c82]/10 hover:border-[#0a7c82] border-[#0a7c82]/50"
    }
  }

  return (
    <div className={`min-h-screen ${getBgColor()} flex flex-col transition-colors duration-300`}>
      <header className="py-3">
          <div className="container mx-auto flex justify-center items-center">
            <Link href="/">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-8 w-auto"
                priority
              />
            </Link>
          </div>
        </header>

      <main className="flex-1 flex flex-col items-start pt-4 px-4">
        <div className="w-full max-w-4xl mx-auto">
          <Button
            variant="outline"
            className={`mb-3 flex items-center gap-[0.375rem] transition-all duration-300 bg-white px-[0.75rem] py-[0.375rem] rounded-lg shadow-sm hover:shadow-md ${getButtonAccentColor()}`}
            onClick={() => {
              if (formType === "establishment" || formType === "individual") {
                // Reset to selection screen
                setFormType("selection");
              } else {
                // Go back to previous page
                router.back();
              }
            }}
          >
            <ArrowLeft className="w-[1rem] h-[1rem] transition-colors" />
            <span className="font-medium text-[0.875rem]">Volver</span>
          </Button>

          <ProfessionalRegisterForm
            onFormTypeChange={setFormType}
            formType={formType}
          />
        </div>
      </main>

      <footer className="bg-white border-t border-gray-200 py-8 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-7 w-auto mb-3"
              />
              <p className="text-gray-500 text-sm">
                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
              </p>
            </div>

            <div className="flex flex-col items-center md:items-end">
              <div className="flex items-center mb-3">
                <ShieldCheck className="h-4 w-4 text-[#1cd8e1] mr-1.5" />
                <span className="text-sm text-[#1c2533] font-medium">Reservá turnos médicos 24/7</span>
              </div>
              <div className="flex space-x-4">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                  Términos y Condiciones
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                  Privacidad
                </Button>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}