export class AppointmentSlot {
  constructor(
    public datetime: Date,
    public isReserved = false,
    public isBlocked = false,
  ) {}
}

export class ConsecutiveAppointmentScheduler {
  private slots: AppointmentSlot[] = []
  private maxSlots: number
  private useLastToFirst: boolean = false

  constructor(startTime: Date, endTime: Date, intervalMinutes = 15, maxSlots = 4, useLastToFirst = false) {
    this.useLastToFirst = useLastToFirst
    this.maxSlots = maxSlots
    const current = new Date(startTime)
    while (current < endTime) {
      this.slots.push(new AppointmentSlot(new Date(current)))
      current.setMinutes(current.getMinutes() + intervalMinutes)
    }
  }

  getAllSlots(): AppointmentSlot[] {
    return this.slots
  }

  getAvailableSlots(): AppointmentSlot[] {
    const reservedIndices = this.slots
      .map((slot, index) => ((slot.isReserved || slot.isBlocked) ? index : -1))
      .filter((index) => index !== -1)

    // Check if we should return all slots (when maxSlots is very large)
    const returnAllSlots = this.maxSlots === Number.MAX_SAFE_INTEGER;

    if (reservedIndices.length === 0) {
      // If no reservations, return slots based on last-to-first setting
      if (this.useLastToFirst) {
        // Sort from latest to earliest (for last-to-first)
        const sortedSlots = [...this.slots].sort((a, b) => b.datetime.getTime() - a.datetime.getTime());
        return returnAllSlots ? sortedSlots : sortedSlots.slice(0, this.maxSlots);
      } else {
        // Sort from earliest to latest (default)
        return returnAllSlots ? this.slots : this.slots.slice(0, this.maxSlots);
      }
    }

    // Calculate multi-reservation influence with time awareness
    const slotPriorities = this.slots
      .map((slot, index) => {
        // Skip both reserved and blocked slots
        if (slot.isReserved || slot.isBlocked) return null

        let totalWeight = 0
        let earliestInfluence = Number.POSITIVE_INFINITY

        reservedIndices.forEach((rIndex) => {
          const timeDiff = Math.abs(slot.datetime.getTime() - this.slots[rIndex].datetime.getTime())
          const minutesDiff = timeDiff / (1000 * 60)

          // Enhanced proximity calculation with temporal awareness
          const proximityWeight = 1 / (1 + Math.pow(minutesDiff / 30, 2)) // 30-minute half-life
          const timeWeight = 1 / (1 + Math.pow(index / this.slots.length, 2)) // Favor earlier slots

          totalWeight += proximityWeight * timeWeight

          // Track earliest influencing reservation
          if (this.slots[rIndex].datetime.getTime() < earliestInfluence) {
            earliestInfluence = this.slots[rIndex].datetime.getTime()
          }
        })

        return {
          slot,
          totalWeight,
          earliestInfluence,
          slotTime: slot.datetime.getTime(),
        }
      })
      .filter((item) => item !== null)

    // Sort by:
    // 1. Total influence weight (highest first)
    // 2. Earliest influencing reservation
    // 3. Slot's own time
    const sortedSlots = slotPriorities
      .sort((a, b) => {
        if (a.totalWeight !== b.totalWeight) return b.totalWeight - a.totalWeight
        if (a.earliestInfluence !== b.earliestInfluence) return a.earliestInfluence - b.earliestInfluence
        return a.slotTime - b.slotTime
      })
      .map((item) => item.slot)

    // Return all slots if maxSlots is very large, otherwise limit to maxSlots
    return this.maxSlots === Number.MAX_SAFE_INTEGER ? sortedSlots : sortedSlots.slice(0, this.maxSlots)
  }

  markSlotsAsReserved(reservedTimes: string[]): void {
    this.slots.forEach(slot => {
      const slotTime = `${slot.datetime.getHours().toString().padStart(2, '0')}:${slot.datetime.getMinutes().toString().padStart(2, '0')}`;
      if (reservedTimes.includes(slotTime)) {
        slot.isReserved = true;
      }
    });
  }

  // Helper method to check if an appointment should be considered for scheduling
  static isActiveAppointment(appointment: { status?: string }): boolean {
    // Ignore appointments with status "Cancelado"
    return appointment.status !== "Cancelado";
  }

  markSlotsAsBlocked(blockedTimes: string[]): void {
    this.slots.forEach(slot => {
      const slotTime = `${slot.datetime.getHours().toString().padStart(2, '0')}:${slot.datetime.getMinutes().toString().padStart(2, '0')}`;
      if (blockedTimes.includes(slotTime)) {
        slot.isBlocked = true;
      }
    });
  }

  getAvailableTimesAsStrings(): string[] {
    // Get available slots using the existing logic (blocked slots are already filtered out)
    const availableSlots = this.getAvailableSlots();

    // Convert to time strings
    return availableSlots.map(slot =>
      `${slot.datetime.getHours().toString().padStart(2, '0')}:${slot.datetime.getMinutes().toString().padStart(2, '0')}`
    );
  }

  // Setter for useLastToFirst
  setUseLastToFirst(value: boolean): void {
    this.useLastToFirst = value;
  }

  // Mark a slot as blocked
  blockSlot(time: string): void {
    this.slots.forEach(slot => {
      const slotTime = `${slot.datetime.getHours().toString().padStart(2, '0')}:${slot.datetime.getMinutes().toString().padStart(2, '0')}`;
      if (slotTime === time) {
        slot.isBlocked = true;
      }
    });
  }

  // Unblock a slot
  unblockSlot(time: string): void {
    this.slots.forEach(slot => {
      const slotTime = `${slot.datetime.getHours().toString().padStart(2, '0')}:${slot.datetime.getMinutes().toString().padStart(2, '0')}`;
      if (slotTime === time) {
        slot.isBlocked = false;
      }
    });
  }

  // Get all blocked times as strings
  getBlockedTimesAsStrings(): string[] {
    return this.slots
      .filter(slot => slot.isBlocked)
      .map(slot => `${slot.datetime.getHours().toString().padStart(2, '0')}:${slot.datetime.getMinutes().toString().padStart(2, '0')}`);
  }
}
