"use client"

import {useMemo, useState} from "react"
import {useParams, useRouter} from "next/navigation"
import Image from "next/image"
import {medicalCenters} from "@/data/medicalCenters"
import {doctors} from "@/data/doctors"
import {useAppointments} from "@/contexts/AppointmentContext"
import {addDays, eachDayOfInterval, format, startOfWeek} from "date-fns"
import {es} from "date-fns/locale"
import {getMonthData} from "@/utils/dateUtils"
import {Calendar, ChevronLeft, ChevronRight, User} from "lucide-react"
import {Button} from "@/components/ui/button"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue,} from "@/components/ui/select"

const findNextAvailableDate = (startDate: Date, availableDays: number[]): Date => {
    const currentDate = new Date(startDate)
    while (!availableDays.includes(currentDate.getDay())) {
        currentDate.setDate(currentDate.getDate() + 1)
    }
    return currentDate
}

export default function SharedAgenda() {
    const params = useParams()
    const router = useRouter()
    const initialMedicalCenterId = params.medicalCenterId as string
    const doctorId = params.doctorId as string

    const doctor = doctors.find((d) => d.id === doctorId)
    const {appointments} = useAppointments()

    const [selectedMedicalCenterId, setSelectedMedicalCenterId] = useState(initialMedicalCenterId)
    const availableDays = Object.keys(doctor?.workingDays || {})
        .filter((day) => doctor?.workingDays[day].enabled)
        .map((day) => Number.parseInt(day))
    const [currentDate, setCurrentDate] = useState(() => findNextAvailableDate(new Date(), availableDays))
    const [weekStart, setWeekStart] = useState(() => startOfWeek(currentDate, {weekStartsOn: 1}))

    const medicalCenter = useMemo(() => medicalCenters.find((mc) => mc.id === selectedMedicalCenterId), [selectedMedicalCenterId])
    const doctorMedicalCenters = useMemo(() => medicalCenters.filter(mc => mc.doctors.includes(doctorId)), [doctorId])

    const {days, startingDay} = useMemo(() => {
        const data = getMonthData(currentDate.getFullYear(), currentDate.getMonth())
        const adjustedStartingDay = (data.startingDay - 1 + 7) % 7
        return {days: data.days, startingDay: adjustedStartingDay}
    }, [currentDate])

    const weekDays = useMemo(() => {
        const weekEnd = addDays(weekStart, 6)
        const daysInWeek = eachDayOfInterval({start: weekStart, end: weekEnd})
        return daysInWeek.map((day) => {
            const year = day.getFullYear()
            const month = String(day.getMonth() + 1).padStart(2, '0')
            const date = String(day.getDate()).padStart(2, '0')
            const dateStr = `${year}-${month}-${date}`
            const dayAppointments = (appointments[dateStr] || [])
                .filter(apt => apt.doctorId === doctorId)
                .sort((a, b) => a.time.localeCompare(b.time))
            return {date: day, appointments: dayAppointments}
        }).filter(day => day.appointments.length > 0)
    }, [weekStart, appointments, doctorId])

    if (!doctor || !medicalCenter) {
        return <div>Medical Center or Doctor not found</div>
    }

    const handlePrevMonth = () => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1))
    const handleNextMonth = () => setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1))
    const handleDateSelect = (date: Date) => {
        setCurrentDate(date)
        setWeekStart(startOfWeek(date, {weekStartsOn: 1}))
    }
    const handleMedicalCenterChange = (value: string) => {
        setSelectedMedicalCenterId(value)
        router.push(`/plataforma/profesional/${doctorId}/shared-agenda/${selectedMedicalCenterId}`)
    }

    return (
        <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white flex flex-col">
            <style jsx global>{`
                html {
                    font-size: 13px;
                }

                @media (min-width: 1500px) {
                    html {
                        font-size: 15px;
                    }
                }

                @media (min-width: 2560px) {
                    html {
                        font-size: 17px;
                    }
                }

                .container {
                    max-width: 1400px;
                }
            `}</style>

            <header className="bg-white shadow-md border-b border-blue-100">
                <div
                    className="container mx-auto px-6 py-4 flex flex-col lg:flex-row items-center justify-between gap-4">
                    <div className="flex items-center justify-between w-full lg:w-auto">
                        <Image
                            src="/images/turnera-logo.svg"
                            alt="Turnera Logo"
                            width={120}
                            height={40}
                            className="h-8 w-auto"
                            priority
                        />
                    </div>
                    <div className="flex flex-col lg:flex-row items-center gap-4 w-full lg:w-auto">
                        <Select value={selectedMedicalCenterId} onValueChange={handleMedicalCenterChange}>
                            <SelectTrigger className="w-full lg:w-[200px] bg-blue-50 border-blue-200">
                                <SelectValue placeholder="Select Medical Center"/>
                            </SelectTrigger>
                            <SelectContent>
                                {doctorMedicalCenters.map(mc => (
                                    <SelectItem key={mc.id} value={mc.id}>{mc.name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <div
                            className="flex rounded-lg bg-white border border-blue-200 shadow-sm p-1 gap-1 w-full lg:w-auto">
                            <Button
                                variant="default"
                                className="flex-1 lg:flex-none px-3 py-1 text-sm font-medium rounded-md bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                            >
                                Agenda
                            </Button>
                            <Button
                                variant="ghost"
                                className="flex-1 lg:flex-none px-3 py-1 text-sm font-medium rounded-md text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                                onClick={() => router.push(`/plataforma/profesional/${doctorId}/analytics/${selectedMedicalCenterId}`)}
                            >
                                Estadísticas
                            </Button>
                        </div>
                    </div>
                    <div
                        className="bg-blue-50 rounded-lg px-4 py-2 border border-blue-100 w-full lg:w-auto text-center lg:text-left">
                        <h1 className="text-lg font-semibold text-blue-800">{medicalCenter.name}</h1>
                        <div className="flex items-center justify-center lg:justify-start text-blue-600 mt-1">
                            <User size={14} className="mr-1"/>
                            <p className="text-sm">{doctor.name}</p>
                        </div>
                    </div>
                </div>
            </header>

            <main className="container mx-auto px-6 py-6 flex-1 flex flex-col lg:flex-row gap-6">
                <div className="lg:w-1/3">
                    <div className="bg-white border border-blue-100 rounded-xl shadow-sm p-5">
                        <div className="flex items-center justify-between mb-4">
                            <button onClick={handlePrevMonth}
                                    className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-blue-50 text-blue-600 transition-colors">
                                <ChevronLeft size={20}/>
                            </button>
                            <h2 className="text-xl font-semibold text-blue-800 capitalize">{format(currentDate, "MMMM yyyy", {locale: es})}</h2>
                            <button onClick={handleNextMonth}
                                    className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-blue-50 text-blue-600 transition-colors">
                                <ChevronRight size={20}/>
                            </button>
                        </div>
                        <div className="grid grid-cols-7 gap-x-0 gap-y-2 text-center text-xs font-medium">
                            {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                                <div key={i} className="text-gray-600">{day}</div>
                            ))}
                            {Array.from({length: startingDay}).map((_, i) => (
                                <div key={`empty-${i}`} className="p-2"/>
                            ))}
                            {days.map((day, i) => {
                                const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day.dateNumber)
                                const dateStr = date.toISOString().split("T")[0]
                                const hasAppointments = (appointments[dateStr] || []).some(apt => apt.doctorId === doctorId)
                                const isSelected = date.toDateString() === currentDate.toDateString()
                                const isWorkingDay = availableDays.includes(day.date)
                                return (
                                    <button
                                        key={i}
                                        onClick={() => isWorkingDay && handleDateSelect(date)}
                                        className={`relative group w-[2rem] h-[2rem] p-0 rounded-full text-base mx-auto
                      ${isWorkingDay ? 'text-gray-900' : 'text-gray-400 cursor-not-allowed'}
                      ${isSelected ? 'bg-blue-500 text-white' : hasAppointments ? 'hover:bg-blue-50' : ''}`}
                                    >
                                        <div
                                            className="w-full h-full flex items-center justify-center">{day.dateNumber}</div>
                                        {hasAppointments && (
                                            <div className={`absolute left-1/2 -translate-x-1/2 w-[0.375rem] h-[0.375rem] rounded-full transition-all duration-200 bg-blue-500
                        ${isSelected ? "-bottom-[0.625rem]" : "-bottom-[0.125rem] group-hover:-bottom-[0.625rem]"}`}
                                            />
                                        )}
                                    </button>
                                )
                            })}
                        </div>
                        <div className="mt-6 pt-4 border-t border-blue-100">
                            <h3 className="text-sm font-semibold text-blue-800 mb-2">Estado de turnos</h3>
                            <div className="grid grid-cols-2 gap-2 text-xs">
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                    <span>En Atención</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                    <span>Agendado</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                    <span>Atendido</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                    <span>Ausente</span></div>
                                <div className="flex items-center">
                                    <div className="w-3 h-3 rounded-full bg-violet-500 mr-2"></div>
                                    <span>Recepcionado</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="lg:w-2/3">
                    <div className="flex items-center mb-4">
                        <Calendar size={20} className="text-blue-600 mr-2"/>
                        <h2 className="text-xl font-semibold text-blue-800">Agenda Semanal</h2>
                    </div>
                    {weekDays.length === 0 ? (
                        <div className="bg-white rounded-xl border border-blue-100 p-8 text-center shadow-sm">
                            <Calendar size={40} className="text-blue-300 mx-auto mb-3"/>
                            <p className="text-blue-600">No hay turnos programados para esta semana.</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {weekDays.map(({date, appointments}) => {
                                const isSelectedDate = date.toDateString() === currentDate.toDateString()
                                return (
                                    <div key={date.toISOString()} className="flex gap-3">
                                        <div className="w-16 flex-shrink-0 text-left">
                                            <div
                                                className={`${isSelectedDate ? 'bg-blue-600 shadow-md' : 'bg-blue-600'} text-white rounded-lg p-1 text-center mb-1 transition-all duration-200`}>
                                                <p className="text-xs font-medium">{format(date, "EEEE", {locale: es})}</p>
                                                <p className="text-lg font-bold leading-none">{format(date, "d", {locale: es})}</p>
                                                <p className="text-xs text-center">{format(date, "MMM", {locale: es})}</p>
                                            </div>
                                        </div>
                                        <div className="flex-1 space-y-2">
                                            {appointments.map((appointment) => {
                                                const statusColor = appointment.status === "Ausente" ? "text-red-700" : appointment.status === "Atendido" ? "text-green-700" :
                                                    appointment.status === "Agendado" ? "text-yellow-700" : appointment.status === "Recepcionado" ? "text-violet-800" : "text-blue-700"
                                                const borderClass = isSelectedDate ? "border-2 shadow-md" : "border border-[0.05vw] shadow-sm"
                                                return (
                                                    <div
                                                        key={appointment.id}
                                                        className={`${borderClass} rounded-lg p-2 text-sm
                              ${appointment.status === "Ausente" ? "bg-red-50 border-red-300" : appointment.status === "Atendido" ? "bg-green-50 border-green-300" :
                                                            appointment.status === "Agendado" ? "bg-yellow-50 border-yellow-300" : appointment.status === "Recepcionado" ? "bg-violet-50 border-violet-300" : "bg-blue-50 border-blue-300"}
                              ${isSelectedDate ? "transition-all duration-200" : ""}`}
                                                    >
                                                        <p className={`font-semibold ${statusColor}`}>{appointment.time}</p>
                                                        <p className={statusColor}>{appointment.patient}</p>
                                                        <p className={statusColor}>{appointment.type}</p>
                                                    </div>
                                                )
                                            })}
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    )}
                </div>
            </main>

            <footer className="bg-white border-t border-blue-100 py-3 mt-auto">
                <div className="container mx-auto px-6 text-center text-sm text-blue-500">
                    Turnera © {new Date().getFullYear()} - Sistema de Gestión de Turnos
                </div>
            </footer>
        </div>
    )
}