"use client"

import { useContext, useEffect, useRef, useState } from "react"
import { DoctorContext } from "@/contexts/DoctorContext"
import { AppStateContext } from "@/contexts/AppStateContext"
import { useAppointments } from "@/contexts/AppointmentContext"
import { useScheduleChanges } from "@/contexts/ScheduleChangesContext"
import { CoverageContext } from "@/contexts/CoverageContext"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { LoadingButton } from "@/components/ui/loading-button"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertD<PERSON>og<PERSON>ooter, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Save } from "lucide-react"
import DoctorInfoTab from "./doctor-dialog/DoctorInfoTab"
import DoctorCoveragesTab from "./doctor-dialog/DoctorCoveragesTab"
import DoctorWeeklyScheduleTab from "./doctor-dialog/DoctorWeeklyScheduleTab"
import DoctorConsultationTypesTab from "./doctor-dialog/DoctorConsultationTypesTab"
import DoctorBookingPoliciesTab from "./doctor-dialog/DoctorBookingPoliciesTab"
import DoctorDisassociateTab from "./doctor-dialog/DoctorDisassociateTab"

export default function DoctorDialog() {
  const { isDialogOpen, setIsDialogOpen, selectedDoctor, handleSaveConfig, refreshDoctorsFromStorage, activeMedicalCenterId, pendingDoctorCoverageExceptions, doctors } = useContext(DoctorContext)
  const { activeConfigTab, setActiveConfigTab } = useContext(AppStateContext)
  const { cancelAppointment } = useAppointments()
  const { appointmentsToCancel, cancellationOption, cancellationReason, clearAppointmentsToCancel } = useScheduleChanges()
  const { doctorCoverageExceptions, setDoctorCoverageExceptions, toggleMedicalCenterCoverage, removeCoverageFromMedicalCenter, isCoverageAcceptedByMedicalCenter, getAcceptedCoveragesForCenter } = useContext(CoverageContext)

  // State for confirmation dialog
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  // State for tracking when we're saving changes
  const [isSaving, setIsSaving] = useState(false)

  // Use a ref to track if we've already refreshed the data
  const hasRefreshedRef = useRef(false);

  // Refresh doctors from storage when dialog opens, but only once
  useEffect(() => {
    if (isDialogOpen && !hasRefreshedRef.current) {
      console.log("DoctorDialog: Refreshing doctors from storage when dialog opens")
      hasRefreshedRef.current = true;
      setTimeout(() => {
        refreshDoctorsFromStorage();
      }, 0);
    } else if (!isDialogOpen) {
      // Reset the ref when the dialog closes
      hasRefreshedRef.current = false;

      // Check if there are pending doctor-exclusive coverages to apply
      if (typeof window !== 'undefined') {
        // Clear any pending medical center coverages
        window.localStorage.removeItem('pendingMedicalCenterCoverages');

        // Clear any pending doctor-exclusive coverages
        window.localStorage.removeItem('pendingDoctorExclusiveCoverages');

        // Clear any pending coverage removals from center
        window.localStorage.removeItem('pendingCoverageRemovalsFromCenter');

        // Clear any pending visual removals
        window.localStorage.removeItem('pendingVisualRemovals');
      }
    }
  }, [isDialogOpen, refreshDoctorsFromStorage, selectedDoctor])

  // Force a refresh when the tab changes to the weekly schedule tab
  useEffect(() => {
    if (isDialogOpen && activeConfigTab === "schedule") {
      console.log("DoctorDialog: Tab changed to schedule, refreshing data")
      // Use setTimeout to avoid immediate state updates
      setTimeout(() => {
        refreshDoctorsFromStorage();
      }, 0);
    }
  }, [isDialogOpen, activeConfigTab, refreshDoctorsFromStorage])

  // Function to handle saving and triggering a page refresh
  const handleSaveAndRefresh = () => {
    if (!selectedDoctor) return;

    // Set saving state to true
    setIsSaving(true);

    // We're now using the isLoading state of the configuration page
    // No need to set any flags

    // Apply pending doctor coverage exceptions
    if (selectedDoctor) {
      const newExceptions = { ...doctorCoverageExceptions };
      // Always update the exceptions, even if empty (to handle removing all exceptions)
      newExceptions[selectedDoctor.id] = [...pendingDoctorCoverageExceptions];
      setDoctorCoverageExceptions(newExceptions);
      console.log("Applied pending doctor coverage exceptions:", pendingDoctorCoverageExceptions);

      // Apply any pending doctor-exclusive coverages
      const pendingExclusiveCoveragesJson = window.localStorage.getItem('pendingDoctorExclusiveCoverages');
      if (pendingExclusiveCoveragesJson) {
        try {
          const { doctorId, coverageIds } = JSON.parse(pendingExclusiveCoveragesJson);

          // Create exceptions for all doctors except this one
          doctors.forEach(doctor => {
            if (doctor.id !== doctorId) {
              // Add exceptions for this doctor
              if (!newExceptions[doctor.id]) {
                newExceptions[doctor.id] = [];
              }

              // Add exceptions for each coverage
              coverageIds.forEach((coverageId: string) => {
                // Check if this doctor already has an exception for this coverage
                const hasException = newExceptions[doctor.id].some(
                  e => e.coverageId === coverageId && e.planId === null
                );

                // If not, add one
                if (!hasException) {
                  newExceptions[doctor.id].push({
                    coverageId,
                    planId: null,
                    excluded: true
                  });
                }
              });
            }
          });

          // Update the exceptions
          setDoctorCoverageExceptions(newExceptions);
          console.log("Applied doctor-exclusive coverages for doctor:", doctorId);
        } catch (error) {
          console.error("Error applying doctor-exclusive coverages:", error);
        }
      }

      // Apply any pending medical center coverages
      const pendingMedicalCenterCoveragesJson = window.localStorage.getItem('pendingMedicalCenterCoverages');
      if (pendingMedicalCenterCoveragesJson) {
        try {
          const coverageIds = JSON.parse(pendingMedicalCenterCoveragesJson);
          coverageIds.forEach((coverageId: string) => {
            toggleMedicalCenterCoverage(coverageId);
          });
          console.log("Applied pending medical center coverages:", coverageIds);
        } catch (error) {
          console.error("Error applying pending medical center coverages:", error);
        }
      }

      // Apply any pending coverage removals from the medical center
      const pendingCoverageRemovalsFromCenterJson = window.localStorage.getItem('pendingCoverageRemovalsFromCenter');
      if (pendingCoverageRemovalsFromCenterJson) {
        try {
          const coverageIds = JSON.parse(pendingCoverageRemovalsFromCenterJson);
          console.log("Found pending coverage removals from center:", coverageIds);

          // Log the current state of medical center coverages
          const beforeAccepted = getAcceptedCoveragesForCenter ? getAcceptedCoveragesForCenter() : [];
          console.log("Medical center coverages before removal:", beforeAccepted);

          coverageIds.forEach((coverageId: string) => {
            // Only remove if the coverage is currently accepted
            const isAccepted = isCoverageAcceptedByMedicalCenter(coverageId);
            console.log(`Coverage ${coverageId} is ${isAccepted ? 'accepted' : 'not accepted'} by medical center`);

            if (isAccepted) {
              console.log(`Completely removing coverage ${coverageId} from medical center`);
              removeCoverageFromMedicalCenter(coverageId);
            }
          });

          // Log the updated state of medical center coverages
          const afterAccepted = getAcceptedCoveragesForCenter ? getAcceptedCoveragesForCenter() : [];
          console.log("Medical center coverages after removal:", afterAccepted);

          console.log("Applied pending coverage removals from center:", coverageIds);
        } catch (error) {
          console.error("Error applying pending coverage removals from center:", error);
        }
      }

      // Clear pending changes
      window.localStorage.removeItem('pendingDoctorExclusiveCoverages');
      window.localStorage.removeItem('pendingMedicalCenterCoverages');
      window.localStorage.removeItem('pendingCoverageRemovalsFromCenter');
      window.localStorage.removeItem('pendingVisualRemovals');
    }

    // Save the configuration
    handleSaveConfig();

    // Dispatch custom events to force immediate UI updates
    if (typeof window !== 'undefined' && selectedDoctor) {
      // Event for config saved
      const savedEvent = new CustomEvent('doctor-config-saved', {
        detail: {
          doctorId: selectedDoctor.id,
          medicalCenterId: activeMedicalCenterId,
          timestamp: Date.now()
        }
      });
      window.dispatchEvent(savedEvent);
      console.log(`DoctorDialog: Dispatched doctor-config-saved event for ${selectedDoctor.id}`);

      // Event for dialog closed with saving
      const closedEvent = new CustomEvent('doctor-dialog-closed', {
        detail: {
          doctorId: selectedDoctor.id,
          saved: true,
          timestamp: Date.now()
        }
      });
      window.dispatchEvent(closedEvent);
      console.log(`DoctorDialog: Dispatched doctor-dialog-closed event for ${selectedDoctor.id} (saved)`);

      // Show the loading overlay immediately
      if (typeof window !== 'undefined') {
        // Dispatch a custom event to show the loading overlay
        const loadingEvent = new CustomEvent('show-loading-overlay');
        window.dispatchEvent(loadingEvent);

        // Trigger a hard refresh after a short delay to allow events to be processed
        setTimeout(() => {
          window.location.reload();
        }, 100);
      }
    }
  };

  if (!selectedDoctor) {
    return null
  }

  return (
    <>
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          // If dialog is being closed (open is false), dispatch the event
          if (!open && typeof window !== 'undefined' && selectedDoctor) {
            const event = new CustomEvent('doctor-dialog-closed', {
              detail: {
                doctorId: selectedDoctor.id,
                saved: false,
                timestamp: Date.now()
              }
            });
            window.dispatchEvent(event);
            console.log(`DoctorDialog: Dispatched doctor-dialog-closed event for ${selectedDoctor.id} (not saved, clicked outside)`);
          }
          setIsDialogOpen(open);
        }}
      >
        {/* Apply flex column, wider max-width, max-height, overflow-hidden, and fixed top position */}
        <DialogContent className="flex flex-col max-w-6xl max-h-[85vh] bg-white shadow-md overflow-hidden p-0 data-[state=open]:top-10 data-[state=open]:translate-y-0">
          {/* Fixed Header */}
          <DialogHeader className="flex-shrink-0 px-6 pt-6 pb-2 border-b">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-md bg-blue-600 text-white flex items-center justify-center text-lg font-semibold">
                {selectedDoctor.initial}
              </div>
              <div>
                <DialogTitle className="text-xl text-gray-900">{selectedDoctor.name}</DialogTitle>
                <DialogDescription className="text-gray-600">
                  {selectedDoctor.specialties.join(", ")}{" "}
                  {selectedDoctor.mn && `(M.N. ${selectedDoctor.mn})`}
                  {selectedDoctor.email && <span> | {selectedDoctor.email}</span>}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {/* Tabs component manages tabs list and scrollable content */}
          <Tabs value={activeConfigTab} onValueChange={setActiveConfigTab} className="flex flex-col flex-grow overflow-hidden">
            {/* Fixed Tabs List container */}
            <div className="flex-shrink-0 border-b px-6 pt-2">
              <TabsList className="bg-transparent border-b-0 p-0 h-auto">
                <TabsTrigger
                  value="info"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none py-3 px-4 text-gray-700"
                >
                  Información
                </TabsTrigger>
                <TabsTrigger
                  value="coverages"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none py-3 px-4 text-gray-700"
                >
                  Coberturas
                </TabsTrigger>
                <TabsTrigger
                  value="weeklySchedule"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none py-3 px-4 text-gray-700"
                >
                  Horarios Semanales
                </TabsTrigger>
                <TabsTrigger
                  value="consultationTypes"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none py-3 px-4 text-gray-700"
                >
                  Tipos de Atención
                </TabsTrigger>
                <TabsTrigger
                  value="bookingPolicies"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none py-3 px-4 text-gray-700"
                >
                  Políticas de Reserva
                </TabsTrigger>
                <TabsTrigger
                  value="disassociate"
                  className="data-[state=active]:border-b-2 data-[state=active]:border-red-600 rounded-none py-3 px-4 text-red-700"
                >
                  Desasociar Profesional
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Scrollable Content Area */}
            <div className="flex-grow overflow-y-auto p-6">
              <TabsContent value="info" className="mt-0"> {/* Use mt-0 to remove default Shadcn margin */}
                <DoctorInfoTab />
              </TabsContent>
              <TabsContent value="coverages" className="mt-0">
                <DoctorCoveragesTab />
              </TabsContent>
              <TabsContent value="weeklySchedule" className="mt-0">
                <DoctorWeeklyScheduleTab />
              </TabsContent>
              <TabsContent value="consultationTypes" className="mt-0">
                <DoctorConsultationTypesTab />
              </TabsContent>
              <TabsContent value="bookingPolicies" className="mt-0">
                <DoctorBookingPoliciesTab />
              </TabsContent>
              <TabsContent value="disassociate" className="mt-0">
                <DoctorDisassociateTab />
              </TabsContent>
            </div>
          </Tabs>

          {/* Fixed Footer */}
          <DialogFooter className="flex-shrink-0 px-6 py-4 bg-blue-50 border-t border-blue-100">
            <Button
              variant="outline"
              onClick={() => {
                // Dispatch a custom event to notify that the dialog was closed without saving
                if (typeof window !== 'undefined' && selectedDoctor) {
                  const event = new CustomEvent('doctor-dialog-closed', {
                    detail: {
                      doctorId: selectedDoctor.id,
                      saved: false,
                      timestamp: Date.now()
                    }
                  });
                  window.dispatchEvent(event);
                  console.log(`DoctorDialog: Dispatched doctor-dialog-closed event for ${selectedDoctor.id} (not saved)`);
                }

                // Close the dialog
                setIsDialogOpen(false);
              }}
              className="border-gray-300 text-gray-700 hover:bg-gray-100"
            >
              Cancelar
            </Button>
            <LoadingButton
              isLoading={isSaving}
              onClick={() => {
                if (appointmentsToCancel.length > 0) {
                  // Show confirmation dialog if there are appointments to cancel
                  setShowConfirmDialog(true)
                } else {
                  // Otherwise, just save normally with refresh
                  handleSaveAndRefresh();
                }
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white gap-2"
            >
              <Save className="mr-2 h-4 w-4" />
              Guardar cambios
            </LoadingButton>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog for Cancelling Appointments */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar cambios</AlertDialogTitle>
            <AlertDialogDescription>
              {appointmentsToCancel.length > 0 &&
                `Se cancelarán ${appointmentsToCancel.length} turnos debido a los cambios en el horario.`
              }
            </AlertDialogDescription>
            {appointmentsToCancel.length > 0 && (
              <div className="mt-2 text-sm text-gray-600">
                {cancellationOption === "auto" && (
                  <div className="mt-2">Se enviará una notificación automática a los pacientes con el motivo: &quot;{cancellationReason}&quot;</div>
                )}
                {cancellationOption === "manual" && (
                  <div className="mt-2">Deberá notificar manualmente a los pacientes sobre la cancelación.</div>
                )}
              </div>
            )}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowConfirmDialog(false)}>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                // Cancel the appointments
                if (appointmentsToCancel.length > 0) {
                  appointmentsToCancel.forEach(apt => {
                    // Use cancelAppointment instead of removeAppointment
                    cancelAppointment(apt.id, cancellationReason)

                    // In a real implementation, we would send emails if cancellationOption is "auto"
                    // if (cancellationOption === "auto") {
                    //   sendCancellationEmail(apt, cancellationReason)
                    // }
                  })
                }

                // Clear the appointments to cancel
                clearAppointmentsToCancel()

                // Close the dialog
                setShowConfirmDialog(false)

                // Save with refresh
                handleSaveAndRefresh();
              }}
            >
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}