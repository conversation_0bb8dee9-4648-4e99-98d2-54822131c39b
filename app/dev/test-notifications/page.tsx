"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Send, Calendar, MessageCircle, Mail } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"
import {
  handleNewAppointment,
  handleAppointmentCancellation,
  processAllDueNotifications,
  getAllScheduledNotifications,
  clearAllScheduledNotifications,
  saveScheduledNotifications
} from "@/services/notifications"
import { Appointment } from "@/types/scheduler"
import { Patient } from "@/types/patient"
import { Doctor } from "@/types/doctor"
import { MedicalCenter } from "@/types/medical-center"

export default function TestNotificationsPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [scheduledCount, setScheduledCount] = useState(0)
  const [testData, setTestData] = useState({
    patientName: "<PERSON>",
    patientEmail: "<EMAIL>",
    patientPhone: "+5491123456789",
    doctorName: "Dr. <PERSON> García",
    medicalCenterName: "Centro Médico San Juan",
    medicalCenterAddress: "Av. Corrientes 1234, CABA",
    appointmentDate: "2025-05-23",
    appointmentTime: "14:30",
    consultationType: "Consulta General"
  })

  // Update scheduled count on component mount and after actions
  const updateScheduledCount = () => {
    const scheduled = getAllScheduledNotifications()
    setScheduledCount(scheduled.length)
  }

  // Update count on mount
  useEffect(() => {
    updateScheduledCount()
  }, [])

  // Utility function to format date in local timezone as YYYY-MM-DD
  const formatDateLocal = (date: Date): string => {
    return date.getFullYear() + '-' +
      String(date.getMonth() + 1).padStart(2, '0') + '-' +
      String(date.getDate()).padStart(2, '0')
  }

  const createMockData = () => {
    const appointment: Appointment = {
      id: `test-${Date.now()}`,
      date: testData.appointmentDate,
      time: testData.appointmentTime,
      patient: "test-patient-id",
      status: "Agendado",
      source: "Turnera",
      type: testData.consultationType,
      coverage: "OSDE",
      contact: testData.patientPhone,
      doctorId: "test-doctor-id",
      medicalCenterId: "test-medical-center-id",
      duration: 30
    }

    const patient: Patient = {
      id: "test-patient-id",
      name: testData.patientName,
      dni: "12345678",
      phone: testData.patientPhone,
      email: testData.patientEmail,
      coverage: "OSDE",
      defaultCoverage: "OSDE"
    }

    const doctor: Doctor = {
      id: "test-doctor-id",
      name: testData.doctorName,
      mn: "12345",
      initial: testData.doctorName.charAt(0).toUpperCase(),
      specialties: [],
      consultationTypes: [],
      workingDays: {
        "1": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "2": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "3": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "4": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "5": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "0": { enabled: false, hours: [] },
        "6": { enabled: false, hours: [] }
      },
      onlineBookingAdvanceDays: 60,
      onlineBookingMinHours: 2,
      appointmentSlotDuration: 30
    }

    const medicalCenter: MedicalCenter = {
      id: "test-medical-center-id",
      name: testData.medicalCenterName,
      address: testData.medicalCenterAddress,
      phone: "+5491187654321",
      email: "<EMAIL>",
      doctors: ["test-doctor-id"],
      workingDays: {
        "0": { enabled: false, hours: [] },
        "1": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "2": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "3": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "4": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "5": { enabled: true, hours: [{ start: "09:00", end: "17:00" }] },
        "6": { enabled: false, hours: [] }
      }
    }

    return { appointment, patient, doctor, medicalCenter }
  }

  const testConfirmationNotification = async () => {
    setIsLoading(true)
    try {
      const mockData = createMockData()
      // Ensure this is a future appointment (tomorrow) to trigger normal confirmation
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      // Use local timezone formatting to avoid date shifts
      mockData.appointment.date = formatDateLocal(tomorrow)

      await handleNewAppointment(mockData)
      updateScheduledCount()
      toast.success("¡Notificación de confirmación enviada! Revisá las páginas de Email y WhatsApp en Dev Tools.")
    } catch (error) {
      console.error("Error sending confirmation notification:", error)
      toast.error("Error al enviar la notificación de confirmación")
    } finally {
      setIsLoading(false)
    }
  }

  const testCancellationNotification = async () => {
    setIsLoading(true)
    try {
      const mockData = createMockData()
      await handleAppointmentCancellation(mockData, "Cancelado por el paciente para prueba")
      toast.success("¡Notificación de cancelación enviada! Revisá las páginas de Email y WhatsApp en Dev Tools.")
    } catch (error) {
      console.error("Error sending cancellation notification:", error)
      toast.error("Error al enviar la notificación de cancelación")
    } finally {
      setIsLoading(false)
    }
  }

  const testSameDayConfirmation = async () => {
    setIsLoading(true)
    try {
      // Create mock data with today's date
      const today = new Date()
      const mockData = createMockData()
      // Override the appointment date to today using local timezone formatting
      mockData.appointment.date = formatDateLocal(today)

      await handleNewAppointment(mockData)
      updateScheduledCount()
      toast.success("¡Notificación de confirmación para HOY enviada! Revisá la página de Email en Dev Tools.")
    } catch (error) {
      console.error("Error sending same-day confirmation notification:", error)
      toast.error("Error al enviar la notificación de confirmación para hoy")
    } finally {
      setIsLoading(false)
    }
  }

  const createTestScheduledNotifications = async () => {
    setIsLoading(true)
    try {
      const mockData = createMockData()

      // Create notifications scheduled for the past (so they're due now)
      const now = new Date()
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000) // 1 day ago
      const threeHoursAgo = new Date(now.getTime() - 3 * 60 * 60 * 1000) // 3 hours ago

      // Get current scheduled notifications
      const currentNotifications = getAllScheduledNotifications()

      // Create test scheduled notifications
      const testNotifications = [
        {
          id: `test-1day-${Date.now()}`,
          appointmentId: mockData.appointment.id,
          type: 'reminder_1day' as const,
          scheduledFor: oneDayAgo.toISOString(),
          notificationData: mockData
        },
        {
          id: `test-3hours-${Date.now()}`,
          appointmentId: mockData.appointment.id,
          type: 'reminder_3hours' as const,
          scheduledFor: threeHoursAgo.toISOString(),
          notificationData: mockData
        }
      ]

      // Save the test notifications
      saveScheduledNotifications([...currentNotifications, ...testNotifications])

      updateScheduledCount()
      toast.success("Notificaciones de prueba creadas y programadas para ser procesadas")
    } catch (error) {
      console.error("Error creating test scheduled notifications:", error)
      toast.error("Error al crear las notificaciones de prueba")
    } finally {
      setIsLoading(false)
    }
  }

  const processDueNotifications = async () => {
    setIsLoading(true)
    try {
      const result = await processAllDueNotifications()
      updateScheduledCount()
      if (result.processed === 0) {
        toast.info("No hay notificaciones pendientes para procesar. Usá 'Crear Notificaciones de Prueba' primero.")
      } else {
        toast.success(`Procesadas ${result.processed} notificaciones, ${result.errors} errores`)
      }
    } catch (error) {
      console.error("Error processing due notifications:", error)
      toast.error("Error al procesar las notificaciones pendientes")
    } finally {
      setIsLoading(false)
    }
  }

  const clearScheduledNotifications = () => {
    clearAllScheduledNotifications()
    updateScheduledCount()
    toast.success("Todas las notificaciones programadas fueron eliminadas")
  }

  const handleInputChange = (field: string, value: string) => {
    setTestData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href="/dev" passHref>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a Dev Tools
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Probar Notificaciones</h1>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Data Form */}
        <Card>
          <CardHeader>
            <CardTitle>Configuración de Datos de Prueba</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="patientName">Nombre del Paciente</Label>
                <Input
                  id="patientName"
                  value={testData.patientName}
                  onChange={(e) => handleInputChange("patientName", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="doctorName">Nombre del Médico</Label>
                <Input
                  id="doctorName"
                  value={testData.doctorName}
                  onChange={(e) => handleInputChange("doctorName", e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="patientEmail">Email del Paciente</Label>
              <Input
                id="patientEmail"
                type="email"
                value={testData.patientEmail}
                onChange={(e) => handleInputChange("patientEmail", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="patientPhone">Teléfono del Paciente</Label>
              <Input
                id="patientPhone"
                value={testData.patientPhone}
                onChange={(e) => handleInputChange("patientPhone", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="medicalCenterName">Nombre del Centro Médico</Label>
              <Input
                id="medicalCenterName"
                value={testData.medicalCenterName}
                onChange={(e) => handleInputChange("medicalCenterName", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="medicalCenterAddress">Dirección del Centro Médico</Label>
              <Input
                id="medicalCenterAddress"
                value={testData.medicalCenterAddress}
                onChange={(e) => handleInputChange("medicalCenterAddress", e.target.value)}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="appointmentDate">Fecha del Turno</Label>
                <Input
                  id="appointmentDate"
                  type="date"
                  value={testData.appointmentDate}
                  onChange={(e) => handleInputChange("appointmentDate", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="appointmentTime">Hora del Turno</Label>
                <Input
                  id="appointmentTime"
                  type="time"
                  value={testData.appointmentTime}
                  onChange={(e) => handleInputChange("appointmentTime", e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="consultationType">Tipo de Consulta</Label>
              <Input
                id="consultationType"
                value={testData.consultationType}
                onChange={(e) => handleInputChange("consultationType", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Test Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Probar Notificaciones</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-700">
                <strong>Notificaciones programadas:</strong> {scheduledCount}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                <strong>Hoy:</strong> {formatDateLocal(new Date())} |
                <strong> Mañana:</strong> {(() => {
                  const tomorrow = new Date()
                  tomorrow.setDate(tomorrow.getDate() + 1)
                  return formatDateLocal(tomorrow)
                })()}
              </p>
            </div>

            <div className="space-y-4">
              <Button
                onClick={testConfirmationNotification}
                disabled={isLoading}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Probar Confirmación de Turno
              </Button>

              <Button
                onClick={testSameDayConfirmation}
                disabled={isLoading}
                className="w-full bg-emerald-600 hover:bg-emerald-700"
              >
                <Calendar className="h-4 w-4 mr-2" />
                Probar Confirmación para HOY
              </Button>

              <Button
                onClick={testCancellationNotification}
                disabled={isLoading}
                variant="destructive"
                className="w-full"
              >
                <Send className="h-4 w-4 mr-2" />
                Probar Cancelación de Turno
              </Button>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Notificaciones Programadas</h4>
                <div className="space-y-2">
                  <Button
                    onClick={createTestScheduledNotifications}
                    disabled={isLoading}
                    variant="outline"
                    className="w-full"
                  >
                    Crear Notificaciones de Prueba
                  </Button>

                  <Button
                    onClick={processDueNotifications}
                    disabled={isLoading}
                    variant="outline"
                    className="w-full"
                  >
                    Procesar Notificaciones Pendientes
                  </Button>

                  <Button
                    onClick={clearScheduledNotifications}
                    disabled={isLoading}
                    variant="outline"
                    className="w-full text-red-600 hover:text-red-700"
                  >
                    Limpiar Todas las Programadas
                  </Button>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">Cómo Probar:</h3>
              <ol className="text-sm text-blue-700 space-y-1">
                <li>1. Configurá los datos de prueba arriba</li>
                <li>2. Probá las notificaciones inmediatas con los botones de confirmación/cancelación</li>
                <li>3. Usá &quot;Probar Confirmación para HOY&quot; para probar turnos del mismo día (solo email)</li>
                <li>4. Usá &quot;Crear Notificaciones de Prueba&quot; para crear recordatorios vencidos</li>
                <li>5. Usá &quot;Procesar Notificaciones Pendientes&quot; para enviar los recordatorios programados</li>
                <li>6. Revisá las páginas de Email y WhatsApp para verificar las notificaciones</li>
                <li>7. Nota: Las cancelaciones se envían solo por email</li>
                <li>8. Nota: Los turnos del mismo día no programan recordatorios automáticos</li>
              </ol>
            </div>

            <div className="mt-4 space-y-2">
              <Link href="/dev/emails" className="block">
                <Button variant="outline" className="w-full">
                  <Mail className="h-4 w-4 mr-2" />
                  Ver Notificaciones de Email
                </Button>
              </Link>
              <Link href="/dev/whatsapp" className="block">
                <Button variant="outline" className="w-full">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Ver Notificaciones de WhatsApp
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
