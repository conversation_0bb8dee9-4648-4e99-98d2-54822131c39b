"use client"

import React, { useState, useEffect, Suspense } from "react"
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import { Eye, EyeOff, AlertCircle, ArrowLeft, CheckCircle } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"
import Image from "next/image"
import { User } from "@/types/users"
import { Patient } from "@/types/patient"

// Loading component for Suspense fallback
function ResetPasswordLoading() {
  return (
    <div className="min-h-screen bg-blue-50 flex flex-col transition-colors duration-300">
      <header className="py-3">
        <div className="container mx-auto flex justify-center items-center">
          <div className="h-8 w-32 bg-gray-200 animate-pulse rounded"></div>
        </div>
      </header>
      <main className="flex-grow container mx-auto px-4 py-2 sm:py-4 flex items-center justify-center">
        <div className="w-full max-w-md">
          <div className="bg-white p-8 rounded-2xl shadow-xl border border-gray-100">
            <div className="text-center mb-6">
              <div className="h-8 w-48 bg-gray-200 animate-pulse rounded mx-auto mb-2"></div>
              <div className="h-4 w-64 bg-gray-100 animate-pulse rounded mx-auto"></div>
            </div>
            <div className="space-y-4">
              <div className="h-10 bg-gray-100 animate-pulse rounded"></div>
              <div className="h-10 bg-gray-100 animate-pulse rounded"></div>
              <div className="h-10 bg-blue-100 animate-pulse rounded"></div>
            </div>
          </div>
        </div>
      </main>
      <footer className="bg-white border-t border-gray-200 py-8 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <div className="h-7 w-32 bg-gray-200 animate-pulse rounded mb-3"></div>
              <div className="h-4 w-64 bg-gray-100 animate-pulse rounded"></div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

// Client component that uses useSearchParams
function ResetPasswordContent() {
  const searchParams = useSearchParams()
  const router = useRouter()

  const email = searchParams.get("email") || ""
  const phone = searchParams.get("phone") || ""
  const codeFromUrl = searchParams.get("code") || ""

  const [verificationCode, setVerificationCode] = useState(codeFromUrl)
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [user, setUser] = useState<User | null>(null)
  const [step, setStep] = useState<"verification" | "reset">("verification")
  const [success, setSuccess] = useState(false)

  useEffect(() => {
    // If neither email nor phone is provided, redirect to recovery page
    if (!email && !phone) {
      router.push("/plataforma/recuperar-contrasena")
      return
    }

    // Find user by email or phone
    let foundUser: User | null = null;

    if (email) {
      foundUser = storage.getUserByEmail(email)
      if (foundUser) {
        setUser(foundUser)
      }
    } else if (phone) {
      // Find patient by phone
      const patients = JSON.parse(localStorage.getItem("patients") || "[]")
      const patient = patients.find((p: Patient) => p.phone === phone)

      if (patient && patient.userId) {
        // Find user by patient's userId
        const users = storage.getUsers()
        foundUser = users.find(u => u.id === patient.userId) || null
        if (foundUser) {
          setUser(foundUser)
        }
      }
    }

    // If code is provided in URL and user is found, verify it automatically
    if (codeFromUrl && foundUser && foundUser.verificationCode) {
      // Check if verification code is expired
      if (foundUser.verificationCodeExpiry) {
        const expiryDate = new Date(foundUser.verificationCodeExpiry)
        if (expiryDate < new Date()) {
          setError("El código de verificación ha expirado. Por favor, solicite uno nuevo.")
          return
        }
      }

      // Check if verification code matches
      if (foundUser.verificationCode === codeFromUrl) {
        // If verification is successful, move to reset step
        setStep("reset")
      } else {
        setError("El código de verificación es incorrecto")
      }
    }
  }, [email, phone, router, codeFromUrl])

  const handleVerificationSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!verificationCode) {
      setError("Por favor, ingrese el código de verificación")
      return
    }

    if (!user) {
      setError("No se encontró ningún usuario con los datos proporcionados")
      return
    }

    // Check if verification code exists and is not expired
    if (!user.verificationCode) {
      setError("No hay un código de verificación activo para este usuario")
      return
    }

    // Check if verification code is expired
    if (user.verificationCodeExpiry) {
      const expiryDate = new Date(user.verificationCodeExpiry)
      if (expiryDate < new Date()) {
        setError("El código de verificación ha expirado. Por favor, solicite uno nuevo.")
        return
      }
    }

    // Check if verification code matches
    if (user.verificationCode !== verificationCode) {
      setError("El código de verificación es incorrecto")
      return
    }

    // If verification is successful, move to reset step
    setStep("reset")
  }

  const handleResetSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    if (!user) {
      setError("No se encontró ningún usuario con los datos proporcionados")
      return
    }

    if (password.length < 6) {
      setError("La contraseña debe tener al menos 6 caracteres")
      return
    }

    if (password !== confirmPassword) {
      setError("Las contraseñas no coinciden")
      return
    }

    setIsLoading(true)

    try {
      // Update the user's password and clear verification code
      const updatedUser = {
        ...user,
        password,
        verificationCode: undefined,
        verificationCodeExpiry: undefined
      }

      storage.saveUser(updatedUser)

      setSuccess(true)
      toast.success("Contraseña actualizada correctamente")

      // Redirect to login page after successful password reset
      setTimeout(() => {
        router.push("/plataforma/login")
      }, 2000)
    } catch (error) {
      console.error("Error updating password:", error)
      setError("Error al actualizar la contraseña")
    } finally {
      setIsLoading(false)
    }
  }

  // Determine the background color based on the user type
  const getBgColor = () => {
    if (!user) return "bg-blue-50"

    if (user.roles === "patient") {
      return "bg-blue-50"
    } else if (user.roles === "doctor") {
      return "bg-cyan-50"
    } else {
      return "bg-[#0fb5bd]/5"
    }
  }

  // Get button color based on user type
  const getButtonColor = () => {
    if (!user) return "bg-[#0070F3] hover:bg-[#0070F3]/90"

    if (user.roles === "patient") {
      return "bg-[#0070F3] hover:bg-[#0070F3]/90"
    } else if (user.roles === "doctor") {
      return "bg-[#0fb5bd] hover:bg-[#0fb5bd]/90"
    } else {
      return "bg-[#0a7c82] hover:bg-[#0a7c82]/90"
    }
  }

  // Get accent color based on user type
  const getAccentColor = () => {
    if (!user) return "text-[#0070F3] border-[#0070F3] hover:bg-[#0070F3]/5"

    if (user.roles === "patient") {
      return "text-[#0070F3] border-[#0070F3] hover:bg-[#0070F3]/5"
    } else if (user.roles === "doctor") {
      return "text-[#0fb5bd] border-[#0fb5bd] hover:bg-[#0fb5bd]/5"
    } else {
      return "text-[#0a7c82] border-[#0a7c82] hover:bg-[#0a7c82]/5"
    }
  }

  return (
    <div className={`min-h-screen ${getBgColor()} flex flex-col transition-colors duration-300`}>
      <header className="py-3">
        <div className="container mx-auto flex justify-center items-center">
          <Link href="/">
            <Image
              src="/images/turnera-logo.svg"
              alt="Turnera Logo"
              width={120}
              height={36}
              className="h-8 w-auto"
              priority
            />
          </Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-2 sm:py-4 flex items-center justify-center">
        <div className="w-full max-w-md">
          <Card className="bg-white p-8 rounded-2xl shadow-xl border border-gray-100">
            <div className="text-center mb-6">
              <div className="flex items-center justify-center mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="mr-2 p-0 h-8 w-8 absolute left-8"
                  onClick={() => router.push("/plataforma/recuperar-contrasena")}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-2xl font-bold text-[#1c2533]">
                  {step === "verification" ? "Verificar código" : "Restablecer contraseña"}
                </h1>
              </div>
              <p className="text-gray-600">
                {step === "verification"
                  ? "Ingrese el código de verificación que recibió"
                  : "Cree una nueva contraseña para su cuenta"}
              </p>
            </div>

            {error && (
              <div className="bg-red-50 p-3 rounded-md flex items-start text-red-600 text-sm mb-4">
                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>{error}</span>
              </div>
            )}

            {success ? (
              <div className="bg-green-50 p-4 rounded-md flex flex-col items-center text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mb-2" />
                <h3 className="text-lg font-medium text-green-800">¡Contraseña actualizada!</h3>
                <p className="text-green-600 mt-1">Su contraseña ha sido actualizada correctamente.</p>
                <p className="text-green-600 mt-1">Será redirigido al inicio de sesión en unos segundos...</p>
              </div>
            ) : step === "verification" ? (
              <form onSubmit={handleVerificationSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="verificationCode" className="text-gray-700">Código de verificación</Label>
                  <Input
                    id="verificationCode"
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    placeholder="Ingrese el código de 6 dígitos"
                    className="border-input text-center text-lg tracking-widest"
                    maxLength={6}
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className={`w-full ${getButtonColor()} transition-colors h-11 text-base font-medium text-white`}
                >
                  Verificar código
                </Button>
              </form>
            ) : (
              <form onSubmit={handleResetSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-gray-700">Nueva contraseña</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Ingrese su nueva contraseña"
                      className="pr-10"
                      required
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4 text-gray-400" /> : <Eye className="h-4 w-4 text-gray-400" />}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-gray-700">Confirmar contraseña</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showPassword ? "text" : "password"}
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="Confirme su nueva contraseña"
                      className="pr-10"
                      required
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className={`w-full ${getButtonColor()} transition-colors h-11 text-base font-medium text-white`}
                  disabled={isLoading}
                >
                  {isLoading ? "Actualizando..." : "Actualizar contraseña"}
                </Button>
              </form>
            )}

            <div className="mt-6 pt-6 border-t border-gray-100 text-center">
              <Button
                variant="link"
                onClick={() => router.push("/plataforma/login")}
                className={`text-sm font-medium ${getAccentColor()}`}
              >
                Volver al inicio de sesión
              </Button>
            </div>
          </Card>
        </div>
      </main>

      <footer className="bg-white border-t border-gray-200 py-8 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-7 w-auto mb-3"
              />
              <p className="text-gray-500 text-sm">
                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

// Main page component that uses Suspense
export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<ResetPasswordLoading />}>
      <ResetPasswordContent />
    </Suspense>
  )
}
