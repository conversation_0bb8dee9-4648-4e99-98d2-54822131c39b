'use client';

import React, {createContext, useContext, useEffect, useState} from 'react';
import {User, UserRole} from '@/types/users';

interface Auth0User {
    sub: string;
    email?: string;
    name?: string;

    [key: string]: unknown;
}


interface Auth0ContextType {
    auth0User: Auth0User | undefined;
    auth0Token: string | null;
    isAuth0Loading: boolean;
    isAuth0Authenticated: boolean;
    syncWithLocalUser: () => User | null;
    saveAuth0Token: (token: string) => void;
    getAuth0Token: () => string | null;
    clearAuth0Token: () => void;
    checkAuth0Session: () => Promise<void>;
}

const Auth0Context = createContext<Auth0ContextType | undefined>(undefined);

export const useAuth0Integration = () => {
    const context = useContext(Auth0Context);
    if (!context) {
        throw new Error('useAuth0Integration must be used within Auth0IntegrationProvider');
    }
    return context;
};

interface Auth0IntegrationProviderProps {
    children: React.ReactNode;
}

const Auth0IntegrationProvider: React.FC<Auth0IntegrationProviderProps> = ({children}) => {
    const [auth0User, setAuth0User] = useState<Auth0User | undefined>(undefined);
    const [auth0Token, setAuth0Token] = useState<string | null>(null);
    const [isAuth0Loading, setIsAuth0Loading] = useState<boolean>(true);

    const isAuth0Authenticated = !!auth0User && !isAuth0Loading;

    // Check for Auth0 session on mount
    useEffect(() => {
        checkAuth0Session();
    }, []);

    const checkAuth0Session = async () => {
        setIsAuth0Loading(true);
        try {
            // Check if we have stored Auth0 data
            const storedToken = localStorage.getItem('auth0_jwt');
            const storedUser = localStorage.getItem('auth0_user');
            const isAuthenticated = localStorage.getItem('auth0_authenticated') === 'true';

            if (storedToken && storedUser && isAuthenticated) {
                setAuth0Token(storedToken);
                setAuth0User(JSON.parse(storedUser));
            }
        } catch (error) {
            console.error('Error checking Auth0 session:', error);
        } finally {
            setIsAuth0Loading(false);
        }
    };

    const syncWithLocalUser = (): User | null => {
        if (!auth0User) return null
        const storedUser = localStorage.getItem('user');
        if (!storedUser) return null;

        const parsedUser = JSON.parse(storedUser) as User;

        if (parsedUser.idFromRole && !(parsedUser.idFromRole instanceof Map)) {
            parsedUser.idFromRole = new Map(
                Object.entries(parsedUser.idFromRole).map(([key, value]) => [key as UserRole, value as number])
            );
        }

        return parsedUser;
    };

    const saveAuth0Token = (token: string) => {
        setAuth0Token(token);
        if (typeof window !== 'undefined') {
            localStorage.setItem('auth0_jwt', token);
        }
    };

    const getAuth0Token = (): string | null => {
        return auth0Token;
    };

    const clearAuth0Token = () => {
        setAuth0Token(null);
        setAuth0User(undefined);
        if (typeof window !== 'undefined') {
            localStorage.removeItem('auth0_jwt');
            localStorage.removeItem('auth0_access_token');
            localStorage.removeItem('auth0_user');
            localStorage.removeItem('employee_user');
            localStorage.removeItem('auth0_authenticated');
        }
    };

    const value: Auth0ContextType = {
        auth0User,
        auth0Token,
        isAuth0Loading,
        isAuth0Authenticated,
        syncWithLocalUser,
        saveAuth0Token,
        getAuth0Token,
        clearAuth0Token,
        checkAuth0Session,
    };

    return (
        <Auth0Context.Provider value={value}>
            {children}
        </Auth0Context.Provider>
    );
};

export default Auth0IntegrationProvider;
