import {NextRequest, NextResponse} from 'next/server'
import {cookies} from 'next/headers'

export async function PUT(request: NextRequest, {params}: { params: { auth0Id: string } }) {
    try {
        const cookieStore = await cookies()
        const tokenFromCookie = cookieStore.get('auth0_access_token')?.value || cookieStore.get('auth0_jwt')?.value
        const authHeader = request.headers.get('authorization') || ''
        const tokenFromHeader = authHeader.toLowerCase().startsWith('bearer ') ? authHeader.slice(7) : undefined
        const token = tokenFromHeader || tokenFromCookie
        if (!token) {
            return NextResponse.json({error: 'Not authenticated'}, {status: 401})
        }

        const auth0Id = params.auth0Id
        const payload = await request.json()

        const upstreamRes = await fetch(`${process.env.BACKEND_URL}/user/${encodeURIComponent(auth0Id)}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify(payload),
        })

        const text = await upstreamRes.text()
        const isJson = upstreamRes.headers.get('content-type')?.includes('application/json')
        const data = isJson ? JSON.parse(text) : text

        return NextResponse.json(data, {status: upstreamRes.status})
    } catch (error: any) {
        return NextResponse.json({error: error?.message || 'Upstream error'}, {status: 500})
    }
}

