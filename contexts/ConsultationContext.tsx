"use client"

import { createContext, useContext, useState, ReactNode } from "react"
import { ConsultationType } from "@/types/doctor"
import { DoctorContext } from "./DoctorContext"

interface ConsultationContextProps {
  newConsultationType: ConsultationType
  setNewConsultationType: (type: ConsultationType) => void
  addConsultationType: () => void
  removeConsultationType: (name: string) => void
}

export const ConsultationContext = createContext<ConsultationContextProps>({} as ConsultationContextProps)

export function ConsultationProvider({ children }: { children: ReactNode }) {
  const { editedConfig, setEditedConfig } = useContext(DoctorContext)
  const [newConsultationType, setNewConsultationType] = useState<ConsultationType>({
    name: "",
    availableOnline: true,
    onlineBookingHoursByDay: {},
    requiresMedicalOrder: false,
    duration: "default",
    dailyLimit: "unlimited",
    basePrice: 0,
    copays: [],
    excludedCoverages: [],
    acceptsPrivatePay: true,
  })

  const addConsultationType = () => {
    if (newConsultationType.name && editedConfig) {
      setEditedConfig({
        ...editedConfig,
        consultationTypes: [...editedConfig.consultationTypes, { ...newConsultationType }],
      })
      setNewConsultationType({
        name: "",
        availableOnline: true,
        onlineBookingHoursByDay: {},
        requiresMedicalOrder: false,
        duration: "default",
        dailyLimit: "unlimited",
        basePrice: 0,
        copays: [],
        excludedCoverages: [],
        acceptsPrivatePay: true,
      })
    }
  }

  const removeConsultationType = (name: string) => {
    if (editedConfig) {
      setEditedConfig({
        ...editedConfig,
        consultationTypes: editedConfig.consultationTypes.filter((t) => t.name !== name),
      })
    }
  }

  return (
    <ConsultationContext.Provider
      value={{ newConsultationType, setNewConsultationType, addConsultationType, removeConsultationType }}
    >
      {children}
    </ConsultationContext.Provider>
  )
}