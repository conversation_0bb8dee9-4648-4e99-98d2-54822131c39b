"use client"

import { useEffect, useRef, useState } from "react"
import { Check, ChevronDown, LogOut, Share2 } from "lucide-react"
import Link from "next/link"

import type { User as UserType } from "@/types/users"
import { UserRole } from "@/types/users"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MedicalCenterRoleForEmployeeUser } from "@/types/MedicalCenter/medicalCenterRoleForEmployeeUser"
import { getProfessionalUserId } from "@/utils/userUtils"

interface MedicalCenterUserPillProps {
    medicalCenter: MedicalCenterRoleForEmployeeUser | undefined;
    currentUser: UserType | null;
    medicalCenterId: number;
    logout: () => void;
    variant?: 'light' | 'dark';
    showShare?: boolean;
    dropUp?: boolean;
}

export function MedicalCenterUserPill({
    medicalCenter,
    currentUser,
    medicalCenterId,
    logout,
    variant = 'light',
    showShare = true,
    dropUp = false
}: MedicalCenterUserPillProps) {
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
    const [isCopied, setIsCopied] = useState(false)
    const userMenuRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
                setIsUserMenuOpen(false)
            }
        }

        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    const copyMedicalCenterBookingUrl = () => {
        const url = `${window.location.origin}/plataforma/reservar/cm/${medicalCenterId}`
        navigator.clipboard.writeText(url)
            .then(() => {
                setIsCopied(true)
                setTimeout(() => setIsCopied(false), 2000)
            })
            .catch(err => console.error('Failed to copy URL: ', err))
    }

    return (
        <div className="flex items-center gap-3">
            {/* Share Button */}
            {showShare && (
                <Button
                    variant="outline"
                    size="sm"
                    className="h-9 w-9 rounded-full bg-white text-slate-700 border-slate-200 hover:bg-slate-50 text-base"
                    onClick={copyMedicalCenterBookingUrl}
                    aria-label="Copiar enlace de reserva"
                >
                    {isCopied ? <Check className="h-4 w-4 text-green-600" /> : <Share2 className="h-4 w-4" />}
                </Button>
            )}

            {/* User Menu */}
            <div className="relative" ref={userMenuRef}>
                <button
                    className="flex items-center gap-2 p-2 rounded-lg hover:bg-slate-50 transition-colors"
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                    aria-label="Abrir menú de usuario"
                >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-medium text-sm">
                        {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                    </div>
                    <ChevronDown className={`h-4 w-4 text-slate-600 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} />
                </button>

                {/* Simplified Dropdown Menu */}
                {isUserMenuOpen && (
                    <div className={`absolute ${dropUp ? 'right-0 bottom-full mb-2' : 'right-0 top-full mt-2'} w-72 z-50 animate-in fade-in-0 zoom-in-95 duration-200`}>
                        <div className="bg-white rounded-lg shadow-lg border border-slate-200 overflow-hidden">
                            {/* User Header - Now clickable as profile button */}
                            <Link
                                href="/plataforma/paciente?editProfile=true"
                                className="block px-4 py-4 bg-slate-50 border-b border-slate-100 hover:bg-blue-50 transition-colors"
                                onClick={() => setIsUserMenuOpen(false)}
                            >
                                <div className="flex items-center gap-3">
                                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-medium text-base">
                                        {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <p className="font-semibold text-slate-900 text-base truncate">
                                            {currentUser?.name || 'Usuario'}
                                        </p>
                                        <p className="text-slate-500 text-sm">Mi perfil</p>
                                        {medicalCenter?.name && (
                                            <p className="text-slate-500 text-sm truncate">
                                                {medicalCenter.name}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </Link>

                            {/* Menu Items */}
                            <div className="py-1">
                                {currentUser?.roles.includes(UserRole.TURNERA_USER) && (
                                    <Link
                                        href="/plataforma/paciente"
                                        className="block px-4 py-3 text-base text-slate-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                                        onClick={() => setIsUserMenuOpen(false)}
                                    >
                                        <span>Mis turnos</span>
                                    </Link>
                                )}

                                {currentUser?.roles.includes(UserRole.PROFESSIONAL_USER) && (
                                    <Link
                                        href={`/plataforma/profesional/${getProfessionalUserId(currentUser)}`}
                                        className="block px-4 py-3 text-base text-slate-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                                        onClick={() => setIsUserMenuOpen(false)}
                                    >
                                        <span>Mi agenda profesional</span>
                                    </Link>
                                )}

                                {currentUser?.roles.includes(UserRole.EMPLOYEE_USER) && (
                                    <Link
                                        href="/plataforma/establecimiento"
                                        className="block px-4 py-3 text-base text-slate-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                                        onClick={() => setIsUserMenuOpen(false)}
                                    >
                                        <span>Mis establecimientos</span>
                                    </Link>
                                )}
                            </div>

                            {/* Logout */}
                            <div className="border-t border-slate-100">
                                <button
                                    className="w-full text-left px-4 py-3 text-base text-red-600 hover:bg-red-50 transition-colors"
                                    onClick={() => {
                                        logout()
                                        setIsUserMenuOpen(false)
                                    }}
                                >
                                    <span>Cerrar sesión</span>
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}
