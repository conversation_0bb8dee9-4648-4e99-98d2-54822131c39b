"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"

type DoctorSettings = {
  [doctorId: string]: {
    appointmentDuration: number
  }
}

type DoctorSettingsContextType = {
  doctorSettings: DoctorSettings
  updateDoctorSettings: (doctorId: string, settings: { appointmentDuration: number }) => void
}

const DoctorSettingsContext = createContext<DoctorSettingsContextType | undefined>(undefined)

export const useDoctorSettings = () => {
  const context = useContext(DoctorSettingsContext)
  if (!context) {
    throw new Error("useDoctorSettings must be used within a DoctorSettingsProvider")
  }
  return context
}

export const DoctorSettingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [doctorSettings, setDoctorSettings] = useState<DoctorSettings>({})

  useEffect(() => {
    const storedSettings = localStorage.getItem("doctorSettings")
    if (storedSettings) {
      setDoctorSettings(JSON.parse(storedSettings))
    }
  }, [])

  const updateDoctorSettings = (doctorId: string, settings: { appointmentDuration: number }) => {
    setDoctorSettings((prevSettings) => {
      const newSettings = {
        ...prevSettings,
        [doctorId]: {
          ...prevSettings[doctorId],
          ...settings,
        },
      }
      localStorage.setItem("doctorSettings", JSON.stringify(newSettings))
      return newSettings
    })
  }

  return (
    <DoctorSettingsContext.Provider value={{ doctorSettings, updateDoctorSettings }}>
      {children}
    </DoctorSettingsContext.Provider>
  )
}

