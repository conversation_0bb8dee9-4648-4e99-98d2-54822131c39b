"use client"

import { useContext, useState, useEffect, useMemo } from "react"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Clock, Settings2, Share2, Copy, Check } from "lucide-react"
import { DoctorContext } from "@/contexts/DoctorContext"
import { ScheduleContext } from "@/contexts/ScheduleContext"
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext"
import { DAYS } from "@/utils/constants"
import { MedicalCenter } from "@/types/medical-center"
import { Doctor } from "@/types/doctor"

interface DoctorCardProps {
  medicalCenterId: string
}

export default function DoctorCard({ medicalCenterId }: Doctor<PERSON>ardProps) {
  // We're now handling the saving state at the page level
  const { doctors, editDoctor, refreshDoctorsFromStorage } = useContext(DoctorContext) || { doctors: [], editDoctor: () => {}, refreshDoctorsFromStorage: () => {} }
  const { getWorkingDaysCount } = useContext(ScheduleContext) || { getWorkingDaysCount: () => 0 }
  const { medicalCenters } = useContext(MedicalCenterContext) || { medicalCenters: [] }

  // State to force re-renders when a doctor is updated
  const [forceUpdate, setForceUpdate] = useState(0);

  // Get doctors directly from localStorage to ensure we have the latest data
  // Using useEffect + useState instead of useMemo to avoid ESLint warnings
  const [directDoctors, setDirectDoctors] = useState<Doctor[]>([]);

  // Update directDoctors whenever medicalCenterId or forceUpdate changes
  useEffect(() => {
    if (typeof window === 'undefined' || !medicalCenterId) {
      setDirectDoctors([]);
      return;
    }

    try {
      const storageKey = `${STORAGE_KEYS.DOCTORS_PREFIX}${medicalCenterId}`;
      const data = localStorage.getItem(storageKey);
      if (!data) {
        setDirectDoctors([]);
        return;
      }

      const storedDoctors = JSON.parse(data) as Doctor[];
      console.log(`DoctorCard: Directly loaded ${storedDoctors.length} doctors from localStorage for ${medicalCenterId}`);

      // Log the working days for each doctor
      storedDoctors.forEach(doctor => {
        if (doctor.workingDays) {
          console.log(`Direct localStorage - Doctor ${doctor.id} (${doctor.name}) working days:`,
            Object.entries(doctor.workingDays).map(([day, config]: [string, { enabled: boolean; hours: Array<{ start: string; end: string }>; weeksFrequency?: number }]) =>
              `${day}: ${config.enabled ? 'enabled' : 'disabled'}`
            ).join(', ')
          );
        }
      });

      setDirectDoctors(storedDoctors);
    } catch (error) {
      console.error("Error reading doctors directly from localStorage:", error);
      setDirectDoctors([]);
    }
  }, [medicalCenterId, forceUpdate]);

  // Force a refresh of the doctors data when the component mounts
  useEffect(() => {
    console.log("DoctorCard: Refreshing doctors from storage on mount");
    refreshDoctorsFromStorage();
  }, [refreshDoctorsFromStorage]);



  // Listen for storage events to refresh the component when localStorage changes
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key && event.key.startsWith(STORAGE_KEYS.DOCTORS_PREFIX)) {
        console.log("DoctorCard: Storage event detected, refreshing doctors");
        refreshDoctorsFromStorage();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [refreshDoctorsFromStorage]);

  // Listen for custom doctor-updated events
  useEffect(() => {
    const handleDoctorUpdated = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.medicalCenterId === medicalCenterId) {
        console.log("DoctorCard: Doctor updated event detected", customEvent.detail);

        // Force a re-render
        setForceUpdate(prev => prev + 1);

        // Also refresh from storage to ensure we have the latest data
        refreshDoctorsFromStorage();
      }
    };

    // Listen for the doctor-config-saved event (from DoctorDialog)
    const handleDoctorConfigSaved = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.medicalCenterId === medicalCenterId) {
        console.log("DoctorCard: Doctor config saved event detected", customEvent.detail);

        // Force an immediate re-render
        setForceUpdate(prev => prev + 1);

        // Refresh from storage with a small delay to ensure we have the latest data
        setTimeout(() => {
          refreshDoctorsFromStorage();
          // Force another re-render after the refresh
          setForceUpdate(prev => prev + 1);
        }, 50);
      }
    };

    window.addEventListener('doctor-updated', handleDoctorUpdated);
    window.addEventListener('doctor-config-saved', handleDoctorConfigSaved);

    return () => {
      window.removeEventListener('doctor-updated', handleDoctorUpdated);
      window.removeEventListener('doctor-config-saved', handleDoctorConfigSaved);
    };
  }, [medicalCenterId, refreshDoctorsFromStorage, setForceUpdate]);

  console.log("DoctorCard: Raw doctors from context:", doctors.map(d => ({ id: d.id, name: d.name })))

  // Find the current medical center from the context
  const currentCenter = medicalCenters.find((mc: MedicalCenter) => mc.id === medicalCenterId);

  if (!currentCenter) {
    console.warn(`Medical center ${medicalCenterId} not found in context`);
  } else {
    console.log(`Medical center ${medicalCenterId} has doctors:`, currentCenter.doctors);
  }

  // Log when forceUpdate changes
  useEffect(() => {
    console.log(`DoctorCard: forceUpdate changed to ${forceUpdate}, re-rendering`);
  }, [forceUpdate]);

  // Combine doctors from context and direct localStorage access
  // Prefer direct localStorage data when available
  const combinedDoctors = useMemo(() => {
    // Create a map of doctors from context
    const doctorsMap = new Map(doctors.map(doctor => [doctor.id, doctor]));

    // Update the map with direct localStorage data
    directDoctors.forEach(doctor => {
      doctorsMap.set(doctor.id, doctor);
    });

    // Convert the map back to an array
    return Array.from(doctorsMap.values());
  }, [doctors, directDoctors]); // directDoctors already depends on forceUpdate

  // Filter doctors to only include those assigned to this medical center
  // This is a critical filter that ensures we only show doctors that belong to this center
  const filteredDoctors = combinedDoctors.filter(doctor => {
    // Check if this doctor's ID is in the current medical center's doctors array
    const isAssigned = currentCenter?.doctors?.includes(doctor.id) || false;

    console.log(`Doctor ${doctor.id} (${doctor.name}) belongs to medical center ${medicalCenterId}: ${isAssigned}`);

    // Log the working days for this doctor
    if (doctor.workingDays) {
      console.log(`Combined doctor ${doctor.id} (${doctor.name}) working days:`,
        Object.entries(doctor.workingDays).map(([day, config]: [string, { enabled: boolean; hours: Array<{ start: string; end: string }>; weeksFrequency?: number }]) =>
          `${day}: ${config.enabled ? 'enabled' : 'disabled'}`
        ).join(', ')
      );
    }

    return isAssigned;
  });

  // Sort the filtered doctors by name (alphabetically)
  // We use localeCompare for proper alphabetical sorting that handles accents and special characters
  const sortedDoctors = useMemo(() => {
    return [...filteredDoctors].sort((a, b) => a.name.localeCompare(b.name, undefined, { sensitivity: 'base' }));
  }, [filteredDoctors]);

  console.log("DoctorCard: Sorted doctors for medicalCenterId", medicalCenterId, ":", sortedDoctors.map(d => ({ id: d.id, name: d.name })))

  const [shareDialogOpen, setShareDialogOpen] = useState<string | null>(null)
  const [copiedDoctorId, setCopiedDoctorId] = useState<string | null>(null)

  const handleShare = (doctorId: string) => {
    setShareDialogOpen(doctorId)
  }

  const handleCopy = (shareUrl: string, doctorId: string) => {
    navigator.clipboard
      .writeText(shareUrl)
      .then(() => {
        setCopiedDoctorId(doctorId)
        setTimeout(() => setCopiedDoctorId(null), 1500)
      })
      .catch((err) => console.error("Copy failed:", err))
  }



  if (!sortedDoctors || sortedDoctors.length === 0) {
    return (
      <div className="p-6 text-center bg-gray-50 border border-gray-200 rounded-lg">
        <p className="text-gray-600">No hay profesionales asignados a este establecimiento.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 gap-4">
      {sortedDoctors.map((doctor) => {
        const shareUrl = `${window.location.origin}/plataforma/profesional/${doctor.id}/agenda/${medicalCenterId}`
        const isCopied = copiedDoctorId === doctor.id
        return (
          <Card key={doctor.id} className="bg-white overflow-hidden shadow-md border-2 border-transparent rounded-lg">
            <div className="p-[1.25rem]">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 rounded-md bg-blue-600 text-white flex items-center justify-center text-xl font-semibold">
                  {doctor.initial}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{doctor.name}</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>{doctor.specialties?.join(", ") || "No specialties available"}</span>
                    <span className="block w-1 h-1 rounded-full bg-gray-400"></span>
                    <div className="flex items-center text-blue-600">
                      <Clock className="h-3 w-3 mr-1" />
                      <span>{getWorkingDaysCount(doctor)} días configurados</span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    M.N. {doctor.mn}
                    {doctor.email && <span> | {doctor.email}</span>}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => editDoctor(doctor)}
                    className="h-10 px-4 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 hover:border-blue-200 text-blue-700 rounded-md"
                  >
                    <Settings2 className="mr-2 h-4 w-4" />
                    Configurar
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleShare(doctor.id)}
                    className="h-10 w-10 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 hover:border-blue-200 text-blue-700 rounded-md"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="mt-4">
                <div className="flex flex-wrap gap-2 mb-4">
                  {doctor.consultationTypes.slice(0, 3).map((type) => (
                    <Badge key={type.name} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 rounded-full">
                      {type.name}
                    </Badge>
                  ))}
                  {doctor.consultationTypes.length > 3 && (
                    <Badge variant="outline" className="bg-gray-100 text-gray-700 rounded-full">
                      +{doctor.consultationTypes.length - 3} más
                    </Badge>
                  )}
                </div>
                <div className="grid grid-cols-7 gap-1">
                  {DAYS.map((day) => {
                    const isWorking = doctor.workingDays[day.id]?.enabled
                    return (
                      <TooltipProvider key={day.id} delayDuration={0}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div
                              className={`text-center py-1 px-2 rounded text-xs font-medium ${
                                isWorking ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-400"
                              }`}
                            >
                              {day.name.substring(0, 3)}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            {isWorking ? (
                              <div>
                                <p className="font-medium">{day.name}</p>
                                {doctor.workingDays[day.id]?.hours.map((hour, idx) => (
                                  <p key={idx} className="text-xs">
                                    {hour.start} - {hour.end}
                                  </p>
                                ))}
                              </div>
                            ) : (
                              <p>No atiende los {day.name.toLowerCase()}</p>
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )
                  })}
                </div>
              </div>
            </div>

            <Dialog open={shareDialogOpen === doctor.id} onOpenChange={(open) => setShareDialogOpen(open ? doctor.id : null)}>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Compartir agenda de {doctor.name}</DialogTitle>
                </DialogHeader>
                <p className="text-sm text-gray-600">
                  IMPORTANTE: Compartir este enlace exclusivamente con el doctor correspondiente, ya que contiene
                  información sensible.
                </p>
                <div className="flex items-center gap-2">
                  <Input value={shareUrl} readOnly className="flex-1" />
                  <Button
                    onClick={() => handleCopy(shareUrl, doctor.id)}
                    className={`h-10 px-4 flex items-center gap-2 ${
                      isCopied ? "bg-green-600 hover:bg-green-500 text-white" : "bg-blue-600 hover:bg-blue-500 text-white"
                    }`}
                  >
                    {isCopied ? (
                      <>
                        <Check className="h-4 w-4" />
                        Copiado
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        Copiar
                      </>
                    )}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </Card>
        )
      })}
    </div>
  )
}