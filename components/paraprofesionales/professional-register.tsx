"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from "@/components/ui/select"
import { CABA_LOCATIONS, GBA_LOCATIONS } from "@/data/locations"
import { PhoneInput } from "react-international-phone"
import { PhoneNumberUtil } from 'google-libphonenumber'
import "react-international-phone/style.css"
import "@/styles/phone-input.css"
import { getSpanishCountries } from "@/data/phoneCountries"
import { Check, Building2, Stethoscope } from "lucide-react"

// Initialize phone utility
const phoneUtil = PhoneNumberUtil.getInstance()

// Form data interface
interface ProfessionalFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  establishmentName?: string
  locationArea: string
  locationId: string
  officeCount?: string
  professionalCount?: string
  monthlyAppointments: string
  formType: "establishment" | "individual"
  submittedAt: string
}

interface ProfessionalRegisterProps {
  isOpen?: boolean
  onOpenChange?: (open: boolean) => void
}

export function ProfessionalRegister({ isOpen, onOpenChange }: ProfessionalRegisterProps = {}) {
  const [open, setOpen] = useState(true)
  const [formType, setFormType] = useState<"selection" | "establishment" | "individual">("selection")
  const [locationArea, setLocationArea] = useState<"" | "caba" | "gba">("")
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false)

  const handleOpenChange = (open: boolean) => {
    setOpen(open)
    if (onOpenChange) {
      onOpenChange(open)
    }

    if (!open) {
      // Reset to initial selection when dialog is closed
      setTimeout(() => {
        setFormType("selection")
        setLocationArea("")
      }, 300)
    }
  }

  // Convert the location data to the format expected by the form components
  const cabaLocalities = CABA_LOCATIONS.map(location => ({
    value: location.id,
    label: location.name
  }))

  const gbaLocations = GBA_LOCATIONS.map(location => ({
    value: location.id,
    label: location.name
  }))

  return (
    <>
      <Dialog open={isOpen !== undefined ? isOpen : open} onOpenChange={handleOpenChange}>
        <DialogContent
          className={`p-0 overflow-hidden ${formType === "selection" ? "sm:max-w-[450px]" : "sm:max-w-[800px]"} max-w-[92vw]`}
        >
          {formType === "selection" && (
            <div className="p-0">
              <div className="rounded-t-2xl bg-gradient-to-r from-blue-50 to-cyan-50 px-5 py-4 border-b border-blue-100">
                <DialogHeader className="text-center m-0">
                  <DialogTitle className="text-xl sm:text-2xl font-semibold">
                    Seleccioná la opción que corresponda
                  </DialogTitle>
                  <DialogDescription className="text-xs sm:text-sm text-slate-600 mt-1">
                    Elegí cómo querés empezar a usar Turnera
                  </DialogDescription>
                </DialogHeader>
              </div>

              <div className="p-5 pt-5 sm:p-6 sm:pt-6 space-y-3 sm:space-y-4">
                <Button
                  onClick={() => setFormType("establishment")}
                  className="w-[85%] sm:w-full py-5 sm:py-6 text-sm sm:text-base font-medium rounded-lg bg-blue-600 hover:bg-blue-500 mx-auto flex justify-center items-center gap-2 text-center"
                >
                  <Building2 className="h-4 w-4" />
                  Tengo un establecimiento
                </Button>

                <Button
                  onClick={() => setFormType("individual")}
                  className="w-[85%] sm:w-full py-5 sm:py-6 text-sm sm:text-base font-medium rounded-lg bg-blue-600 hover:bg-blue-500 mx-auto flex justify-center items-center gap-2 text-center"
                >
                  <Stethoscope className="h-4 w-4" />
                  Tengo un consultorio particular
                </Button>
              </div>
            </div>
          )}

          {formType === "establishment" && (
            <EstablishmentForm
              locationArea={locationArea}
              setLocationArea={setLocationArea}
              cabaLocalities={cabaLocalities}
              gbaLocations={gbaLocations}
              onSuccess={() => setIsSuccessDialogOpen(true)}
            />
          )}

          {formType === "individual" && (
            <IndividualForm
              locationArea={locationArea}
              setLocationArea={setLocationArea}
              cabaLocalities={cabaLocalities}
              gbaLocations={gbaLocations}
              onSuccess={() => setIsSuccessDialogOpen(true)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Success Dialog */}
      <Dialog open={isSuccessDialogOpen} onOpenChange={setIsSuccessDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <Check className="h-8 w-8 text-green-600" />
            </div>
            <DialogTitle className="text-center text-xl mt-4">¡Registro Exitoso!</DialogTitle>
            <DialogDescription className="text-center">
              Gracias por registrarte. Nos comunicaremos con vos a la brevedad para activar tu agenda médica.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-center">
            <Button
              onClick={() => {
                setIsSuccessDialogOpen(false);
                // Use window.location.href to force a full page refresh when redirecting
                window.location.href = "/para-profesionales";
              }}
              className={formType === "individual" ? "bg-[#0fb5bd] hover:bg-[#0fb5bd]/90" : "bg-[#0a7c82] hover:bg-[#0a7c82]/90"}
            >
              Volver a la página principal
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

interface FormProps {
  locationArea: string
  setLocationArea: (area: "" | "caba" | "gba") => void
  cabaLocalities: { value: string, label: string }[]
  gbaLocations: { value: string, label: string }[]
  onSuccess?: () => void
}

function EstablishmentForm({ locationArea, setLocationArea, cabaLocalities, gbaLocations, onSuccess }: FormProps) {

  // Form state
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("+54")
  const [establishmentName, setEstablishmentName] = useState("")
  const [selectedLocationId, setSelectedLocationId] = useState("")
  const [officeCount, setOfficeCount] = useState("")
  const [professionalCount, setProfessionalCount] = useState("")
  const [monthlyAppointments, setMonthlyAppointments] = useState("")

  // Error states
  const [firstNameError, setFirstNameError] = useState("")
  const [lastNameError, setLastNameError] = useState("")
  const [emailError, setEmailError] = useState("")
  const [phoneError, setPhoneError] = useState("")
  const [establishmentNameError, setEstablishmentNameError] = useState("")
  const [locationError, setLocationError] = useState("")
  const [formError, setFormError] = useState("")

  // Validate email format
  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Reset errors
    setFirstNameError("")
    setLastNameError("")
    setEmailError("")
    setPhoneError("")
    setEstablishmentNameError("")
    setLocationError("")
    setFormError("")

    // Validate required fields
    let hasError = false

    if (!firstName.trim()) {
      setFirstNameError("El nombre es obligatorio")
      hasError = true
    }

    if (!lastName.trim()) {
      setLastNameError("El apellido es obligatorio")
      hasError = true
    }

    if (!email.trim()) {
      setEmailError("El email es obligatorio")
      hasError = true
    } else if (!validateEmail(email)) {
      setEmailError("El formato del email no es válido")
      hasError = true
    }

    if (!phone || phone === "+" || phone.length <= 5) {
      setPhoneError("El teléfono es obligatorio")
      hasError = true
    } else {
      // Validate phone format
      try {
        const phoneNumber = phoneUtil.parseAndKeepRawInput(phone)
        const isValid = phoneUtil.isValidNumber(phoneNumber)

        if (!isValid) {
          setPhoneError("El número de teléfono no es válido")
          hasError = true
        }
      } catch {
        setPhoneError("El número de teléfono no es válido")
        hasError = true
      }
    }

    if (!establishmentName.trim()) {
      setEstablishmentNameError("El nombre del establecimiento es obligatorio")
      hasError = true
    }

    if (!locationArea) {
      setLocationError("Debe seleccionar una localidad")
      hasError = true
    } else if ((locationArea === "caba" || locationArea === "gba") && !selectedLocationId) {
      setLocationError("Debe seleccionar un barrio/partido")
      hasError = true
    }

    if (!officeCount) {
      setFormError("Debe seleccionar la cantidad de consultorios")
      hasError = true
    }

    if (!professionalCount) {
      setFormError("Debe seleccionar la cantidad de profesionales")
      hasError = true
    }

    if (!monthlyAppointments) {
      setFormError("Debe seleccionar los turnos estimados mensuales")
      hasError = true
    }

    if (hasError) {
      return
    }

    // Create form data object
    const formData: ProfessionalFormData = {
      firstName,
      lastName,
      email,
      phone,
      establishmentName,
      locationArea,
      locationId: selectedLocationId,
      officeCount,
      professionalCount,
      monthlyAppointments,
      formType: "establishment",
      submittedAt: new Date().toISOString()
    }

    // Save to localStorage
    try {
      const existingData = localStorage.getItem("professional-registrations")
      const registrations = existingData ? JSON.parse(existingData) : []
      registrations.push(formData)
      localStorage.setItem("professional-registrations", JSON.stringify(registrations))

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error("Error saving form data:", error)
      setFormError("Ocurrió un error al enviar el formulario. Por favor, intente nuevamente.")
    }
  }

  return (
    <div className="relative">
      <div className="flex">
        {/* Image Section */}
        <div className="hidden md:flex md:w-2/5 relative bg-[#0a7c82] flex-col">
          <div className="p-6 flex justify-center">
            <Image
              src="/images/turnera_logo_white.svg"
              alt="Turnera"
              width={120}
              height={30}
              className="h-8 w-auto"
            />
          </div>
          <div className="flex-1 flex items-center justify-center p-2 pb-14">
            <div className="mx-auto">
              <Image
                src="/images/doctors-registration.svg"
                alt="Medical professionals"
                width={300}
                height={400}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>

        {/* Form Section */}
        <div className="w-full md:w-3/5 p-6 max-h-[90vh] overflow-y-auto">
          <h2 className="text-xl font-bold mb-2">Comenzá a usar Turnera, gratis.</h2>
          <p className="text-gray-500 text-sm mb-6">
            Al completar este formulario, nos comunicaremos a la brevedad para activar tu agenda médica.
          </p>

          <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Input
                  placeholder="Nombre"
                  value={firstName}
                  onChange={(e) => {
                    setFirstName(e.target.value)
                    setFirstNameError("")
                  }}
                  className={firstNameError ? "border-red-500" : ""}
                />
                {firstNameError && (
                  <div className="text-xs text-red-600 mt-1">
                    {firstNameError}
                  </div>
                )}
              </div>
              <div>
                <Input
                  placeholder="Apellido"
                  value={lastName}
                  onChange={(e) => {
                    setLastName(e.target.value)
                    setLastNameError("")
                  }}
                  className={lastNameError ? "border-red-500" : ""}
                />
                {lastNameError && (
                  <div className="text-xs text-red-600 mt-1">
                    {lastNameError}
                  </div>
                )}
              </div>
            </div>

            <div>
              <Input
                placeholder="Email"
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value)
                  setEmailError("")
                }}
                className={emailError ? "border-red-500" : ""}
              />
              {emailError && (
                <div className="text-xs text-red-600 mt-1">
                  {emailError}
                </div>
              )}
            </div>

            <div>
              <div className="custom-phone-input">
                <PhoneInput
                  defaultCountry="ar"
                  value={phone}
                  onChange={(phoneValue) => {
                    setPhone(phoneValue)
                    setPhoneError("")

                    try {
                      // Parse the phone number using Google's libphonenumber
                      const phoneNumber = phoneUtil.parseAndKeepRawInput(phoneValue)
                      const isValid = phoneUtil.isValidNumber(phoneNumber)

                      if (!isValid && phoneValue !== "+" && phoneValue.length > 5) {
                        setPhoneError("El número de teléfono no es válido")
                      }
                    } catch {
                      if (phoneValue !== "+" && phoneValue.length > 5) {
                        setPhoneError("El número de teléfono no es válido")
                      }
                    }
                  }}
                  inputStyle={{
                    width: '100%',
                    height: '2.5rem'
                  }}
                  className="w-full custom-phone-input with-dial-code-preview"
                  placeholder="Teléfono"
                  countrySelectorStyleProps={{
                    buttonStyle: {
                      paddingLeft: '10px',
                      paddingRight: '5px'
                    }
                  }}
                  hideDropdown={false}
                  disableDialCodeAndPrefix={true}
                  showDisabledDialCodeAndPrefix={true}
                  disableFormatting={false}
                  preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                  countries={getSpanishCountries()}
                />
                {phoneError && (
                  <div className="text-xs text-red-600 mt-1">
                    {phoneError}
                  </div>
                )}
              </div>
            </div>

            <div>
              <Input
                placeholder="Nombre del Establecimiento"
                value={establishmentName}
                onChange={(e) => {
                  setEstablishmentName(e.target.value)
                  setEstablishmentNameError("")
                }}
                className={establishmentNameError ? "border-red-500" : ""}
              />
              {establishmentNameError && (
                <div className="text-xs text-red-600 mt-1">
                  {establishmentNameError}
                </div>
              )}
            </div>

            <div className="space-y-4">
              <Select
                value={locationArea}
                onValueChange={(value) => {
                  setLocationArea(value as "" | "caba" | "gba")
                  setSelectedLocationId("")
                  setLocationError("")
                }}
              >
                <SelectTrigger className={`text-left ${locationError && !locationArea ? "border-red-500" : ""}`}>
                  <SelectValue placeholder="Localidad" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="caba">Capital Federal</SelectItem>
                  <SelectItem value="gba">Gran Buenos Aires</SelectItem>
                </SelectContent>
              </Select>

              {locationArea === "caba" && (
                <Select
                  value={selectedLocationId}
                  onValueChange={(value) => {
                    setSelectedLocationId(value)
                    setLocationError("")
                  }}
                >
                  <SelectTrigger className={`text-left ${locationError && !selectedLocationId ? "border-red-500" : ""}`}>
                    <SelectValue placeholder="Barrio" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Barrios</SelectLabel>
                      {cabaLocalities.map((locality) => (
                        <SelectItem key={locality.value} value={locality.value}>{locality.label}</SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}

              {locationArea === "gba" && (
                <Select
                  value={selectedLocationId}
                  onValueChange={(value) => {
                    setSelectedLocationId(value)
                    setLocationError("")
                  }}
                >
                  <SelectTrigger className={`text-left ${locationError && !selectedLocationId ? "border-red-500" : ""}`}>
                    <SelectValue placeholder="Partido" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Partidos</SelectLabel>
                      {gbaLocations.map((location) => (
                        <SelectItem key={location.value} value={location.value}>{location.label}</SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}

              {locationError && (
                <div className="text-xs text-red-600 mt-1">
                  {locationError}
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Select
                  value={officeCount}
                  onValueChange={(value) => {
                    setOfficeCount(value)
                    setFormError("")
                  }}
                >
                  <SelectTrigger className={`text-left ${formError && !officeCount ? "border-red-500" : ""}`}>
                    <SelectValue placeholder="Cantidad de Consultorios" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1</SelectItem>
                    <SelectItem value="2-5">2-5</SelectItem>
                    <SelectItem value="6-10">6-10</SelectItem>
                    <SelectItem value="11+">11+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select
                  value={professionalCount}
                  onValueChange={(value) => {
                    setProfessionalCount(value)
                    setFormError("")
                  }}
                >
                  <SelectTrigger className={`text-left ${formError && !professionalCount ? "border-red-500" : ""}`}>
                    <SelectValue placeholder="Cantidad de Profesionales" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1-5">1-5</SelectItem>
                    <SelectItem value="6-10">6-10</SelectItem>
                    <SelectItem value="11-20">11-20</SelectItem>
                    <SelectItem value="21+">21+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Select
                value={monthlyAppointments}
                onValueChange={(value) => {
                  setMonthlyAppointments(value)
                  setFormError("")
                }}
              >
                <SelectTrigger className={`text-left ${formError && !monthlyAppointments ? "border-red-500" : ""}`}>
                  <SelectValue placeholder="Turnos Estimados Mensuales" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="<100">Menos de 100</SelectItem>
                  <SelectItem value="100-500">100-500</SelectItem>
                  <SelectItem value="501-1000">501-1000</SelectItem>
                  <SelectItem value="1000+">Más de 1000</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formError && (
              <div className="text-xs text-red-600 mt-1 text-center">
                {formError}
              </div>
            )}

            <Button
              className="w-full py-6 bg-[#0a7c82] hover:bg-[#0a7c82]/90"
              type="submit"
            >
              Enviar
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
}

function IndividualForm({ locationArea, setLocationArea, cabaLocalities, gbaLocations, onSuccess }: FormProps) {

  // Form state
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("+54")
  const [selectedLocationId, setSelectedLocationId] = useState("")
  const [monthlyAppointments, setMonthlyAppointments] = useState("")

  // Error states
  const [firstNameError, setFirstNameError] = useState("")
  const [lastNameError, setLastNameError] = useState("")
  const [emailError, setEmailError] = useState("")
  const [phoneError, setPhoneError] = useState("")
  const [locationError, setLocationError] = useState("")
  const [formError, setFormError] = useState("")

  // Validate email format
  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Reset errors
    setFirstNameError("")
    setLastNameError("")
    setEmailError("")
    setPhoneError("")
    setLocationError("")
    setFormError("")

    // Validate required fields
    let hasError = false

    if (!firstName.trim()) {
      setFirstNameError("El nombre es obligatorio")
      hasError = true
    }

    if (!lastName.trim()) {
      setLastNameError("El apellido es obligatorio")
      hasError = true
    }

    if (!email.trim()) {
      setEmailError("El email es obligatorio")
      hasError = true
    } else if (!validateEmail(email)) {
      setEmailError("El formato del email no es válido")
      hasError = true
    }

    if (!phone || phone === "+" || phone.length <= 5) {
      setPhoneError("El teléfono es obligatorio")
      hasError = true
    } else {
      // Validate phone format
      try {
        const phoneNumber = phoneUtil.parseAndKeepRawInput(phone)
        const isValid = phoneUtil.isValidNumber(phoneNumber)

        if (!isValid) {
          setPhoneError("El número de teléfono no es válido")
          hasError = true
        }
      } catch {
        setPhoneError("El número de teléfono no es válido")
        hasError = true
      }
    }

    if (!locationArea) {
      setLocationError("Debe seleccionar una localidad")
      hasError = true
    } else if ((locationArea === "caba" || locationArea === "gba") && !selectedLocationId) {
      setLocationError("Debe seleccionar un barrio/partido")
      hasError = true
    }

    if (!monthlyAppointments) {
      setFormError("Debe seleccionar los turnos estimados mensuales")
      hasError = true
    }

    if (hasError) {
      return
    }

    // Create form data object
    const formData: ProfessionalFormData = {
      firstName,
      lastName,
      email,
      phone,
      locationArea: locationArea as "" | "caba" | "gba",
      locationId: selectedLocationId,
      monthlyAppointments,
      formType: "individual",
      submittedAt: new Date().toISOString()
    }

    // Save to localStorage
    try {
      const existingData = localStorage.getItem("professional-registrations")
      const registrations = existingData ? JSON.parse(existingData) : []
      registrations.push(formData)
      localStorage.setItem("professional-registrations", JSON.stringify(registrations))

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error("Error saving form data:", error)
      setFormError("Ocurrió un error al enviar el formulario. Por favor, intente nuevamente.")
    }
  }

  return (
    <div className="relative">
      <div className="flex">
        {/* Image Section */}
        <div className="hidden md:flex md:w-2/5 relative bg-[#0fb5bd] flex-col">
          <div className="p-6 flex justify-center">
            <Image
              src="/images/turnera_logo_white.svg"
              alt="Turnera"
              width={120}
              height={30}
              className="h-8 w-auto"
            />
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="w-4/5 mx-auto">
              <Image
                src="/images/doctors-registration.svg"
                alt="Medical professional"
                width={300}
                height={400}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>

        {/* Form Section */}
        <div className="w-full md:w-3/5 p-6 max-h-[90vh] overflow-y-auto">
          <h2 className="text-xl font-bold mb-2">Comenzá a usar Turnera gratis.</h2>
          <p className="text-gray-500 text-sm mb-6">
            Al completar este formulario, nos comunicaremos a la brevedad para activar tu agenda médica.
          </p>

          <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Input
                  placeholder="Nombre"
                  value={firstName}
                  onChange={(e) => {
                    setFirstName(e.target.value)
                    setFirstNameError("")
                  }}
                  className={firstNameError ? "border-red-500" : ""}
                />
                {firstNameError && (
                  <div className="text-xs text-red-600 mt-1">
                    {firstNameError}
                  </div>
                )}
              </div>
              <div>
                <Input
                  placeholder="Apellido"
                  value={lastName}
                  onChange={(e) => {
                    setLastName(e.target.value)
                    setLastNameError("")
                  }}
                  className={lastNameError ? "border-red-500" : ""}
                />
                {lastNameError && (
                  <div className="text-xs text-red-600 mt-1">
                    {lastNameError}
                  </div>
                )}
              </div>
            </div>

            <div>
              <Input
                placeholder="Email"
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value)
                  setEmailError("")
                }}
                className={emailError ? "border-red-500" : ""}
              />
              {emailError && (
                <div className="text-xs text-red-600 mt-1">
                  {emailError}
                </div>
              )}
            </div>

            <div>
              <div className="custom-phone-input">
                <PhoneInput
                  defaultCountry="ar"
                  value={phone}
                  onChange={(phoneValue) => {
                    setPhone(phoneValue)
                    setPhoneError("")

                    try {
                      // Parse the phone number using Google's libphonenumber
                      const phoneNumber = phoneUtil.parseAndKeepRawInput(phoneValue)
                      const isValid = phoneUtil.isValidNumber(phoneNumber)

                      if (!isValid && phoneValue !== "+" && phoneValue.length > 5) {
                        setPhoneError("El número de teléfono no es válido")
                      }
                    } catch {
                      if (phoneValue !== "+" && phoneValue.length > 5) {
                        setPhoneError("El número de teléfono no es válido")
                      }
                    }
                  }}
                  inputStyle={{
                    width: '100%',
                    height: '2.5rem'
                  }}
                  className="w-full custom-phone-input with-dial-code-preview"
                  placeholder="Teléfono"
                  countrySelectorStyleProps={{
                    buttonStyle: {
                      paddingLeft: '10px',
                      paddingRight: '5px'
                    }
                  }}
                  hideDropdown={false}
                  disableDialCodeAndPrefix={true}
                  showDisabledDialCodeAndPrefix={true}
                  disableFormatting={false}
                  preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                  countries={getSpanishCountries()}
                />
                {phoneError && (
                  <div className="text-xs text-red-600 mt-1">
                    {phoneError}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <Select
                value={locationArea}
                onValueChange={(value) => {
                  setLocationArea(value as "" | "caba" | "gba")
                  setSelectedLocationId("")
                  setLocationError("")
                }}
              >
                <SelectTrigger className={`text-left ${locationError && !locationArea ? "border-red-500" : ""}`}>
                  <SelectValue placeholder="Localidad" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="caba">Capital Federal</SelectItem>
                  <SelectItem value="gba">Gran Buenos Aires</SelectItem>
                </SelectContent>
              </Select>

              {locationArea === "caba" && (
                <Select
                  value={selectedLocationId}
                  onValueChange={(value) => {
                    setSelectedLocationId(value)
                    setLocationError("")
                  }}
                >
                  <SelectTrigger className={`text-left ${locationError && !selectedLocationId ? "border-red-500" : ""}`}>
                    <SelectValue placeholder="Barrio" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Barrios</SelectLabel>
                      {cabaLocalities.map((locality) => (
                        <SelectItem key={locality.value} value={locality.value}>{locality.label}</SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}

              {locationArea === "gba" && (
                <Select
                  value={selectedLocationId}
                  onValueChange={(value) => {
                    setSelectedLocationId(value)
                    setLocationError("")
                  }}
                >
                  <SelectTrigger className={`text-left ${locationError && !selectedLocationId ? "border-red-500" : ""}`}>
                    <SelectValue placeholder="Partido" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Partidos</SelectLabel>
                      {gbaLocations.map((location) => (
                        <SelectItem key={location.value} value={location.value}>{location.label}</SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}

              {locationError && (
                <div className="text-xs text-red-600 mt-1">
                  {locationError}
                </div>
              )}
            </div>

            <div>
              <Select
                value={monthlyAppointments}
                onValueChange={(value) => {
                  setMonthlyAppointments(value)
                  setFormError("")
                }}
              >
                <SelectTrigger className={`text-left ${formError && !monthlyAppointments ? "border-red-500" : ""}`}>
                  <SelectValue placeholder="Turnos Estimados Mensuales" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="<100">Menos de 100</SelectItem>
                  <SelectItem value="100-300">100-300</SelectItem>
                  <SelectItem value="301-500">301-500</SelectItem>
                  <SelectItem value="500+">Más de 500</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formError && (
              <div className="text-xs text-red-600 mt-1 text-center">
                {formError}
              </div>
            )}

            <Button
              className="w-full py-6 bg-[#0fb5bd] hover:bg-[#0fb5bd]/90"
              type="submit"
            >
              Enviar
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
}
