export class ConsultationHealthInsuranceExtraProperties {
    constructor(
        public healthInsuranceId: number | null,
        public name: string,
        public plan: string,
        public isExcluded: boolean,
        public coPaymentPrice: number) {
    }

    static fromJSON(json: Record<string, unknown>): ConsultationHealthInsuranceExtraProperties {
        return new ConsultationHealthInsuranceExtraProperties(
            json.healthInsuranceId as number | null,
            json.name as string,
            json.plan as string,
            json.isExcluded as boolean,
            json.coPaymentPrice as number
        );
    }
}