import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

interface ConfigurationNavProps {
  medicalCenterId: string
}

export default function ConfigurationNav({ medicalCenterId }: ConfigurationNavProps) {
  return (
    <nav className="bg-blue-50">
      <div className="container mx-auto px-[6rem] py-[1rem]">
        <div className="flex items-center">
          <Link href={`/plataforma/establecimiento/${medicalCenterId}`} className="group">
            <Button
              variant="outline"
              className="flex items-center gap-[0.375rem] transition-all duration-300 bg-white border-blue-300/50 hover:bg-blue-100/70 hover:border-blue-400 px-[0.75rem] py-[0.375rem] rounded-lg shadow-sm hover:shadow-md"
            >
              <ArrowLeft className="w-[1rem] h-[1rem] text-blue-600 group-hover:text-blue-800 transition-colors" />
              <span className="text-blue-700 group-hover:text-blue-900 font-medium text-[0.875rem]">Atrás</span>
            </Button>
          </Link>
        </div>
      </div>
    </nav>
  )
}