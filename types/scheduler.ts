export interface Appointment {
    id: string
    date: string
    time: string
    patient: string // This is the patient ID, not the name
    status: "Agendado" | "Ausente" | "Atendido" | "En Atención" | "Recepcionado" | "Reservado" | "Cancelado"
    source: "Turnera" | "Presencial" | "Telefónico" | "Manual"
    type: string
    coverage: string
    contact: string
    doctorId: string
    duration?: number;
    medicalCenterId?: string;
    cancellationReason?: string;
    cancelledAt?: string;
    cancelledBy?: string;
}

export interface CalendarDay {
    date: number
    dayOfWeek: number
}