import {NextRequest} from 'next/server';

export async function GET(request: NextRequest) {
    try {
        // Extract query parameters
        const {searchParams} = new URL(request.url);
        const medicalCenterId = searchParams.get('medical-center-id');
        const employeeUserId = searchParams.get('employee-user-id');

        console.log(`Connecting to SSE for Medical Center: ${medicalCenterId}, Employee: ${employeeUserId}`);
        console.log(process.env.BACKEND_URL);
        // Use fetch to connect to the external SSE endpoint with parameters
        const response = await fetch(`${process.env.BACKEND_URL}/sse/data/${medicalCenterId}?employee-user-id=${employeeUserId}`, {
            headers: {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache',
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to connect to stream: ${response.status}`);
        }

        if (!response.body) {
            throw new Error('No response body');
        }

        // Create a readable stream that will proxy the SSE connection
        const stream = new ReadableStream({
            start(controller) {
                const reader = response.body!.getReader();
                const decoder = new TextDecoder();

                const pump = async () => {
                    try {
                        while (true) {
                            const {done, value} = await reader.read();

                            if (done) {
                                controller.close();
                                break;
                            }

                            // Decode the chunk and forward it to the client
                            const chunk = decoder.decode(value, {stream: true});
                            controller.enqueue(new TextEncoder().encode(chunk));
                        }
                    } catch (error) {
                        console.error('Stream error:', error);
                        controller.error(error);
                    }
                };

                pump();

                // Clean up when the client disconnects
                request.signal.addEventListener('abort', () => {
                    reader.cancel();
                    controller.close();
                });
            }
        });

        return new Response(stream, {
            headers: {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
            },
        });
    } catch (error) {
        console.error('API route error:', error);
        return new Response(JSON.stringify({error: 'Failed to connect to stream'}), {
            status: 500,
            headers: {'Content-Type': 'application/json'},
        });
    }
}

export async function OPTIONS() {
    return new Response(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
        },
    });
}
