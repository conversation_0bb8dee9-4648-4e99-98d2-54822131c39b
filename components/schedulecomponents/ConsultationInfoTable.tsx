"use client"

import React from "react"
import {<PERSON>ert<PERSON>ircle, AlertTriangle, Info} from "lucide-react"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"

// Interface for consultation type information
export class ConsultationTypeInfo {
    constructor(
        public name: string,
        public requiresMedicalOrder: boolean,
        public instructions: string,
        public isExcluded: boolean,
        public copayAmount: number | null,
        public basePrice: number,
        public acceptsPrivatePay: boolean
    ) {
    }
}

interface ConsultationInfoTableProps {
    isOpen: boolean
    onOpenChange: (open: boolean) => void
    consultationTypesInfo: ConsultationTypeInfo[]
    selectedCoverage: string
    // Optional props to allow customization
    title?: string
    className?: string
}

export function ConsultationInfoTable({
                                          isOpen,
                                          onOpenChange,
                                          consultationTypesInfo,
                                          selectedCoverage,
                                          title = "Información importante",
                                          className = "",
                                      }: ConsultationInfoTableProps) {
    return (
        <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
            <AlertDialogContent
                className={`border border-gray-300 shadow-lg ${consultationTypesInfo.length > 1 ? "max-w-2xl" : "max-w-md"} ${className}`}>
                <AlertDialogHeader className="pb-2 border-b border-gray-200">
                    <AlertDialogTitle
                        className="text-center text-lg font-semibold text-gray-800">{title}</AlertDialogTitle>
                </AlertDialogHeader>

                {/* Content outside of AlertDialogDescription to avoid p > div nesting issue */}
                <div className="mt-4 space-y-4 overflow-auto max-h-[70vh]">
                    {/* Table layout for consultation type information */}
                    <div className="w-full overflow-x-auto">
                        <table className="w-full border-collapse">
                            <thead>
                            <tr>
                                <th className="p-2 bg-gray-50 border border-gray-200 text-left text-gray-600 font-medium text-sm">Categoría</th>
                                {consultationTypesInfo.map((typeInfo) => (
                                    <th key={typeInfo.name}
                                        className="p-2 bg-gray-50 border border-gray-200 text-center text-gray-600 font-medium text-sm">
                                        {typeInfo.name}
                                    </th>
                                ))}
                            </tr>
                            </thead>
                            <tbody>
                            {/* Restrictions row */}
                            <tr>
                                <td className="p-2 border border-gray-200 bg-gray-50 font-medium text-sm">Restricciones</td>
                                {consultationTypesInfo.map((typeInfo) => (
                                    <td key={`${typeInfo.name}-restrictions`}
                                        className="p-2 border border-gray-200 text-center">
                                        {(() => {
                                            // Check if coverage is Sin Cobertura
                                            const isSinCobertura = selectedCoverage === "Sin Cobertura";

                                            if (typeInfo.isExcluded) {
                                                // If coverage is not accepted
                                                return (
                                                    <div
                                                        className="flex flex-col items-center justify-center text-red-600">
                                                        <div className="flex items-center gap-0.5">
                                                            <AlertCircle className="h-4 w-4 text-red-500 mr-0.5"/>
                                                            <span className="text-sm font-medium">No cubierto</span>
                                                        </div>
                                                        <span className="text-sm">
                                {!typeInfo.acceptsPrivatePay
                                    ? "No acepta pago particular"
                                    : `$${typeInfo.basePrice.toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}`}
                              </span>
                                                    </div>
                                                );
                                            } else if (isSinCobertura) {
                                                // If coverage is Sin Cobertura, show price
                                                return (
                                                    <div
                                                        className="flex items-center justify-center gap-1 text-blue-600">
                              <span className="text-sm font-medium">
                                ${typeInfo.basePrice.toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}
                              </span>
                                                    </div>
                                                );
                                            } else {
                                                // Default case - no restrictions
                                                return <span className="text-sm text-gray-500">-</span>;
                                            }
                                        })()}
                                    </td>
                                ))}
                            </tr>

                            {/* Copay row */}
                            <tr>
                                <td className="p-2 border border-gray-200 bg-gray-50 font-medium text-sm">Copago</td>
                                {consultationTypesInfo.map((typeInfo) => (
                                    <td key={`${typeInfo.name}-copay`}
                                        className="p-2 border border-gray-200 text-center">
                                        {typeInfo.copayAmount !== null ? (
                                            <div className="flex items-center justify-center gap-1 text-blue-600">
                                                <span
                                                    className="text-sm font-medium">${typeInfo.copayAmount?.toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}</span>
                                            </div>
                                        ) : (
                                            <span className="text-sm text-gray-500">-</span>
                                        )}
                                    </td>
                                ))}
                            </tr>

                            {/* Medical order row */}
                            <tr>
                                <td className="p-2 border border-gray-200 bg-gray-50 font-medium text-sm">Orden médica
                                </td>
                                {consultationTypesInfo.map((typeInfo) => (
                                    <td key={`${typeInfo.name}-order`}
                                        className="p-2 border border-gray-200 text-center">
                                        {typeInfo.requiresMedicalOrder ? (
                                            <div className="flex items-center justify-center gap-1 text-amber-600">
                                                <AlertTriangle className="h-4 w-4 text-amber-500"/>
                                                <span className="text-sm">Requerida</span>
                                            </div>
                                        ) : (
                                            <span className="text-sm text-gray-500">-</span>
                                        )}
                                    </td>
                                ))}
                            </tr>

                            {/* Instructions row - only show if at least one type has instructions */}
                            {consultationTypesInfo.some(typeInfo => typeInfo.instructions) && (
                                <tr>
                                    <td className="p-2 border border-gray-200 bg-gray-50 font-medium text-sm">Indicaciones</td>
                                    {consultationTypesInfo.map((typeInfo) => (
                                        <td key={`${typeInfo.name}-instructions`}
                                            className="p-2 border border-gray-200">
                                            {typeInfo.instructions ? (
                                                <div className="text-sm text-gray-700 whitespace-pre-line">
                                                    {typeInfo.instructions}
                                                </div>
                                            ) : (
                                                <span className="text-sm text-gray-500 text-center block">-</span>
                                            )}
                                        </td>
                                    ))}
                                </tr>
                            )}
                            </tbody>
                        </table>
                    </div>

                    {/* Coverage information */}
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p className="text-blue-700 flex items-center gap-2">
                            <Info className="h-5 w-5 text-blue-500 flex-shrink-0"/>
                            <span>
                Cobertura seleccionada: <span className="font-semibold">{selectedCoverage || "No seleccionada"}</span>
                                {consultationTypesInfo.length > 1 &&
                                    consultationTypesInfo.some(type => type.isExcluded) &&
                                    !consultationTypesInfo.every(type => type.isExcluded) &&
                                    <span className="ml-2 text-amber-600">(Aceptada parcialmente)</span>}
              </span>
                        </p>
                    </div>

                    {/* Action button */}
                    <div className="flex justify-center mt-6">
                        <AlertDialogAction
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 rounded-md transition-colors">
                            Entendido
                        </AlertDialogAction>
                    </div>
                </div>
            </AlertDialogContent>
        </AlertDialog>
    )
}
