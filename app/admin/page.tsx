"use client"

import {Suspense, useCallback, useContext, useEffect, useState} from "react"
import {
    <PERSON>,
    <PERSON><PERSON>hart,
    CartesianGrid,
    Cell,
    Legend,
    Pie,
    <PERSON><PERSON><PERSON>,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from "recharts"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card"
import {<PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger} from "@/components/ui/tabs"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {
    AlertCircle,
    AlertTriangle,
    Building2,
    Calendar,
    Check,
    CheckCircle2,
    ChevronDown,
    ChevronUp,
    Clock,
    Download,
    Home,
    Inbox,
    Key,
    Link2,
    Mail,
    MessageSquare,
    PlusCircle,
    RefreshCw,
    Search,
    User as UserIcon,
    UserPlus,
    Users,
    X
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import {MedicalCenter} from "@/types/medical-center"
import {Doctor} from "@/types/doctor"
import {User, UserRole} from "@/types/users"
import {useSearchParams} from "next/navigation"
import {AdminBulkCreateForms} from "@/components/admin/configuration/AdminBulkCreateForms"
import {useAppointments} from "@/contexts/AppointmentContext"
import {PatientContext} from "@/contexts/PatientContext"
import {generateMedicalCenterId, generateUserId} from "@/utils/idGenerator"
import {toast, Toaster} from "sonner"
import {sendUserInvitationEmail} from "@/services/userEmail"

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

// Wrapper component that doesn't use useSearchParams directly
export default function AdminDashboardWrapper() {
    return (
        <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Cargando...</div>}>
            <AdminDashboard/>
        </Suspense>
    )
}

// Main component that uses useSearchParams
function AdminDashboard() {
    // State for system statistics
    const [medicalCenters, setMedicalCenters] = useState<MedicalCenter[]>([])
    const [allDoctors, setAllDoctors] = useState<Doctor[]>([])
    const [isLoading, setIsLoading] = useState(true)
    const [newMedicalCenter, setNewMedicalCenter] = useState({
        name: "",
        address: "",
        phone: "",
        email: "",
    })
    const [newSuperUser, setNewSuperUser] = useState({
        name: "",
        email: "",
        password: "",
        phone: "",
        dni: "",
        medicalCenterId: "",
        medicalCenterIds: [] as string[],
        role: UserRole.RECEPTIONIST,
        medicalCenterRoles: [] as { medicalCenterId: string, role: UserRole }[]
    })
    const [formError, setFormError] = useState<string | null>(null)
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [isSuperUserDialogOpen, setIsSuperUserDialogOpen] = useState(false)
    const [isAddDoctorDialogOpen, setIsAddDoctorDialogOpen] = useState(false)
    const [isResetDialogOpen, setIsResetDialogOpen] = useState(false)
    const [isResetting, setIsResetting] = useState(false)
    const [resetComplete, setResetComplete] = useState(false)
    const [resetError, setResetError] = useState<string | null>(null)
    const [expandedCenterId, setExpandedCenterId] = useState<string | null>(null)
    const [copiedCenterId, setCopiedCenterId] = useState<string | null>(null)
    const [selectedCenterId, setSelectedCenterId] = useState<string>("") // For adding doctor to center
    const [selectedDoctorId, setSelectedDoctorId] = useState("")
    const [expandedDoctorId, setExpandedDoctorId] = useState<string | null>(null)
    const [expandedUserId, setExpandedUserId] = useState<string | null>(null)
    const [selectedUser, setSelectedUser] = useState<User | null>(null)
    const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false)
    const [doctorPasswordReset, setDoctorPasswordReset] = useState<{ doctorId: string, password: string } | null>(null)

    // State for user filtering and search
    const [userRoleFilter, setUserRoleFilter] = useState<string>("all")
    const [userSearchQuery, setUserSearchQuery] = useState<string>("")

    // State for medical centers search
    const [medicalCenterSearchQuery, setMedicalCenterSearchQuery] = useState<string>("")

    // State for doctors search
    const [doctorSearchQuery, setDoctorSearchQuery] = useState<string>("")

    // State for existing user search
    const [existingUserForSuperUser, setExistingUserForSuperUser] = useState<User | null>(null)
    const {appointments} = useAppointments()
    const patientContext = useContext(PatientContext)

    // Load data from storage
    useEffect(() => {
        // Initialize storage
        storage.initializeStorage()

        // Get medical centers
        const centers = storage.getMedicalCenters()
        setMedicalCenters(centers)

        // Get all doctors in the system
        const doctors = storage.getAllDoctorsInSystem()
        setAllDoctors(doctors)

        setIsLoading(false)
    }, [])

    // Create a client component that uses useSearchParams
    const SearchParamsHandler = () => {
        const searchParams = useSearchParams()

        useEffect(() => {
            // Check if we should auto-open the reset dialog
            const openResetDialog = searchParams.get('openResetDialog')
            if (openResetDialog === 'true') {
                setIsResetDialogOpen(true)
            }
        }, [searchParams])

        return null
    }

    // Add doctor to medical center
    const handleAddDoctorToCenter = useCallback(() => {
        if (!selectedDoctorId || !selectedCenterId) return

        const success = storage.addDoctorToMedicalCenter(selectedDoctorId, selectedCenterId)

        if (success) {
            // Refresh medical centers
            const updatedCenters = storage.getMedicalCenters()
            setMedicalCenters(updatedCenters)
            toast.success("Médico añadido exitosamente al establecimiento")
            // Reset selection
            setSelectedDoctorId("")
        } else {
            toast.error("Error al añadir médico al establecimiento")
        }

        setIsAddDoctorDialogOpen(false)
    }, [selectedDoctorId, selectedCenterId])

    // Remove doctor from medical center
    const handleRemoveDoctorFromCenter = useCallback((doctorId: string, centerId: string) => {
        try {
            // Get the current medical centers
            const centers = storage.getMedicalCenters()

            // Find the center we want to update
            const centerIndex = centers.findIndex(c => c.id === centerId)
            if (centerIndex === -1) {
                toast.error("No se encontró el establecimiento médico")
                return
            }

            // Remove the doctor from the center's doctors list
            // Note: doctors array contains doctor IDs (strings), not doctor objects
            const updatedCenter = {
                ...centers[centerIndex],
                doctors: centers[centerIndex].doctors.filter(d => d !== doctorId)
            }

            // Update the centers array
            const updatedCenters = [...centers]
            updatedCenters[centerIndex] = updatedCenter

            // Save the updated centers
            storage.saveMedicalCenters(updatedCenters)

            // Refresh the UI
            setMedicalCenters(updatedCenters)
            toast.success("Médico removido exitosamente del establecimiento")
        } catch (error) {
            console.error("Error removing doctor:", error)
            toast.error("Error al remover médico del establecimiento")
        }
    }, [])

    // Calculate appointment and patient statistics for each medical center
    const getMedicalCenterStats = (centerId: string) => {
        // Count appointments for this medical center
        const centerAppointments = Object.values(appointments)
            .flat()
            .filter(apt => !apt.medicalCenterId || apt.medicalCenterId === centerId)

        // Count unique patients for this medical center
        const patientNames = new Set(centerAppointments.map(apt => apt.patient))

        return {
            appointmentCount: centerAppointments.length,
            patientCount: patientNames.size
        }
    }

    // Get medical centers where a doctor works
    const getDoctorMedicalCenters = (doctorId: string) => {
        return medicalCenters.filter(center => center.doctors.includes(doctorId))
    }

    // Prepare data for charts
    const doctorsByMedicalCenter = medicalCenters.map(center => ({
        name: center.name,
        doctors: center.doctors.length
    }))

    const doctorsBySpecialty = allDoctors.reduce((acc, doctor) => {
        doctor.specialties.forEach((specialty: string) => {
            const existingIndex = acc.findIndex(item => item.name === specialty)
            if (existingIndex >= 0) {
                acc[existingIndex].value += 1
            } else {
                acc.push({name: specialty, value: 1})
            }
        })
        return acc
    }, [] as { name: string; value: number }[])

    // Helper function to get role name
    const getRoleName = (role: UserRole): string => {
        switch (role) {
            case UserRole.SUPERUSER:
                return "Superusuario";
            case UserRole.ADMIN:
                return "Administrador";
            case UserRole.RECEPTIONIST:
                return "Recepcionista";
            case UserRole.DOCTOR:
                return "Médico";
            default:
                return "Desconocido";
        }
    };

    // Helper function to get role badge color
    const getRoleBadgeColor = (role: UserRole): string => {
        switch (role) {
            case UserRole.SUPERUSER:
                return "bg-purple-100 text-purple-800";
            case UserRole.ADMIN:
                return "bg-blue-100 text-blue-800";
            case UserRole.RECEPTIONIST:
                return "bg-green-100 text-green-800";
            case UserRole.DOCTOR:
                return "bg-amber-100 text-amber-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    // Calculate statistics
    const totalDoctors = allDoctors.length
    const totalAppointmentSlots = allDoctors.reduce((acc, doctor) => {
        // Estimate weekly appointment slots based on working days and slot duration
        let weeklySlots = 0
        Object.entries(doctor.workingDays).forEach(([, dayConfig]) => {
            if (dayConfig.enabled && dayConfig.hours.length > 0) {
                dayConfig.hours.forEach((timeRange: { start: string; end: string }) => {
                    const start = new Date(`2000-01-01T${timeRange.start}:00`)
                    const end = new Date(`2000-01-01T${timeRange.end}:00`)
                    const durationMs = end.getTime() - start.getTime()
                    const durationMinutes = durationMs / 1000 / 60
                    weeklySlots += Math.floor(durationMinutes / doctor.appointmentSlotDuration)
                })
            }
        })
        return acc + (weeklySlots * 52) // Annual estimate
    }, 0)

    // Handle creating a new medical center
    const handleCreateMedicalCenter = () => {
        // Generate a unique ID using short-unique-id with 'mc-' prefix
        const newId = generateMedicalCenterId()

        // Create the new medical center object
        const newCenter: MedicalCenter = {
            id: newId,
            name: newMedicalCenter.name,
            address: newMedicalCenter.address,
            phone: newMedicalCenter.phone,
            email: newMedicalCenter.email,
            doctors: [],
            workingDays: {
                "0": {enabled: false, hours: []},
                "1": {enabled: true, hours: [{start: "09:00", end: "18:00"}]},
                "2": {enabled: true, hours: [{start: "09:00", end: "18:00"}]},
                "3": {enabled: true, hours: [{start: "09:00", end: "18:00"}]},
                "4": {enabled: true, hours: [{start: "09:00", end: "18:00"}]},
                "5": {enabled: true, hours: [{start: "09:00", end: "18:00"}]},
                "6": {enabled: false, hours: []},
            },
            openingHours: {
                monday: {start: '09:00', end: '18:00', enabled: true},
                tuesday: {start: '09:00', end: '18:00', enabled: true},
                wednesday: {start: '09:00', end: '18:00', enabled: true},
                thursday: {start: '09:00', end: '18:00', enabled: true},
                friday: {start: '09:00', end: '18:00', enabled: true},
                saturday: {start: '00:00', end: '00:00', enabled: false},
                sunday: {start: '00:00', end: '00:00', enabled: false}
            },
            acceptedCoverages: ["8"], // Add Sin Cobertura by default
        }

        // Add to the list of medical centers
        const updatedCenters = [...medicalCenters, newCenter]
        setMedicalCenters(updatedCenters)

        // Save to storage
        storage.saveMedicalCenters(updatedCenters)

        // Show success message
        toast.success(`Establecimiento "${newCenter.name}" creado exitosamente`)

        // Reset form and close dialog
        setNewMedicalCenter({
            name: "",
            address: "",
            phone: "",
            email: "",
        })
        setIsDialogOpen(false)
    }

    // Handle editing a user's medical centers and roles
    const handleEditSuperUser = () => {
        // Clear previous errors
        setFormError(null)

        if (!selectedUser) return

        // Validate at least one medical center is selected
        if (selectedUser.medicalCenterIds?.length === 0 && !selectedUser.medicalCenterId) {
            setFormError("Por favor, seleccione al menos un establecimiento")
            return
        }

        // Determine medical centers
        const initialIds = [...(selectedUser.medicalCenterIds || [])]
        const medicalCenterIds = selectedUser.medicalCenterId && !initialIds.includes(selectedUser.medicalCenterId)
            ? [...initialIds, selectedUser.medicalCenterId]
            : initialIds

        // Ensure medicalCenterRoles is properly initialized
        if (!selectedUser.medicalCenterRoles || selectedUser.medicalCenterRoles.length === 0) {
            // Initialize with default roles based on the user's main role
            selectedUser.medicalCenterRoles = medicalCenterIds.map(centerId => ({
                medicalCenterId: centerId,
                role: selectedUser.roles,
                permissions: selectedUser.permissions
            }))
        }

        // Make sure all selected medical centers have a role
        medicalCenterIds.forEach(centerId => {
            if (!selectedUser.medicalCenterRoles!.some(mcr => mcr.medicalCenterId === centerId)) {
                selectedUser.medicalCenterRoles!.push({
                    medicalCenterId: centerId,
                    role: selectedUser.roles,
                    permissions: selectedUser.permissions
                })
            }
        })

        // Remove roles for unselected medical centers
        selectedUser.medicalCenterRoles = selectedUser.medicalCenterRoles.filter(
            mcr => medicalCenterIds.includes(mcr.medicalCenterId)
        )

        // Update the user object
        const updatedUser: User = {
            ...selectedUser,
            medicalCenterId: medicalCenterIds[0], // Set first center as primary for backward compatibility
            medicalCenterIds: medicalCenterIds,
            medicalCenterRoles: selectedUser.medicalCenterRoles
        }

        // Save to storage
        storage.saveUser(updatedUser)

        // Show success message
        toast.success(`Usuario "${updatedUser.name}" actualizado con acceso a ${medicalCenterIds.length} centro(s) médico(s)`)

        // Reset form and close dialog
        setSelectedUser(null)
        setIsEditUserDialogOpen(false)
    }

    // Handle creating a new super user for a medical center
    const handleCreateSuperUser = async () => {
        // Clear previous errors
        setFormError(null)

        // Check if we're working with an existing user
        const existingUser = existingUserForSuperUser || storage.getUserByEmail(newSuperUser.email)

        if (existingUser) {
            // For existing users, we only need to validate the medical center
            if (newSuperUser.medicalCenterIds.length === 0) {
                setFormError("Por favor, seleccione al menos un establecimiento")
                return
            }

            // Check if user already has any role for this center
            const existingRoleForCenter = existingUser.medicalCenterRoles?.find(
                mcr => mcr.medicalCenterId === newSuperUser.medicalCenterId
            );

            if (existingRoleForCenter) {
                if (existingRoleForCenter.role === UserRole.SUPERUSER) {
                    setFormError(`El usuario ya es Superusuario en este establecimiento`)
                    return
                } else if (existingRoleForCenter.role !== UserRole.DOCTOR) {
                    // If user already has a non-doctor role in this center, suggest editing instead
                    setFormError(`El usuario ya tiene rol de ${getRoleName(existingRoleForCenter.role)} en este establecimiento. Utilice la opción "Editar Establecimientos" para modificar su rol.`)
                    return
                }
            }

            // Check if user is only a doctor or has admin roles
            const hasNonDoctorRole = existingUser.roles !== UserRole.DOCTOR ||
                (existingUser.medicalCenterRoles &&
                    existingUser.medicalCenterRoles.some(mcr => mcr.role !== UserRole.DOCTOR));

            // If user already has administrative roles (doctor or not), don't allow adding more
            if (hasNonDoctorRole) {
                if (existingUser.doctorId) {
                    setFormError(`Este usuario es médico y ya tiene roles administrativos en otros centros. Utilice la opción "Editar Establecimientos" para gestionar sus roles.`)
                } else {
                    setFormError(`Este usuario ya tiene roles administrativos en otros centros. Utilice la opción "Editar Establecimientos" para gestionar sus roles.`)
                }
                return
            }

            // At this point, we only allow:
            // - Adding a doctor user (without admin roles) as a superuser to a new medical center
            // - Upgrading a doctor's role in a medical center where they're already a doctor
            // - Adding a completely new user

            // Create a copy of the user to update
            const updatedUser = {...existingUser}

            // Initialize medicalCenterRoles if it doesn't exist
            if (!updatedUser.medicalCenterRoles) {
                updatedUser.medicalCenterRoles = [{
                    medicalCenterId: updatedUser.medicalCenterId,
                    role: updatedUser.roles,
                    permissions: updatedUser.permissions
                }]
            }

            // Initialize medicalCenterIds if it doesn't exist
            if (!updatedUser.medicalCenterIds) {
                updatedUser.medicalCenterIds = [updatedUser.medicalCenterId]
            }

            // Add or update roles for each selected medical center
            for (const medicalCenterId of newSuperUser.medicalCenterIds) {
                // Find the role for this center
                const centerRole = newSuperUser.medicalCenterRoles?.find(
                    mcr => mcr.medicalCenterId === medicalCenterId
                )?.role || UserRole.SUPERUSER; // Default to SUPERUSER if not specified

                // Check if user already has access to this medical center
                const centerRoleIndex = updatedUser.medicalCenterRoles.findIndex(
                    mcr => mcr.medicalCenterId === medicalCenterId
                )

                if (centerRoleIndex >= 0) {
                    // Update existing role to the selected role
                    updatedUser.medicalCenterRoles[centerRoleIndex].role = centerRole
                    // Remove specific permissions for admin roles
                    delete updatedUser.medicalCenterRoles[centerRoleIndex].permissions
                } else {
                    // Add new role for this center
                    updatedUser.medicalCenterRoles.push({
                        medicalCenterId: medicalCenterId,
                        role: centerRole
                    })

                    // Add medical center to medicalCenterIds if not already there
                    if (!updatedUser.medicalCenterIds.includes(medicalCenterId)) {
                        updatedUser.medicalCenterIds.push(medicalCenterId)
                    }
                }
            }

            // Set the primary medical center if not already set
            if (!updatedUser.medicalCenterId && newSuperUser.medicalCenterIds.length > 0) {
                updatedUser.medicalCenterId = newSuperUser.medicalCenterIds[0]
            }

            // Save updated user
            storage.saveUser(updatedUser)

            // Show success message
            toast.success(`Usuario ${updatedUser.name} actualizado con los roles seleccionados`)

            // Reset form and close dialog
            setNewSuperUser({
                name: "",
                email: "",
                password: "",
                phone: "",
                dni: "",
                medicalCenterId: "",
                medicalCenterIds: [],
                role: UserRole.RECEPTIONIST,
                medicalCenterRoles: []
            })
            setExistingUserForSuperUser(null)
            setIsSuperUserDialogOpen(false)
        } else {
            // For new users, validate all fields except password (will be generated)
            if (!newSuperUser.name || !newSuperUser.email) {
                setFormError("Por favor, complete todos los campos")
                return
            }

            // Validate medical center
            if (newSuperUser.medicalCenterIds.length === 0) {
                setFormError("Por favor, seleccione al menos un establecimiento")
                return
            }

            // Generate a unique ID using the standard user ID format
            const userId = generateUserId()

            // Generate a temporary password
            const tempPassword = `temp${Math.floor(100000 + Math.random() * 900000)}`;

            // Create the new user object
            const newUser: User = {
                id: userId,
                name: newSuperUser.name,
                email: newSuperUser.email,
                password: tempPassword,
                phone: newSuperUser.phone,
                dni: newSuperUser.dni,
                roles: newSuperUser.medicalCenterRoles.find(mcr => mcr.medicalCenterId === newSuperUser.medicalCenterIds[0])?.role || UserRole.SUPERUSER,
                medicalCenterId: newSuperUser.medicalCenterIds[0],
                medicalCenterIds: [...newSuperUser.medicalCenterIds],
                medicalCenterRoles: [...newSuperUser.medicalCenterRoles]
            }

            // Save to storage
            storage.saveUser(newUser)

            // Create patient profile if phone and DNI are provided
            if (newSuperUser.phone && newSuperUser.dni && patientContext) {
                const patientId = patientContext.createPatientForProfessional(newUser);
                if (patientId) {
                    // Update the user with the default patient ID
                    const updatedUser = {...newUser, defaultPatientId: patientId};
                    storage.saveUser(updatedUser);
                    console.log(`Created patient profile ${patientId} for user ${newUser.name}`);
                }
            }

            // Send email invitation to the user
            try {
                const emailSent = await sendUserInvitationEmail(newUser.email, tempPassword, newUser.name);
                if (emailSent) {
                    toast.success(`Se ha enviado un correo electrónico a ${newUser.email} con las credenciales temporales`);
                } else {
                    toast.error(`No se pudo enviar el correo electrónico a ${newUser.email}`);
                }
            } catch (error) {
                console.error("Error sending user invitation email:", error);
                toast.error("Error al enviar el correo electrónico de invitación");
            }

            // Show success message
            const hasPatientProfile = newSuperUser.phone && newSuperUser.dni;
            toast.success(
                <div>
                    <p>Usuario &quot;{newUser.name}&quot; creado con acceso
                        a {newSuperUser.medicalCenterIds.length} centro(s) médico(s)</p>
                    {hasPatientProfile && (
                        <p className="text-xs mt-1 text-green-600">Perfil de paciente creado automáticamente</p>
                    )}
                </div>
            )

            // Reset form and close dialog
            setNewSuperUser({
                name: "",
                email: "",
                password: "",
                phone: "",
                dni: "",
                medicalCenterId: "",
                medicalCenterIds: [],
                role: UserRole.RECEPTIONIST,
                medicalCenterRoles: []
            })
            setIsSuperUserDialogOpen(false)
        }
    }

    // Handle refreshing the data
    const handleRefresh = () => {
        setIsLoading(true)

        // Re-initialize storage (to ensure we have latest data)
        storage.initializeStorage()

        // Re-fetch all data
        const centers = storage.getMedicalCenters()
        setMedicalCenters(centers)

        const doctors = storage.getAllDoctorsInSystem()
        setAllDoctors(doctors)

        setIsLoading(false)
    }

    // Export data function
    const handleExportData = () => {
        const exportData = {
            medicalCenters,
            doctors: allDoctors,
            exportDate: new Date().toISOString()
        }

        // Create a blob and download it
        const jsonString = JSON.stringify(exportData, null, 2)
        const blob = new Blob([jsonString], {type: 'application/json'})
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = `turnera-export-${new Date().toISOString().slice(0, 10)}.json`
        a.click()

        URL.revokeObjectURL(url)
    }

    // Function to clear all cookies
    const clearAllCookies = () => {
        // Get all cookies and clear them one by one
        const cookies = document.cookie.split(";");

        console.log(`Clearing ${cookies.length} cookies:`, cookies.map(c => c.trim()).join(', '));

        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i];
            const eqPos = cookie.indexOf("=");
            const name = eqPos > -1 ? cookie.substring(0, eqPos).trim() : cookie.trim();

            if (name) {
                console.log(`Clearing cookie: ${name}`);

                // Set expiration date to the past to delete the cookie
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;

                // Also try with different paths to ensure all cookies are cleared
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/plataforma;`;
                document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/admin;`;
            }
        }

        console.log("Cleared all cookies. Remaining cookies:", document.cookie);
    };

    // Handle resetting the application data
    const handleReset = () => {
        setIsResetting(true)
        setResetError(null)

        try {
            // Clear all local storage for the application
            localStorage.clear()
            console.log("Cleared all localStorage data")

            // Clear all cookies
            clearAllCookies()

            // Explicitly remove specific keys that might be causing issues
            localStorage.removeItem(STORAGE_KEYS.ACTIVE_MEDICAL_CENTER)
            localStorage.removeItem("blockedSlots")
            console.log("Explicitly removed active medical center and blocked slots data")

            // Dispatch a storage event to notify components about the blockedSlots change
            if (typeof window !== 'undefined') {
                const event = new StorageEvent('storage', {
                    key: 'blockedSlots',
                    newValue: JSON.stringify({}),
                    oldValue: localStorage.getItem('blockedSlots'),
                    storageArea: localStorage,
                    url: window.location.href
                });
                window.dispatchEvent(event);
            }

            // Reinitialize storage
            storage.initializeStorage()

            setResetComplete(true)

            // Close the dialog after a short delay to show the success message
            setTimeout(() => {
                setIsResetDialogOpen(false)
                setResetComplete(false) // Reset the state for next time
            }, 1500) // 1.5 second delay to show the success message
        } catch (err) {
            setResetError(`Error resetting application data: ${err instanceof Error ? err.message : String(err)}`)
        } finally {
            setIsResetting(false)
        }
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Wrap the SearchParamsHandler in a Suspense boundary */}
            <Suspense fallback={null}>
                <SearchParamsHandler/>
            </Suspense>
            {/* Header */}
            <header className="bg-white shadow-sm">
                <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
                    <div className="flex items-center">
                        <Link href="/" passHref>
                            <div className="cursor-pointer">
                                <Image
                                    src="/images/turnera-logo.svg"
                                    alt="Turnera Logo"
                                    width={140}
                                    height={40}
                                    className="h-8 w-auto mr-4"
                                    priority
                                />
                            </div>
                        </Link>
                    </div>
                    <div className="flex items-center space-x-4">
                        <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center"
                            onClick={handleRefresh}
                            disabled={isLoading}
                        >
                            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}/>
                            {isLoading ? 'Actualizando...' : 'Actualizar'}
                        </Button>
                        <Button
                            variant="destructive"
                            size="sm"
                            className="flex items-center"
                            onClick={() => setIsResetDialogOpen(true)}
                        >
                            <RefreshCw className="h-4 w-4 mr-2"/>
                            Restablecer Datos
                        </Button>
                        <Link href="/dev/emails" passHref>
                            <Button variant="ghost" size="sm" className="flex items-center">
                                <Inbox className="h-5 w-5 mr-2"/>
                                Email Preview
                            </Button>
                        </Link>
                        <Link href="/dev/phone" passHref>
                            <Button variant="ghost" size="sm" className="flex items-center">
                                <MessageSquare className="h-5 w-5 mr-2"/>
                                Phone Messages
                            </Button>
                        </Link>
                        <Link href="/dev/professional-registrations" passHref>
                            <Button variant="ghost" size="sm" className="flex items-center">
                                <UserPlus className="h-5 w-5 mr-2"/>
                                Formulario de Profesionales
                            </Button>
                        </Link>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                {/* Add Sonner toast container */}
                <Toaster/>
                {isLoading ? (
                    <div className="h-80 flex items-center justify-center">
                        <div className="text-center">
                            <RefreshCw className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4"/>
                            <h3 className="text-lg font-medium text-gray-900">Cargando datos...</h3>
                            <p className="mt-1 text-sm text-gray-500">Obteniendo información del sistema</p>
                        </div>
                    </div>
                ) : (
                    /* Dashboard */
                    <div className="px-4 py-6 sm:px-0">
                        {/* Stat Cards */}
                        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Establecimientos</CardTitle>
                                    <Building2 className="h-4 w-4 text-blue-500"/>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{medicalCenters.length}</div>
                                    <p className="text-xs text-muted-foreground">
                                        Establecimientos médicos activos
                                    </p>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Médicos</CardTitle>
                                    <Users className="h-4 w-4 text-green-500"/>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{totalDoctors}</div>
                                    <p className="text-xs text-muted-foreground">
                                        Profesionales registrados en el sistema
                                    </p>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Especialidades</CardTitle>
                                    <Calendar className="h-4 w-4 text-purple-500"/>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{doctorsBySpecialty.length}</div>
                                    <p className="text-xs text-muted-foreground">
                                        Especialidades médicas disponibles
                                    </p>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">Turnos Estimados</CardTitle>
                                    <Calendar className="h-4 w-4 text-orange-500"/>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{totalAppointmentSlots.toLocaleString()}</div>
                                    <p className="text-xs text-muted-foreground">
                                        Disponibilidad anual estimada
                                    </p>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Tabs for different views */}
                        <Tabs defaultValue="dashboard" className="pt-6 mb-2">
                            <TabsList className="mb-0">
                                <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                                <TabsTrigger value="medical-centers">Establecimientos</TabsTrigger>
                                <TabsTrigger value="doctors">Profesionales</TabsTrigger>
                                <TabsTrigger value="users">Usuarios</TabsTrigger>
                                <TabsTrigger value="bulk-create">Creación Masiva</TabsTrigger>
                            </TabsList>

                            <TabsContent value="dashboard">
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4">
                                    <Card className="col-span-1">
                                        <CardHeader>
                                            <CardTitle>Médicos por Establecimiento</CardTitle>
                                            <CardDescription>
                                                Distribución de profesionales por cada establecimiento
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="h-72">
                                                <ResponsiveContainer width="100%" height="100%">
                                                    <BarChart
                                                        data={doctorsByMedicalCenter}
                                                        margin={{top: 5, right: 30, left: 20, bottom: 30}}
                                                    >
                                                        <CartesianGrid strokeDasharray="3 3"/>
                                                        <XAxis dataKey="name" angle={-45} textAnchor="end" height={60}/>
                                                        <YAxis/>
                                                        <Tooltip/>
                                                        <Legend/>
                                                        <Bar dataKey="doctors" fill="#8884d8" name="Médicos"/>
                                                    </BarChart>
                                                </ResponsiveContainer>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    <Card className="col-span-1">
                                        <CardHeader>
                                            <CardTitle>Especialidades</CardTitle>
                                            <CardDescription>
                                                Distribución de especialidades médicas
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="h-72">
                                                <ResponsiveContainer width="100%" height="100%">
                                                    <PieChart>
                                                        <Pie
                                                            data={doctorsBySpecialty}
                                                            cx="50%"
                                                            cy="50%"
                                                            labelLine={true}
                                                            label={({
                                                                        name,
                                                                        percent
                                                                    }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                                            outerRadius={80}
                                                            fill="#8884d8"
                                                            dataKey="value"
                                                        >
                                                            {doctorsBySpecialty.map((_, index) => (
                                                                <Cell key={`cell-${index}`}
                                                                      fill={COLORS[index % COLORS.length]}/>
                                                            ))}
                                                        </Pie>
                                                        <Tooltip
                                                            formatter={(value) => [`${value} médicos`, 'Cantidad']}/>
                                                    </PieChart>
                                                </ResponsiveContainer>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </TabsContent>

                            <TabsContent value="medical-centers">
                                <div className="mt-4 space-y-6">
                                    <div className="flex justify-between items-center">
                                        <h3 className="text-lg font-medium">Establecimientos Registrados</h3>
                                        <div className="flex items-center space-x-2">
                                            <Input
                                                placeholder="Buscar por establecimiento o médico..."
                                                className="w-64"
                                                value={medicalCenterSearchQuery}
                                                onChange={(e) => setMedicalCenterSearchQuery(e.target.value)}
                                            />
                                            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                                                <DialogTrigger asChild>
                                                    <Button className="flex items-center">
                                                        <PlusCircle className="h-4 w-4 mr-2"/>
                                                        Nuevo Establecimiento
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent>
                                                    <DialogHeader>
                                                        <DialogTitle>Crear Nuevo Establecimiento</DialogTitle>
                                                        <DialogDescription>
                                                            Complete los datos para registrar un nuevo establecimiento
                                                            en el sistema.
                                                        </DialogDescription>
                                                    </DialogHeader>
                                                    <div className="grid gap-4 py-4">
                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                            <Label htmlFor="name" className="text-right">
                                                                Nombre
                                                            </Label>
                                                            <Input
                                                                id="name"
                                                                placeholder="Establecimiento"
                                                                className="col-span-3"
                                                                value={newMedicalCenter.name}
                                                                onChange={(e) => setNewMedicalCenter({
                                                                    ...newMedicalCenter,
                                                                    name: e.target.value
                                                                })}
                                                            />
                                                        </div>
                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                            <Label htmlFor="address" className="text-right">
                                                                Dirección
                                                            </Label>
                                                            <Input
                                                                id="address"
                                                                placeholder="Av. Principal 123"
                                                                className="col-span-3"
                                                                value={newMedicalCenter.address}
                                                                onChange={(e) => setNewMedicalCenter({
                                                                    ...newMedicalCenter,
                                                                    address: e.target.value
                                                                })}
                                                            />
                                                        </div>
                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                            <Label htmlFor="phone" className="text-right">
                                                                Teléfono
                                                            </Label>
                                                            <Input
                                                                id="phone"
                                                                placeholder="+54 11 1234-5678"
                                                                className="col-span-3"
                                                                value={newMedicalCenter.phone}
                                                                onChange={(e) => setNewMedicalCenter({
                                                                    ...newMedicalCenter,
                                                                    phone: e.target.value
                                                                })}
                                                            />
                                                        </div>
                                                        <div className="grid grid-cols-4 items-center gap-4">
                                                            <Label htmlFor="email" className="text-right">
                                                                Email
                                                            </Label>
                                                            <Input
                                                                id="email"
                                                                placeholder="<EMAIL>"
                                                                className="col-span-3"
                                                                value={newMedicalCenter.email}
                                                                onChange={(e) => setNewMedicalCenter({
                                                                    ...newMedicalCenter,
                                                                    email: e.target.value
                                                                })}
                                                            />
                                                        </div>
                                                    </div>
                                                    <DialogFooter>
                                                        <Button
                                                            type="submit"
                                                            onClick={handleCreateMedicalCenter}
                                                            disabled={!newMedicalCenter.name}
                                                        >
                                                            Crear Establecimiento
                                                        </Button>
                                                    </DialogFooter>
                                                </DialogContent>
                                            </Dialog>
                                        </div>
                                    </div>

                                    <div className="bg-white shadow overflow-hidden sm:rounded-md">
                                        <ul role="list" className="divide-y divide-gray-200">
                                            {medicalCenters
                                                .filter(center => {
                                                    if (!medicalCenterSearchQuery) return true;
                                                    const query = medicalCenterSearchQuery.toLowerCase();

                                                    // Search by center name, address, or email
                                                    if (center.name.toLowerCase().includes(query) ||
                                                        center.address?.toLowerCase().includes(query) ||
                                                        center.email?.toLowerCase().includes(query)) {
                                                        return true;
                                                    }

                                                    // Search by doctor names who work at this center
                                                    const centerDoctors = allDoctors.filter(doctor => center.doctors.includes(doctor.id));
                                                    return centerDoctors.some(doctor =>
                                                        doctor.name.toLowerCase().includes(query) ||
                                                        doctor.specialties.some(specialty => specialty.toLowerCase().includes(query))
                                                    );
                                                })
                                                .sort((a, b) => a.name.localeCompare(b.name))
                                                .map((center) => {
                                                    const {
                                                        appointmentCount,
                                                        patientCount
                                                    } = getMedicalCenterStats(center.id);
                                                    const isExpanded = expandedCenterId === center.id;
                                                    const centerDoctors = allDoctors.filter(doctor => center.doctors.includes(doctor.id));

                                                    return (
                                                        <li key={center.id}>
                                                            <div className="block hover:bg-gray-50">
                                                                <div className="px-4 py-4 sm:px-6">
                                                                    <div className="flex items-center justify-between">
                                                                        <div className="flex items-center">
                                                                            <Link
                                                                                href={`/plataforma/establecimiento/${center.id}`}>
                                                                                <p className="text-sm font-medium text-blue-600 truncate">{center.name}</p>
                                                                            </Link>
                                                                            <p className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                                                {center.doctors.length} médicos
                                                                            </p>
                                                                            <Button
                                                                                variant="ghost"
                                                                                size="sm"
                                                                                className="ml-2 h-7 text-xs flex items-center text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                                                                                onClick={(e) => {
                                                                                    e.preventDefault();
                                                                                    e.stopPropagation();
                                                                                    // Copy the patient-facing booking URL to clipboard
                                                                                    const url = `${window.location.origin}/plataforma/reservar/cm/${center.id}`;

                                                                                    // Try to use the Clipboard API with fallback for Safari on iOS
                                                                                    const copyToClipboard = async (text: string): Promise<boolean> => {
                                                                                        // For iOS Safari, use a simpler approach with a visible input
                                                                                        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);

                                                                                        if (isIOS) {
                                                                                            try {
                                                                                                // Create a visible input element for iOS
                                                                                                const el = document.createElement('input');
                                                                                                el.value = text;
                                                                                                el.style.position = 'fixed';
                                                                                                el.style.top = '50%';
                                                                                                el.style.left = '50%';
                                                                                                el.style.transform = 'translate(-50%, -50%)';
                                                                                                el.style.width = '80%';
                                                                                                el.style.padding = '10px';
                                                                                                el.style.zIndex = '9999';
                                                                                                el.style.backgroundColor = '#f0f0f0';
                                                                                                el.style.border = '1px solid #ccc';
                                                                                                el.style.borderRadius = '4px';
                                                                                                el.style.fontSize = '16px'; // Larger font size for better selection on mobile
                                                                                                el.readOnly = true; // Make it read-only
                                                                                                document.body.appendChild(el);

                                                                                                // Select the text
                                                                                                el.focus();
                                                                                                el.select();
                                                                                                el.setSelectionRange(0, text.length);

                                                                                                // Show a message to guide the user
                                                                                                toast.info('Mantenga presionado y seleccione "Copiar"');

                                                                                                // Create a close button
                                                                                                const closeBtn = document.createElement('button');
                                                                                                closeBtn.textContent = 'Cerrar';
                                                                                                closeBtn.style.position = 'fixed';
                                                                                                closeBtn.style.top = 'calc(50% + 50px)';
                                                                                                closeBtn.style.left = '50%';
                                                                                                closeBtn.style.transform = 'translateX(-50%)';
                                                                                                closeBtn.style.padding = '8px 16px';
                                                                                                closeBtn.style.backgroundColor = '#4a90e2';
                                                                                                closeBtn.style.color = 'white';
                                                                                                closeBtn.style.border = 'none';
                                                                                                closeBtn.style.borderRadius = '4px';
                                                                                                closeBtn.style.zIndex = '9999';
                                                                                                closeBtn.style.cursor = 'pointer';
                                                                                                document.body.appendChild(closeBtn);

                                                                                                // Add event listener to close button
                                                                                                closeBtn.addEventListener('click', () => {
                                                                                                    document.body.removeChild(el);
                                                                                                    document.body.removeChild(closeBtn);
                                                                                                });

                                                                                                // Auto-close after a delay
                                                                                                setTimeout(() => {
                                                                                                    if (document.body.contains(el)) {
                                                                                                        document.body.removeChild(el);
                                                                                                    }
                                                                                                    if (document.body.contains(closeBtn)) {
                                                                                                        document.body.removeChild(closeBtn);
                                                                                                    }
                                                                                                }, 10000);

                                                                                                return true;
                                                                                            } catch (err) {
                                                                                                console.error('iOS clipboard copy failed:', err);
                                                                                                return false;
                                                                                            }
                                                                                        }

                                                                                        // Try using the Clipboard API for non-iOS devices
                                                                                        if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
                                                                                            try {
                                                                                                await navigator.clipboard.writeText(text);
                                                                                                return true;
                                                                                            } catch {
                                                                                                // Fall through to the fallback method
                                                                                            }
                                                                                        }

                                                                                        // Fallback for browsers that don't support Clipboard API
                                                                                        try {
                                                                                            const textArea = document.createElement('textarea');
                                                                                            textArea.value = text;
                                                                                            textArea.style.position = 'fixed';
                                                                                            textArea.style.left = '0';
                                                                                            textArea.style.top = '0';
                                                                                            textArea.style.width = '2em';
                                                                                            textArea.style.height = '2em';
                                                                                            textArea.style.padding = '0';
                                                                                            textArea.style.border = 'none';
                                                                                            textArea.style.outline = 'none';
                                                                                            textArea.style.boxShadow = 'none';
                                                                                            textArea.style.background = 'transparent';
                                                                                            document.body.appendChild(textArea);
                                                                                            textArea.focus();
                                                                                            textArea.select();

                                                                                            // Execute copy command
                                                                                            // Note: execCommand is deprecated but still works as a fallback
                                                                                            // We're intentionally using this deprecated API as a fallback for older browsers
                                                                                            const success = document.execCommand('copy');
                                                                                            document.body.removeChild(textArea);
                                                                                            return success;
                                                                                        } catch (err) {
                                                                                            console.error('Fallback clipboard copy failed:', err);
                                                                                            return false;
                                                                                        }
                                                                                    };

                                                                                    copyToClipboard(url)
                                                                                        .then(success => {
                                                                                            // For iOS, the success message is shown in the copyToClipboard function
                                                                                            // and we just need to update the UI state
                                                                                            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);

                                                                                            if (success) {
                                                                                                setCopiedCenterId(center.id);
                                                                                                setTimeout(() => setCopiedCenterId(null), 2000);

                                                                                                // Only show success toast for non-iOS devices
                                                                                                // (iOS already shows a different message)
                                                                                                if (!isIOS) {
                                                                                                    toast.success('URL de reserva copiada al portapapeles');
                                                                                                }
                                                                                            } else if (!isIOS) { // Only show error for non-iOS
                                                                                                toast.error('Error al copiar URL');
                                                                                            }
                                                                                        });
                                                                                }}
                                                                            >
                                                                                {copiedCenterId === center.id ? (
                                                                                    <>
                                                                                        <Check
                                                                                            className="h-3.5 w-3.5 mr-1"/>
                                                                                        URL Copiada
                                                                                    </>
                                                                                ) : (
                                                                                    <>
                                                                                        <Link2
                                                                                            className="h-3.5 w-3.5 mr-1"/>
                                                                                        Copiar URL de reserva
                                                                                    </>
                                                                                )}
                                                                            </Button>
                                                                        </div>
                                                                        <div className="ml-2 flex-shrink-0 flex">
                                                                            <p className="flex items-center text-sm text-gray-500">
                                                                                <Home
                                                                                    className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                                {center.address || "Sin dirección"}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div className="mt-2 sm:flex sm:justify-between">
                                                                        <div className="sm:flex">
                                                                            <p className="flex items-center text-sm text-gray-500">
                                                                                <Users
                                                                                    className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                                {center.doctors.length} médicos
                                                                            </p>
                                                                            <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                                                <Calendar
                                                                                    className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                                {Object.values(center.workingDays).filter(day => day.enabled).length} días
                                                                                operativos
                                                                            </p>
                                                                            <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                                                <Clock
                                                                                    className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                                {appointmentCount} turnos
                                                                            </p>
                                                                            <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                                                <UserIcon
                                                                                    className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                                {patientCount} pacientes
                                                                            </p>
                                                                        </div>
                                                                        <Button
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            onClick={() => setExpandedCenterId(isExpanded ? null : center.id)}
                                                                            className="mt-2 sm:mt-0"
                                                                        >
                                                                            {isExpanded ? (
                                                                                <ChevronUp className="h-4 w-4"/>
                                                                            ) : (
                                                                                <ChevronDown className="h-4 w-4"/>
                                                                            )}
                                                                        </Button>
                                                                    </div>
                                                                </div>
                                                                {isExpanded && (
                                                                    <div className="px-4 py-3 bg-gray-50 border-t">
                                                                        <div className="flex flex-col space-y-4">
                                                                            <div>
                                                                                <div
                                                                                    className="flex justify-between items-center">
                                                                                    <h4 className="text-sm font-medium mb-2">Médicos
                                                                                        en este centro:</h4>
                                                                                    <div className="flex space-x-2">
                                                                                        <Button
                                                                                            variant="outline"
                                                                                            size="sm"
                                                                                            className="flex items-center text-xs h-7"
                                                                                            onClick={() => {
                                                                                                setSelectedCenterId(center.id);
                                                                                                setIsAddDoctorDialogOpen(true);
                                                                                            }}
                                                                                        >
                                                                                            <UserPlus
                                                                                                className="h-3 w-3 mr-1"/>
                                                                                            Añadir Médico
                                                                                        </Button>
                                                                                    </div>
                                                                                    <Dialog
                                                                                        open={isSuperUserDialogOpen && newSuperUser.medicalCenterId === center.id}
                                                                                        onOpenChange={(open) => {
                                                                                            setIsSuperUserDialogOpen(open);
                                                                                            if (!open) {
                                                                                                setNewSuperUser({
                                                                                                    ...newSuperUser,
                                                                                                    medicalCenterId: ""
                                                                                                });
                                                                                                setFormError(null);
                                                                                            }
                                                                                        }}>
                                                                                        <DialogTrigger asChild>
                                                                                            <Button
                                                                                                variant="outline"
                                                                                                size="sm"
                                                                                                className="flex items-center text-xs h-7"
                                                                                                onClick={() => {
                                                                                                    // Initialize with default role as RECEPTIONIST instead of SUPERUSER
                                                                                                    setNewSuperUser({
                                                                                                        ...newSuperUser,
                                                                                                        medicalCenterId: center.id,
                                                                                                        medicalCenterIds: [center.id], // Add the center ID to the array
                                                                                                        role: UserRole.RECEPTIONIST,
                                                                                                        medicalCenterRoles: [
                                                                                                            {
                                                                                                                medicalCenterId: center.id,
                                                                                                                role: UserRole.RECEPTIONIST
                                                                                                            }
                                                                                                        ]
                                                                                                    });
                                                                                                    setFormError(null);
                                                                                                }}
                                                                                            >
                                                                                                <PlusCircle
                                                                                                    className="h-3 w-3 mr-1"/>
                                                                                                Agregar Usuarios
                                                                                            </Button>
                                                                                        </DialogTrigger>
                                                                                        <DialogContent>
                                                                                            <DialogHeader>
                                                                                                <DialogTitle>Crear
                                                                                                    Usuario
                                                                                                    para {center.name}</DialogTitle>
                                                                                                <DialogDescription>
                                                                                                    Complete los datos
                                                                                                    para crear un
                                                                                                    usuario que tendrá
                                                                                                    acceso a este
                                                                                                    establecimiento.
                                                                                                </DialogDescription>
                                                                                            </DialogHeader>
                                                                                            {formError && (
                                                                                                <div
                                                                                                    className="bg-red-50 border-l-4 border-red-400 p-4 my-2">
                                                                                                    <div
                                                                                                        className="flex">
                                                                                                        <div
                                                                                                            className="flex-shrink-0">
                                                                                                            <AlertCircle
                                                                                                                className="h-5 w-5 text-red-500"/>
                                                                                                        </div>
                                                                                                        <div
                                                                                                            className="ml-3">
                                                                                                            <p className="text-sm text-red-700">{formError}</p>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            )}
                                                                                            <div
                                                                                                className="grid gap-4 py-4">
                                                                                                {/* Email search section */}
                                                                                                <div
                                                                                                    className="grid grid-cols-4 items-center gap-4">
                                                                                                    <Label
                                                                                                        htmlFor="superuser-email"
                                                                                                        className="text-right">
                                                                                                        Email
                                                                                                    </Label>
                                                                                                    <div
                                                                                                        className="col-span-3 flex gap-2">
                                                                                                        <Input
                                                                                                            id="superuser-email"
                                                                                                            type="email"
                                                                                                            placeholder="<EMAIL>"
                                                                                                            className="flex-1"
                                                                                                            value={newSuperUser.email}
                                                                                                            onChange={(e) => {
                                                                                                                setNewSuperUser({
                                                                                                                    ...newSuperUser,
                                                                                                                    email: e.target.value
                                                                                                                });
                                                                                                                // Clear existing user when email changes
                                                                                                                setExistingUserForSuperUser(null);
                                                                                                            }}
                                                                                                        />
                                                                                                        <Button
                                                                                                            type="button"
                                                                                                            variant="outline"
                                                                                                            onClick={() => {
                                                                                                                // Search for existing user
                                                                                                                const existingUser = storage.getUserByEmail(newSuperUser.email);
                                                                                                                if (existingUser) {
                                                                                                                    setExistingUserForSuperUser(existingUser);
                                                                                                                    // Pre-fill name if user exists
                                                                                                                    setNewSuperUser({
                                                                                                                        ...newSuperUser,
                                                                                                                        name: existingUser.name,
                                                                                                                        // Don't need password for existing users
                                                                                                                        password: ""
                                                                                                                    });
                                                                                                                } else {
                                                                                                                    setExistingUserForSuperUser(null);
                                                                                                                    // Show a success message instead of an error
                                                                                                                    toast.success(`No se encontró ningún usuario con el email ${newSuperUser.email}. Puede crear uno nuevo.`);
                                                                                                                }
                                                                                                            }}
                                                                                                        >
                                                                                                            <Search
                                                                                                                className="h-4 w-4 mr-1"/>
                                                                                                            Buscar
                                                                                                        </Button>
                                                                                                    </div>
                                                                                                </div>

                                                                                                {/* Show existing user info if found */}
                                                                                                {existingUserForSuperUser && (
                                                                                                    <div
                                                                                                        className="col-span-4 bg-blue-50 p-4 rounded-md border border-blue-200 mb-2">
                                                                                                        <div
                                                                                                            className="flex items-center gap-2 mb-2">
                                                                                                            <CheckCircle2
                                                                                                                className="h-5 w-5 text-green-600"/>
                                                                                                            <span
                                                                                                                className="font-medium">Usuario existente encontrado</span>
                                                                                                        </div>

                                                                                                        {/* Check if user already has access to this medical center */}
                                                                                                        {existingUserForSuperUser.medicalCenterIds?.includes(newSuperUser.medicalCenterId) ? (
                                                                                                            <div>
                                                                                                                <p className="text-sm mb-2">
                                                                                                                    Este
                                                                                                                    usuario
                                                                                                                    ya
                                                                                                                    tiene
                                                                                                                    acceso
                                                                                                                    a
                                                                                                                    este
                                                                                                                    establecimiento.
                                                                                                                </p>

                                                                                                                {/* Check if user already has superuser role for this center */}
                                                                                                                {(() => {
                                                                                                                    const centerRole = existingUserForSuperUser.medicalCenterRoles?.find(
                                                                                                                        mcr => mcr.medicalCenterId === newSuperUser.medicalCenterId
                                                                                                                    );

                                                                                                                    if (centerRole?.role === UserRole.SUPERUSER) {
                                                                                                                        return (
                                                                                                                            <p className="text-sm text-amber-600">
                                                                                                                                <AlertTriangle
                                                                                                                                    className="h-4 w-4 inline mr-1"/>
                                                                                                                                Este
                                                                                                                                usuario
                                                                                                                                ya
                                                                                                                                tiene
                                                                                                                                acceso
                                                                                                                                a
                                                                                                                                este
                                                                                                                                establecimiento.
                                                                                                                            </p>
                                                                                                                        );
                                                                                                                    } else {
                                                                                                                        return (
                                                                                                                            <p className="text-sm text-amber-600">
                                                                                                                                <AlertTriangle
                                                                                                                                    className="h-4 w-4 inline mr-1"/>
                                                                                                                                Este
                                                                                                                                usuario
                                                                                                                                tiene
                                                                                                                                rol
                                                                                                                                de {centerRole ? getRoleName(centerRole.role) : getRoleName(existingUserForSuperUser.roles)} en
                                                                                                                                este
                                                                                                                                centro.
                                                                                                                                Cambiar
                                                                                                                                su
                                                                                                                                rol
                                                                                                                                modificará
                                                                                                                                sus
                                                                                                                                permisos.
                                                                                                                            </p>
                                                                                                                        );
                                                                                                                    }
                                                                                                                })()}
                                                                                                            </div>
                                                                                                        ) : (
                                                                                                            <p className="text-sm mb-2">
                                                                                                                Este
                                                                                                                usuario
                                                                                                                ya
                                                                                                                existe
                                                                                                                en el
                                                                                                                sistema.
                                                                                                                Se le
                                                                                                                asignará
                                                                                                                acceso a
                                                                                                                este
                                                                                                                establecimiento.
                                                                                                            </p>
                                                                                                        )}

                                                                                                        <div
                                                                                                            className="grid grid-cols-2 gap-2 text-sm">
                                                                                                            <div><span
                                                                                                                className="font-medium">Nombre:</span> {existingUserForSuperUser.name}
                                                                                                            </div>
                                                                                                            <div><span
                                                                                                                className="font-medium">Email:</span> {existingUserForSuperUser.email}
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                )}

                                                                                                {/* Name field - disabled for existing users */}
                                                                                                <div
                                                                                                    className="grid grid-cols-4 items-center gap-4">
                                                                                                    <Label
                                                                                                        htmlFor="superuser-name"
                                                                                                        className="text-right">
                                                                                                        Nombre
                                                                                                    </Label>
                                                                                                    <Input
                                                                                                        id="superuser-name"
                                                                                                        placeholder="Nombre completo"
                                                                                                        className="col-span-3"
                                                                                                        value={newSuperUser.name}
                                                                                                        onChange={(e) => setNewSuperUser({
                                                                                                            ...newSuperUser,
                                                                                                            name: e.target.value
                                                                                                        })}
                                                                                                        disabled={!!existingUserForSuperUser} // Disable if existing user
                                                                                                    />
                                                                                                </div>

                                                                                                {/* Password field removed - using temporary password */}
                                                                                                {!existingUserForSuperUser && (
                                                                                                    <div
                                                                                                        className="grid grid-cols-4 items-center gap-4">
                                                                                                        <Label
                                                                                                            htmlFor="superuser-password-info"
                                                                                                            className="text-right">
                                                                                                            Contraseña
                                                                                                        </Label>
                                                                                                        <div
                                                                                                            className="col-span-3 text-sm text-gray-600 flex items-center">
                                                                                                            <Key
                                                                                                                className="h-4 w-4 mr-2 text-blue-500"/>
                                                                                                            Se generará
                                                                                                            una
                                                                                                            contraseña
                                                                                                            temporal y
                                                                                                            se enviará
                                                                                                            por correo
                                                                                                            electrónico
                                                                                                        </div>
                                                                                                    </div>
                                                                                                )}

                                                                                                {/* Role selection */}
                                                                                                <div
                                                                                                    className="grid grid-cols-4 items-center gap-4">
                                                                                                    <Label
                                                                                                        htmlFor="user-role"
                                                                                                        className="text-right">
                                                                                                        Rol
                                                                                                    </Label>
                                                                                                    <Select
                                                                                                        value={newSuperUser.role}
                                                                                                        onValueChange={(value) => {
                                                                                                            const newRole = value as UserRole;
                                                                                                            // Update the role for this medical center
                                                                                                            const updatedRoles = [...(newSuperUser.medicalCenterRoles || [])];
                                                                                                            const centerRoleIndex = updatedRoles.findIndex(mcr => mcr.medicalCenterId === newSuperUser.medicalCenterId);

                                                                                                            if (centerRoleIndex >= 0) {
                                                                                                                // Update existing role
                                                                                                                updatedRoles[centerRoleIndex].role = newRole;
                                                                                                            } else {
                                                                                                                // Add new role
                                                                                                                updatedRoles.push({
                                                                                                                    medicalCenterId: newSuperUser.medicalCenterId,
                                                                                                                    role: newRole
                                                                                                                });
                                                                                                            }

                                                                                                            setNewSuperUser({
                                                                                                                ...newSuperUser,
                                                                                                                role: newRole,
                                                                                                                medicalCenterRoles: updatedRoles
                                                                                                            });
                                                                                                        }}
                                                                                                    >
                                                                                                        <SelectTrigger
                                                                                                            className="col-span-3">
                                                                                                            <SelectValue
                                                                                                                placeholder="Seleccionar rol"/>
                                                                                                        </SelectTrigger>
                                                                                                        <SelectContent>
                                                                                                            <SelectItem
                                                                                                                value={UserRole.SUPERUSER}>Superusuario</SelectItem>
                                                                                                            <SelectItem
                                                                                                                value={UserRole.ADMIN}>Administrador</SelectItem>
                                                                                                            <SelectItem
                                                                                                                value={UserRole.RECEPTIONIST}>Recepcionista</SelectItem>
                                                                                                        </SelectContent>
                                                                                                    </Select>
                                                                                                </div>
                                                                                            </div>
                                                                                            <DialogFooter>
                                                                                                <Button
                                                                                                    type="submit"
                                                                                                    onClick={handleCreateSuperUser}
                                                                                                    disabled={
                                                                                                        existingUserForSuperUser
                                                                                                            ? existingUserForSuperUser.medicalCenterRoles?.some(
                                                                                                                mcr => mcr.medicalCenterId === newSuperUser.medicalCenterId && mcr.role === UserRole.SUPERUSER
                                                                                                            )
                                                                                                            : !newSuperUser.name || !newSuperUser.email
                                                                                                    }
                                                                                                >
                                                                                                    {existingUserForSuperUser
                                                                                                        ? (existingUserForSuperUser.medicalCenterRoles?.some(
                                                                                                            mcr => mcr.medicalCenterId === newSuperUser.medicalCenterId && mcr.role === UserRole.SUPERUSER
                                                                                                        )
                                                                                                            ? "Usuario Ya Tiene Acceso"
                                                                                                            : "Actualizar Usuario")
                                                                                                        : "Crear Usuario"
                                                                                                    }
                                                                                                </Button>
                                                                                            </DialogFooter>
                                                                                        </DialogContent>
                                                                                    </Dialog>
                                                                                </div>
                                                                                <ul className="space-y-2">
                                                                                    {/* Get the doctors for this medical center */}
                                                                                    {(() => {
                                                                                        // Get the doctor IDs from the center and convert to full doctor objects
                                                                                        const doctorIds = center.doctors || [];

                                                                                        // If no doctors assigned to this center
                                                                                        if (doctorIds.length === 0) {
                                                                                            return <li
                                                                                                className="text-sm text-gray-500">No
                                                                                                hay médicos asignados a
                                                                                                este
                                                                                                establecimiento</li>;
                                                                                        }

                                                                                        // Map doctor IDs to full doctor objects
                                                                                        return doctorIds.map(doctorId => {
                                                                                            const doctor = allDoctors.find(d => d.id === doctorId);
                                                                                            if (!doctor) return null;

                                                                                            return (
                                                                                                <li key={doctorId}
                                                                                                    className="flex items-center justify-between py-1 px-1 hover:bg-gray-50 rounded-md">
                                                                                                    <div
                                                                                                        className="flex items-center">
                                                                                                        <div
                                                                                                            className="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                                                                                            <span
                                                                                                                className="text-blue-600 font-medium">{doctor.name.charAt(0)}</span>
                                                                                                        </div>
                                                                                                        <div>
                                                                                                            <p className="text-sm font-medium">{doctor.name}</p>
                                                                                                            <p className="text-xs text-gray-500">{doctor.specialties.join(", ")}</p>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <Button
                                                                                                        variant="ghost"
                                                                                                        size="sm"
                                                                                                        className="text-red-600 hover:text-red-800 hover:bg-red-50 h-7 w-7 p-0"
                                                                                                        onClick={() => handleRemoveDoctorFromCenter(doctorId, center.id)}
                                                                                                    >
                                                                                                        <X className="h-4 w-4"/>
                                                                                                    </Button>
                                                                                                </li>
                                                                                            );
                                                                                        });
                                                                                    })()}
                                                                                </ul>
                                                                            </div>

                                                                            {/* Dialog for adding doctors to a center */}
                                                                            <Dialog
                                                                                open={isAddDoctorDialogOpen && selectedCenterId === center.id}
                                                                                onOpenChange={setIsAddDoctorDialogOpen}>
                                                                                <DialogContent>
                                                                                    <DialogHeader>
                                                                                        <DialogTitle>Añadir Médico al
                                                                                            Establecimiento</DialogTitle>
                                                                                        <DialogDescription>
                                                                                            Seleccione un médico para
                                                                                            añadirlo a {center.name}
                                                                                        </DialogDescription>
                                                                                    </DialogHeader>

                                                                                    <div className="py-4">
                                                                                        <div className="space-y-4">
                                                                                            <Select
                                                                                                value={selectedDoctorId}
                                                                                                onValueChange={setSelectedDoctorId}
                                                                                            >
                                                                                                <SelectTrigger>
                                                                                                    <SelectValue
                                                                                                        placeholder="Seleccionar médico"/>
                                                                                                </SelectTrigger>
                                                                                                <SelectContent>
                                                                                                    {allDoctors
                                                                                                        .filter(d => !centerDoctors.some(cd => cd.id === d.id))
                                                                                                        .map(doctor => (
                                                                                                            <SelectItem
                                                                                                                key={doctor.id}
                                                                                                                value={doctor.id}>
                                                                                                                {doctor.name} ({doctor.specialties.join(", ")})
                                                                                                            </SelectItem>
                                                                                                        ))
                                                                                                    }
                                                                                                    {allDoctors.filter(d => !centerDoctors.some(cd => cd.id === d.id)).length === 0 && (
                                                                                                        <SelectItem
                                                                                                            value=""
                                                                                                            disabled>
                                                                                                            No hay
                                                                                                            médicos
                                                                                                            disponibles
                                                                                                            para añadir
                                                                                                        </SelectItem>
                                                                                                    )}
                                                                                                </SelectContent>
                                                                                            </Select>
                                                                                        </div>
                                                                                    </div>

                                                                                    <DialogFooter>
                                                                                        <Button variant="outline"
                                                                                                onClick={() => setIsAddDoctorDialogOpen(false)}>Cancelar</Button>
                                                                                        <Button
                                                                                            onClick={handleAddDoctorToCenter}
                                                                                            disabled={!selectedDoctorId}
                                                                                        >
                                                                                            Añadir Médico
                                                                                        </Button>
                                                                                    </DialogFooter>
                                                                                </DialogContent>
                                                                            </Dialog>

                                                                            <div>
                                                                                <h4 className="text-lg font-medium border-b pb-1 mb-3">Usuarios
                                                                                    del establecimiento</h4>
                                                                                <div className="mb-4">
                                                                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                                                                        <div
                                                                                            className="flex-shrink-0 h-6 w-6 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                                                                                            <UserIcon
                                                                                                className="h-3 w-3 text-purple-600"/>
                                                                                        </div>
                                                                                        Super Usuarios:
                                                                                    </h5>
                                                                                    <ul className="space-y-2 pl-8">
                                                                                        {storage.getUsersByMedicalCenter(center.id)
                                                                                            .filter(u => {
                                                                                                // Check if user has SUPERUSER role for this specific medical center
                                                                                                if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                                                                                                    const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                                                    if (centerRole) {
                                                                                                        return centerRole.role === UserRole.SUPERUSER;
                                                                                                    }
                                                                                                }
                                                                                                // Fall back to default role
                                                                                                return u.roles === UserRole.SUPERUSER;
                                                                                            })
                                                                                            .map(user => (
                                                                                                <li key={user.id}
                                                                                                    className="flex items-center">
                                                                                                    <div>
                                                                                                        <p className="text-sm font-medium">{user.name}</p>
                                                                                                        <p className="text-xs text-gray-500">{user.email}</p>
                                                                                                        {user.doctorId && (
                                                                                                            <p className="text-xs text-amber-600 mt-1">
                                                                                                                También
                                                                                                                es
                                                                                                                médico
                                                                                                                en el
                                                                                                                sistema
                                                                                                            </p>
                                                                                                        )}
                                                                                                    </div>
                                                                                                </li>
                                                                                            ))}
                                                                                        {storage.getUsersByMedicalCenter(center.id).filter(u => {
                                                                                            // Check if user has SUPERUSER role for this specific medical center
                                                                                            if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                                                                                                const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                                                if (centerRole) {
                                                                                                    return centerRole.role === UserRole.SUPERUSER;
                                                                                                }
                                                                                            }
                                                                                            // Fall back to default role
                                                                                            return u.roles === UserRole.SUPERUSER;
                                                                                        }).length === 0 && (
                                                                                            <li className="text-sm text-gray-500">No
                                                                                                hay super usuarios
                                                                                                asignados a este
                                                                                                centro</li>
                                                                                        )}
                                                                                    </ul>
                                                                                </div>

                                                                                <div className="mb-4">
                                                                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                                                                        <div
                                                                                            className="flex-shrink-0 h-6 w-6 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                                                                            <UserIcon
                                                                                                className="h-3 w-3 text-blue-600"/>
                                                                                        </div>
                                                                                        Administradores:
                                                                                    </h5>
                                                                                    <ul className="space-y-2 pl-8">
                                                                                        {storage.getUsersByMedicalCenter(center.id)
                                                                                            .filter(u => {
                                                                                                // Check if user has ADMIN role for this specific medical center
                                                                                                if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                                                                                                    const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                                                    if (centerRole) {
                                                                                                        return centerRole.role === UserRole.ADMIN;
                                                                                                    }
                                                                                                }
                                                                                                // Fall back to default role
                                                                                                return u.roles === UserRole.ADMIN;
                                                                                            })
                                                                                            .map(user => (
                                                                                                <li key={user.id}
                                                                                                    className="flex items-center">
                                                                                                    <div>
                                                                                                        <p className="text-sm font-medium">{user.name}</p>
                                                                                                        <p className="text-xs text-gray-500">{user.email}</p>
                                                                                                        {user.doctorId && (
                                                                                                            <p className="text-xs text-amber-600 mt-1">
                                                                                                                También
                                                                                                                es
                                                                                                                médico
                                                                                                                en el
                                                                                                                sistema
                                                                                                            </p>
                                                                                                        )}
                                                                                                    </div>
                                                                                                </li>
                                                                                            ))}
                                                                                        {storage.getUsersByMedicalCenter(center.id).filter(u => {
                                                                                            // Check if user has ADMIN role for this specific medical center
                                                                                            if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                                                                                                const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                                                if (centerRole) {
                                                                                                    return centerRole.role === UserRole.ADMIN;
                                                                                                }
                                                                                            }
                                                                                            // Fall back to default role
                                                                                            return u.roles === UserRole.ADMIN;
                                                                                        }).length === 0 && (
                                                                                            <li className="text-sm text-gray-500">No
                                                                                                hay administradores
                                                                                                asignados a este
                                                                                                centro</li>
                                                                                        )}
                                                                                    </ul>
                                                                                </div>

                                                                                <div className="mb-4">
                                                                                    <h5 className="text-sm font-medium mb-2 flex items-center">
                                                                                        <div
                                                                                            className="flex-shrink-0 h-6 w-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                                                                                            <UserIcon
                                                                                                className="h-3 w-3 text-green-600"/>
                                                                                        </div>
                                                                                        Recepcionistas:
                                                                                    </h5>
                                                                                    <ul className="space-y-2 pl-8">
                                                                                        {storage.getUsersByMedicalCenter(center.id)
                                                                                            .filter(u => {
                                                                                                // Check if user has RECEPTIONIST role for this specific medical center
                                                                                                if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                                                                                                    const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                                                    if (centerRole) {
                                                                                                        return centerRole.role === UserRole.RECEPTIONIST;
                                                                                                    }
                                                                                                }
                                                                                                // Fall back to default role
                                                                                                return u.roles === UserRole.RECEPTIONIST;
                                                                                            })
                                                                                            .map(user => (
                                                                                                <li key={user.id}
                                                                                                    className="flex items-center">
                                                                                                    <div>
                                                                                                        <p className="text-sm font-medium">{user.name}</p>
                                                                                                        <p className="text-xs text-gray-500">{user.email}</p>
                                                                                                        {user.doctorId && (
                                                                                                            <p className="text-xs text-amber-600 mt-1">
                                                                                                                También
                                                                                                                es
                                                                                                                médico
                                                                                                                en el
                                                                                                                sistema
                                                                                                            </p>
                                                                                                        )}
                                                                                                    </div>
                                                                                                </li>
                                                                                            ))}
                                                                                        {storage.getUsersByMedicalCenter(center.id).filter(u => {
                                                                                            // Check if user has RECEPTIONIST role for this specific medical center
                                                                                            if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                                                                                                const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                                                if (centerRole) {
                                                                                                    return centerRole.role === UserRole.RECEPTIONIST;
                                                                                                }
                                                                                            }
                                                                                            // Fall back to default role
                                                                                            return u.roles === UserRole.RECEPTIONIST;
                                                                                        }).length === 0 && (
                                                                                            <li className="text-sm text-gray-500">No
                                                                                                hay recepcionistas
                                                                                                asignados a este
                                                                                                centro</li>
                                                                                        )}
                                                                                    </ul>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </li>
                                                    );
                                                })}
                                        </ul>
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="doctors">
                                <div className="mt-4 space-y-6">
                                    <div className="flex justify-between items-center">
                                        <h3 className="text-lg font-medium">Médicos Registrados</h3>
                                        <div className="flex items-center space-x-2">
                                            <Input
                                                placeholder="Buscar por médico o establecimiento..."
                                                className="w-64"
                                                value={doctorSearchQuery}
                                                onChange={(e) => setDoctorSearchQuery(e.target.value)}
                                            />
                                            <Button
                                                variant="outline"
                                                onClick={handleExportData}
                                                className="flex items-center"
                                            >
                                                <Download className="h-4 w-4 mr-2"/>
                                                Exportar Datos
                                            </Button>
                                            <Button
                                                variant="outline"
                                                className="flex items-center text-red-600 border-red-200 hover:bg-red-50"
                                                onClick={() => setIsResetDialogOpen(true)}
                                            >
                                                <RefreshCw className="h-4 w-4 mr-2"/>
                                                Restablecer Datos
                                            </Button>
                                        </div>
                                    </div>

                                    <div className="bg-white shadow overflow-hidden sm:rounded-md">
                                        <ul role="list" className="divide-y divide-gray-200">
                                            {allDoctors
                                                .filter(doctor => {
                                                    if (!doctorSearchQuery) return true;
                                                    const query = doctorSearchQuery.toLowerCase();

                                                    // Search by doctor name, specialties, MN, or email
                                                    if (doctor.name.toLowerCase().includes(query) ||
                                                        doctor.specialties.some(specialty => specialty.toLowerCase().includes(query)) ||
                                                        doctor.mn?.toLowerCase().includes(query) ||
                                                        doctor.email?.toLowerCase().includes(query)) {
                                                        return true;
                                                    }

                                                    // Search by medical center names where this doctor works
                                                    const doctorMedicalCenters = medicalCenters.filter(center => center.doctors.includes(doctor.id));
                                                    return doctorMedicalCenters.some(center =>
                                                        center.name.toLowerCase().includes(query) ||
                                                        center.address?.toLowerCase().includes(query)
                                                    );
                                                })
                                                .sort((a, b) => a.name.localeCompare(b.name))
                                                .map((doctor) => {
                                                    const isExpanded = expandedDoctorId === doctor.id;
                                                    const doctorMedicalCenters = getDoctorMedicalCenters(doctor.id);

                                                    return (
                                                        <li key={doctor.id}>
                                                            <div className="px-4 py-4 sm:px-6">
                                                                <div className="flex items-center justify-between">
                                                                    <div className="flex items-center">
                                                                        <div
                                                                            className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                                                            <span
                                                                                className="text-blue-600 font-medium">{doctor.initial}</span>
                                                                        </div>
                                                                        <div className="ml-4">
                                                                            <p className="text-sm font-medium text-blue-600 truncate">{doctor.name}</p>
                                                                            <p className="text-sm text-gray-500">
                                                                                {doctor.specialties.join(", ")}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div className="ml-2 flex-shrink-0 flex">
                                                                        <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                                            MN: {doctor.mn}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                                <div className="mt-2 sm:flex sm:justify-between">
                                                                    <div className="sm:flex">
                                                                        <p className="flex items-center text-sm text-gray-500">
                                                                            <Calendar
                                                                                className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                            {Object.values(doctor.workingDays).filter((day) => day.enabled).length} días
                                                                            de atención
                                                                        </p>
                                                                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                                            <Users
                                                                                className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                            {doctor.consultationTypes.length} tipos de
                                                                            consulta
                                                                        </p>
                                                                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                                            <Building2
                                                                                className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                            {doctorMedicalCenters.length} Establecimientos
                                                                        </p>
                                                                        {doctor.email && (
                                                                            <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                                                <Mail
                                                                                    className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                                {doctor.email}
                                                                                {(() => {
                                                                                    // Find the doctor user to display password
                                                                                    const doctorUser = storage.getUserByEmail(doctor.email);
                                                                                    if (doctorUser && doctorUser.roles === UserRole.DOCTOR && doctorUser.password) {
                                                                                        return (
                                                                                            <span
                                                                                                className="ml-2 bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded">
                                              Password: {doctorUser.password}
                                            </span>
                                                                                        );
                                                                                    }
                                                                                    return null;
                                                                                })()}
                                                                            </p>
                                                                        )}
                                                                    </div>
                                                                    <div className="flex items-center">
                                                                        {doctor.email && (
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={() => {
                                                                                    // Generate a new temporary password
                                                                                    const tempPassword = `temp${Math.floor(100000 + Math.random() * 900000)}`;

                                                                                    // Find the doctor user
                                                                                    const doctorUser = doctor.email ? storage.getUserByEmail(doctor.email) : null;

                                                                                    if (doctorUser && doctorUser.roles === UserRole.DOCTOR) {
                                                                                        // Update the password
                                                                                        const updatedUser = {
                                                                                            ...doctorUser,
                                                                                            password: tempPassword
                                                                                        };

                                                                                        // Save the updated user
                                                                                        storage.saveUser(updatedUser);

                                                                                        // Set the doctor password reset state
                                                                                        setDoctorPasswordReset({
                                                                                            doctorId: doctor.id,
                                                                                            password: tempPassword
                                                                                        });

                                                                                        toast.success(`Contraseña restablecida para ${doctor.name}`);
                                                                                    } else if (!doctorUser && doctor.email) {
                                                                                        // Create a new doctor user
                                                                                        const newDoctorUser = {
                                                                                            id: Math.random().toString(36).substring(2, 15),
                                                                                            name: doctor.name,
                                                                                            email: doctor.email,
                                                                                            password: tempPassword,
                                                                                            role: UserRole.DOCTOR,
                                                                                            medicalCenterId: doctorMedicalCenters[0]?.id || "",
                                                                                            doctorId: doctor.id,
                                                                                            permissions: {
                                                                                                canViewOwnSchedule: true,
                                                                                                canViewOwnAnalytics: true
                                                                                            }
                                                                                        };

                                                                                        // Save the new user
                                                                                        storage.saveUser(newDoctorUser);

                                                                                        // Set the doctor password reset state
                                                                                        setDoctorPasswordReset({
                                                                                            doctorId: doctor.id,
                                                                                            password: tempPassword
                                                                                        });

                                                                                        toast.success(`Usuario creado para ${doctor.name}`);
                                                                                    } else {
                                                                                        toast.error(`No se pudo encontrar o crear el usuario para ${doctor.name}`);
                                                                                    }
                                                                                }}
                                                                                className="mr-2"
                                                                            >
                                                                                <Key className="h-4 w-4 mr-1"/>
                                                                                Restablecer Contraseña
                                                                            </Button>
                                                                        )}
                                                                        <Button
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            onClick={() => setExpandedDoctorId(isExpanded ? null : doctor.id)}
                                                                            className="mt-2 sm:mt-0"
                                                                        >
                                                                            {isExpanded ? (
                                                                                <ChevronUp className="h-4 w-4"/>
                                                                            ) : (
                                                                                <ChevronDown className="h-4 w-4"/>
                                                                            )}
                                                                        </Button>
                                                                    </div>
                                                                </div>
                                                                {isExpanded && (
                                                                    <div
                                                                        className="px-4 py-3 mt-3 bg-gray-50 border-t rounded-md">
                                                                        <div
                                                                            className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                            <div>
                                                                                <h4 className="text-sm font-medium mb-2">Establecimientos
                                                                                    donde trabaja:</h4>
                                                                                <ul className="space-y-2">
                                                                                    {doctorMedicalCenters.length > 0 ? (
                                                                                        doctorMedicalCenters.map(center => (
                                                                                            <li key={center.id}
                                                                                                className="flex items-center">
                                                                                                <Building2
                                                                                                    className="h-4 w-4 mr-2 text-gray-400"/>
                                                                                                <div>
                                                                                                    <Link
                                                                                                        href={`/plataforma/establecimiento/${center.id}`}>
                                                                                                        <p className="text-sm font-medium text-blue-600">{center.name}</p>
                                                                                                    </Link>
                                                                                                    <p className="text-xs text-gray-500">{center.address || "Sin dirección"}</p>
                                                                                                </div>
                                                                                            </li>
                                                                                        ))
                                                                                    ) : (
                                                                                        <li className="text-sm text-gray-500">No
                                                                                            está asignado a ningún
                                                                                            establecimiento</li>
                                                                                    )}
                                                                                </ul>
                                                                            </div>

                                                                            <div className="bg-blue-50 p-4 rounded-md">
                                                                                <h4 className="text-sm font-medium text-blue-800 mb-2">Credenciales
                                                                                    de acceso:</h4>
                                                                                <div className="space-y-1">
                                                                                    <p className="text-sm"><span
                                                                                        className="font-medium">Email:</span> {doctor.email || "No email"}
                                                                                    </p>
                                                                                    {(() => {
                                                                                        // Show reset password if available
                                                                                        if (doctorPasswordReset && doctorPasswordReset.doctorId === doctor.id) {
                                                                                            return (
                                                                                                <p className="text-sm">
                                                                                                    <span
                                                                                                        className="font-medium">Contraseña temporal:</span>
                                                                                                    <span
                                                                                                        className="bg-yellow-100 text-yellow-800 px-1 rounded">{doctorPasswordReset.password}</span>
                                                                                                </p>
                                                                                            );
                                                                                        }

                                                                                        // Otherwise show current password if available
                                                                                        const doctorUser = doctor.email ? storage.getUserByEmail(doctor.email) : null;
                                                                                        if (doctorUser && doctorUser.roles === UserRole.DOCTOR && doctorUser.password) {
                                                                                            return (
                                                                                                <p className="text-sm">
                                                                                                    <span
                                                                                                        className="font-medium">Contraseña actual:</span>
                                                                                                    <span
                                                                                                        className="bg-yellow-100 text-yellow-800 px-1 rounded">{doctorUser.password}</span>
                                                                                                </p>
                                                                                            );
                                                                                        }

                                                                                        return <p
                                                                                            className="text-sm text-gray-500">No
                                                                                            hay cuenta de usuario</p>;
                                                                                    })()}
                                                                                    <p className="text-xs text-gray-500 mt-2">El
                                                                                        doctor puede acceder en:
                                                                                        /plataforma/profesional/login</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </li>
                                                    );
                                                })}
                                        </ul>
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="users">
                                <div className="mt-4 space-y-6">
                                    <div className="flex justify-between items-center">
                                        <h3 className="text-lg font-medium">Usuarios Registrados</h3>
                                        <div className="flex items-center space-x-2">
                                            <div className="flex items-center space-x-2 mr-4">
                                                <Input
                                                    placeholder="Buscar por nombre o establecimiento..."
                                                    className="w-64"
                                                    value={userSearchQuery}
                                                    onChange={(e) => setUserSearchQuery(e.target.value)}
                                                />
                                                <Select
                                                    value={userRoleFilter}
                                                    onValueChange={setUserRoleFilter}
                                                >
                                                    <SelectTrigger className="w-40">
                                                        <SelectValue placeholder="Filtrar por rol"/>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="all">Todos los roles</SelectItem>
                                                        <SelectItem
                                                            value={UserRole.SUPERUSER}>Superusuarios</SelectItem>
                                                        <SelectItem value={UserRole.ADMIN}>Administradores</SelectItem>
                                                        <SelectItem
                                                            value={UserRole.RECEPTIONIST}>Recepcionistas</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <Dialog open={isSuperUserDialogOpen}
                                                    onOpenChange={setIsSuperUserDialogOpen}>
                                                <DialogTrigger asChild>
                                                    <Button className="flex items-center">
                                                        <UserPlus className="h-4 w-4 mr-2"/>
                                                        Nuevo Usuario
                                                    </Button>
                                                </DialogTrigger>
                                                <DialogContent className="sm:max-w-3xl">
                                                    <DialogHeader>
                                                        <DialogTitle>Crear Nuevo Usuario</DialogTitle>
                                                        <DialogDescription>
                                                            Complete los datos para crear un usuario con acceso a
                                                            múltiples centros médicos.
                                                        </DialogDescription>
                                                    </DialogHeader>

                                                    {formError && (
                                                        <div className="bg-red-50 border-l-4 border-red-400 p-4 my-2">
                                                            <div className="flex">
                                                                <div className="flex-shrink-0">
                                                                    <AlertCircle className="h-5 w-5 text-red-500"/>
                                                                </div>
                                                                <div className="ml-3">
                                                                    <p className="text-sm text-red-700">{formError}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )}

                                                    <div className="flex flex-col gap-4 py-4">
                                                        {/* Email search section */}
                                                        <div>
                                                            <Label htmlFor="superuser-email" className="block mb-2">
                                                                Email
                                                            </Label>
                                                            <div className="flex gap-2">
                                                                <Input
                                                                    id="superuser-email"
                                                                    type="email"
                                                                    placeholder="<EMAIL>"
                                                                    className="flex-1"
                                                                    value={newSuperUser.email}
                                                                    onChange={(e) => {
                                                                        setNewSuperUser({
                                                                            ...newSuperUser,
                                                                            email: e.target.value
                                                                        });
                                                                        // Clear existing user when email changes
                                                                        setExistingUserForSuperUser(null);
                                                                    }}
                                                                />
                                                                <Button
                                                                    type="button"
                                                                    variant="outline"
                                                                    onClick={() => {
                                                                        // Search for existing user
                                                                        const existingUser = storage.getUserByEmail(newSuperUser.email);
                                                                        if (existingUser) {
                                                                            setExistingUserForSuperUser(existingUser);
                                                                            // Pre-fill name if user exists
                                                                            setNewSuperUser({
                                                                                ...newSuperUser,
                                                                                name: existingUser.name,
                                                                                // Don't need password for existing users
                                                                                password: ""
                                                                            });
                                                                        } else {
                                                                            setExistingUserForSuperUser(null);
                                                                            // Show a success message instead of an error
                                                                            toast.success(`No se encontró ningún usuario con el email ${newSuperUser.email}. Puede crear uno nuevo.`);
                                                                        }
                                                                    }}
                                                                >
                                                                    <Search className="h-4 w-4 mr-1"/>
                                                                    Buscar
                                                                </Button>
                                                            </div>
                                                        </div>

                                                        {/* Show existing user info if found */}
                                                        {existingUserForSuperUser && (
                                                            <div
                                                                className="bg-blue-50 p-4 rounded-md border border-blue-200 mb-2">
                                                                <div className="flex items-center gap-2 mb-2">
                                                                    <CheckCircle2 className="h-5 w-5 text-green-600"/>
                                                                    <span className="font-medium">Usuario existente encontrado</span>
                                                                </div>

                                                                <div className="grid grid-cols-2 gap-2 text-sm">
                                                                    <div><span
                                                                        className="font-medium">Nombre:</span> {existingUserForSuperUser.name}
                                                                    </div>
                                                                    <div><span
                                                                        className="font-medium">Email:</span> {existingUserForSuperUser.email}
                                                                    </div>

                                                                    {/* Check if user is only a doctor or has admin roles */}
                                                                    {(() => {
                                                                        // Check if user has any non-doctor role in any medical center
                                                                        const hasNonDoctorRole = existingUserForSuperUser.roles !== UserRole.DOCTOR ||
                                                                            (existingUserForSuperUser.medicalCenterRoles &&
                                                                                existingUserForSuperUser.medicalCenterRoles.some(mcr => mcr.role !== UserRole.DOCTOR));

                                                                        if (existingUserForSuperUser.doctorId && !hasNonDoctorRole) {
                                                                            // User is only a doctor
                                                                            return (
                                                                                <div className="col-span-2">
                                                                                    <p className="text-amber-600 mb-1">
                                                                                        <span
                                                                                            className="font-medium">Nota:</span> Este
                                                                                        usuario es médico en el sistema.
                                                                                    </p>
                                                                                    <p className="text-green-600">
                                                                                        Puede asignarle roles
                                                                                        administrativos seleccionando
                                                                                        los centros médicos
                                                                                        correspondientes.
                                                                                    </p>
                                                                                </div>
                                                                            );
                                                                        } else if (existingUserForSuperUser.doctorId && hasNonDoctorRole) {
                                                                            // User is a doctor with admin roles
                                                                            return (
                                                                                <div className="col-span-2">
                                                                                    <p className="text-amber-600 mb-1">
                                                                                        <span
                                                                                            className="font-medium">Nota:</span> Este
                                                                                        usuario es médico y ya tiene
                                                                                        roles administrativos en otros
                                                                                        centros.
                                                                                    </p>
                                                                                    <p className="text-red-600">
                                                                                        <AlertTriangle
                                                                                            className="h-4 w-4 inline mr-1"/>
                                                                                        Para gestionar sus roles,
                                                                                        utilice la opción &quot;Editar
                                                                                        Establecimientos&quot; en la
                                                                                        lista de usuarios.
                                                                                    </p>
                                                                                </div>
                                                                            );
                                                                        } else if (hasNonDoctorRole) {
                                                                            // User has admin roles but is not a doctor
                                                                            return (
                                                                                <div className="col-span-2">
                                                                                    <p className="text-amber-600 mb-1">
                                                                                        <span
                                                                                            className="font-medium">Nota:</span> Este
                                                                                        usuario ya tiene roles
                                                                                        administrativos en otros
                                                                                        centros.
                                                                                    </p>
                                                                                    <p className="text-red-600">
                                                                                        <AlertTriangle
                                                                                            className="h-4 w-4 inline mr-1"/>
                                                                                        Para gestionar sus roles,
                                                                                        utilice la opción &quot;Editar
                                                                                        Establecimientos&quot; en la
                                                                                        lista de usuarios.
                                                                                    </p>
                                                                                </div>
                                                                            );
                                                                        }

                                                                        return null;
                                                                    })()}
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* Name field - disabled for existing users */}
                                                        <div>
                                                            <Label htmlFor="superuser-name" className="block mb-2">
                                                                Nombre
                                                            </Label>
                                                            <Input
                                                                id="superuser-name"
                                                                placeholder="Nombre completo"
                                                                value={newSuperUser.name}
                                                                onChange={(e) => setNewSuperUser({
                                                                    ...newSuperUser,
                                                                    name: e.target.value
                                                                })}
                                                                disabled={!!existingUserForSuperUser} // Disable if existing user
                                                            />
                                                        </div>

                                                        {/* Phone and DNI fields - only for new users */}
                                                        {!existingUserForSuperUser && (
                                                            <div className="grid grid-cols-2 gap-4">
                                                                <div>
                                                                    <Label htmlFor="superuser-phone"
                                                                           className="block mb-2">
                                                                        Teléfono (Opcional)
                                                                    </Label>
                                                                    <Input
                                                                        id="superuser-phone"
                                                                        type="tel"
                                                                        placeholder="+54 9 11 1234-5678"
                                                                        value={newSuperUser.phone}
                                                                        onChange={(e) => setNewSuperUser({
                                                                            ...newSuperUser,
                                                                            phone: e.target.value
                                                                        })}
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <Label htmlFor="superuser-dni"
                                                                           className="block mb-2">
                                                                        DNI (Opcional)
                                                                    </Label>
                                                                    <Input
                                                                        id="superuser-dni"
                                                                        type="text"
                                                                        placeholder="12345678"
                                                                        value={newSuperUser.dni}
                                                                        onChange={(e) => setNewSuperUser({
                                                                            ...newSuperUser,
                                                                            dni: e.target.value
                                                                        })}
                                                                    />
                                                                </div>
                                                            </div>
                                                        )}

                                                        {/* Password field removed - using temporary password */}
                                                        {!existingUserForSuperUser && (
                                                            <div>
                                                                <Label htmlFor="superuser-password-info"
                                                                       className="block mb-2">
                                                                    Contraseña
                                                                </Label>
                                                                <div
                                                                    className="text-sm text-gray-600 flex items-center p-2 bg-blue-50 rounded-md">
                                                                    <Key className="h-4 w-4 mr-2 text-blue-500"/>
                                                                    Se generará una contraseña temporal y se enviará por
                                                                    correo electrónico
                                                                    {newSuperUser.phone && newSuperUser.dni && (
                                                                        <span className="ml-2 text-green-600">
                                      • Se creará perfil de paciente automáticamente
                                    </span>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}

                                                        <div className="space-y-3 border-t pt-4 mt-2">
                                                            <Label className="text-base font-medium">Establecimientos y
                                                                Roles</Label>
                                                            <div
                                                                className="border rounded-md p-3 space-y-3 max-h-60 overflow-y-auto">
                                                                {medicalCenters.map(center => {
                                                                    // Check if this center is selected
                                                                    const isSelected = newSuperUser.medicalCenterIds.includes(center.id);

                                                                    // Find the role for this center if it exists
                                                                    const centerRole = newSuperUser.medicalCenterRoles?.find(
                                                                        mcr => mcr.medicalCenterId === center.id
                                                                    )?.role || UserRole.SUPERUSER; // Default to SUPERUSER

                                                                    return (
                                                                        <div key={center.id}
                                                                             className="flex flex-col space-y-2 pb-2 border-b border-gray-100">
                                                                            <div
                                                                                className="flex items-center justify-between">
                                                                                <div
                                                                                    className="flex items-center space-x-2">
                                                                                    <input
                                                                                        type="checkbox"
                                                                                        id={`center-${center.id}`}
                                                                                        checked={isSelected}
                                                                                        onChange={(e) => {
                                                                                            if (e.target.checked) {
                                                                                                // Add center to the list
                                                                                                const updatedIds = [...newSuperUser.medicalCenterIds, center.id];

                                                                                                // Initialize or update medicalCenterRoles
                                                                                                const updatedRoles = [...(newSuperUser.medicalCenterRoles || [])];
                                                                                                if (!updatedRoles.some(mcr => mcr.medicalCenterId === center.id)) {
                                                                                                    updatedRoles.push({
                                                                                                        medicalCenterId: center.id,
                                                                                                        role: newSuperUser.role // Use the currently selected role
                                                                                                    });
                                                                                                }

                                                                                                setNewSuperUser({
                                                                                                    ...newSuperUser,
                                                                                                    medicalCenterIds: updatedIds,
                                                                                                    medicalCenterId: updatedIds[0], // Set first as primary
                                                                                                    medicalCenterRoles: updatedRoles
                                                                                                });
                                                                                            } else {
                                                                                                // Remove center from the list
                                                                                                const updatedIds = newSuperUser.medicalCenterIds.filter(id => id !== center.id);

                                                                                                // Remove from medicalCenterRoles
                                                                                                const updatedRoles = (newSuperUser.medicalCenterRoles || []).filter(
                                                                                                    mcr => mcr.medicalCenterId !== center.id
                                                                                                );

                                                                                                setNewSuperUser({
                                                                                                    ...newSuperUser,
                                                                                                    medicalCenterIds: updatedIds,
                                                                                                    medicalCenterId: updatedIds.length > 0 ? updatedIds[0] : "", // Update primary
                                                                                                    medicalCenterRoles: updatedRoles
                                                                                                });
                                                                                            }
                                                                                        }}
                                                                                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                                                    />
                                                                                    <Label
                                                                                        htmlFor={`center-${center.id}`}
                                                                                        className="text-sm font-medium">
                                                                                        {center.name}
                                                                                    </Label>
                                                                                </div>

                                                                                {isSelected && (
                                                                                    <Select
                                                                                        value={centerRole}
                                                                                        onValueChange={(value) => {
                                                                                            const newRole = value as UserRole;

                                                                                            // Update the role for this medical center
                                                                                            const updatedRoles = [...(newSuperUser.medicalCenterRoles || [])];
                                                                                            const centerRoleIndex = updatedRoles.findIndex(mcr => mcr.medicalCenterId === center.id);

                                                                                            if (centerRoleIndex >= 0) {
                                                                                                // Update existing role
                                                                                                updatedRoles[centerRoleIndex].role = newRole;
                                                                                            } else {
                                                                                                // Add new role
                                                                                                updatedRoles.push({
                                                                                                    medicalCenterId: center.id,
                                                                                                    role: newRole
                                                                                                });
                                                                                            }

                                                                                            // If this is the primary medical center, also update the main role
                                                                                            const updatedUser = {
                                                                                                ...newSuperUser,
                                                                                                medicalCenterRoles: updatedRoles
                                                                                            };

                                                                                            if (center.id === newSuperUser.medicalCenterId) {
                                                                                                updatedUser.role = newRole;
                                                                                            }

                                                                                            setNewSuperUser(updatedUser);
                                                                                        }}
                                                                                    >
                                                                                        <SelectTrigger
                                                                                            className="w-[140px] h-8 text-xs">
                                                                                            <SelectValue
                                                                                                placeholder="Seleccionar rol"/>
                                                                                        </SelectTrigger>
                                                                                        <SelectContent>
                                                                                            <SelectItem
                                                                                                value={UserRole.SUPERUSER}>Superusuario</SelectItem>
                                                                                            <SelectItem
                                                                                                value={UserRole.ADMIN}>Administrador</SelectItem>
                                                                                            <SelectItem
                                                                                                value={UserRole.RECEPTIONIST}>Recepcionista</SelectItem>
                                                                                        </SelectContent>
                                                                                    </Select>
                                                                                )}
                                                                            </div>
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>
                                                            <p className="text-xs text-gray-500">Seleccione los centros
                                                                médicos y roles para este usuario.</p>
                                                        </div>
                                                    </div>

                                                    <DialogFooter>
                                                        <Button
                                                            type="submit"
                                                            onClick={handleCreateSuperUser}
                                                            disabled={Boolean(
                                                                existingUserForSuperUser
                                                                    ? (newSuperUser.medicalCenterIds.length === 0) ||
                                                                    // Disable for any user with admin roles (doctor or not)
                                                                    (existingUserForSuperUser.roles !== UserRole.DOCTOR ||
                                                                        (existingUserForSuperUser.medicalCenterRoles &&
                                                                            existingUserForSuperUser.medicalCenterRoles.some(mcr => mcr.role !== UserRole.DOCTOR)))
                                                                    : !newSuperUser.name || !newSuperUser.email || (newSuperUser.medicalCenterIds.length === 0)
                                                            )}
                                                        >
                                                            {existingUserForSuperUser
                                                                ? (existingUserForSuperUser.roles !== UserRole.DOCTOR ||
                                                                    (existingUserForSuperUser.medicalCenterRoles &&
                                                                        existingUserForSuperUser.medicalCenterRoles.some(mcr => mcr.role !== UserRole.DOCTOR)))
                                                                    ? "Use Editar Establecimientos"
                                                                    : "Actualizar Usuario"
                                                                : "Crear Usuario"
                                                            }
                                                        </Button>
                                                    </DialogFooter>
                                                </DialogContent>
                                            </Dialog>
                                        </div>
                                    </div>

                                    <div className="bg-white shadow overflow-hidden sm:rounded-md">
                                        <ul role="list" className="divide-y divide-gray-200">
                                            {storage.getUsers()
                                                // Include all users, even doctors who have admin/superuser/receptionist roles
                                                .filter(user => {
                                                    // Include users who have at least one non-doctor role
                                                    if (user.roles !== UserRole.DOCTOR) return true;

                                                    // Check if user has specific roles for medical centers
                                                    if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                                        // Include if they have at least one non-doctor role in any medical center
                                                        return user.medicalCenterRoles.some(mcr => mcr.role !== UserRole.DOCTOR);
                                                    }

                                                    // Filter out users who are only doctors
                                                    return false;
                                                })
                                                // Apply role filter
                                                .filter(user => {
                                                    if (userRoleFilter === "all") return true;

                                                    // Check if user has the filtered role in any medical center
                                                    if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                                        return user.medicalCenterRoles.some(mcr => mcr.role === userRoleFilter);
                                                    }

                                                    // Fall back to default role
                                                    return user.roles === userRoleFilter;
                                                })
                                                // Apply search filter
                                                .filter(user => {
                                                    if (!userSearchQuery) return true;
                                                    const query = userSearchQuery.toLowerCase();

                                                    // Search by user name
                                                    if (user.name.toLowerCase().includes(query)) return true;

                                                    // Search by email
                                                    if (user.email.toLowerCase().includes(query)) return true;

                                                    // Search by medical center name
                                                    const userCenters = medicalCenters.filter(center =>
                                                        user.medicalCenterIds?.includes(center.id) || center.id === user.medicalCenterId
                                                    );
                                                    if (userCenters.some(center => center.name.toLowerCase().includes(query))) return true;

                                                    return false;
                                                })
                                                .sort((a, b) => a.name.localeCompare(b.name))
                                                .map(user => {
                                                    // Get medical centers this user has access to
                                                    const userCenters = medicalCenters.filter(center =>
                                                        user.medicalCenterIds?.includes(center.id) || center.id === user.medicalCenterId
                                                    );
                                                    const isExpanded = expandedUserId === user.id;

                                                    return (
                                                        <li key={user.id}>
                                                            <div className="block hover:bg-gray-50">
                                                                <div className="px-4 py-4 sm:px-6">
                                                                    <div className="flex items-center justify-between">
                                                                        <div className="flex items-center">
                                                                            <div
                                                                                className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                                                                <UserIcon
                                                                                    className="h-5 w-5 text-purple-600"/>
                                                                            </div>
                                                                            <div>
                                                                                <p className="text-sm font-medium text-blue-600">{user.name}</p>
                                                                                <div
                                                                                    className="flex items-center text-sm text-gray-500">
                                                                                    <Mail className="h-3.5 w-3.5 mr-1"/>
                                                                                    {user.email}
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div className="ml-2 flex-shrink-0 flex">
                                                                            <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                                                {userCenters.length} {userCenters.length === 1 ? "centro" : "centros"}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                    <div className="mt-2 sm:flex sm:justify-between">
                                                                        <div className="sm:flex">
                                                                            <p className="flex items-center text-sm text-gray-500">
                                                                                <Building2
                                                                                    className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"/>
                                                                                {userCenters.length} centros médicos
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex items-center space-x-2">
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={() => {
                                                                                    // Generate a new temporary password
                                                                                    const tempPassword = `temp${Math.floor(100000 + Math.random() * 900000)}`;

                                                                                    // Update the user with the new password
                                                                                    const updatedUser = {
                                                                                        ...user,
                                                                                        password: tempPassword
                                                                                    };

                                                                                    // Save to storage
                                                                                    storage.saveUser(updatedUser);

                                                                                    // Show success message
                                                                                    toast.success(`Contraseña restablecida para ${user.name}`);

                                                                                    // Force a refresh
                                                                                    handleRefresh();
                                                                                }}
                                                                                className="flex items-center text-xs h-7"
                                                                            >
                                                                                <Key className="h-3 w-3 mr-1"/>
                                                                                Restablecer Contraseña
                                                                            </Button>
                                                                            <Button
                                                                                variant="ghost"
                                                                                size="sm"
                                                                                onClick={() => setExpandedUserId(isExpanded ? null : user.id)}
                                                                                className="mt-2 sm:mt-0"
                                                                            >
                                                                                {isExpanded ? (
                                                                                    <ChevronUp className="h-4 w-4"/>
                                                                                ) : (
                                                                                    <ChevronDown className="h-4 w-4"/>
                                                                                )}
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                {isExpanded && (
                                                                    <div className="px-4 py-3 bg-gray-50 border-t">
                                                                        <div className="flex flex-col space-y-4">
                                                                            <div
                                                                                className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                                <div>
                                                                                    <div
                                                                                        className="flex justify-between items-center">
                                                                                        <h4 className="text-sm font-medium mb-2">Establecimientos
                                                                                            asignados:</h4>
                                                                                        <Button
                                                                                            variant="outline"
                                                                                            size="sm"
                                                                                            className="flex items-center text-xs h-7"
                                                                                            onClick={() => {
                                                                                                setSelectedUser(user);
                                                                                                setIsEditUserDialogOpen(true);
                                                                                            }}
                                                                                        >
                                                                                            <Building2
                                                                                                className="h-3 w-3 mr-1"/>
                                                                                            Editar Establecimientos
                                                                                        </Button>
                                                                                    </div>
                                                                                    <div
                                                                                        className="flex flex-wrap gap-2 mt-2">
                                                                                        {userCenters.length > 0 ? (
                                                                                            userCenters.map(center => {
                                                                                                // Get the role for this specific medical center
                                                                                                let roleForCenter = user.roles; // Default to main role

                                                                                                // Check if user has specific roles for medical centers
                                                                                                if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                                                                                    const centerRole = user.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                                                    if (centerRole) {
                                                                                                        roleForCenter = centerRole.role;
                                                                                                    }
                                                                                                }

                                                                                                return (
                                                                                                    <span
                                                                                                        key={center.id}
                                                                                                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-700 border border-blue-100">
                                                    <Building2 className="h-3 w-3 mr-1"/>
                                                    <span className="mr-1">{center.name}</span>
                                                    <span
                                                        className={`ml-1 px-1.5 py-0.5 rounded-full text-xs ${getRoleBadgeColor(roleForCenter)}`}>
                                                      {getRoleName(roleForCenter)}
                                                    </span>
                                                  </span>
                                                                                                );
                                                                                            })
                                                                                        ) : (
                                                                                            <p className="text-sm text-gray-500">No
                                                                                                hay centros médicos
                                                                                                asignados</p>
                                                                                        )}
                                                                                    </div>
                                                                                </div>

                                                                                <div
                                                                                    className="bg-blue-50 p-4 rounded-md">
                                                                                    <h4 className="text-sm font-medium text-blue-800 mb-2">Credenciales
                                                                                        de acceso:</h4>
                                                                                    <div className="space-y-1">
                                                                                        <p className="text-sm"><span
                                                                                            className="font-medium">Email:</span> {user.email}
                                                                                        </p>
                                                                                        <p className="text-sm">
                                                                                            <span
                                                                                                className="font-medium">Contraseña:</span>
                                                                                            <span
                                                                                                className="bg-yellow-100 text-yellow-800 px-1 rounded ml-1">{user.password}</span>
                                                                                        </p>
                                                                                        <p className="text-xs text-gray-500 mt-2">El
                                                                                            superusuario puede acceder
                                                                                            en:
                                                                                            /plataforma/establecimiento/login</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </li>
                                                    );
                                                })}

                                            {storage.getUsers()
                                                // Filter out doctors
                                                .filter(user => {
                                                    // Check if user is a doctor in any medical center
                                                    if (user.roles === UserRole.DOCTOR) return false;

                                                    // Check if user has specific roles for medical centers
                                                    if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                                        // Check if any of the roles is DOCTOR
                                                        return !user.medicalCenterRoles.some(mcr => mcr.role === UserRole.DOCTOR);
                                                    }

                                                    return true;
                                                })
                                                // Apply role filter
                                                .filter(user => {
                                                    if (userRoleFilter === "all") return true;

                                                    // Check if user has the filtered role in any medical center
                                                    if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                                        return user.medicalCenterRoles.some(mcr => mcr.role === userRoleFilter);
                                                    }

                                                    // Fall back to default role
                                                    return user.roles === userRoleFilter;
                                                })
                                                // Apply search filter
                                                .filter(user => {
                                                    if (!userSearchQuery) return true;
                                                    const query = userSearchQuery.toLowerCase();
                                                    if (user.name.toLowerCase().includes(query)) return true;
                                                    if (user.email.toLowerCase().includes(query)) return true;
                                                    const userCenters = medicalCenters.filter(center =>
                                                        user.medicalCenterIds?.includes(center.id) || center.id === user.medicalCenterId
                                                    );
                                                    if (userCenters.some(center => center.name.toLowerCase().includes(query))) return true;
                                                    return false;
                                                }).length === 0 && (
                                                <li className="px-4 py-6 text-center">
                                                    <UserIcon className="h-12 w-12 mx-auto text-gray-400 mb-3"/>
                                                    <h3 className="text-lg font-medium text-gray-900 mb-1">No se
                                                        encontraron usuarios</h3>
                                                    <p className="text-sm text-gray-500 mb-4">
                                                        {userRoleFilter !== "all"
                                                            ? `No hay usuarios con el rol ${getRoleName(userRoleFilter as UserRole)}.`
                                                            : userSearchQuery
                                                                ? "No hay usuarios que coincidan con su búsqueda."
                                                                : "No hay usuarios registrados en el sistema."}
                                                    </p>
                                                    <Button
                                                        onClick={() => setIsSuperUserDialogOpen(true)}
                                                        className="flex items-center mx-auto"
                                                    >
                                                        <UserPlus className="h-4 w-4 mr-2"/>
                                                        Crear Usuario
                                                    </Button>
                                                </li>
                                            )}
                                        </ul>
                                    </div>
                                </div>

                                {/* Edit User Dialog */}
                                <Dialog open={isEditUserDialogOpen} onOpenChange={setIsEditUserDialogOpen}>
                                    <DialogContent className="sm:max-w-3xl">
                                        <DialogHeader>
                                            <DialogTitle>Editar Establecimientos y Roles</DialogTitle>
                                            <DialogDescription>
                                                Modifique los establecimientos y roles a los que tiene acceso este
                                                usuario.
                                            </DialogDescription>
                                        </DialogHeader>

                                        {formError && (
                                            <div className="bg-red-50 border-l-4 border-red-400 p-4 my-2">
                                                <div className="flex">
                                                    <div className="flex-shrink-0">
                                                        <AlertCircle className="h-5 w-5 text-red-500"/>
                                                    </div>
                                                    <div className="ml-3">
                                                        <p className="text-sm text-red-700">{formError}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {selectedUser && (
                                            <div className="space-y-4">
                                                <div className="flex items-center space-x-3 mb-4">
                                                    <div
                                                        className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                                                        <UserIcon className="h-5 w-5 text-purple-600"/>
                                                    </div>
                                                    <div>
                                                        <h4 className="font-medium">{selectedUser.name}</h4>
                                                        <p className="text-sm text-gray-500">{selectedUser.email}</p>
                                                    </div>
                                                </div>

                                                <div className="space-y-3">
                                                    <Label className="text-base font-medium">Establecimientos y
                                                        Roles</Label>
                                                    <div
                                                        className="border rounded-md p-3 space-y-2 max-h-60 overflow-y-auto">
                                                        {medicalCenters.map(center => {
                                                            // Check if this center is assigned to the user
                                                            const isAssigned = selectedUser.medicalCenterIds?.includes(center.id) ||
                                                                selectedUser.medicalCenterId === center.id;

                                                            // Get the role for this specific medical center
                                                            let roleForCenter = selectedUser.roles; // Default to main role
                                                            if (selectedUser.medicalCenterRoles && selectedUser.medicalCenterRoles.length > 0) {
                                                                const centerRole = selectedUser.medicalCenterRoles.find(mcr => mcr.medicalCenterId === center.id);
                                                                if (centerRole) {
                                                                    roleForCenter = centerRole.role;
                                                                }
                                                            }

                                                            return (
                                                                <div key={center.id}
                                                                     className="flex items-center justify-between py-2 border-b last:border-b-0">
                                                                    <div className="flex items-center space-x-2">
                                                                        <input
                                                                            type="checkbox"
                                                                            id={`edit-center-${center.id}`}
                                                                            checked={isAssigned}
                                                                            onChange={(e) => {
                                                                                // Initialize medicalCenterIds if it doesn't exist
                                                                                const currentIds = selectedUser.medicalCenterIds ||
                                                                                    (selectedUser.medicalCenterId ? [selectedUser.medicalCenterId] : []);

                                                                                let newIds;
                                                                                if (e.target.checked) {
                                                                                    // Add center if not already included
                                                                                    newIds = currentIds.includes(center.id) ?
                                                                                        currentIds : [...currentIds, center.id];

                                                                                    // Initialize or update medicalCenterRoles
                                                                                    const updatedRoles = [...(selectedUser.medicalCenterRoles || [])]
                                                                                    if (!updatedRoles.some(mcr => mcr.medicalCenterId === center.id)) {
                                                                                        updatedRoles.push({
                                                                                            medicalCenterId: center.id,
                                                                                            role: selectedUser.role // Default to user's main role
                                                                                        })
                                                                                    }

                                                                                    setSelectedUser({
                                                                                        ...selectedUser,
                                                                                        medicalCenterIds: newIds,
                                                                                        // Always keep the first one as the primary for backward compatibility
                                                                                        medicalCenterId: newIds[0],
                                                                                        medicalCenterRoles: updatedRoles
                                                                                    });
                                                                                } else {
                                                                                    // Remove center, but ensure at least one remains
                                                                                    newIds = currentIds.filter(id => id !== center.id);
                                                                                    if (newIds.length === 0) {
                                                                                        // Don't allow removing the last center
                                                                                        newIds = currentIds;
                                                                                        toast.error("El usuario debe tener acceso a al menos un establecimiento");
                                                                                        return;
                                                                                    }

                                                                                    // Remove from medicalCenterRoles
                                                                                    const updatedRoles = (selectedUser.medicalCenterRoles || []).filter(
                                                                                        mcr => mcr.medicalCenterId !== center.id
                                                                                    );

                                                                                    // Update the user with new medical center IDs
                                                                                    setSelectedUser({
                                                                                        ...selectedUser,
                                                                                        medicalCenterIds: newIds,
                                                                                        // Always keep the first one as the primary for backward compatibility
                                                                                        medicalCenterId: newIds[0],
                                                                                        medicalCenterRoles: updatedRoles
                                                                                    });
                                                                                }
                                                                            }}
                                                                            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                                        />
                                                                        <Label htmlFor={`edit-center-${center.id}`}
                                                                               className="text-sm font-medium">
                                                                            {center.name}
                                                                        </Label>
                                                                    </div>

                                                                    {isAssigned && (
                                                                        <div className="flex items-center space-x-2">
                                                                            <span
                                                                                className="text-sm text-gray-500">Rol:</span>
                                                                            <Select
                                                                                value={roleForCenter}
                                                                                onValueChange={(value) => {
                                                                                    const newRole = value as UserRole;

                                                                                    // Update the role for this medical center
                                                                                    const updatedRoles = [...(selectedUser.medicalCenterRoles || [])]
                                                                                    const centerRoleIndex = updatedRoles.findIndex(mcr => mcr.medicalCenterId === center.id)

                                                                                    if (centerRoleIndex >= 0) {
                                                                                        // Update existing role
                                                                                        updatedRoles[centerRoleIndex].role = newRole
                                                                                    } else {
                                                                                        // Add new role
                                                                                        updatedRoles.push({
                                                                                            medicalCenterId: center.id,
                                                                                            role: newRole
                                                                                        })
                                                                                    }

                                                                                    // If this is the primary medical center, also update the main role
                                                                                    const updatedUser = {
                                                                                        ...selectedUser,
                                                                                        medicalCenterRoles: updatedRoles
                                                                                    }

                                                                                    if (center.id === selectedUser.medicalCenterId) {
                                                                                        updatedUser.role = newRole
                                                                                    }

                                                                                    setSelectedUser(updatedUser)
                                                                                }}
                                                                                disabled={!isAssigned}
                                                                            >
                                                                                <SelectTrigger
                                                                                    className="w-[140px] h-8 text-xs">
                                                                                    <SelectValue
                                                                                        placeholder="Seleccionar rol"/>
                                                                                </SelectTrigger>
                                                                                <SelectContent>
                                                                                    <SelectItem
                                                                                        value={UserRole.SUPERUSER}>Superusuario</SelectItem>
                                                                                    <SelectItem
                                                                                        value={UserRole.ADMIN}>Administrador</SelectItem>
                                                                                    <SelectItem
                                                                                        value={UserRole.RECEPTIONIST}>Recepcionista</SelectItem>
                                                                                </SelectContent>
                                                                            </Select>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            );
                                                        })}
                                                    </div>
                                                    <p className="text-xs text-gray-500">Seleccione los centros médicos
                                                        y roles para este usuario.</p>
                                                </div>
                                            </div>
                                        )}

                                        <DialogFooter>
                                            <Button variant="outline" onClick={() => {
                                                setIsEditUserDialogOpen(false);
                                                setFormError(null);
                                            }}>Cancelar</Button>
                                            <Button
                                                onClick={handleEditSuperUser}
                                                disabled={!selectedUser || (selectedUser.medicalCenterIds?.length === 0 && !selectedUser.medicalCenterId)}
                                            >
                                                Guardar Cambios
                                            </Button>
                                        </DialogFooter>
                                    </DialogContent>
                                </Dialog>
                            </TabsContent>

                            <TabsContent value="bulk-create">
                                <AdminBulkCreateForms/>
                            </TabsContent>
                        </Tabs>
                    </div>
                )}
            </main>

            {/* Reset Dialog */}
            <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Restablecer datos de la aplicación</DialogTitle>
                        <DialogDescription>
                            Esta acción limpiará los datos almacenados localmente, eliminará todas las cookies y
                            restablecerá la aplicación.
                        </DialogDescription>
                    </DialogHeader>

                    {resetError && (
                        <div className="bg-red-50 border-l-4 border-red-400 p-4">
                            <div className="flex">
                                <div className="ml-3">
                                    <p className="text-sm text-red-700">{resetError}</p>
                                </div>
                            </div>
                        </div>
                    )}

                    {resetComplete ? (
                        <div className="bg-green-50 p-4 rounded-md">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <Check className="h-5 w-5 text-green-500"/>
                                </div>
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-green-800">¡Restablecimiento
                                        completado!</h3>
                                    <div className="mt-2 text-sm text-green-700">
                                        <p>
                                            Los datos de la aplicación y las cookies han sido restablecidos
                                            correctamente.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="text-sm text-red-600 mb-4">
                            ¡Atención! Esta operación no se puede deshacer y restablecerá todos los datos a sus valores
                            iniciales. También eliminará todas las cookies, lo que cerrará la sesión actual.
                        </div>
                    )}

                    {!resetComplete && (
                        <div className="mt-4 space-y-2">
                            <div className="flex items-center">
                                <input
                                    type="radio"
                                    id="reset-full"
                                    name="reset-type"
                                    value="full"
                                    defaultChecked={true}
                                    className="h-4 w-4 text-blue-600"
                                    onChange={() => {
                                    }} // Empty onChange handler to avoid React warning
                                />
                                <label htmlFor="reset-full" className="text-sm text-gray-700">
                                    Restablecer todos los datos
                                </label>
                            </div>
                        </div>
                    )}

                    <DialogFooter className="flex justify-between space-x-2">
                        <Button
                            variant="outline"
                            onClick={() => setIsResetDialogOpen(false)}
                        >
                            Cancelar
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={handleReset}
                            disabled={isResetting}
                        >
                            {isResetting ? (
                                <>
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin"/>
                                    Restableciendo...
                                </>
                            ) : (
                                "Restablecer Datos"
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Footer */}
            <footer className="bg-white border-t border-gray-200 mt-8">
                <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-500">
                            &copy; {new Date().getFullYear()} Turnera. Todos los derechos reservados.
                        </div>
                        <div className="flex items-center space-x-3 text-sm text-gray-500">
                            <span>Panel de Administración v1.0</span>
                            <Link href="/admin/reset" className="text-gray-400 hover:text-red-500 transition-colors">
                                Reset
                            </Link>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    )
}