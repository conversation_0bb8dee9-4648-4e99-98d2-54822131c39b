"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Clock } from "lucide-react"

export default function MedicalCenterDoctorCardSkeleton() {
  return (
    <Card className="group relative overflow-hidden hover:shadow-md transition-all duration-300 border border-gray-200 rounded-lg card-grid-item w-[22rem] animate-[verySlowPulse_15s_ease-in-out_infinite]">
      {/* Card Header with <PERSON><PERSON><PERSON> and <PERSON><PERSON> */}
      <CardHeader className="bg-gradient-to-br from-blue-500 to-blue-700 group-hover:from-blue-500 group-hover:to-blue-600 transition-colors duration-300 p-4 relative overflow-hidden border-b border-blue-600/30">
        <div
          className="absolute inset-0 z-0 opacity-[0.05]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='0 0 10 10'%3E%3C<PERSON>rcle fill='%23ffffff' cx='2' cy='2' r='0.75'/%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat',
            backgroundSize: '10px 10px'
          }}
        ></div>
        <div className="flex items-center relative z-10 gap-3">
          <div className="w-10 h-10 rounded-full bg-primary-foreground text-primary flex items-center justify-center text-lg font-semibold shadow-sm shrink-0">
            <div className="w-6 h-6 bg-blue-300 rounded-full"></div>
          </div>
          <div className="flex-1 min-w-0">
            <CardTitle className="text-primary-foreground group-hover:text-white transition-colors duration-300 text-base sm:text-lg font-semibold truncate">
              <div className="h-5 bg-white/30 rounded w-3/4"></div>
            </CardTitle>
            <div className="text-sm text-blue-100 group-hover:text-blue-50 transition-colors duration-300 mt-0.5 line-clamp-1">
              <div className="h-4 bg-white/20 rounded w-1/2 mt-1"></div>
            </div>
          </div>
        </div>
      </CardHeader>

      {/* Card Content with Status */}
      <CardContent className="p-4">
        <div className="flex items-center justify-between gap-2 mb-3">
          <div className="flex items-center gap-2">
            <div className="w-2.5 h-2.5 rounded-full bg-gray-300"></div>
            <span className="text-xs sm:text-sm font-medium text-muted-foreground">
              <div className="h-4 bg-gray-200 rounded w-40"></div>
            </span>
          </div>

          {/* Appointment Count Badge */}
          <span className="bg-blue-100 text-blue-800 text-xs font-semibold py-0.5 px-2.5 rounded-full shrink-0">
            <div className="h-3 bg-blue-200 rounded w-16"></div>
          </span>
        </div>
      </CardContent>

      {/* Card Footer */}
      <CardFooter className="bg-muted/50 border-t p-3 flex justify-between items-center group-hover:bg-muted/70 transition-colors duration-300">
        <div className="flex items-center gap-1.5">
          <Clock className="h-4 w-4 text-primary" />
          <div className="h-4 bg-gray-200 rounded w-20"></div>
        </div>
        <div className="text-xs text-muted-foreground">
          <div className="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </CardFooter>
    </Card>
  )
}
