"use client"

import {useEffect} from "react"
import {use<PERSON><PERSON><PERSON>, useRouter} from "next/navigation"
import {useAuth} from "@/hooks/useAuth"
import {UserRole} from "@/types/users"

export default function DoctorLayout({
                                         children,
                                     }: {
    children: React.ReactNode
}) {
    const params = useParams()
    const router = useRouter()
    const doctorId = params.doctorId as string

    const {currentUser, isAuthenticated, isLoading} = useAuth()

    // Authentication check
    useEffect(() => {
        // This layout should never apply to reset-password page, but check just in case
        if (window.location.pathname.includes('/reset-password')) {
            console.log('DoctorLayout - Detected reset-password page, skipping auth check');
            return;
        }

        if (!isLoading && !isAuthenticated) {
            // Not authenticated, redirect to login
            router.push('/plataforma/profesional/login')
            return
        }

        if (isAuthenticated && currentUser) {
            // Check if user has a doctor role anywhere
            const hasDoctorRole = currentUser.roles === UserRole.DOCTOR ||
                (currentUser.medicalCenterRoles &&
                    currentUser.medicalCenterRoles.some(mcr => mcr.role === UserRole.DOCTOR));

            if (!hasDoctorRole) {
                // Non-doctor users should not access this page
                console.warn(`User ${currentUser.name} with role ${currentUser.roles} attempted to access doctor page for doctor ${doctorId}`)
                router.push('/plataforma/unauthorized')
                return
            }

            // If user is a doctor, check if they're accessing their own page
            if (currentUser.doctorId && currentUser.doctorId !== doctorId) {
                console.warn(`Doctor ${currentUser.name} attempted to access page for doctor ${doctorId}`)
                // Redirect to their own page
                router.push(`/plataforma/profesional/${currentUser.doctorId}`)
                return
            }
        }
    }, [isLoading, isAuthenticated, currentUser, doctorId, router])

    // Only handle authentication, don't add any UI elements
    return children
}
