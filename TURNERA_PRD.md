# Product Requirements Document (PRD)
# Turnera Medical Scheduling Platform

![Turnera Logo](public/images/turnera-logo.svg)

## Table of Contents

1. [Introduction](#introduction)
2. [Product Overview](#product-overview)
3. [User Personas](#user-personas)
4. [App Design & Layout](#app-design-layout)
5. [Key Features](#key-features)
6. [Software Architecture](#software-architecture)
7. [Component Interactions](#component-interactions)
8. [Software Rules](#software-rules)
9. [User Stories](#user-stories)
10. [Data Models](#data-models)
11. [Technical Requirements](#technical-requirements)
12. [Non-Functional Requirements](#non-functional-requirements)
13. [Future Enhancements](#future-enhancements)
14. [Glossary](#glossary)

## Introduction

Turnera is a double-sided marketplace for medical appointments, connecting healthcare providers with patients. This PRD focuses specifically on the medical personnel platform, which enables doctors and medical staff to manage their availability, appointments, and patient information.

### Purpose of Document

This document provides a comprehensive definition of the Turnera medical scheduling platform's features, functionality, requirements, and design specifications. It serves as the foundation for development, testing, and stakeholder alignment.

## Product Overview

### Mission

To provide a comprehensive, user-friendly scheduling and patient management platform for medical professionals that seamlessly integrates with a patient-facing appointment booking system.

### Target Market

- **Primary**: Medical professionals (doctors, specialists)
- **Secondary**: Medical administrators and support staff
- **Tertiary**: Medical facilities and healthcare institutions

### Business Goals

1. Streamline appointment scheduling for healthcare providers
2. Reduce no-shows and administrative overhead
3. Improve patient access to healthcare services
4. Provide insights and analytics to optimize provider schedules

### Monetization Strategy

The platform operates on a freemium model with the following revenue streams:

1. **Free Core Features**
   - Complete medical scheduling system
   - Doctor and patient management
   - Schedule configuration
   - Appointment booking
   - Insurance integration
   - Basic reporting

2. **Reminder Packs**
   - Doctors can purchase reminder packs to send automated notifications to patients
   - Reminders are sent via WhatsApp, SMS, and Email (counts as one reminder)
   - Available pack sizes and prices:
     - 20 reminders: $5,500
     - 50 reminders: $11,000
     - 100 reminders: $19,000
     - 200 reminders: $32,000
     - 500 reminders: $45,000
     - 1,000 reminders: $70,000
     - 2,000 reminders: $120,000
   - Automatic pack renewal available
   - Reminders are only counted when sent (cancellations pre-reminder don't count)

3. **New Patient Fee**
   - $500 fee for each new patient booking through the platform
   - Only applies to patients who haven't seen the doctor before
   - Fee is charged when the appointment reminder is sent
   - No fee if the appointment is cancelled before the reminder is sent

4. **Individual Reminder Pricing**
   - $300 per individual reminder for appointments booked through the platform
   - Only applies when no reminder pack is active
   - Automatically deducted from active reminder pack if available
   - No charge if the appointment is cancelled before the reminder is sent

## User Personas

### Dr. Maria González
- **Role**: Family physician at a multi-doctor clinic
- **Technical Proficiency**: Moderate
- **Goals**: Efficiently manage appointments, minimize schedule gaps, ensure care continuity
- **Pain Points**: Double-bookings, no-shows, uneven distribution of urgent care

### Carlos Rodríguez
- **Role**: Medical center administrator
- **Technical Proficiency**: High
- **Goals**: View overall clinic schedule, manage multiple doctors, report generation
- **Pain Points**: Schedule conflicts, manual overrides, insurance billing complications

### Nurse Patricia Vega
- **Role**: Nurse who handles phone appointments and walk-ins
- **Technical Proficiency**: Low to moderate
- **Goals**: Quickly book appointments, handle urgent cases, manage patient check-ins
- **Pain Points**: Limited schedule visibility, unclear doctor availability

## App Design Layout

### Authentication Layer

- **Login Screen**
  - Email and password authentication
  - Security measures (2FA capability)
  - Forgot password flow

### Navigation Structure

- **Main Navigation**
  - Dashboard/Home
  - Schedule Views (Daily, Weekly, Monthly)
  - Patient Management
  - Appointment Booking
  - Settings/Configuration
  - Center/Establishment Administration

### Dashboard Views

- **Center/Establishment Dashboard**
  - Overview of all doctors' schedules
  - Daily appointment summary
  - Quick action buttons (add appointment, doctor, patient)
  - Statistics (today's appointments, no-shows, cancellations)

- **Doctor Dashboard**
  - Individual doctor schedule
  - Upcoming appointments
  - Patient quick view
  - Consultation type statistics

### Schedule Management

- **Schedule Configuration Interface**
  - Working days/hours setup
  - Appointment slot duration
  - Exception dates (vacations, holidays)
  - Overbooking settings

- **Calendar Views**
  - Day view with time slots
  - Week view with doctor columns
  - Month overview
  - List view for appointments

### Appointment Management

- **Appointment Creation Flow**
  - Patient selection/creation
  - Date and time selection
  - Consultation type selection
  - Insurance/payment information
  - Confirmation and notifications

- **Appointment Actions**
  - Check-in functionality
  - Reschedule interface
  - Cancellation flow
  - No-show marking
  - Follow-up scheduling

### Configuration Interfaces

- **Doctor Configuration**
  - Personal information
  - Specialties management
  - Schedule configuration
  - Consultation types setup
  - Insurance/coverage settings

- **Medical Center Configuration**
  - Center details
  - Doctor association
  - Global settings (working hours, holidays)
  - Branding settings

## Key Features

### Doctor Management

- **Functionality**:
  - Create, update, and delete doctor profiles
  - Configure specialties, credentials, and contact information
  - Set doctor-specific appointment durations and booking rules
  - Doctor schedule management

- **Priority**: High
- **Dependencies**: Medical Center Configuration

### Schedule Management

- **Functionality**:
  - Define working days and hours per doctor
  - Configure date-specific exceptions (holidays, special hours)
  - Set booking advance notice requirements
  - Configure appointment slot durations
  - Overbooking policies

- **Priority**: High
- **Dependencies**: Doctor Configuration

### Appointment Booking & Management

- **Functionality**:
  - Book appointments for patients
  - Manage appointment status (scheduled, confirmed, canceled, completed)
  - Handle appointment rescheduling
  - Track appointment history
  - Send notifications/reminders

- **Priority**: High
- **Dependencies**: Schedule Management, Patient Records

### Patient Records

- **Functionality**:
  - Create and manage patient profiles
  - Store contact and demographic information
  - Manage insurance/coverage details
  - View appointment history
  - Store relevant medical history notes

- **Priority**: Medium
- **Dependencies**: None

### Medical Insurance Integration

- **Functionality**:
  - Configure insurance providers and plans
  - Set up co-payment amounts
  - Define excluded coverages by plan or provider
  - Track insurance approvals

- **Priority**: Medium
- **Dependencies**: Patient Records

### Analytics & Reporting

- **Functionality**:
  - Generate schedule utilization reports
  - Track no-show and cancellation rates
  - Analyze patient demographics
  - Export data for billing purposes

- **Priority**: Low
- **Dependencies**: Appointment Management

## Software Architecture

### Technology Stack

- **Frontend**: Next.js 15 with TypeScript
- **UI Components**:
  - Custom UI components with Radix UI primitives
  - MUI components (Material UI)
  - Styled with Tailwind CSS
- **State Management**: React Context API
- **Styling**:
  - Tailwind CSS
  - Emotion/Styled Components (via MUI)
- **Date Handling**: date-fns
- **Data Visualization**: Recharts
- **HTTP Client**: Axios
- **Notifications**: React Toastify
- **Animations**: Framer Motion
- **Build System**: Turbopack
- **Data Persistence**: Local Storage with fallback to initial data
- **Cross-Tab Communication**: Storage events for real-time synchronization

### Directory Structure

```
medical-scheduler/
├── app/                    # Next.js app directory (pages)
│   ├── admin/             # Admin panel and configuration
│   │   ├── layout.tsx     # Admin layout wrapper
│   │   ├── page.tsx       # Main admin dashboard
│   │   └── reset.tsx      # Admin reset functionality
│   ├── plataforma/        # Main platform pages
│   │   ├── establecimiento/ # Medical center management
│   │   │   └── [medicalCenterId]/ # Dynamic medical center routes
│   │   │       ├── [doctorId]/   # Doctor-specific views
│   │   │       ├── analytics/    # Center analytics
│   │   │       ├── configuration/ # Center settings
│   │   │       └── general/      # General information
│   │   ├── profesional/   # Doctor platform
│   │   │   └── [doctorId]/ # Doctor-specific routes
│   │   │       ├── agenda/  # Schedule management
│   │   │       └── analytics/ # Doctor analytics
│   │   └── login/         # Authentication pages
│   ├── reset/             # Password reset functionality
│   ├── layout.tsx         # Root layout component
│   └── page.tsx           # Landing page
├── components/            # Reusable React components
│   ├── admin/            # Admin-specific components
│   │   └── configuration/ # Admin config components
│   │       ├── AdminBulkCreateForms.tsx
│   │       ├── MultiDoctorForm.tsx
│   │       └── MultiMedicalCenterForm.tsx
│   ├── configuration/    # General configuration components
│   │   ├── AddCoverageDialog.tsx
│   │   ├── CoverageCard.tsx
│   │   ├── DoctorCard.tsx
│   │   ├── DoctorDialog.tsx
│   │   └── MedicalCenterConfigCard.tsx
│   ├── ui/               # Base UI components
│   ├── dashboard/        # Dashboard components
│   ├── doctorscheduleviews/ # Doctor schedule views
│   ├── schedulecomponents/  # Schedule-related components
│   └── generalscheduleviews/ # General schedule views
├── contexts/             # React Context providers
│   ├── AppStateContext  # Global UI state management
│   ├── DoctorContext    # Doctor data and operations
│   └── MedicalCenterContext # Medical center management
├── data/                 # Static data and configurations
│   ├── initialData.ts    # Initial doctors and centers data
│   ├── consultationTypes.ts # Predefined consultation types
│   └── specialties.ts    # Medical specialties list
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries and helpers
├── public/               # Static assets and images
│   └── images/          # Image assets including logos
├── services/             # API and data services
│   └── storage.ts       # Local storage service
├── styles/               # Global styles and Tailwind config
├── types/                # TypeScript type definitions
└── utils/                # Utility functions and helpers
```

## Component Interactions

### Data Management Architecture

The application uses a three-tier data management architecture:

1. **Initial Data Layer**
   - Centralized in `data/initialData.ts`
   - Provides default data for doctors and medical centers
   - Serves as fallback when local storage is empty or unavailable

2. **Storage Service Layer**
   - Implemented in `services/storage.ts`
   - Manages local storage operations with consistent prefixed keys
   - Handles saving and retrieving data with fallbacks to initial data
   - Provides storage initialization to ensure consistent initial state

3. **Context Layer**
   - React Context providers for state management
   - Handles UI state and business logic
   - Interacts with storage service for data persistence
   - Maintains in-memory state synchronized with local storage

### Context Hierarchy and Data Flow

The application follows a hierarchical context structure for state management:

1. **AppStateContext** (Top Level)
   - Manages UI state across the application
   - Controls active configuration tabs
   - Provides global app state

2. **MedicalCenterContext**
   - Loads medical centers from storage service
   - Manages active medical center selection
   - Persists changes to local storage through the storage service
   - Provides data and operations to child contexts

3. **DoctorContext**
   - Uses active medical center ID from MedicalCenterContext
   - Loads doctors for the active center using the useDoctors hook
   - Manages doctor selection, editing, and CRUD operations
   - Updates medical center doctor list when doctors are added/removed
   - Handles synchronization between in-memory state and local storage

4. **Child Contexts**
   - **ScheduleContext**: Manages schedule operations for selected doctor
   - **ConsultationContext**: Handles consultation types for doctors
   - **CoverageContext**: Manages insurance coverage configurations
   - **AppointmentContext**: Handles appointment operations

### Data Flow Sequence

1. **Initialization Flow**
   - Application starts
   - `storage.initializeStorage()` ensures local storage is populated
   - Context providers load from local storage or fall back to initial data
   - Components render with the loaded data

2. **Medical Center Selection Flow**
   - User selects a medical center
   - `MedicalCenterContext` updates active center ID in state and local storage
   - `DoctorContext` detects the change and loads doctors for that center
   - UI updates to show the selected center's data

3. **Doctor Operation Flow**
   - User performs doctor CRUD operation
   - `DoctorContext` updates doctor data in state
   - Changes are persisted to local storage
   - Medical center's doctor list is updated if necessary
   - UI refreshes to show the updated doctor data

4. **Configuration Update Flow**
   - User edits doctor configuration
   - `editedConfig` in `DoctorContext` stores temporary changes
   - When saved, changes are applied to the doctor in state
   - Updated doctor data is saved to local storage
   - Storage events are triggered to notify other tabs of the changes
   - UI updates to reflect the changes in all open tabs

### Key Hooks

- **useDoctors**
  - Handles doctor CRUD operations for a specific medical center
  - Manages loading state during data retrieval
  - Provides methods to add, edit, and remove doctors
  - Handles synchronization with local storage
  - Supports refreshing doctors from storage

### Storage Keys

The application uses consistent key prefixes for local storage:

- `medical-scheduler-centers`: All medical centers
- `medical-scheduler-active-center`: ID of the active medical center
- `medical-scheduler-doctors_[centerId]`: Doctors for a specific medical center
- `medical-scheduler-doctor-id-map`: Map of doctor MN to doctor ID for consistent reference
- `medical-scheduler-unassigned-doctors`: Doctors not assigned to any medical center
- `appointments`: Patient appointments organized by date

This design ensures:
1. No key collisions with other applications
2. Efficient data retrieval (only loading doctors for the active center)
3. Data persistence across page refreshes and browser sessions
4. Clear separation of concerns between contexts and storage
5. Consistent doctor identity across medical centers
6. Real-time synchronization between multiple browser tabs

## Software Rules

### Authentication Rules

1. All users must authenticate before accessing the system
2. Authentication session expires after 24 hours of inactivity
3. Failed login attempts are limited to 5 before temporary lockout

### Medical Center Rules

1. Each medical center can have multiple doctors
2. Medical center administrators can view and manage all doctors within their center
3. Global center settings (working hours, holidays) serve as defaults for new doctors
4. Centers can customize their own branding elements

### Doctor Management Rules

1. Doctors must belong to at least one medical center
2. Each doctor must have at least one specialty defined
3. Doctor profiles require mandatory fields: name, medical license number, specialties
4. Doctors' center-specific workday settings don't override global configurations, just that center's settings.

### Schedule Rules

1. Working days must have at least one time range defined
2. Time ranges cannot overlap for the same day
3. Start time must be earlier than end time for all time ranges
4. Exception dates override regular working day schedules
5. Doctors can configure recurring schedules with custom frequencies

### Appointment Rules

1. Appointments cannot be scheduled outside working hours
2. Double booking prevention based on slot duration (unless overbooking enabled)
3. Appointments can have one of these statuses: scheduled, confirmed, in-progress, completed, canceled, no-show
4. Appointments require a patient, date/time, and consultation type
5. Appointments cannot be scheduled in the past
6. Online booking requires minimum advance notice (configurable per doctor)

### Consultation Type Rules

1. Each doctor must have at least one consultation type defined
2. Consultation types determine appointment duration (default or multiple slots)
3. Types can be configured for online booking availability with specific hours
4. Types can have specific pricing and insurance coverage rules
5. Daily limits can be set for specific consultation types

### Insurance/Coverage Rules

1. Insurance providers can have multiple plans
2. Doctors can exclude specific insurance providers or plans
3. Consultation types can have different co-pay amounts per insurance plan
4. Private pay options can be enabled/disabled per doctor and consultation type

## User Stories

### Authentication

#### Primary Scenarios

1. **Login to Platform**
   - **As a** medical staff member
   - **I want to** securely log in to the platform
   - **So that** I can access the scheduling system
   - **Acceptance Criteria**:
     - Enter email and password
     - System authenticates credentials
     - Redirect to appropriate dashboard based on role

2. **Password Recovery**
   - **As a** user who forgot their password
   - **I want to** reset my password
   - **So that** I can regain access to my account
   - **Acceptance Criteria**:
     - Request password reset via email
     - Receive email with reset instructions
     - Create and confirm new password
     - Log in with new credentials

#### Edge Cases

1. **Account Lockout**
   - **As a** security system
   - **I want to** temporarily lock accounts after multiple failed login attempts
   - **So that** unauthorized access is prevented
   - **Acceptance Criteria**:
     - Lock account after 5 failed attempts
     - Provide clear error message
     - Unlock account after 30 minutes or via admin action

### Medical Center Administration

#### Primary Scenarios

1. **View Center Dashboard**
   - **As a** medical center administrator
   - **I want to** view an overview of all doctors and appointments
   - **So that** I can monitor the center's operations
   - **Acceptance Criteria**:
     - Display summary statistics
     - Show today's appointments across doctors
     - Highlight potential schedule conflicts
     - Provide quick access to common actions

2. **Manage Doctors**
   - **As a** medical center administrator
   - **I want to** add, edit, or remove doctors from my center
   - **So that** I can maintain an up-to-date staff roster
   - **Acceptance Criteria**:
     - Add new doctors with basic information
     - Edit existing doctor details
     - Remove doctors from center (with confirmation)
     - Prevent removal of doctors with future appointments

#### Alternative Scenarios

1. **Set Center-Wide Rules**
   - **As a** medical center administrator
   - **I want to** define global settings for the center
   - **So that** individual doctors inherit consistent default settings
   - **Acceptance Criteria**:
     - Configure default working hours
     - Set center holidays and closures
     - Define default appointment rules

#### Edge Cases

1. **Center License Expiration**
   - **As a** system administrator
   - **I want to** handle expired center licenses appropriately
   - **So that** compliance is maintained
   - **Acceptance Criteria**:
     - Display clear warnings before expiration
     - Restrict certain features upon expiration
     - Provide renewal flow

### Doctor Management

#### Primary Scenarios

1. **Add New Doctor**
   - **As a** medical center administrator
   - **I want to** add a new doctor to the system
   - **So that** they can begin scheduling appointments
   - **Acceptance Criteria**:
     - Enter doctor's personal and professional information
     - Select specialties from pre-defined list
     - Set initial schedule configuration
     - Associate with medical center

2. **Edit Doctor Profile**
   - **As a** doctor or administrator
   - **I want to** update doctor information
   - **So that** details remain accurate and current
   - **Acceptance Criteria**:
     - Modify personal information
     - Update credentials and specialties
     - Change contact details
     - Upload/change profile image

3. **Configure Doctor Schedule**
   - **As a** doctor or administrator
   - **I want to** set up the doctor's working schedule
   - **So that** appointments can be booked during appropriate times
   - **Acceptance Criteria**:
     - Define working days and hours
     - Set up lunch breaks or regular pauses
     - Configure recurring schedules
     - Add exception dates (vacations, conferences)

#### Alternative Scenarios

1. **Temporary Schedule Changes**
   - **As a** doctor
   - **I want to** temporarily modify my schedule for a specific date
   - **So that** appointments are not booked when I'm unavailable
   - **Acceptance Criteria**:
     - Select specific date(s) for modification
     - Set different hours or mark as unavailable
     - Override regular schedule for selected dates
     - Preserve existing appointments with warnings

#### Edge Cases

1. **Doctor Departure Handling**
   - **As a** medical center administrator
   - **I want to** properly handle a doctor leaving the practice
   - **So that** patient care is not disrupted
   - **Acceptance Criteria**:
     - Prevent new appointments beyond departure date
     - Provide options for transferring existing appointments
     - Maintain historical records
     - Archive doctor profile with option to reactivate

2. **Schedule Conflicts Resolution**
   - **As a** medical staff member
   - **I want to** be alerted to scheduling conflicts
   - **So that** I can resolve them appropriately
   - **Acceptance Criteria**:
     - Detect and highlight overlapping appointments
     - Provide conflict resolution options
     - Maintain audit trail of changes
     - Allow certain conflicts with proper authorization

### Appointment Management

#### Primary Scenarios

1. **Create New Appointment**
   - **As a** medical staff member
   - **I want to** schedule a new appointment
   - **So that** patients can receive care at specific times
   - **Acceptance Criteria**:
     - Select patient from database or add new patient
     - Choose doctor, date, and time slot
     - Select consultation type
     - Enter insurance information
     - Confirm appointment creation
     - Option to send confirmation to patient

2. **Check-in Patient**
   - **As a** receptionist
   - **I want to** check in patients upon arrival
   - **So that** doctors know the patient is ready
   - **Acceptance Criteria**:
     - Locate appointment by time or patient name
     - Mark patient as checked in
     - Record actual arrival time
     - Update waiting room display if applicable

3. **Reschedule Appointment**
   - **As a** medical staff member
   - **I want to** reschedule an existing appointment
   - **So that** patients can be accommodated when schedule changes are needed
   - **Acceptance Criteria**:
     - Find existing appointment
     - View available alternate slots
     - Select new date/time
     - Record reason for reschedule
     - Update appointment record with change history
     - Option to notify patient

4. **Cancel Appointment**
   - **As a** medical staff member
   - **I want to** cancel an appointment
   - **So that** the time slot becomes available for others
   - **Acceptance Criteria**:
     - Find existing appointment
     - Record cancellation reason
     - Select cancellation policy (late fee if applicable)
     - Free up time slot
     - Option to notify patient
     - Maintain cancellation in historical record

#### Alternative Scenarios

1. **Handle Walk-in Patient**
   - **As a** receptionist
   - **I want to** accommodate walk-in patients when possible
   - **So that** urgent cases can be seen without prior appointment
   - **Acceptance Criteria**:
     - View current schedule gaps
     - Create expedited appointment record
     - Mark as walk-in
     - Prioritize based on urgency

2. **Bulk Reschedule**
   - **As a** medical staff member
   - **I want to** reschedule multiple appointments at once
   - **So that** I can efficiently handle doctor unavailability
   - **Acceptance Criteria**:
     - Select date range or specific doctor's schedule
     - Identify affected appointments
     - Provide alternate scheduling options
     - Process changes in batch
     - Send bulk notifications with personalization

#### Edge Cases

1. **Late Patient Handling**
   - **As a** receptionist
   - **I want to** manage patients who arrive late
   - **So that** schedule disruption is minimized
   - **Acceptance Criteria**:
     - Record late arrival time
     - Follow clinic policy for late arrivals
     - Provide options: wait for opening, reschedule, abbreviated visit
     - Document decision in appointment record

2. **Emergency Accommodation**
   - **As a** medical staff member
   - **I want to** fit in emergency patients
   - **So that** urgent medical needs are addressed
   - **Acceptance Criteria**:
     - Override normal scheduling rules
     - Mark appointment as emergency
     - Notify doctor of urgency
     - Adjust other appointments if necessary

3. **Appointment Series Management**
   - **As a** medical staff member
   - **I want to** schedule a series of recurring appointments
   - **So that** patients needing regular care are accommodated
   - **Acceptance Criteria**:
     - Create multiple linked appointments
     - Set recurrence pattern
     - Apply changes to individual or all future occurrences
     - Handle exceptions appropriately

### Patient Management

#### Primary Scenarios

1. **Add New Patient**
   - **As a** medical staff member
   - **I want to** create a new patient record
   - **So that** their information is available for appointments
   - **Acceptance Criteria**:
     - Enter demographic information
     - Record contact details
     - Enter insurance information
     - Set preferred contact method
     - Create unique patient identifier

2. **Update Patient Information**
   - **As a** medical staff member
   - **I want to** modify patient details
   - **So that** records remain current
   - **Acceptance Criteria**:
     - Edit personal information
     - Update insurance details
     - Modify contact preferences
     - Track changes for audit purposes

3. **View Patient History**
   - **As a** medical staff member
   - **I want to** see a patient's appointment history
   - **So that** I have context for their care
   - **Acceptance Criteria**:
     - Display past appointments with dates and doctors
     - Show appointment outcomes/status
     - Filter history by date range or doctor
     - Include no-shows and cancellations

#### Alternative Scenarios

1. **Merge Duplicate Patient Records**
   - **As a** medical administrator
   - **I want to** combine duplicate patient entries
   - **So that** patient history is consolidated
   - **Acceptance Criteria**:
     - Identify potential duplicates
     - Compare record details
     - Select master record
     - Merge appointment histories
     - Maintain audit trail

#### Edge Cases

1. **Deceased Patient Handling**
   - **As a** medical staff member
   - **I want to** appropriately mark and handle records of deceased patients
   - **So that** inappropriate communications are prevented
   - **Acceptance Criteria**:
     - Mark patient record with deceased status
     - Record date of death if known
     - Prevent new appointment scheduling
     - Maintain historical records
     - Handle family access appropriately

2. **Patient Privacy Requests**
   - **As a** medical staff member
   - **I want to** handle special privacy requests
   - **So that** patient confidentiality preferences are respected
   - **Acceptance Criteria**:
     - Flag records with special privacy concerns
     - Apply appropriate viewing restrictions
     - Handle opt-out requests for reminders/communications
     - Document consent status

### Insurance and Billing

#### Primary Scenarios

1. **Configure Insurance Providers**
   - **As a** medical administrator
   - **I want to** set up insurance providers and plans
   - **So that** coverage can be verified during appointment booking
   - **Acceptance Criteria**:
     - Add provider details
     - Configure multiple plans per provider
     - Set default co-pay amounts
     - Specify verification requirements

2. **Apply Insurance to Appointment**
   - **As a** medical staff member
   - **I want to** associate insurance information with appointments
   - **So that** billing can be processed correctly
   - **Acceptance Criteria**:
     - Select from patient's insurance options
     - Verify coverage for appointment type
     - Calculate expected co-pay
     - Flag potential coverage issues

#### Alternative Scenarios

1. **Handle Self-Pay Patients**
   - **As a** medical staff member
   - **I want to** process appointments for patients without insurance
   - **So that** payment expectations are clear
   - **Acceptance Criteria**:
     - Mark as self-pay
     - Calculate full service price
     - Record payment method
     - Apply any applicable discounts

#### Edge Cases

1. **Insurance Verification Failures**
   - **As a** medical staff member
   - **I want to** handle situations where insurance cannot be verified
   - **So that** appropriate patient communication occurs
   - **Acceptance Criteria**:
     - Flag verification issues
     - Record verification attempt details
     - Provide staff with instruction scripts
     - Offer patient options (reschedule, self-pay, etc.)

### Reporting and Analytics

#### Primary Scenarios

1. **Generate Schedule Utilization Report**
   - **As a** medical administrator
   - **I want to** analyze doctor schedule utilization
   - **So that** I can optimize staffing and availability
   - **Acceptance Criteria**:
     - Calculate percentage of slots filled
     - Analyze by doctor, day of week, time of day
     - Identify peak and low-utilization periods
     - Export data in common formats

2. **View No-Show Analytics**
   - **As a** medical administrator
   - **I want to** track appointment no-shows
   - **So that** I can implement targeted mitigation strategies
   - **Acceptance Criteria**:
     - Calculate no-show rates by doctor, time period
     - Identify patients with multiple no-shows
     - Analyze factors correlated with no-shows
     - Generate actionable recommendations

#### Alternative Scenarios

1. **Generate Custom Reports**
   - **As a** medical administrator
   - **I want to** create custom reports based on specific criteria
   - **So that** I can answer specific business questions
   - **Acceptance Criteria**:
     - Select report parameters
     - Choose visualization options
     - Save report configurations for reuse
     - Schedule automated report generation

#### Edge Cases

1. **Data Anomaly Detection**
   - **As a** system administrator
   - **I want to** identify unusual patterns in scheduling data
   - **So that** potential issues can be investigated
   - **Acceptance Criteria**:
     - Flag statistically significant deviations
     - Alert appropriate personnel
     - Provide drill-down capabilities for investigation
     - Document anomalies for future reference

## User and Patient Relationship Model

### Overview

The Turnera Medical Scheduler implements a sophisticated relationship model between Users and Patients that enables flexible authentication, appointment booking, and patient management. This section documents the core concepts, relationships, constraints, and implementation details to guide future development.

### Core Concepts

1. **User**: A system account with authentication credentials and role-based permissions.
2. **Patient**: A person who receives medical care and can have appointments with doctors.
3. **Default Patient**: The primary patient associated with a user account, used for authentication.
4. **Associated Patient**: Secondary patients (e.g., family members) linked to a user account.

### User-Patient Relationship

#### Relationship Structure

- A User can have multiple Patients associated with their account (one-to-many relationship).
- Each Patient can be associated with at most one User (many-to-one relationship).
- Every User with role `PATIENT` must have exactly one Default Patient.
- A Patient can be either:
  - A Default Patient for a User (indicated by `isDefault: true` or matching the User's `defaultPatientId`).
  - An Associated Patient for a User (has `userId` set but `isDefault: false`).
  - Unassociated (no `userId` set).

#### Authentication Rules

1. **Login with DNI**:
   - Users can only log in with the DNI of their Default Patient.
   - Attempting to log in with the DNI of an Associated Patient (non-default) will be rejected.
   - The system validates both the DNI and password during authentication.

2. **Registration**:
   - New users can register with any DNI that doesn't belong to a Default Patient.
   - Registration is allowed for DNIs that belong to:
     - Patients without any user association.
     - Patients that are Associated Patients (non-default) for another user.
   - When registering with a DNI that belongs to an Associated Patient, the system treats it similarly to a patient without a user, requiring verification.
   - When a non-default patient is claimed during registration, it becomes the default patient for the new user and its association with the previous user is removed.

#### Patient Association Rules

1. **Creating a Default Patient**:
   - When a new user registers, their first patient becomes their Default Patient.
   - The Default Patient has `isDefault: true` and the User has `defaultPatientId` set to this patient's ID.

2. **Adding Associated Patients**:
   - Users can add additional patients to their account as Associated Patients.
   - Associated Patients have `isDefault: false` and `userId` set to the user's ID.
   - A user can manage appointments for all their Associated Patients.

3. **Patient Verification**:
   - When claiming an existing patient (without a user or as a non-default associated patient), verification is required.
   - Verification is done via SMS code sent to the patient's registered phone number.
   - After verification, the patient can be associated with the user account.

4. **Contact Information Handling**:
   - If a patient's phone or email is empty or invalid during association, the system uses the current user's contact information for reminders and communications.
   - Helper text indicates when the user's contact information will be used.

### Implementation Details

#### Key Data Structures

```typescript
// User entity
interface User {
  id: string;
  name: string;
  email: string;
  password?: string;
  role: UserRole; // PATIENT, DOCTOR, ADMIN, etc.
  defaultPatientId?: string; // Reference to the default patient
  // Other fields...
}

// Patient entity
interface Patient {
  id?: string;
  name: string;
  dni: string;
  phone: string;
  email: string;
  coverage: string;
  defaultCoverage?: string;
  userId?: string; // Reference to the associated user
  isDefault?: boolean; // Whether this is the default patient
  // Other fields...
}
```

#### Authentication Flow

1. **Patient Login**:
   - The system searches for a patient with the provided DNI.
   - Verifies the patient has a user association (`userId` is set).
   - Checks if the patient is the default patient for that user.
   - Validates the password against the associated user account.
   - If successful, logs in as the user with the PATIENT role.

2. **Patient Registration**:
   - Validates that the DNI doesn't belong to a default patient.
   - If the DNI belongs to a patient without a user or a non-default associated patient, requires verification.
   - After verification or for new patients, creates a user account and associates the patient.
   - Sets the patient as the default patient for the new user.
   - If the patient was previously an associated patient for another user, removes that association and transfers the patient to the new user.

#### Patient Association Flow

1. **Associating an Existing Patient**:
   - User enters the patient's DNI.
   - System finds the patient and verifies ownership via SMS code.
   - After verification, user can update contact information if needed.
   - When the "Asociar paciente" button is clicked, the patient is associated with the user account.
   - The patient is marked as non-default (`isDefault: false`).

2. **Creating a New Associated Patient**:
   - User enters patient details (name, DNI, contact info, coverage).
   - System validates the DNI is not already in use as a default patient.
   - When the "Crear paciente" button is clicked, a new patient is created and associated with the user.
   - The new patient is marked as non-default (`isDefault: false`).

### Common Scenarios and Edge Cases

1. **Multiple Users Wanting to Claim the Same Patient**:
   - A patient can only be the default patient for one user.
   - A patient can be an associated patient for one user while being the default for another.
   - Verification ensures only authorized individuals can claim patients.
   - If a new user registers with the DNI of a non-default associated patient:
     - The patient is transferred from the original user to the new user.
     - The patient becomes the default patient for the new user (`isDefault: true`).
     - The patient's `userId` is updated to the new user's ID.
     - The original user loses access to this patient's appointments and data.

2. **Changing Default Patient**:
   - Not currently supported in the UI, but could be implemented by updating both the User's `defaultPatientId` and the Patients' `isDefault` flags.

3. **Deleting Patient Associations**:
   - Removing an associated patient should set its `userId` to null and `isDefault` to false.
   - Cannot remove the default patient association without deleting the user account.

4. **Contact Information Synchronization**:
   - Changes to a user's contact information don't automatically propagate to associated patients.
   - Each patient maintains its own contact information, with fallback to the user's information when empty.

### Best Practices for Development

1. **Authentication**:
   - Always use the `loginPatient` function from `AuthContext` for patient authentication.
   - Verify both DNI and password during login.
   - Only allow login with the default patient's DNI.

2. **Patient Management**:
   - Use the `associatePatientWithUser` function to link patients to users.
   - Set `isDefault: true` only for the primary patient of a user.
   - Always verify patient ownership before association.

3. **Contact Information**:
   - Implement fallback mechanisms for empty contact fields.
   - Clearly indicate to users when their contact information will be used for reminders.

4. **Data Integrity**:
   - Maintain consistency between `User.defaultPatientId` and `Patient.isDefault`.
   - Ensure every patient has a unique DNI.
   - Validate that every user with role `PATIENT` has exactly one default patient.

## Data Models

### Core Entity Models

#### Doctor
```typescript
interface Doctor {
  id: string
  name: string
  specialties: Specialty[]
  consultationTypes: ConsultationType[]
  workingDays: {
    [key: string]: {
      enabled: boolean
      hours: Array<{ start: string; end: string }>
      weeksFrequency?: number
    }
  }
  dateExceptions?: {
    [date: string]: {
      enabled: boolean
      hours?: Array<{ start: string; end: string }>
    }
  }
  mn: string // Medical license number
  email?: string
  onlineBookingAdvanceDays: number
  onlineBookingMinHours: number
  appointmentSlotDuration: number
  lastToFirstRangesByDay?: {
    [dayId: string]: Array<{
      start: string;
      end: string;
      enabled: boolean
    }>
  }
  overbookingConfig?: {
    ranges: Array<{
      start: string
      end: string
      ratio: "none" | "2per1" | "1per1" | "1per2" | "1per3"
    }>
  }
}
```

#### ConsultationType
```typescript
interface ConsultationType {
  name: string
  availableOnline?: boolean
  onlineBookingHours?: { start: string; end: string }
  onlineBookingHoursByDay?: {
    [dayId: string]: {
      start: string;
      end: string;
      enabled: boolean
    }[]
  }
  requiresMedicalOrder: boolean
  duration: "default" | "2slots" | "3slots" | "4slots"
  dailyLimit: number | "unlimited"
  basePrice: number
  copays?: Array<{
    coverageId: string;
    planId: string | null;
    amount: number
  }>
  excludedCoverages?: Array<{
    coverageId: string;
    planId: string | null
  }>
  acceptsPrivatePay?: boolean
}
```

#### Appointment
```typescript
interface Appointment {
  id: string
  patientId: string
  doctorId: string
  date: string
  time: string
  endTime: string
  consultationType: string
  status: "scheduled" | "confirmed" | "in-progress" | "completed" | "canceled" | "no-show"
  createdAt: string
  updatedAt: string
  notes?: string
  coverageId?: string
  planId?: string
  isWalkIn?: boolean
  isEmergency?: boolean
}
```

#### Patient
```typescript
interface Patient {
  id: string
  name: string
  email?: string
  phone: string
  dateOfBirth: string
  gender?: string
  address?: string
  insuranceInfo?: Array<{
    coverageId: string
    planId?: string
    memberId: string
    primary: boolean
  }>
  preferredContactMethod: "email" | "phone" | "sms"
  notes?: string
}
```

#### MedicalCenter
```typescript
interface MedicalCenter {
  id: string
  name: string
  address: string
  phone: string
  email?: string
  doctors: string[] // Array of doctor IDs
  globalSettings?: {
    defaultWorkingHours: {
      [dayId: string]: Array<{ start: string; end: string }>
    }
    holidays: string[] // Array of date strings
  }
}
```

## Technical Requirements

### Performance Requirements
- Page load time < 2 seconds
- Calendar view rendering < 1 second even with 50+ doctors
- Search results returned < 500ms
- Mobile responsiveness across devices
- Real-time updates < 500ms across browser tabs

### Security Requirements
- HIPAA compliance for all patient data
- Role-based access controls
- Data encryption at rest and in transit
- Audit logging for all data modifications
- Automatic session timeout after inactivity

### Compatibility Requirements
- Modern web browsers (Chrome, Firefox, Safari, Edge)
- Mobile device compatibility
- Minimum screen resolution support: 1024x768
- Offline capability for basic functions with synchronization

### Integration Requirements
- API endpoints for patient portal integration
- Standardized data interchange formats
- Webhook support for external notifications
- Authentication token support for third-party services

## Non-Functional Requirements

### Usability
- Intuitive navigation requiring minimal training
- Accessibility compliance (WCAG 2.1 AA)
- Multilingual support (Spanish and English initially)
- Consistent UI patterns across all features
- Comprehensive tooltips and contextual help
- Real-time synchronization across multiple browser tabs
- Debounced data operations to prevent excessive storage operations

### Reliability
- 99.9% uptime during business hours
- Data backup every 6 hours
- Disaster recovery plan with RTO < 4 hours
- Graceful degradation during partial system failures

### Scalability
- Support for up to 1,000 concurrent users
- Handle up to 100,000 appointments per month
- Storage capacity for 10+ years of historical data
- Performance maintained with increasing data volume

## Future Enhancements

### Phase 2 Features
- Telemedicine integration
- Automated patient reminders
- Machine learning for optimized scheduling
- Advanced analytics dashboard
- Server-side data persistence
- User authentication and role-based access control

### Integration Opportunities
- EMR/EHR system integration
- Lab result delivery
- Prescription management
- Payment processing
- Patient feedback collection

## Glossary

- **Appointment Slot**: The smallest unit of schedulable time, determined by doctor configuration.
- **Consultation Type**: A category of medical visit with specific duration, price, and insurance rules.
- **Overbooking**: The practice of scheduling multiple patients in a single time slot.
- **Medical Center/Establishment**: A physical location where healthcare services are provided.
- **MN (Matrícula Nacional)**: Medical license number for healthcare providers.
- **Co-pay**: A fixed amount paid by a patient for a covered healthcare service.
- **Work Day Exception**: A specific date that overrides the regular schedule.

---

© 2024 Turnera - Medical Scheduling Platform