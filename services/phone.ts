// No storage import needed for the phone service
import { v4 as uuidv4 } from 'uuid';

// Phone service interface
export interface PhoneProvider {
  sendSMS(to: string, message: string): Promise<boolean>;
}

// Mock phone message interface for storage
export interface MockPhoneMessage {
  id: string;
  to: string;
  message: string;
  sentAt: string;
  read: boolean;
}

// Storage key for mock phone messages
const MOCK_PHONE_MESSAGES_STORAGE_KEY = 'medical-scheduler-mock-phone-messages';

// Mock phone provider for development
class MockPhoneProvider implements PhoneProvider {
  async sendSMS(to: string, message: string): Promise<boolean> {
    console.log(`[MOCK SMS] To: ${to}, Message: ${message}`);

    // Store the message in local storage for preview
    const messages = this.getMockMessages();
    const newMessage: MockPhoneMessage = {
      id: uuidv4(),
      to,
      message,
      sentAt: new Date().toISOString(),
      read: false
    };

    this.saveMockMessages([...messages, newMessage]);

    // In development, always return true to simulate successful sending
    return true;
  }

  // Helper methods to manage mock messages
  getMockMessages(): MockPhoneMessage[] {
    if (typeof localStorage === 'undefined') return [];

    const data = localStorage.getItem(MOCK_PHONE_MESSAGES_STORAGE_KEY);
    if (!data) return [];

    try {
      return JSON.parse(data);
    } catch (e) {
      console.error("Error parsing mock phone messages from localStorage:", e);
      return [];
    }
  }

  saveMockMessages(messages: MockPhoneMessage[]): void {
    if (typeof localStorage === 'undefined') return;
    localStorage.setItem(MOCK_PHONE_MESSAGES_STORAGE_KEY, JSON.stringify(messages));
  }

  markMessageAsRead(messageId: string): void {
    if (typeof localStorage === 'undefined') return;

    const messages = this.getMockMessages();
    const updatedMessages = messages.map(message =>
      message.id === messageId ? { ...message, read: true } : message
    );

    this.saveMockMessages(updatedMessages);
  }

  clearAllMessages(): void {
    if (typeof localStorage === 'undefined') return;
    localStorage.setItem(MOCK_PHONE_MESSAGES_STORAGE_KEY, JSON.stringify([]));
  }
}

// Default provider - will be replaced with a real provider in production
let phoneProvider: PhoneProvider = new MockPhoneProvider();

// Function to set a different phone provider
export function setPhoneProvider(provider: PhoneProvider) {
  phoneProvider = provider;
}

// Export the mock provider for direct access to its methods
export const mockPhoneProvider = new MockPhoneProvider();

// Helper function to send password reset verification code SMS
export async function sendPasswordResetSMS(
  phone: string,
  verificationCode: string
): Promise<boolean> {
  const message = `Tu código de verificación para recuperar tu contraseña en Turnera es: ${verificationCode}. Este código expira en 10 minutos.`;

  try {
    return await phoneProvider.sendSMS(phone, message);
  } catch (error) {
    console.error("Error sending password reset SMS:", error);
    return false;
  }
}

// Helper function to send verification code SMS
export async function sendVerificationSMS(
  phone: string,
  verificationCode: string
): Promise<boolean> {
  const message = `Tu código de verificación para Turnera es: ${verificationCode}. Este código expira en 10 minutos.`;

  try {
    return await phoneProvider.sendSMS(phone, message);
  } catch (error) {
    console.error("Error sending verification SMS:", error);
    return false;
  }
}

// Helper function to send patient association SMS
export async function sendPatientAssociationSMS(
  phone: string,
  patientName: string,
  verificationCode: string
): Promise<boolean> {
  const message = `Hola ${patientName}, alguien está intentando asociar tu cuenta de paciente a un usuario. Tu código de verificación es: ${verificationCode}. Si no solicitaste esto, ignora este mensaje.`;

  try {
    return await phoneProvider.sendSMS(phone, message);
  } catch (error) {
    console.error("Error sending patient association SMS:", error);
    return false;
  }
}
