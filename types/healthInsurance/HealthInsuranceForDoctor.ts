export class HealthInsuranceForDoctor {
    constructor(
        public name: string,
        public plans: HealthInsurancePlan[],
    ) {
    }

    static fromJSON(json: Record<string, unknown>): HealthInsuranceForDoctor {
        const plans: HealthInsurancePlan[] = Array.isArray(json.plans)
            ? (json.plans as Record<string, unknown>[]).map((plan) => HealthInsurancePlan.fromJSON(plan))
            : [];

        return new HealthInsuranceForDoctor(
            json.name as string,
            plans
        );
    }

}


export class HealthInsurancePlan {
    constructor(
        public name: string,
        public isAccepted: boolean
    ) {
    }

    static fromJSON(json: Record<string, unknown>): HealthInsurancePlan {
        return new HealthInsurancePlan(
            json.name as string,
            json.isAccepted as boolean
        );
    }
}