import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { ShieldCheck } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-gray-800 py-8">
      <div className="container mx-auto px-6 md:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          {/* Logo and Copyright */}
          <div className="mb-6 md:mb-0 md:w-1/3">
            <Link href="/">
              <Image
                src="/images/turnera_logo_white.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-8 w-auto mb-3"
              />
            </Link>
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
            </p>
          </div>

          {/* Center Links */}
          <div className="mb-6 md:mb-0 md:w-1/3">
            <ul className="flex flex-wrap justify-center gap-4">
              <li>
                <Link href="#" className="text-gray-300 hover:text-white text-sm">
                  Sobre Nosotros
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-300 hover:text-white text-sm">
                  Cómo Funciona
                </Link>
              </li>
              <li>
                <Link href="#" className="text-gray-300 hover:text-white text-sm">
                  Contacto
                </Link>
              </li>
            </ul>
          </div>

          {/* Right Side - Security Badge and Legal Links */}
          <div className="md:w-1/3 flex flex-col items-center md:items-end">
            <div className="flex items-center mb-3">
              <ShieldCheck className="h-4 w-4 text-blue-300 mr-1.5" />
              <span className="text-sm text-gray-200 font-medium">Reservá turnos médicos 24/7</span>
            </div>
            <div className="flex space-x-4">
              <Link href="/terminos-y-condiciones">
                <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white hover:bg-gray-700 rounded-md px-2 py-1">
                  Términos y Condiciones
                </Button>
              </Link>
              <Link href="/privacidad">
                <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white hover:bg-gray-700 rounded-md px-2 py-1">
                  Privacidad
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

