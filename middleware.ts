import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the path
  const path = request.nextUrl.pathname

  // Define public paths that don't require authentication
  const exactPublicPaths = [
    '/plataforma/login',
    '/plataforma/paciente/login',
    '/plataforma/establecimiento/login',
    '/plataforma/profesional/login',
    '/plataforma/registro',
    '/plataforma/paciente/registro',
    '/plataforma/profesional/registro',
    '/plataforma/establecimiento/registro',
    '/plataforma/recuperar-contrasena',
    '/plataforma/recuperar-contrasena/reset',
    '/admin'
  ];

  const publicPathPrefixes = [
    '/plataforma/reservar',
    '/plataforma/buscar',
    '/dev/'
  ];

  const isPublicPath =
    exactPublicPaths.includes(path) ||
    publicPathPrefixes.some(prefix => path.startsWith(prefix));

  // Check if user is authenticated by looking for the authenticatedUserId cookie (or fall back to email for backward compatibility)
  // Also check for Auth0 authentication cookies
  const isAuthenticatedById = request.cookies.has('authenticatedUserId')
  const isAuthenticatedByEmail = request.cookies.has('authenticatedUserEmail')
  const isAuth0Authenticated = request.cookies.has('auth0_jwt') && request.cookies.has('auth0_user')
  const isAuthenticated = isAuthenticatedById || isAuthenticatedByEmail || isAuth0Authenticated



  // Debug authentication status (only log when not authenticated or accessing protected paths)
  if (!isAuthenticated && !isPublicPath) {
    console.log(`Middleware: Redirecting ${path} - not authenticated (byId: ${isAuthenticatedById}, byEmail: ${isAuthenticatedByEmail}, byAuth0: ${isAuth0Authenticated})`);
  }

  // If the path requires authentication and the user is not authenticated, redirect to login
  if (!isPublicPath && !isAuthenticated) {
    // Use the unified login page for all redirects
    return NextResponse.redirect(new URL('/', request.url))
  }

  // If the user is authenticated and trying to access a login page, redirect to appropriate dashboard
  if (isAuthenticated) {
    if (path === '/plataforma/login') {
      return NextResponse.next()
    } else if (path === '/plataforma/paciente/login') {
      return NextResponse.redirect(new URL('/plataforma/paciente', request.url))
    } else if (path === '/plataforma/profesional/login') {
      return NextResponse.redirect(new URL('/plataforma/profesional', request.url))
    } else if (path === '/plataforma/establecimiento/login') {
      return NextResponse.redirect(new URL('/plataforma/establecimiento', request.url))
    }
  }

  // Continue with the request
  return NextResponse.next()
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    '/plataforma/:path*',
    '/admin/:path*'
  ],
}
