'use client';

import React from 'react';
import {useAuth} from '@/contexts/AuthContext';
import {Button} from '@/components/ui/button';

interface Auth0LoginButtonProps {
    className?: string;
    children?: React.ReactNode;
}

export const Auth0LoginButton: React.FC<Auth0LoginButtonProps> = ({
        className = "",
        children = "Iniciar sesión con Auth0"
        }) => {
    const {loginWithAuth0, isLoading} = useAuth();

    return (
        <Button
            onClick={loginWithAuth0}
            disabled={isLoading}
            className={`w-full ${className}`}
            variant="outline"
        >
            {isLoading ? 'Cargando...' : children}
        </Button>
    );
};

export default Auth0LoginButton;
