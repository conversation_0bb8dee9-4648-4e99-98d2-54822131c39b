"use client"

import { useState } from "react"
import { Check } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import type { Appointment } from "../../types/scheduler"

const statuses: { value: Appointment["status"]; label: string; color: string }[] = [
  { value: "Agendado", label: "Agendado", color: "bg-yellow-100 text-yellow-700 border border-yellow-300" },
  { value: "Ausente", label: "Ausente", color: "bg-red-100 text-red-700 border border-red-300" },
  { value: "Recepcionado", label: "Recepcionado", color: "bg-violet-200 text-violet-800 border border-violet-300" },
  { value: "En Atención", label: "En Atención", color: "bg-blue-100 text-blue-700 border border-blue-300" },
  { value: "Atendido", label: "Atendido", color: "bg-green-100 text-green-700 border border-green-300" },
]

interface StatusChangeTooltipProps {
  currentStatus: Appointment["status"]
  onStatusChange: (newStatus: Appointment["status"]) => void
}

export function StatusChangeTooltip({ currentStatus, onStatusChange }: StatusChangeTooltipProps) {
  const [open, setOpen] = useState(false)

  const handleStatusChange = (status: Appointment["status"]) => {
    onStatusChange(status)
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline">Estado de turno</Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-2">
        {statuses.length === 0 ? (
          <div className="text-sm text-gray-500">No se encontraron estados.</div>
        ) : (
          <ul className="space-y-1">
            {statuses.map((status) => (
              <li key={status.value}>
                <button
                  className={`w-full text-left px-2 py-2.5 rounded-md flex items-center ${status.color} hover:opacity-80`}
                  onClick={() => handleStatusChange(status.value)}
                >
                  <Check className={`mr-2 h-4 w-4 ${currentStatus === status.value ? "opacity-100" : "opacity-0"}`} />
                  <span className="text-xs">{status.label}</span>
                </button>
              </li>
            ))}
          </ul>
        )}
      </PopoverContent>
    </Popover>
  )
}

