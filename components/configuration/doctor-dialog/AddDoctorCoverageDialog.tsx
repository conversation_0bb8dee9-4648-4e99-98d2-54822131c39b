"use client"

import { useContext, useState, useEffect } from "react"
import { CoverageContext } from "@/contexts/CoverageContext"
import { DoctorContext } from "@/contexts/DoctorContext"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Check } from "lucide-react"

interface AddDoctorCoverageDialogProps {
  isOpen: boolean
  onClose: () => void
  doctorId: string
}

export default function AddDoctorCoverageDialog({ isOpen, onClose, doctorId }: AddDoctorCoverageDialogProps) {
  const {
    medicalCoverages,
    setMedicalCoverages,
    availableCoverages,
    getAcceptedCoveragesForCenter,
    doctorCoverageExceptions,
    setDoctorCoverageExceptions
  } = useContext(CoverageContext)

  const { doctors, pendingDoctorCoverageExceptions, setPendingDoctorCoverageExceptions } = useContext(Doctor<PERSON>ontext)

  // State for selected coverages
  const [selectedCoverages, setSelectedCoverages] = useState<string[]>([])

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedCoverages([])
    }
  }, [isOpen])

  // Toggle coverage selection
  const toggleCoverageSelection = (coverageId: string) => {
    setSelectedCoverages((prev) => {
      if (prev.includes(coverageId)) {
        // Remove coverage
        return prev.filter((id) => id !== coverageId)
      } else {
        // Add coverage
        return [...prev, coverageId]
      }
    })
  }

  // Plan-related functions removed as requested

  // Save the selected coverages and make them exclusive to this doctor
  const saveSelectedCoverages = () => {
    // First, add the selected coverages to the medical center's coverage list
    const updatedCoverages = [...medicalCoverages];

    selectedCoverages.forEach(coverageId => {
      // Check if the coverage already exists in medicalCoverages
      const existingIndex = updatedCoverages.findIndex(c => c.id === coverageId);

      // If it doesn't exist, add it from availableCoverages
      if (existingIndex === -1) {
        const coverageToAdd = availableCoverages.find(c => c.id === coverageId);
        if (coverageToAdd) {
          updatedCoverages.push(coverageToAdd);
          console.log(`Added coverage ${coverageToAdd.name} to medical center`);
        }
      }
    });

    // Update medicalCoverages with the new coverages
    setMedicalCoverages(updatedCoverages);

    // Create exceptions for all OTHER doctors (not this doctor) for these coverages
    const newExceptions = { ...doctorCoverageExceptions };

    doctors.forEach(doctor => {
      if (doctor.id !== doctorId) {
        // Initialize exceptions array for this doctor if it doesn't exist
        if (!newExceptions[doctor.id]) {
          newExceptions[doctor.id] = [];
        }

        // Add exceptions for each selected coverage
        selectedCoverages.forEach(coverageId => {
          // Check if this doctor already has an exception for this coverage
          const hasException = newExceptions[doctor.id].some(
            e => e.coverageId === coverageId && e.planId === null
          );

          // If not, add one
          if (!hasException) {
            newExceptions[doctor.id].push({
              coverageId,
              planId: null,
              excluded: true
            });
            console.log(`Added exception for doctor ${doctor.name} for coverage ${coverageId}`);
          }
        });
      }
    });

    // Update the exceptions
    setDoctorCoverageExceptions(newExceptions);

    // Remove any pending exceptions for this doctor for these coverages
    const filteredPendingExceptions = pendingDoctorCoverageExceptions.filter(
      e => !selectedCoverages.includes(e.coverageId) || e.planId !== null
    );
    setPendingDoctorCoverageExceptions(filteredPendingExceptions);

    console.log(`Successfully added ${selectedCoverages.length} exclusive coverages for doctor ${doctorId}`);

    // Close the dialog
    onClose();
  }

  // Get coverages that are accepted by the medical center
  const centerAcceptedCoverageIds = getAcceptedCoveragesForCenter()

  // Filter available coverages to show all that aren't accepted by the medical center
  const availableCoveragesForDoctor = availableCoverages.filter(coverage =>
    !centerAcceptedCoverageIds.includes(coverage.id)
  )

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Agregar cobertura exclusiva</DialogTitle>
          <DialogDescription>
            Seleccione las coberturas que acepta este profesional pero no el establecimiento.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {/* Coverages selection */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Coberturas disponibles</h3>

            {availableCoveragesForDoctor.length === 0 ? (
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <p className="text-gray-600">
                  No hay coberturas disponibles para agregar.
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Todas las coberturas ya están configuradas para este profesional o el establecimiento.
                </p>
              </div>
            ) : (
              <div className="space-y-2 max-h-[300px] overflow-y-auto p-2">
                {availableCoveragesForDoctor.map((coverage) => (
                  <div
                    key={coverage.id}
                    className={`p-3 rounded-md border flex items-center justify-between cursor-pointer ${
                      selectedCoverages.includes(coverage.id)
                        ? "bg-blue-50 border-blue-200"
                        : "bg-white border-gray-200 hover:bg-gray-50"
                    }`}
                    onClick={() => toggleCoverageSelection(coverage.id)}
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-5 h-5 rounded-full flex items-center justify-center ${
                          selectedCoverages.includes(coverage.id)
                            ? "bg-blue-600 text-white"
                            : "border border-gray-300"
                        }`}
                      >
                        {selectedCoverages.includes(coverage.id) && <Check className="h-3 w-3" />}
                      </div>
                      <span className="font-medium">{coverage.name}</span>
                    </div>

                    {/* Plans selection button removed as requested */}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Selected coverages summary section removed as requested */}
        </div>

        {/* Plan selection dropdown removed as requested */}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button
            onClick={saveSelectedCoverages}
            disabled={selectedCoverages.length === 0}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Guardar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
