import { Search, Calendar, ThumbsUp } from "lucide-react"

const steps = [
  {
    icon: Search,
    title: "Busca",
    description: "Encuentra el médico adecuado para ti basado en tu ubicación y necesidades.",
  },
  {
    icon: Calendar,
    title: "<PERSON>ser<PERSON>",
    description: "Elige una fecha y hora conveniente para tu cita médica.",
  },
  {
    icon: ThumbsUp,
    title: "<PERSON>ist<PERSON>",
    description: "Acude a tu cita y recibe la atención médica que necesitas.",
  },
]

export default function HowItWorks() {
  return (
    <section id="como-funciona" className="py-16 bg-white">
      <div className="container mx-auto px-4 md:px-8 max-w-7xl">
        <h2 className="text-3xl md:text-4xl font-recoleta font-bold text-center mb-14">
          Cómo Funciona <span className="text-blue-600">Turnera</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300 bg-blue-50">
              <div className="bg-blue-100 rounded-full p-4 mb-5">
                <step.icon className="h-10 w-10 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">{step.title}</h3>
              <p className="text-gray-600 text-sm leading-relaxed">{step.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

