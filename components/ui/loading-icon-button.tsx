"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import { ButtonHTMLAttributes, forwardRef } from "react"
import { cn } from "@/lib/utils"

interface LoadingIconButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  children: React.ReactNode
  className?: string
}

const LoadingIconButton = forwardRef<HTMLButtonElement, LoadingIconButtonProps>(
  ({ isLoading = false, variant = "default", size = "icon", children, className, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        className={cn(
          isLoading && "opacity-70 cursor-not-allowed",
          className
        )}
        disabled={isLoading || props.disabled}
        {...props}
      >
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          children
        )}
      </Button>
    )
  }
)

LoadingIconButton.displayName = "LoadingIconButton"

export { LoadingIconButton }
