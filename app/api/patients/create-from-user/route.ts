import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const tokenFromCookie = cookieStore.get('auth0_access_token')?.value || cookieStore.get('auth0_jwt')?.value
    const authHeader = request.headers.get('authorization') || ''
    const tokenFromHeader = authHeader.toLowerCase().startsWith('bearer ') ? authHeader.slice(7) : undefined
    const token = tokenFromHeader || tokenFromCookie
    if (!token) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }
//TODO FACU hacer como el route de medical-center-info payload podria ser tipado de tipo UserPatientCreationRequest {
//name: string
//surname:string
//identificationNumber: string
//healthInsuranceId: number
//}
    const payload = await request.json()
    const userIdFromCookie = cookieStore.get('authenticatedUserId')?.value
    const userIdFromHeader = request.headers.get('x-user-id') || undefined
    const userId = userIdFromCookie || userIdFromHeader
    if (!userId) {
      return NextResponse.json({ error: 'Missing user identifier' }, { status: 400 })
    }

            const upstreamRes = await fetch(`${process.env.BACKEND_URL}/patients/create-from-user/${encodeURIComponent(userId)}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    })

    const text = await upstreamRes.text()
    const isJson = upstreamRes.headers.get('content-type')?.includes('application/json')
    const data = isJson ? JSON.parse(text) : text

    return NextResponse.json(data, { status: upstreamRes.status })
  } catch (error: any) {
    return NextResponse.json({ error: error?.message || 'Upstream error' }, { status: 500 })
  }
}

