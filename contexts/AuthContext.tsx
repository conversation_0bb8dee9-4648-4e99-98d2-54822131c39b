'use client';

import React, {createContext, ReactNode, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import {User} from '@/types/users';
import FirstLoginOverlay from '@/components/auth/FirstLoginOverlay';
import {useAuth0Integration} from './Auth0Context';
import {MedicalCenterPermission} from "@/types/MedicalCenter/medicalCenterPermission";
import {MedicalCenterRoleForEmployeeUser} from "@/types/MedicalCenter/medicalCenterRoleForEmployeeUser";

// Helper type for combined permission keys
type AllPermissionKeys = keyof typeof MedicalCenterPermission;

interface AuthContextType {
    currentUser: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    loginWithAuth0: () => void;
    signupWithAuth0: () => void;
    logout: () => void;
    hasPermission: (permissionKey: AllPermissionKeys, medicalCenterId?: number) => boolean;
    hasMultipleMedicalCenters: () => boolean;
    getUserMedicalCenters: () => MedicalCenterRoleForEmployeeUser[];
    isAuth0User: boolean;
    auth0Token: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
    children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({children}) => {
    const {
        auth0User,
        auth0Token,
        isAuth0Loading,
        isAuth0Authenticated,
        syncWithLocalUser,
        clearAuth0Token
    } = useAuth0Integration();

    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [showFirstLoginOverlay, setShowFirstLoginOverlay] = useState(false);
    const [authContextLoading, setAuthContextLoading] = useState(true);

    // Auth0 integration

    // Sync Auth0 user with local user system
    useEffect(() => {
        if (isAuth0Authenticated && auth0User && auth0Token && !currentUser) {
            console.log('Auth0: Syncing Auth0 user with local system');
            const localUser = syncWithLocalUser();
            if (localUser) {
                setCurrentUser(localUser);

                // Set authentication cookies for the local user
                const expirationDate = new Date();
                expirationDate.setDate(expirationDate.getDate() + 7);
                document.cookie = `authenticatedUserId=${localUser.auth0Sub}; expires=${expirationDate.toUTCString()}; path=/`;
                localStorage.setItem('authenticatedUserId', localUser.auth0Sub);
                console.log('Auth0: Successfully synced user:', localUser.name);
                console.log('Auth0: Medical centers:', localUser.medicalCenters);
                setAuthContextLoading(false)
            }
            setAuthContextLoading(false)
        } else if (!isAuth0Loading) {
            // Set loading to false when Auth0 loading is complete, even if not authenticated
            setAuthContextLoading(false);
        }
    }, [isAuth0Authenticated, isAuth0Loading, auth0User, auth0Token, currentUser, syncWithLocalUser]);


    const isAuthenticated = isAuth0Authenticated;

    const isLoading = isAuth0Loading || authContextLoading;


    // Auth0 login method
    const loginWithAuth0 = useCallback(() => {
        if (typeof window !== 'undefined') {
            window.location.href = '/api/auth/login';
        }
    }, []);

    // Auth0 signup method
    const signupWithAuth0 = useCallback(() => {
        if (typeof window !== 'undefined') {
            window.location.href = '/api/auth/signup';
        }
    }, []);

    // Enhanced logout to handle Auth0
    const logout = useCallback(() => {
        console.log('Logging out...');
        setCurrentUser(null);

        // Clear both localStorage and cookies
        localStorage.removeItem('authenticatedUserId');
        // Clear the cookies by setting an expired date
        document.cookie = 'authenticatedUserId=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/';
        // Clear Auth0 token
        clearAuth0Token();
        // If this was an Auth0 user, redirect to Auth0 logout
        if (currentUser?.auth0Sub) {
            window.location.href = '/api/auth/logout';
        }
        console.log('Cleared cookies and localStorage');
    }, [currentUser, clearAuth0Token]);


    const getPermissionForMedicalCenter = useCallback((medicalCenterId: number): MedicalCenterPermission | null => {
        if (!currentUser) return null;

        // Check if user has access to medical centers
        if (currentUser.medicalCenters && currentUser.medicalCenters.length > 0) {
            // Find the medical center
            const medicalCenter = currentUser.medicalCenters.find(mc => mc.id === medicalCenterId);
            if (medicalCenter) {
                return medicalCenter.role;
            }
        }

        return null; // User doesn't have access to this medical center
    }, [currentUser]);


    // Check if user has a specific permission for a medical center
    const hasPermission = useCallback((permissionKey: AllPermissionKeys, medicalCenterId?: number): boolean => {
        if (!currentUser) return false;

        // Convert the permission key to the actual enum value
        const permissionValue = MedicalCenterPermission[permissionKey];

        // If no medicalCenterId provided, check if user has this permission in any medical center
        if (!medicalCenterId) {
            return false;
        }
        const centerPermission = getPermissionForMedicalCenter(medicalCenterId);
        return centerPermission === permissionValue;
    }, [currentUser, getPermissionForMedicalCenter]);

    // Check if the user has access to multiple medical centers
    const hasMultipleMedicalCenters = useCallback((): boolean => {
        if (!currentUser) return false;
        return (currentUser.medicalCenters?.length || 0) > 1;
    }, [currentUser]);

    // Get all medical centers the user has access to
    const getUserMedicalCenters = useCallback((): MedicalCenterRoleForEmployeeUser[] => {
        if (!currentUser) return [];
        return currentUser.medicalCenters || [];
    }, [currentUser]);

    const value = useMemo(() => ({
        currentUser,
        isAuthenticated,
        isLoading,
        loginWithAuth0,
        signupWithAuth0,
        logout,
        hasPermission,
        hasMultipleMedicalCenters,
        getUserMedicalCenters,
        isAuth0User: !!currentUser?.auth0Sub,
        auth0Token,
    }), [currentUser, isAuthenticated, isLoading, loginWithAuth0, signupWithAuth0, logout, hasPermission, hasMultipleMedicalCenters, getUserMedicalCenters, auth0Token]);

    return (
        <AuthContext.Provider value={value}>
            {children}
            {currentUser && showFirstLoginOverlay && (
                <FirstLoginOverlay
                    isOpen={showFirstLoginOverlay}
                    onClose={() => setShowFirstLoginOverlay(false)}
                    user={currentUser}
                />
            )}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
