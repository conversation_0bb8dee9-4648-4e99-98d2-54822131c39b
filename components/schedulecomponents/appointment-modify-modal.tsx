import {use<PERSON><PERSON>back, useEffect, useRef, useState} from "react"
import {AlertCircle, AlertTriangle, ChevronLeft, ChevronRight, Info, Search} from "lucide-react"
import {Input} from "@/components/ui/input"
import {Button} from "@/components/ui/button"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Label} from "@/components/ui/label"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {cn} from "@/lib/utils"
import {
    AppointmentConsultationType,
    getConsultationTypesInfo as getConsultationTypesInfoUtil
} from "@/types/consultationTypes/ConsultationType"

import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";
import {AppointmentSchedule, AppointmentState, SpecialSchedule, VacationSchedule} from "@/types/professional-schedules";
import {generateTimeSlots} from "@/utils/dateUtils";

interface AppointmentModifyModalProps {
    isOpen: boolean
    onClose: () => void
    appointment: ProfessionalAppointment
    doctors: DoctorsForMedicalCenter[]
    doctorId: number
    appointments: Record<string, ProfessionalAppointment[]>
    onModifyAppointment: (date: Date, time: string, consultationTypes: AppointmentConsultationType[], doctorId: number, duration?: number) => void
}

export function AppointmentModifyModal({
                                           isOpen,
                                           onClose,
                                           appointment,
                                           doctors,
                                           doctorId,
                                           appointments,
                                           onModifyAppointment
                                       }: AppointmentModifyModalProps) {
    const [selectedDate, setSelectedDate] = useState<Date>(new Date(appointment.date))
    const [modifyTime, setModifyTime] = useState<string | null>(appointment.startTime)
    const [isDateChanged, setIsDateChanged] = useState(false)
    const [selectedDoctor, setSelectedDoctor] = useState<DoctorsForMedicalCenter | null>(
        doctors.length > 0 ? doctors[0] : null
    )
    const [consultationSearch, setConsultationSearch] = useState("")
    const [showConsultationDropdown, setShowConsultationDropdown] = useState(false)
    const [selectedTypes, setSelectedTypes] = useState<AppointmentConsultationType[]>(
        appointment.consultationTypes
    )
    const consultationRef = useRef<HTMLDivElement>(null)

    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false)
    const [consultationTypesInfo, setConsultationTypesInfo] = useState<ConsultationTypeInfo[]>([])
    const [selectedCoverage, setSelectedCoverage] = useState("")


    const hasConsultationTypeInfo = useCallback((type: AppointmentConsultationType, healthInsuranceId: number | null, doctor: DoctorsForMedicalCenter): boolean => {
        const consultationType = doctor?.consultationTypes.find(ct => ct.consultationTypeId === type.consultationTypeId)
        return consultationType?.hasConsultationTypeInfo(healthInsuranceId) || false
    }, [])

    const getConsultationTypesInfo = useCallback((types: AppointmentConsultationType[], healthInsuranceId: number | null, doctor: DoctorsForMedicalCenter): ConsultationTypeInfo[] => {
        return getConsultationTypesInfoUtil(types, healthInsuranceId, doctor.consultationTypes)
    }, [])

    // Function to check if any of the selected consultation types have information to show
    const hasAnyConsultationTypeInfo = useCallback((types: AppointmentConsultationType[], healthInsuranceId: number | null, doctor?: DoctorsForMedicalCenter): boolean => {
        if (!doctor) return false

        return types.some(type => hasConsultationTypeInfo(type, healthInsuranceId, doctor))
    }, [hasConsultationTypeInfo])

    useEffect(() => {
        if (isOpen) {
            const appointmentDate = new Date(appointment.date + "T00:00:00")
            const currentDoctor = doctors.find(d => d.id === doctorId)
            setSelectedDate(appointmentDate)
            setModifyTime(appointment.startTime)
            setIsDateChanged(false)
            setSelectedTypes(appointment.consultationTypes)
            setSelectedDoctor(currentDoctor || null)
        }
    }, [isOpen, appointment, doctors, doctorId])

    // Check if anything has changed from the original appointment
    const hasChanges = () => {
        if (!selectedDoctor) return false
        if (selectedDoctor.id !== doctorId) return true
        const originalDate = new Date(appointment.date + "T00:00:00")
        if (selectedDate.getTime() !== originalDate.getTime()) return true
        if (modifyTime !== appointment.startTime) return true
        const originalTypes = appointment.consultationTypes
        if (selectedTypes.length !== originalTypes.length) return true
        for (const type of selectedTypes) {
            if (!originalTypes.some(originalType => originalType.consultationTypeId === type.consultationTypeId)) return true
        }
        return false
    }

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (consultationRef.current && !consultationRef.current.contains(e.target as Node)) {
                setShowConsultationDropdown(false)
                setConsultationSearch("")
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    if (!isOpen) return null

    const isDateAvailable = (date: Date) => {
        if (!selectedDoctor) return false


        if (selectedDoctor.agenda.getVacationScheduleByDate(date)) {
            return false;
        }
        const specialSchedules: SpecialSchedule[] = selectedDoctor.agenda.getSpecialSchedulesByDate(date)
        if (specialSchedules.length > 0) {
            return true
        }
        const appointmentSchedules: AppointmentSchedule[] = selectedDoctor.agenda.getAppointmentSchedulesByDate(date)
        if (appointmentSchedules.length > 0) {
            return true;
        }
    }

    const getAvailableTimeSlots = (date: Date) => {
        if (!selectedDoctor) return []
        const vacationSchedule: VacationSchedule | undefined = selectedDoctor.agenda.getVacationScheduleByDate(selectedDate)
        if (vacationSchedule) {
            return [];
        }
        const slots: string[] = []

        const specialSchedulesForDay: SpecialSchedule[] = selectedDoctor.agenda.getSpecialSchedulesByDate(date)
        if (specialSchedulesForDay.length > 0) {
            specialSchedulesForDay.forEach(specialSchedule => {
                slots.concat(generateTimeSlots(specialSchedule.startTime, specialSchedule.endTime, selectedDoctor.appointmentDuration))
            })
        }
        const appointmentSchedulesForDay: AppointmentSchedule[] = selectedDoctor.agenda.getAppointmentSchedulesByDate(date)
        if (appointmentSchedulesForDay.length > 0) {
            appointmentSchedulesForDay.forEach(schedule => {
                slots.concat(generateTimeSlots(schedule.startTime, schedule.endTime, selectedDoctor.appointmentDuration))
            })
        }
        return slots;
    };

    const dayHasAppointments = (dateStr: string) => {
        return appointments[dateStr]?.some(
            apt => apt.id !== appointment.id && apt.state !== AppointmentState.CANCELLED
        ) || false
    }

    const renderCalendarDays = () => {
        const daysInMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0).getDate()
        const firstDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1)
        const startingDay = (firstDay.getDay() + 6) % 7 // Adjust to start week on Monday

        return [
            ...Array.from({length: startingDay}).map((_, i) => (
                <div key={`empty-${i}`} className="p-2"/>
            )),
            ...Array.from({length: daysInMonth}, (_, i) => {
                const date = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), i + 1)
                const dateStr = date.toISOString().split("T")[0]

                const isAvailable = isDateAvailable(date)
                const hasAppointments = dayHasAppointments(dateStr)
                const isSelected =
                    selectedDate.getDate() === date.getDate() &&
                    selectedDate.getMonth() === date.getMonth() &&
                    selectedDate.getFullYear() === date.getFullYear()

                return (
                    <button
                        key={i}
                        onClick={() => {
                            if (isAvailable) {
                                setSelectedDate(date)
                                setModifyTime(null)
                                setIsDateChanged(true)
                            }
                        }}
                        disabled={!isAvailable}
                        className={`
              group w-8 h-8 p-0 rounded-full relative
              ${!isAvailable ? "text-gray-300 cursor-not-allowed" : ""}
              ${isSelected ? "bg-blue-500 text-white" : "hover:bg-blue-50"}
            `}
                    >
                        <div className="w-full h-full flex items-center justify-center">{i + 1}</div>
                        {hasAppointments && (
                            <div
                                className={`
                  absolute left-1/2 -translate-x-1/2
                  w-[0.375rem] h-[0.375rem] rounded-full
                  transition-all duration-200
                  ${isSelected ? "-bottom-[0.625rem]" : "-bottom-[0.125rem] group-hover:-bottom-[0.625rem]"}
                  bg-blue-500
                `}
                            />
                        )}
                    </button>
                )
            })
        ]
    }

    const handleModifyConfirm = () => {
        if (selectedDoctor && selectedDate && modifyTime && selectedTypes.length > 0) {
            // Check if we need to show consultation type information before confirming
            if (appointment.healthInsuranceInformation && hasAnyConsultationTypeInfo(selectedTypes, appointment.healthInsuranceId, selectedDoctor)) {
                const typesInfo = getConsultationTypesInfo(selectedTypes, appointment.healthInsuranceId, selectedDoctor)

                // Check if any consultation type is excluded
                const hasExcludedTypes = typesInfo.some(info => info.isExcluded)

                if (hasExcludedTypes) {
                    // Show information and don't proceed with modification
                    setSelectedCoverage(appointment.healthInsuranceInformation)
                    setConsultationTypesInfo(typesInfo)
                    setShowInstructionsDialog(true)
                    return
                } else if (typesInfo.length > 0) {
                    // Show information but allow modification to proceed
                    setSelectedCoverage(appointment.healthInsuranceInformation)
                    setConsultationTypesInfo(typesInfo)
                    setShowInstructionsDialog(true)
                    // We'll handle the actual modification in the dialog
                    return
                }
            }

            // Proceed with modification if no consultation type info or no exclusions
            proceedWithModification()
        } else {
            console.error("Missing required fields", {
                doctor: !!selectedDoctor,
                date: !!selectedDate,
                time: !!modifyTime,
                types: selectedTypes.length,
            });
        }
    };

    const proceedWithModification = () => {
        if (selectedDoctor && selectedDate && modifyTime && selectedTypes.length > 0) {
            const slotDuration = selectedDoctor.appointmentDuration || 15;
            const consultationTypes = selectedDoctor.consultationTypes || [];
            const maxDuration = Math.max(
                ...selectedTypes.map((type) => {
                    const consultation = consultationTypes.find((t) => t.consultationTypeId === type.consultationTypeId);
                    const durationValue = consultation?.appointmentIntervalAmount || 1;
                    return slotDuration * durationValue;
                })
            );

            onModifyAppointment(
                selectedDate,
                modifyTime,
                selectedTypes,
                selectedDoctor.id,
                maxDuration
            );
            onClose();
        }
    };

    const getConsultationTypes = () => {
        return selectedDoctor?.consultationTypes || []
    }

    return (
        <>
            <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
                <div className="bg-white p-5 rounded-lg shadow-lg w-[650px] flex">
                    <div className="w-1/2 pr-4 border-r border-gray-200">
                        <h3 className="text-lg font-semibold mb-4 text-gray-800">Modificar turno</h3>

                        <div className="space-y-2 mb-4">
                            <Label className="text-sm font-medium text-gray-700">Profesional</Label>
                            <Select
                                value={selectedDoctor?.name || ""}
                                onValueChange={(doctorId) => {
                                    const newDoctor = doctors.find(d => d.id === Number.parseInt(doctorId)) || null
                                    setSelectedDoctor(newDoctor)
                                    setSelectedDate(new Date())
                                    setModifyTime(null)
                                    setSelectedTypes([])
                                    setIsDateChanged(true)
                                }}
                            >
                                <SelectTrigger
                                    className="w-full border-gray-200 focus:ring-blue-500 focus:border-blue-500">
                                    <SelectValue placeholder="Seleccionar profesional">
                                        {selectedDoctor ? selectedDoctor.fullName : "Seleccionar profesional"}
                                    </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                    {doctors.map((doctor) => (
                                        <SelectItem key={doctor.id} value={doctor.id.toString()}>
                                            {doctor.fullName}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="border rounded-lg p-4 bg-white border-black">
                            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium">
                {new Date(selectedDate).toLocaleString('es', {month: 'long', year: 'numeric'})}
              </span>
                                <div className="flex gap-2">
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                            const prevMonth = new Date(selectedDate)
                                            prevMonth.setMonth(prevMonth.getMonth() - 1)
                                            setSelectedDate(prevMonth)
                                        }}
                                    >
                                        <ChevronLeft className="h-4 w-4"/>
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                            const nextMonth = new Date(selectedDate)
                                            nextMonth.setMonth(nextMonth.getMonth() + 1)
                                            setSelectedDate(nextMonth)
                                        }}
                                    >
                                        <ChevronRight className="h-4 w-4"/>
                                    </Button>
                                </div>
                            </div>

                            <div className="grid grid-cols-7 gap-1 text-sm mb-2">
                                {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                                    <div key={i} className="text-center font-medium">
                                        {day}
                                    </div>
                                ))}
                            </div>

                            <div className="grid grid-cols-7 gap-1 gap-y-3 text-sm h-[250px]">
                                {renderCalendarDays()}
                            </div>
                        </div>
                    </div>

                    <div className="w-1/2 pl-4 flex flex-col pt-9 space-y-6">
                        <div className="space-y-2">
                            <Label className="text-sm font-medium text-gray-700">Nuevo horario</Label>
                            <Select
                                value={modifyTime ?? ""}
                                onValueChange={(value) => {
                                    setModifyTime(value)
                                    setIsDateChanged(true)
                                }}
                                disabled={!selectedDoctor}
                            >
                                <SelectTrigger
                                    className="w-full border-gray-200 focus:ring-blue-500 focus:border-blue-500">
                                    <SelectValue placeholder="Seleccionar horario">
                                        {!isDateChanged && modifyTime ? modifyTime : undefined}
                                    </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                    {getAvailableTimeSlots(selectedDate).map((slot) => {
                                        const dateStr = selectedDate.toISOString().split("T")[0];
                                        const existingAppointments = appointments[dateStr] || [];
                                        const isOccupied = existingAppointments
                                            .filter((apt) => apt.id !== appointment.id && apt.state !== AppointmentState.CANCELLED)
                                            .some((apt) => apt.startTime === slot);
                                        return (
                                            <SelectItem key={slot} value={slot}>
                                                {slot} {isOccupied && "(Sobreturno)"}
                                            </SelectItem>
                                        );
                                    })}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2" ref={consultationRef}>
                            <Label className="text-sm font-medium text-gray-700">Tipo de atención</Label>
                            <div className="relative">
                                <div className="relative">
                                    <Input
                                        placeholder="Buscar tipo de atención"
                                        value={consultationSearch}
                                        autoComplete="off"
                                        onFocus={() => setShowConsultationDropdown(true)}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            setConsultationSearch(e.target.value);
                                            setShowConsultationDropdown(true);
                                        }}
                                        disabled={!selectedDoctor}
                                    />
                                    <Search
                                        className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 opacity-50 w-[1rem] h-[1rem] cursor-pointer"/>
                                </div>

                                {showConsultationDropdown && (
                                    <div
                                        className="absolute z-20 w-full bg-white border border-gray-200 rounded-md mt-[0.25rem] shadow-lg">
                                        <div className="max-h-[15rem] overflow-auto">
                                            {getConsultationTypes()
                                                .filter((type) =>
                                                    type.name.toLowerCase().includes(consultationSearch.toLowerCase())
                                                )
                                                .map((type) => {
                                                    const slotDuration = selectedDoctor?.appointmentDuration || 15;
                                                    const duration = slotDuration * type.appointmentIntervalAmount;
                                                    const showDuration = type.appointmentIntervalAmount != 1;
                                                    return (
                                                        <div
                                                            key={type.name}
                                                            className={cn(
                                                                "p-[0.375rem] text-[0.875rem] hover:bg-gray-100 transition-colors cursor-pointer",
                                                                selectedTypes.includes(type.toAppointmentConsultationType())
                                                                    ? "text-white bg-blue-500 hover:bg-blue-400"
                                                                    : "hover:bg-gray-100"
                                                            )}
                                                            onClick={() => {
                                                                setSelectedTypes((prev: AppointmentConsultationType[]) =>
                                                                    prev.some(oldType => oldType.consultationTypeId === type.consultationTypeId)
                                                                        ? prev.filter((oldType) => oldType.consultationTypeId !== type.consultationTypeId)
                                                                        : [...prev, type.toAppointmentConsultationType()]
                                                                );
                                                            }}
                                                        >
                                                            {type.name} {showDuration && `(${duration} min)`}
                                                        </div>
                                                    );
                                                })}
                                        </div>
                                        <div className="border-t p-2 flex justify-between items-center">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => {
                                                    setShowConsultationDropdown(false);
                                                    setConsultationSearch("");
                                                }}
                                                className="text-gray-600 hover:bg-gray-100 h-9"
                                            >
                                                Cancelar
                                            </Button>
                                            <Button
                                                size="sm"
                                                className="bg-blue-500 hover:bg-blue-600 text-white h-9"
                                                onClick={() => {
                                                    setShowConsultationDropdown(false);
                                                    setConsultationSearch("");
                                                }}
                                            >
                                                Confirmar
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="flex flex-wrap gap-2 mt-2">
                                {selectedTypes.map((type) => (
                                    <span
                                        key={type.name}
                                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs flex items-center"
                                    >
                                        {type.name}
                                        {selectedDoctor && appointment.healthInsuranceInformation &&
                                            hasConsultationTypeInfo(type, appointment.healthInsuranceId, selectedDoctor) && (
                                                <div className="ml-1">
                                                    <Info className="h-3.5 w-3.5 text-blue-500"/>
                                                </div>
                                            )}
                                        <button
                                            onClick={() => {
                                                setSelectedTypes((prev) => prev.filter((t) => t !== type));
                                            }}
                                            className="ml-1 rounded-full hover:bg-blue-200 px-1"
                                        >
                                            ×
                                        </button>
                                    </span>
                                ))}
                            </div>
                        </div>

                        <div className="flex justify-end gap-3 pt-2 border-t border-gray-100 mt-4">
                            <Button
                                variant="outline"
                                onClick={onClose}
                                className="text-gray-600 border-gray-300 hover:bg-gray-100"
                            >
                                Cancelar
                            </Button>
                            <Button
                                onClick={handleModifyConfirm}
                                className="bg-blue-500 hover:bg-blue-600 text-white"
                                disabled={
                                    !selectedDoctor ||
                                    !modifyTime ||
                                    selectedTypes.length === 0 ||
                                    !hasChanges()
                                }
                            >
                                Confirmar
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Updated Instructions Dialog to handle multiple consultation types */}
            <AlertDialog open={showInstructionsDialog} onOpenChange={(open) => {
                setShowInstructionsDialog(open)
                if (!open) {
                    // If dialog is closed without exclusions, proceed with modification
                    const hasExcludedTypes = consultationTypesInfo.some(info => info.isExcluded)
                    if (!hasExcludedTypes && consultationTypesInfo.length > 0) {
                        proceedWithModification()
                    }
                }
            }}>
                <AlertDialogContent
                    className={`border border-gray-300 shadow-lg ${consultationTypesInfo.length > 1 ? "max-w-3xl" : "max-w-md"}`}>
                    <AlertDialogHeader className="pb-2 border-b border-gray-200">
                        <AlertDialogTitle className="text-center text-lg font-semibold text-gray-800">Información
                            importante</AlertDialogTitle>
                    </AlertDialogHeader>

                    {/* Content for multiple consultation types */}
                    <div className="mt-4 space-y-4">
                        {consultationTypesInfo.map((consultationInfo, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
                                <h4 className="font-semibold text-gray-800 text-lg">{consultationInfo.name}</h4>

                                {consultationInfo.isExcluded && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                        <p className="text-red-700 flex items-center gap-2">
                                            <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0"/>
                                            <span>
                                                La atención <span
                                                className="font-semibold">{consultationInfo.name}</span> no es cubierta por el profesional con la cobertura <span
                                                className="font-semibold">{selectedCoverage}</span>.
                                            </span>
                                        </p>
                                    </div>
                                )}

                                {consultationInfo.requiresMedicalOrder && (
                                    <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                                        <p className="text-amber-700 flex items-center gap-2">
                                            <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0"/>
                                            <span>Esta atención requiere orden médica.</span>
                                        </p>
                                    </div>
                                )}

                                {consultationInfo.copayAmount !== null && (
                                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <p className="text-blue-700 flex items-center gap-2">
                                            <Info className="h-5 w-5 text-blue-500 flex-shrink-0"/>
                                            <span>
                                                Copago por <span
                                                className="font-semibold">{consultationInfo.name}</span> con plan <span
                                                className="font-semibold">{selectedCoverage}</span>: <span
                                                className="font-semibold">${consultationInfo.copayAmount}</span>
                                            </span>
                                        </p>
                                    </div>
                                )}

                                {consultationInfo.instructions && (
                                    <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div className="text-gray-700">
                                            <p className="font-medium mb-2">Indicaciones para atención <span
                                                className="font-semibold">{consultationInfo.name}</span>:</p>
                                            <p className="whitespace-pre-line">
                                                {consultationInfo.instructions}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>

                    <div className="flex justify-center mt-6">
                        <AlertDialogAction
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 rounded-md transition-colors"
                            onClick={() => {
                                setShowInstructionsDialog(false)
                                // Check if there are excluded types
                                const hasExcludedTypes = consultationTypesInfo.some(info => info.isExcluded)
                                if (!hasExcludedTypes) {
                                    proceedWithModification()
                                }
                            }}
                        >
                            Entendido
                        </AlertDialogAction>
                    </div>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
}
