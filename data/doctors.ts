import type { Doctor } from "@/types/doctor"

export const doctors: Doctor[] = [
  {
    id: "dr-favaloro",
    name: "<PERSON>",
    specialties: ["Cardiología"],
    initial: "F",
    consultationTypes: [
      {
        name: "Primera Consulta",
        availableOnline: true,
        onlineBookingHours: { start: "09:00", end: "12:00" },
        requiresMedicalOrder: false,
        duration: "default",
        dailyLimit: "unlimited",
        basePrice: 500,
        copays: [
          { coverageId: "1", planId: "310", amount: 50 }, // OSDE 310
          { coverageId: "2", planId: null, amount: 75 }   // Swiss Medical all plans
        ],
        excludedCoverages: [
          { coverageId: "3", planId: null } // Medifé all plans
        ]
      },
      {
        name: "Consulta Consecutiva",
        availableOnline: true,
        onlineBookingHours: { start: "09:00", end: "12:00" },
        requiresMedicalOrder: false,
        duration: "default",
        dailyLimit: "unlimited",
        basePrice: 400,
        copays: [],
        excludedCoverages: []
      },
      {
        name: "Electrocardiograma",
        availableOnline: false,
        requiresMedicalOrder: true,
        duration: "2slots",
        dailyLimit: 5,
        basePrice: 800,
        copays: [],
        excludedCoverages: []
      }
    ],
    workingDays: {
      "1": { enabled: true, hours: [{ start: "09:00", end: "16:00" }] },
      "2": { enabled: false, hours: [] },
      "3": { enabled: false, hours: [] },
      "4": { enabled: true, hours: [{ start: "09:00", end: "15:30" }] },
      "5": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "6": { enabled: false, hours: [] },
      "0": { enabled: false, hours: [] },
    },
    mn: "38555",
    email: "<EMAIL>",
    onlineBookingAdvanceDays: 60,
    onlineBookingMinHours: 2,
    appointmentSlotDuration: 15
  },
  {
    id: "dra-grierson",
    name: "dr.Cecilia Grierson",
    specialties: ["Obstetricia"],
    initial: "G",
    consultationTypes: [
      {
        name: "Primera Consulta",
        availableOnline: true,
        onlineBookingHours: { start: "12:00", end: "15:00" },
        requiresMedicalOrder: false,
        duration: "default",
        dailyLimit: "unlimited",
        basePrice: 450,
        copays: [
          { coverageId: "1", planId: null, amount: 40 } // OSDE all plans
        ],
        excludedCoverages: [
          { coverageId: "2", planId: "Plan Básico" } // Swiss Medical Plan Básico
        ]
      },
      {
        name: "Consulta Consecutiva",
        availableOnline: true,
        onlineBookingHours: { start: "12:00", end: "15:00" },
        requiresMedicalOrder: false,
        duration: "default",
        dailyLimit: "unlimited",
        basePrice: 350,
        copays: [],
        excludedCoverages: []
      }
    ],
    workingDays: {
      "1": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "2": { enabled: false, hours: [] },
      "3": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "4": { enabled: false, hours: [] },
      "5": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "6": { enabled: false, hours: [] },
      "0": { enabled: false, hours: [] },
    },
    mn: "20444",
    email: "",
    onlineBookingAdvanceDays: 60,
    onlineBookingMinHours: 2,
    appointmentSlotDuration: 15
  },
]