import {AppointmentState} from "@/types/professional-schedules";

export interface AppointmentModificationRequest {
    appointmentId: number;
    employeeUserId: number;
    newDoctorId?: number;
    newDate?: string; // Format: "dd-MM-yyyy" (e.g., "25-12-2023")
    newStartTime?: string; // Format: "HH:mm:ss" (e.g., "08:30:00")
    consultationTypeId?: number;
    newPrice?: number;
    newAppointmentStatus?: AppointmentState;
}
