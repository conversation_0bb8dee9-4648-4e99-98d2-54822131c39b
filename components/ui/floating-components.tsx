import { But<PERSON> } from "@/components/ui/button"
import { StatusChangeTooltip } from "../schedulecomponents/status-change-tooltip"
import { ModifyAppointmentTooltip } from "../schedulecomponents/modify-appointment-tooltip"
import type { Appointment } from "../../types/scheduler"

// Floating Status Change Component
export const FloatingStatusChange = ({ 
  currentStatus, 
  onStatusChange, 
  onClose 
}: {
  currentStatus: Appointment["status"]
  onStatusChange: (newStatus: Appointment["status"]) => void
  onClose: () => void
}) => (
  <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <StatusChangeTooltip currentStatus={currentStatus} onStatusChange={onStatusChange} />
      <Button variant="outline" onClick={onClose} className="mt-2 w-full">Cerrar</Button>
    </div>
  </div>
)

// Floating Observations Component
export const FloatingObservations = ({ onClose }: { onClose: () => void }) => (
  <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <div>Detalles de turno</div>
      <Button variant="outline" onClick={onClose} className="mt-2 w-full">Cerrar</Button>
    </div>
  </div>
)

// Floating Modify Appointment Component
export const FloatingModifyAppointment = ({ 
  currentDate, 
  currentTime, 
  onConfirm, 
  availableDates, 
  availableTimeSlots, 
  onClose 
}: {
  currentDate: Date
  currentTime: string
  onConfirm: (date: Date, time: string) => void
  availableDates: Date[]
  availableTimeSlots: string[]
  onClose: () => void
}) => (
  <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <ModifyAppointmentTooltip
        currentDate={currentDate}
        currentTime={currentTime}
        onConfirm={onConfirm}
        availableDates={availableDates}
        availableTimeSlots={availableTimeSlots}
      />
      <Button variant="outline" onClick={onClose} className="mt-2 w-full">Cerrar</Button>
    </div>
  </div>
)