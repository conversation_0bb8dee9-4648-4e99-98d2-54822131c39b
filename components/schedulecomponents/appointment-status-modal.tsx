import {Check} from "lucide-react"
import {But<PERSON>} from "@/components/ui/button"
import {AppointmentState} from "@/types/professional-schedules"

interface AppointmentStatusModalProps {
    isOpen: boolean
    onClose: () => void
    currentStatus: AppointmentState
    onStatusChange: (newStatus: AppointmentState) => void
}

export function AppointmentStatusModal({
                                           isOpen,
                                           onClose,
                                           currentStatus,
                                           onStatusChange
                                       }: AppointmentStatusModalProps) {
    if (!isOpen) return null

    const statuses: { value: AppointmentState; label: string; color: string }[] = [
        {
            value: AppointmentState.PENDING,
            label: "Agendado",
            color: "bg-yellow-100 text-yellow-700 border border-yellow-300"
        },
        {
            value: AppointmentState.NO_SHOW,
            label: "Ausente",
            color: "bg-red-100 text-red-700 border border-red-300"
        },
        {
            value: AppointmentState.IN_WAITING_ROOM,
            label: "Recepcionado",
            color: "bg-violet-200 text-violet-800 border border-violet-300"
        },
        {
            value: AppointmentState.IN_CONSULTATION,
            label: "En Atención",
            color: "bg-blue-100 text-blue-700 border border-blue-300"
        },
        {
            value: AppointmentState.COMPLETE,
            label: "Atendido",
            color: "bg-green-100 text-green-700 border border-green-300"
        },
    ]

    const handleStatusChange = (status: AppointmentState) => {
        onStatusChange(status)
        onClose()
    }

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
            <div className="bg-white p-4 rounded-lg shadow-lg w-[200px]">
                <h3 className="text-lg font-semibold mb-2 text-center">Estado de turno</h3>
                {statuses.length === 0 ? (
                    <div className="text-sm text-gray-500">No se encontraron estados.</div>
                ) : (
                    <ul className="space-y-1">
                        {statuses.map((status) => (
                            <li key={status.value}>
                                <button
                                    className={`w-full text-left px-2 py-2.5 rounded-md flex items-center ${status.color} hover:opacity-80`}
                                    onClick={() => handleStatusChange(status.value)}
                                >
                                    <Check
                                        className={`mr-2 h-4 w-4 ${currentStatus === status.value ? "opacity-100" : "opacity-0"}`}
                                    />
                                    <span className="text-xs text-center">{status.label}</span>
                                </button>
                            </li>
                        ))}
                    </ul>
                )}
                <Button variant="outline" onClick={onClose} className="mt-4 w-full">
                    Cerrar
                </Button>
            </div>
        </div>
    )
}
