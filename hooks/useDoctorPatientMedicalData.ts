import { useState, useEffect, useCallback } from 'react'
import { DoctorPatientMedicalData, ConsultationNote, CreateDoctorPatientMedicalData } from '@/types/patient'

export const useDoctorPatientMedicalData = () => {
  const [medicalDataList, setMedicalDataList] = useState<DoctorPatientMedicalData[]>([])
  const [loading, setLoading] = useState(true)

  // Load medical data from storage
  useEffect(() => {
    const loadMedicalData = () => {
      try {
        const data = storage.getDoctorPatientMedicalData()
        setMedicalDataList(data)
      } catch (error) {
        console.error('Error loading medical data:', error)
        setMedicalDataList([])
      } finally {
        setLoading(false)
      }
    }

    loadMedicalData()
  }, [])

  // Get medical data for a specific doctor-patient relation
  const getMedicalDataByRelation = useCallback((doctorId: string, patientId: string): DoctorPatientMedicalData | null => {
    return storage.getDoctorPatientMedicalDataByRelation(doctorId, patientId)
  }, [])

  // Get or create medical data for a doctor-patient relation
  const getOrCreateMedicalData = useCallback((doctorId: string, patientId: string): DoctorPatientMedicalData => {
    let medicalData = storage.getDoctorPatientMedicalDataByRelation(doctorId, patientId)
    
    if (!medicalData) {
      // Create new medical data record
      medicalData = storage.createDoctorPatientMedicalData({ doctorId, patientId })
      // Update local state
      setMedicalDataList(prev => [...prev, medicalData!])
    }
    
    return medicalData
  }, [])

  // Update personal data for a doctor-patient relation
  const updatePersonalData = useCallback((
    doctorId: string, 
    patientId: string, 
    personalData: Partial<DoctorPatientMedicalData['personalData']>
  ): DoctorPatientMedicalData | null => {
    const updatedData = storage.updateDoctorPatientPersonalData(doctorId, patientId, personalData)
    
    if (updatedData) {
      // Update local state
      setMedicalDataList(prev => 
        prev.map(data => 
          data.doctorId === doctorId && data.patientId === patientId 
            ? updatedData 
            : data
        )
      )
    }
    
    return updatedData
  }, [])

  // Add a consultation note
  const addConsultationNote = useCallback((
    doctorId: string, 
    patientId: string, 
    appointmentId: string, 
    notes: string,
    consultationType: string,
    medicalCenterId?: string
  ): ConsultationNote | null => {
    const newNote = storage.addConsultationNote(doctorId, patientId, appointmentId, notes, consultationType, medicalCenterId)
    
    if (newNote) {
      // Update local state
      setMedicalDataList(prev => 
        prev.map(data => {
          if (data.doctorId === doctorId && data.patientId === patientId) {
            return {
              ...data,
              consultationNotes: [...data.consultationNotes, newNote]
            }
          }
          return data
        })
      )
    }
    
    return newNote
  }, [])

  // Update an existing consultation note
  const updateConsultationNote = useCallback((
    doctorId: string, 
    patientId: string, 
    appointmentId: string, 
    notes: string
  ): ConsultationNote | null => {
    const updatedNote = storage.updateConsultationNote(doctorId, patientId, appointmentId, notes)
    
    if (updatedNote) {
      // Update local state
      setMedicalDataList(prev => 
        prev.map(data => {
          if (data.doctorId === doctorId && data.patientId === patientId) {
            return {
              ...data,
              consultationNotes: data.consultationNotes.map(note =>
                note.appointmentId === appointmentId ? updatedNote : note
              )
            }
          }
          return data
        })
      )
    }
    
    return updatedNote
  }, [])

  // Lock a consultation note (make it immutable)
  const lockConsultationNote = useCallback((
    doctorId: string, 
    patientId: string, 
    appointmentId: string
  ): boolean => {
    const success = storage.lockConsultationNote(doctorId, patientId, appointmentId)
    
    if (success) {
      // Update local state
      setMedicalDataList(prev => 
        prev.map(data => {
          if (data.doctorId === doctorId && data.patientId === patientId) {
            return {
              ...data,
              consultationNotes: data.consultationNotes.map(note =>
                note.appointmentId === appointmentId
                  ? { ...note, isLocked: true }
                  : note
              )
            }
          }
          return data
        })
      )
    }
    
    return success
  }, [])

  // Get all medical data for a specific doctor
  const getMedicalDataByDoctor = useCallback((doctorId: string): DoctorPatientMedicalData[] => {
    return storage.getDoctorPatientMedicalDataByDoctor(doctorId)
  }, [])

  // Get medical data for a specific patient (but only for the requesting doctor)
  const getMedicalDataByPatient = useCallback((patientId: string, requestingDoctorId: string): DoctorPatientMedicalData | null => {
    return storage.getDoctorPatientMedicalDataByPatient(patientId, requestingDoctorId)
  }, [])

  // Get consultation notes for a specific appointment
  const getConsultationNoteByAppointment = useCallback((
    doctorId: string, 
    patientId: string, 
    appointmentId: string
  ): ConsultationNote | null => {
    const medicalData = getMedicalDataByRelation(doctorId, patientId)
    if (!medicalData) return null
    
    return medicalData.consultationNotes.find(note => note.appointmentId === appointmentId) || null
  }, [getMedicalDataByRelation])

  // Check if a consultation note exists and is locked for a specific appointment
  const isConsultationNoteLocked = useCallback((
    doctorId: string, 
    patientId: string, 
    appointmentId: string
  ): boolean => {
    const note = getConsultationNoteByAppointment(doctorId, patientId, appointmentId)
    return note?.isLocked || false
  }, [getConsultationNoteByAppointment])

  return {
    medicalDataList,
    loading,
    getMedicalDataByRelation,
    getOrCreateMedicalData,
    updatePersonalData,
    addConsultationNote,
    updateConsultationNote,
    lockConsultationNote,
    getMedicalDataByDoctor,
    getMedicalDataByPatient,
    getConsultationNoteByAppointment,
    isConsultationNoteLocked,
  }
} 