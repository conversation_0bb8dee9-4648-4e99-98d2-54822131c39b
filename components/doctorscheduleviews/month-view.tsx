"use client"

import {useMemo} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {ChevronLeft, ChevronRight} from "lucide-react"
import {amountOfTimeSlots, getDateKey, getMonthData} from "@/utils/dateUtils"
import {
    AppointmentSchedule,
    AppointmentState,
    groupAppointmentSchedulesByDayOfWeek,
    groupSpecialSchedulesByDate,
    ProfessionalSchedulesResponse,
    SpecialSchedule,
} from "@/types/professional-schedules";
import {createIsDayAvailableChecker} from "@/utils/ViewUtils";
import {isSameDay} from "date-fns";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

interface MonthViewProps {
    appointments: Record<string, ProfessionalAppointment[]>
    currentDate: Date
    onDateClick: (date: Date) => void
    availableDays: number[]
    onPrevMonth: () => void
    onNextMonth: () => void
    setView: (view: "day" | "week" | "month") => void
    appointmentDuration: number
    professionalSchedules: ProfessionalSchedulesResponse | null
    doctorId: number
    medicalCenterId: number
}

export function MonthView({
                              appointments,
                              currentDate,
                              onDateClick,
                              availableDays,
                              onPrevMonth,
                              onNextMonth,
                              setView,
                              appointmentDuration,
                              professionalSchedules,
                              doctorId,
                              medicalCenterId
                          }: MonthViewProps) {
    const {days, monthAgendaStartingDate} = useMemo(() => {
        const data = getMonthData(currentDate.getFullYear(), currentDate.getMonth())
        return {...data, monthAgendaStartingDate: (data.startingDay + 6) % 7} // Monday as first day
    }, [currentDate]);


    const appointmentSchedulesByDayOfWeek: Record<string, AppointmentSchedule[]> = useMemo(() => {
        const startDay = days[0];
        const lastDay = days[days.length - 1];
        const appointmentSchedulesForMonth = professionalSchedules?.getAppointmentSchedulesByRange(startDay, lastDay) || [];
        return groupAppointmentSchedulesByDayOfWeek(appointmentSchedulesForMonth);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [professionalSchedules?.appointmentSchedules, days]);

    const datesWithVacationSchedules: Set<Date> = useMemo(() => {
        return professionalSchedules?.datesWithVacationSchedulesFromDates(days) || new Set<Date>();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [professionalSchedules?.vacationSchedules, days]);

    const specialSchedulesByDate: Record<string, SpecialSchedule[]> = useMemo(() => {
        const startDay = days[0];
        const lastDay = days[days.length - 1];
        const specialSchedulesForWeek = professionalSchedules?.getSpecialSchedulesByRange(startDay, lastDay) || [];
        return groupSpecialSchedulesByDate(specialSchedulesForWeek);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [professionalSchedules?.specialSchedules, days]);

    const monthlyStats = useMemo(() => {
        let totalAppointments = 0;
        let pendingCount = 0;
        let absentCount = 0;
        let peakDay = {date: 0, count: 0};

        days.forEach((day, i) => {
            const dateStr = day.toISOString().split("T")[0]
            const dayAppointments = appointments[dateStr] || []


            totalAppointments += dayAppointments.length;
            pendingCount += dayAppointments.filter(apt => apt.state === AppointmentState.PENDING).length;
            absentCount += dayAppointments.filter(apt => apt.state === AppointmentState.NO_SHOW).length;

            if (dayAppointments.length > peakDay.count) {
                peakDay = {date: i + 1, count: dayAppointments.length};
            }
        });

        return {totalAppointments, pendingCount, absentCount, peakDay};
    }, [appointments, days, currentDate]);

    const getAppointmentsForDate = (day: Date) => {
        const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(day).padStart(2, "0")}`;
        const dayAppointments = (appointments[dateStr] || []).filter(
            (apt) =>
                apt.state !== AppointmentState.CANCELLED
        );
        console.log(`getAppointmentsForDate - ${dateStr}:`, dayAppointments);
        return dayAppointments;
    };

    const isToday = (day: Date) => {
        const today = new Date();
        return isSameDay(today, day);
    };

    const isDayAvailable = createIsDayAvailableChecker(datesWithVacationSchedules, specialSchedulesByDate, appointmentSchedulesByDayOfWeek);


    const getDayStatus = (day: Date): "unavailable" | "free" | "partial" | "full" => {
        const dayOfWeek = day.getDay().toString();
        const appointmentsForDay = getAppointmentsForDate(day);
        if (!isDayAvailable(day)) return "unavailable";
        let totalSlots = 0;
        const specialSchedules = specialSchedulesByDate[getDateKey(day)] || [];
        const appointmentSchedules = appointmentSchedulesByDayOfWeek[dayOfWeek] || [];
        totalSlots += specialSchedules.reduce((sum, schedule) => {
            return sum + amountOfTimeSlots(schedule.startTime, schedule.endTime, appointmentDuration);
        }, 0)
        totalSlots += appointmentSchedules.reduce((sum, schedule) => {
            return sum + amountOfTimeSlots(schedule.startTime, schedule.endTime, appointmentDuration);
        }, 0)
        if (totalSlots == 0) return "unavailable";
        if (appointmentsForDay.length === 0) return "free";
        if (appointmentsForDay.length >= totalSlots) return "full";
        return "partial";
    };

    const weekDays = ["Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado", "Domingo"];

    return (
        <div className="flex-1 overflow-auto">
            <div className="bg-white rounded-lg border">
                <div className="flex justify-between items-center p-4 border-b">
                    <Button onClick={onPrevMonth} variant="outline" size="sm">
                        <ChevronLeft className="h-4 w-4"/>
                    </Button>
                    <div className="text-center">
                        <h2 className="text-lg font-semibold">
                            {currentDate.toLocaleDateString("es-ES", {month: "long", year: "numeric"})}
                        </h2>
                        <div className="text-xs text-gray-500">
                            {monthlyStats.totalAppointments} turnos | {monthlyStats.pendingCount} pendientes
                            | {monthlyStats.absentCount} ausentes
                            {monthlyStats.peakDay.count > 0 && (
                                <span> | Pico: {monthlyStats.peakDay.count} el {monthlyStats.peakDay.date}</span>
                            )}
                        </div>
                    </div>
                    <Button onClick={onNextMonth} variant="outline" size="sm">
                        <ChevronRight className="h-4 w-4"/>
                    </Button>
                </div>

                <div className="grid grid-cols-7 text-sm font-medium">
                    {weekDays.map((day) => (
                        <div key={day} className="p-4 text-center border-b">
                            {day}
                        </div>
                    ))}
                </div>

                <div className="grid grid-cols-7 text-sm">
                    {Array.from({length: monthAgendaStartingDate}).map((_, i) => (
                        <div key={`empty-${i}`} className="p-4 border-b border-r min-h-[120px] bg-gray-50"/>
                    ))}
                    {days.map((day, i) => {
                        const isAvailable = isDayAvailable(day);
                        const dayAppointments = getAppointmentsForDate(day);
                        const status = getDayStatus(day);

                        const statusBadges = {
                            free: {text: "Disponible", className: "bg-green-100 text-green-700"},
                            partial: {text: "Hay pacientes", className: "bg-blue-100 text-blue-700"},
                            full: {text: "Completo", className: "bg-red-100 text-red-700"},
                        };

                        const statusBadge = status !== "unavailable" ? statusBadges[status] : null;

                        return (
                            <button
                                key={i}
                                onClick={() => {
                                    if (isAvailable) {
                                        onDateClick(day);
                                        setView("day");
                                    }
                                }}
                                disabled={!isAvailable}
                                className={`-pt-1 px-4 pb-4 border-b border-r min-h-[120px] text-left relative transition-colors
                  ${isAvailable ? "hover:bg-gray-50" : "bg-gray-100 cursor-not-allowed"}
                  ${isToday(day) ? "bg-blue-50" : ""}`}
                            >
                                <div className="flex items-center gap-2">
                                    <div className={`font-medium ${isAvailable ? "" : "text-gray-400"}`}>
                                        {i + 1}
                                    </div>
                                    {statusBadge && (
                                        <div
                                            className={`inline-block px-2 py-0.5 rounded-full text-xs ${statusBadge.className}`}>
                                            {statusBadge.text}
                                        </div>
                                    )}
                                </div>

                                {dayAppointments.length > 0 && (
                                    <div className="space-y-1 mt-2">
                                        <div className={`text-xs ${isAvailable ? "text-gray-600" : "text-gray-400"}`}>
                                            {dayAppointments.length} {dayAppointments.length === 1 ? "turno" : "turnos"}
                                        </div>
                                        <div className="flex gap-1">
                                            {[AppointmentState.PENDING, AppointmentState.COMPLETE, AppointmentState.NO_SHOW].map(status => {
                                                const count = dayAppointments.filter(apt => apt.state === status).length;
                                                return count > 0 ? (
                                                    <span
                                                        key={status}
                                                        className={`w-2 h-2 rounded-full
                              ${status === AppointmentState.PENDING ? "bg-yellow-400" : status === AppointmentState.COMPLETE ? "bg-green-400" : "bg-red-400"}`}
                                                        title={`${status}: ${count}`}
                                                    />
                                                ) : null;
                                            })}
                                        </div>
                                    </div>
                                )}
                            </button>
                        );
                    })}
                </div>
            </div>
        </div>
    );
}