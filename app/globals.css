@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-recoleta: "Recoleta", serif;
    --font-sans: InterVariable, system-ui, sans-serif;
    --font-sans--font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;

    --ring: 217.2 32.6% 17.5%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--font-sans--font-feature-settings);
    font-variation-settings: initial;
    -webkit-tap-highlight-color: transparent;
  }
  .modal-visible {
    display: block !important;
    z-index: 9999 !important;
  }
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Prevent horizontal scrolling on mobile and ensure layout never exceeds viewport width */
@layer base {
  html, body {
    max-width: 100%;
    overflow-x: hidden;
  }
}

/* Landing page specific styles */
.neighborhood-carousel {
  position: relative;
  display: inline-block;
  overflow: hidden;
  vertical-align: bottom;
}

.neighborhood-carousel span {
  display: block;
  position: absolute;
  width: 100%;
  transform: translateY(0);
  opacity: 1;
}

.neighborhood-carousel span:first-child {
  position: relative;
}

.neighborhood-carousel .entering {
  transform: translateY(100%);
  opacity: 0;
}

.neighborhood-carousel .current {
  transform: translateY(0);
  opacity: 1;
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.neighborhood-carousel .exiting {
  transform: translateY(-100%);
  opacity: 0;
  transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.font-recoleta {
  font-family: var(--font-recoleta);
}