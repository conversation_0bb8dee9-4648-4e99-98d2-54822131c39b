import { useContext, useState, useRef, useEffect } from "react"
import { CoverageContext } from "@/contexts/CoverageContext"
import { DoctorContext } from "@/contexts/DoctorContext"
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from "@/components/ui/collapsible"
import { Plus, X, Trash, ChevronDown, ChevronRight, Shield, AlertCircle, Info, Check, CreditCard } from "lucide-react"
import { Switch } from "@/components/ui/switch"

export default function CoverageCard() {
  const {
    medicalCoverages,
    setMedicalCoverages,
    setIsAddCoverageDialogOpen,
    doctorCoverageExceptions,
    setDoctorCoverageExceptions,
    toggleDoctorCoverageException,
    isDoctorCoverageExcluded,
    availableCoverages,
    toggleMedicalCenterCoverage,
    isCoverageAcceptedByMedicalCenter
  } = useContext(CoverageContext)

  const { doctors } = useContext(DoctorContext)
  const { activeMedicalCenterId, medicalCenters } = useContext(MedicalCenterContext)

  const [expandedCoverages, setExpandedCoverages] = useState<{ [coverageId: string]: boolean }>({})
  const [showPlanDropdown, setShowPlanDropdown] = useState(false)
  const [activeCoverageId, setActiveCoverageId] = useState<string | null>(null)
  const [sinCoberturaId, setSinCoberturaId] = useState<string | null>(null)
  const [sinCoberturaEnabled, setSinCoberturaEnabled] = useState(true)
  const [sinCoberturaExpanded, setSinCoberturaExpanded] = useState(false)
  const [sinCoberturaExceptionsExpanded, setSinCoberturaExceptionsExpanded] = useState(false)
  const [selectedPlans, setSelectedPlans] = useState<string[]>([])
  // This state is used to force re-renders to fix the exclusive badge issue
  const [, setForceRender] = useState(false)

  const planDropdownRef = useRef<HTMLDivElement>(null)

  // Initialize Sin Cobertura state
  useEffect(() => {
    if (!availableCoverages || !activeMedicalCenterId) return

    // Find Sin Cobertura in available coverages
    const defaultSinCobertura = availableCoverages.find(c => c.name === "Sin Cobertura")
    if (!defaultSinCobertura) return

    // Store the Sin Cobertura ID for later use
    setSinCoberturaId(defaultSinCobertura.id)

    // Check if Sin Cobertura is accepted by the medical center
    const isAccepted = isCoverageAcceptedByMedicalCenter(defaultSinCobertura.id)
    setSinCoberturaEnabled(isAccepted)

    // Make sure Sin Cobertura is in the medical coverages list
    if (!medicalCoverages.some(c => c.id === defaultSinCobertura.id)) {
      setMedicalCoverages([...medicalCoverages, defaultSinCobertura])
    }
  }, [medicalCoverages, availableCoverages, activeMedicalCenterId, isCoverageAcceptedByMedicalCenter, setMedicalCoverages])

  // Initialize component and force a re-render after a short delay
  // This helps with the exclusive badge issue
  useEffect(() => {
    // Force a re-render after a short delay to ensure all data is properly loaded
    const timer = setTimeout(() => {
      console.log('Forcing re-render of CoverageCard');
      setForceRender(prev => !prev); // Toggle to force re-render
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Close plan dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (planDropdownRef.current && !planDropdownRef.current.contains(event.target as Node)) {
        setShowPlanDropdown(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const toggleCoverageExpanded = (coverageId: string) => {
    setExpandedCoverages({
      ...expandedCoverages,
      [coverageId]: !expandedCoverages[coverageId]
    })
  }

  const handleShowPlanDropdown = (coverageId: string) => {
    setActiveCoverageId(coverageId)

    // Initialize selected plans with the current plans for this coverage
    const coverage = medicalCoverages.find(c => c.id === coverageId)
    if (coverage) {
      setSelectedPlans([...coverage.plans])
    } else {
      setSelectedPlans([])
    }

    setShowPlanDropdown(true)
  }

  // Toggle plan selection
  const togglePlanSelection = (plan: string) => {
    setSelectedPlans(prev => {
      if (prev.includes(plan)) {
        return prev.filter(p => p !== plan)
      } else {
        return [...prev, plan]
      }
    })
  }

  // Save selected plans
  const savePlans = () => {
    if (activeCoverageId) {
      const updatedCoverages = medicalCoverages.map(c => {
        if (c.id === activeCoverageId) {
          return {
            ...c,
            plans: selectedPlans
          }
        }
        return c
      })

      setMedicalCoverages(updatedCoverages)
      setShowPlanDropdown(false)
    }
  }

  const handleRemovePlan = (coverageId: string, plan: string) => {
    const updatedCoverages = medicalCoverages.map((c) => {
      if (c.id === coverageId) {
        return {
          ...c,
          plans: c.plans.filter((p) => p !== plan)
        }
      }
      return c
    })
    setMedicalCoverages(updatedCoverages)
  }

  const handleRemoveCoverage = (coverageId: string) => {
    // Don't allow removing "Sin Cobertura"
    const coverage = medicalCoverages.find(c => c.id === coverageId)
    if (coverage && coverage.name === "Sin Cobertura") {
      return
    }

    // Remove the coverage from the medical coverages list (this is now sufficient)
    setMedicalCoverages(medicalCoverages.filter((c) => c.id !== coverageId))

    // Clean up any doctor exceptions for this coverage
    const newExceptions = { ...doctorCoverageExceptions }
    doctors.forEach((doctor) => {
      if (newExceptions[doctor.id]) {
        const filteredExceptions = newExceptions[doctor.id].filter((e) => e.coverageId !== coverageId)
        if (filteredExceptions.length > 0) newExceptions[doctor.id] = filteredExceptions
        else delete newExceptions[doctor.id]
      }
    })
    setDoctorCoverageExceptions(newExceptions)
  }

  // Toggle Sin Cobertura
  const toggleSinCobertura = (checked: boolean) => {
    if (!sinCoberturaId || !activeMedicalCenterId) return

    // Toggle the coverage for the medical center
    toggleMedicalCenterCoverage(sinCoberturaId)

    // Update local state
    setSinCoberturaEnabled(checked)

    // If disabling, clear any doctor exceptions for Sin Cobertura and collapse the expanded box
    if (!checked && sinCoberturaId) {
      // Collapse the expanded box
      setSinCoberturaExpanded(false)
      setSinCoberturaExceptionsExpanded(false)

      const newExceptions = { ...doctorCoverageExceptions }

      // Remove all Sin Cobertura exceptions for doctors in this medical center
      const medicalCenterDoctors = doctors.filter(d => d.id)
      medicalCenterDoctors.forEach(doctor => {
        if (newExceptions[doctor.id]) {
          // Set all doctors to "No acepta" by adding exceptions for Sin Cobertura
          const existingException = newExceptions[doctor.id].find(
            e => e.coverageId === sinCoberturaId && e.planId === null
          )

          if (!existingException) {
            if (!newExceptions[doctor.id]) {
              newExceptions[doctor.id] = []
            }

            newExceptions[doctor.id].push({
              coverageId: sinCoberturaId,
              planId: null,
              excluded: true
            })
          }
        } else {
          // Create new exceptions for doctors that don't have any
          newExceptions[doctor.id] = [{
            coverageId: sinCoberturaId,
            planId: null,
            excluded: true
          }]
        }
      })

      setDoctorCoverageExceptions(newExceptions)
    }
  }

  return (
    <>
      <Card className="bg-white shadow-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-blue-600" />
              <div>
                <CardTitle className="text-[1.3rem] font-semibold text-gray-900">
                  Coberturas médicas
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Configure las coberturas aceptadas por el establecimiento y sus profesionales.
                </CardDescription>
              </div>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white" onClick={() => setIsAddCoverageDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Agregar cobertura
            </Button>
          </div>
        </CardHeader>
        <CardContent>

          {/* Info box */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 flex items-start">
            <Info className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-blue-800 mb-1">Cómo funcionan las coberturas</h3>
              <p className="text-sm text-blue-700">
                {(() => {
                  // Get doctors in the current medical center
                  const medicalCenterDoctors = doctors.filter(doctor => {
                    const medicalCenter = medicalCenters.find(mc => mc.id === activeMedicalCenterId);
                    return medicalCenter && medicalCenter.doctors.includes(doctor.id);
                  });

                  if (medicalCenterDoctors.length <= 1) {
                    return "Las coberturas que agregue aquí se aplicarán automáticamente al profesional del establecimiento.";
                  } else {
                    return "Las coberturas que agregue aquí estarán disponibles para todos los profesionales del establecimiento. Puede configurar excepciones específicas para cada profesional expandiendo la cobertura.";
                  }
                })()}
              </p>
            </div>
          </div>

          {/* Sin Cobertura section */}
          {sinCoberturaId && (
            <div className="mb-6">
              <Collapsible
                open={sinCoberturaExpanded}
                className="bg-white overflow-hidden shadow-sm border border-blue-100 rounded-lg"
              >
                <div className="p-4 bg-blue-50 border-b flex items-center justify-between">
                  <CollapsibleTrigger
                    onClick={() => setSinCoberturaExpanded(!sinCoberturaExpanded)}
                    className="flex items-center text-left"
                  >
                    {sinCoberturaExpanded ? (
                      <ChevronDown className="h-4 w-4 mr-2 text-blue-600" />
                    ) : (
                      <ChevronRight className="h-4 w-4 mr-2 text-blue-600" />
                    )}
                    <span className="text-lg font-medium text-gray-900">Sin Cobertura</span>
                  </CollapsibleTrigger>
                  <div className="flex items-center gap-2">
                      <span className={`text-sm ${sinCoberturaEnabled ? 'text-blue-600' : 'text-gray-300'}`}>
                        {sinCoberturaEnabled ? "Habilitado" : "Deshabilitado"}
                      </span>
                    <Switch
                      checked={sinCoberturaEnabled}
                      onCheckedChange={toggleSinCobertura}
                    />
                  </div>
                </div>
                <CollapsibleContent>
                  <div className="p-4">
                    <div className="mb-4">
                      <Collapsible
                        open={sinCoberturaExceptionsExpanded}
                        onOpenChange={setSinCoberturaExceptionsExpanded}
                        className="border rounded-lg overflow-hidden"
                      >
                        <CollapsibleTrigger className="w-full p-3 flex items-center justify-between bg-blue-50 text-blue-800 hover:bg-blue-100">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4" />
                            <span className="font-medium">Excepciones por profesional</span>
                          </div>
                          {sinCoberturaExceptionsExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </CollapsibleTrigger>
                        <CollapsibleContent className="border-t">
                          <div className="p-4">
                            <p className="text-sm text-gray-500 mb-4">
                              Por defecto, todos los profesionales aceptan pacientes sin cobertura.
                              Puede configurar excepciones para profesionales específicos.
                            </p>
                            <div className="space-y-3">
                              {doctors
                                .filter(doctor => {
                                  // Only show doctors from the current medical center
                                  const medicalCenter = medicalCenters.find(mc => mc.id === activeMedicalCenterId);
                                  return medicalCenter && medicalCenter.doctors.includes(doctor.id);
                                })
                                .map((doctor) => (
                          <div key={doctor.id} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-semibold">
                                {doctor.initial}
                              </div>
                              <span className="font-medium">{doctor.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-gray-600">
                                {isDoctorCoverageExcluded(doctor.id, sinCoberturaId) ? "No acepta" : "Acepta"}
                              </span>
                              <Switch
                                checked={!isDoctorCoverageExcluded(doctor.id, sinCoberturaId)}
                                onCheckedChange={() => {
                                  if (sinCoberturaId && activeMedicalCenterId && sinCoberturaEnabled) {
                                    toggleDoctorCoverageException(doctor.id, sinCoberturaId)
                                  }
                                }}
                                disabled={!sinCoberturaEnabled}
                              />
                            </div>
                          </div>
                                ))}
                            </div>
                          </div>
                        </CollapsibleContent>
                      </Collapsible>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          )}

          {/* Other coverages */}
          {medicalCoverages.filter(coverage => coverage.name !== "Sin Cobertura").length > 0 ? (
            <div>
              {medicalCoverages
                .filter(coverage => coverage.name !== "Sin Cobertura")
                .sort((a, b) => a.name.localeCompare(b.name))
                .map((coverage) => (
                <Collapsible
                  key={coverage.id}
                  open={expandedCoverages[coverage.id] || false}
                  className="bg-white overflow-hidden shadow-sm border border-blue-100 rounded-lg mb-3"
                >
                  <div className="p-4 bg-blue-50 border-b flex items-center justify-between">
                    <CollapsibleTrigger
                      onClick={() => toggleCoverageExpanded(coverage.id)}
                      className="flex items-center text-left"
                    >
                      {expandedCoverages[coverage.id] ? (
                        <ChevronDown className="h-4 w-4 mr-2 text-blue-600" />
                      ) : (
                        <ChevronRight className="h-4 w-4 mr-2 text-blue-600" />
                      )}
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-medium text-gray-900">{coverage.name}</span>
                        {/* Show doctor-specific badge if this coverage is accepted by exactly one doctor AND there are multiple doctors */}
                        {(() => {
                          // Only check for doctors in the current medical center
                          const medicalCenterDoctors = doctors.filter(doctor => {
                            const medicalCenter = medicalCenters.find(mc => mc.id === activeMedicalCenterId);
                            return medicalCenter && medicalCenter.doctors.includes(doctor.id);
                          });

                          // If there's only one doctor in the medical center, don't show exclusive badges
                          // because all coverages would naturally be "exclusive" to that doctor
                          if (medicalCenterDoctors.length <= 1) {
                            return null;
                          }

                          // Get doctors accepting this coverage
                          const doctorsAcceptingCoverage = medicalCenterDoctors
                            .filter(doctor => !isDoctorCoverageExcluded(doctor.id, coverage.id))
                            .map(doctor => doctor.id);

                          console.log('Coverage:', coverage.name, 'is accepted by', doctorsAcceptingCoverage.length, 'doctors');

                          // A coverage is exclusive to a doctor if exactly one doctor accepts it AND there are multiple doctors
                          if (doctorsAcceptingCoverage.length === 1) {
                            const doctorId = doctorsAcceptingCoverage[0];
                            const doctor = doctors.find(d => d.id === doctorId);

                            if (doctor) {
                              return (
                                <Badge className="bg-purple-100 text-purple-800 border border-purple-200 hover:bg-purple-200">
                                  Exclusiva de {doctor.name}
                                </Badge>
                              );
                            }
                          }
                          return null;
                        })()}
                      </div>
                    </CollapsibleTrigger>
                    <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveCoverage(coverage.id)}
                      className="text-red-500 hover:bg-red-100/50 hover:text-red-600 transition-colors px-2 py-1 rounded-md">
                      <Trash className="h-4 w-4 mr-1" />
                      Eliminar
                    </Button>
                    </div>
                  </div>
                  <CollapsibleContent>
                    <div className="p-4">
                      <div className="mb-4">
                        <h3 className="text-sm font-medium text-gray-700 mb-2">Planes</h3>
                        <div className="flex flex-wrap gap-2 mb-2">
                          {coverage.plans.map((plan) => (
                            <Badge
                              key={plan}
                              className="bg-blue-100 text-blue-800 hover:bg-blue-200 flex items-center gap-1"
                            >
                              {plan}
                              <button
                                onClick={() => handleRemovePlan(coverage.id, plan)}
                                className="ml-1 text-blue-600 hover:text-blue-800"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                        <div className="flex justify-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleShowPlanDropdown(coverage.id)}
                          className="mt-2 flex items-center"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Seleccionar planes
                        </Button>
                        </div>
                      </div>

                      {/* Doctor exceptions section */}
                      <Collapsible className="border rounded-lg overflow-hidden">
                        <CollapsibleTrigger className="w-full p-3 flex items-center justify-between bg-blue-50 text-blue-800 hover:bg-blue-100">
                          <div className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4" />
                            <span className="font-medium">Excepciones por profesional</span>
                          </div>
                          <ChevronDown className="h-4 w-4" />
                        </CollapsibleTrigger>
                        <CollapsibleContent className="border-t">
                          {(
                            doctors
                              .filter(doctor => {
                                // Only show doctors from the current medical center
                                const medicalCenter = medicalCenters.find(mc => mc.id === activeMedicalCenterId);
                                return medicalCenter && medicalCenter.doctors.includes(doctor.id);
                              })
                              .map((doctor) => (
                            <div key={doctor.id} className="p-4 border-b">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-semibold">
                                    {doctor.initial}
                                  </div>
                                  <span className="font-medium">{doctor.name}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="text-sm text-gray-600">
                                    {isDoctorCoverageExcluded(doctor.id, coverage.id) ? "No aceptada" : "Aceptada"}
                                  </span>
                                  <Switch
                                    checked={!isDoctorCoverageExcluded(doctor.id, coverage.id)}
                                    onCheckedChange={() => {
                                      console.log('Toggling exception for doctor:', doctor.name, 'coverage:', coverage.name);
                                      console.log('Current exclusion status:', isDoctorCoverageExcluded(doctor.id, coverage.id));

                                      // Toggle the doctor's exception for this coverage
                                      toggleDoctorCoverageException(doctor.id, coverage.id);

                                      // Log the new status after a short delay
                                      setTimeout(() => {
                                        console.log('New exclusion status:', isDoctorCoverageExcluded(doctor.id, coverage.id));
                                      }, 100);
                                    }}
                                  />
                                </div>
                              </div>
                              {coverage.plans.length > 0 && (
                                <div className="ml-10 mt-2">
                                  <h4 className="text-xs font-medium text-gray-500 mb-2">Planes específicos</h4>
                                  <div className="space-y-2">
                                    {coverage.plans.map((plan) => (
                                      <div key={plan} className="flex items-center justify-between">
                                        <span className="text-sm">{plan}</span>
                                        <div className="flex items-center gap-2">
                                          <span className="text-xs text-gray-600">
                                            {isDoctorCoverageExcluded(doctor.id, coverage.id) ?
                                              "No acepta" :
                                              (isDoctorCoverageExcluded(doctor.id, coverage.id, plan) ? "No acepta" : "Acepta")}
                                          </span>
                                          <Switch
                                            // If the main coverage is excluded, all plans should be off
                                            // Otherwise, check the plan-specific exclusion
                                            checked={!isDoctorCoverageExcluded(doctor.id, coverage.id) ?
                                              !isDoctorCoverageExcluded(doctor.id, coverage.id, plan) : false}
                                            // Disable the switch if the main coverage is excluded
                                            disabled={isDoctorCoverageExcluded(doctor.id, coverage.id)}
                                            onCheckedChange={() => {
                                              // Only toggle if the main coverage is not excluded
                                              if (!isDoctorCoverageExcluded(doctor.id, coverage.id)) {
                                                // Toggle the doctor's exception for this coverage plan
                                                toggleDoctorCoverageException(doctor.id, coverage.id, plan);
                                              }
                                            }}
                                            className="scale-90"
                                          />
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          )))}
                        </CollapsibleContent>
                      </Collapsible>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Shield className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No hay coberturas configuradas</h3>
              <p className="text-gray-600 mb-4">Agregue las coberturas médicas que acepta su establecimiento</p>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white mt-2" onClick={() => setIsAddCoverageDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Agregar cobertura
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Plan selection dropdown */}
      {showPlanDropdown && activeCoverageId && (
        <div
          ref={planDropdownRef}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
        >
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-4">
            <div className="flex justify-between items-center mb-4 border-b pb-2">
              <h3 className="text-lg font-medium">
                Planes para {medicalCoverages.find(c => c.id === activeCoverageId)?.name}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPlanDropdown(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="max-h-[300px] overflow-y-auto mb-4">
              {/* Get available plans for this coverage from availableCoverages */}
              {(() => {
                const coverage = availableCoverages.find(c => c.id === activeCoverageId);
                const availablePlans = coverage?.plans || [];

                if (availablePlans.length === 0) {
                  return (
                    <div className="text-center py-4 text-gray-500">
                      <p>No hay planes disponibles para esta cobertura</p>
                    </div>
                  );
                }

                return (
                  <div className="space-y-2">
                    {availablePlans.map((plan) => (
                      <div
                        key={plan}
                        onClick={() => togglePlanSelection(plan)}
                        className={`p-2 rounded-md cursor-pointer flex justify-between items-center ${selectedPlans.includes(plan) ? 'bg-blue-100' : 'hover:bg-gray-100'}`}
                      >
                        <span>{plan}</span>
                        {selectedPlans.includes(plan) && (
                          <Check className="h-4 w-4 text-blue-600" />
                        )}
                      </div>
                    ))}
                  </div>
                );
              })()}
            </div>

            <div className="flex justify-end space-x-2 pt-2 border-t">
              <Button
                variant="outline"
                onClick={() => setShowPlanDropdown(false)}
              >
                Cancelar
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={savePlans}
              >
                Guardar
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
