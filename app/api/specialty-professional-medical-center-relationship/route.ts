import { NextRequest, NextResponse } from 'next/server';

export interface CreateSpecialtyProfessionalMedicalCenterRelationshipRequest {
    specialtyName: string;
    professionalId: number;
    medicalCenterId: number;
    createdById: number;
}

export interface CreateSpecialtyProfessionalMedicalCenterRelationshipResponse {
    id: object;
    specialty: object;
    professional: any;
    medicalCenter: object;
    createdBy: object;
    createdAt: string; // date-time
    specialtyName: string;
}

export async function POST(request: NextRequest) {
    try {
        const body: CreateSpecialtyProfessionalMedicalCenterRelationshipRequest = await request.json();

        // Validate required fields
        const requiredFields: (keyof CreateSpecialtyProfessionalMedicalCenterRelationshipRequest)[] = [
            'specialtyName',
            'professionalId',
            'medicalCenterId',
            'createdById'
        ];

        for (const field of requiredFields) {
            if (body[field] === undefined || body[field] === null) {
                return NextResponse.json(
                    { error: `Missing required field: ${field}` },
                    { status: 400 }
                );
            }
        }

        // Validate specialtyName is not empty
        if (body.specialtyName.trim().length === 0) {
            return NextResponse.json(
                { error: 'Specialty name cannot be empty' },
                { status: 400 }
            );
        }

        // Validate numeric fields are positive
        if (body.professionalId <= 0) {
            return NextResponse.json(
                { error: 'ProfessionalId must be a positive number' },
                { status: 400 }
            );
        }

        if (body.medicalCenterId <= 0) {
            return NextResponse.json(
                { error: 'MedicalCenterId must be a positive number' },
                { status: 400 }
            );
        }

        if (body.createdById <= 0) {
            return NextResponse.json(
                { error: 'CreatedById must be a positive number' },
                { status: 400 }
            );
        }

        // Make request to external API
        const response = await fetch(
            `${process.env.BACKEND_URL}/specialty-professional-medical-center-relationship`,
            {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to create specialty-professional-medical-center relationship',
                    details: errorText,
                    status: response.status
                },
                { status: response.status }
            );
        }

        const result = await response.json();
        return NextResponse.json(result, { status: 200 });

    } catch (error) {
        console.error('Error creating specialty-professional-medical-center relationship:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
