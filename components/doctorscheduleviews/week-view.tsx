"use client"
import React, {useEffect, useMemo, useState} from "react"
import {But<PERSON>} from "@/components/ui/button"
import {ChevronLeft, ChevronRight, Lock, Unlock} from "lucide-react"
import {endOfWeek, format, isSameDay} from "date-fns"
import {es} from "date-fns/locale"
import {
    AppointmentSchedule,
    BlockedSlot,
    groupAppointmentSchedulesByDayOfWeek,
    groupSpecialSchedulesByDate,
    ProfessionalSchedulesResponse,
    SpecialSchedule
} from "@/types/professional-schedules"
import {OvercrowdedContextMenu} from "@/components/ui/overcrowded-context-menu"
import {generateTimeSlots, getDateKey} from "@/utils/dateUtils"
import {isSlotBlocked} from "@/utils/appointmentUtils";
import {toggleBlockedSlot, ToggleBlockedSlotRequest} from "@/app/api/utils/appointment/SlotApiUtils";
import {useAuth} from "@/contexts/AuthContext";
import {getEmployeeUserId} from "@/utils/userUtils";
import {createIsDayAvailableChecker} from "@/utils/ViewUtils";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

interface WeekViewProps {
    appointmentsByDate: Record<string, ProfessionalAppointment[]>
    blockedSlots: Record<string, BlockedSlot[]>
    currentDate: Date
    onAppointmentSelect: (appointment: ProfessionalAppointment | null) => void
    onDateSelect: (date: Date) => void
    weekStart: Date
    setWeekStart: (date: Date) => void
    selectedAppointment: ProfessionalAppointment | null
    professionalSchedules: ProfessionalSchedulesResponse | null
    appointmentDuration: number
    onContextMenu?: (e: React.MouseEvent, appointment: ProfessionalAppointment) => void
    onOvercrowdedSelect?: (e: React.MouseEvent, appointments: ProfessionalAppointment[]) => void
    // Patient management now handled by PatientContext
    doctorId: number
    medicalCenterId: number
    onFreeSlotSelect?: (date: string, time: string) => void
    selectedSlot: string | null // "date:time" (e.g., "2025-02-24:10:00")
    setSelectedSlot: (slot: string | null) => void
    onWeekChange?: (newWeekStart: Date) => void
}

export function WeekView({
                             appointmentsByDate,
                             blockedSlots,
                             currentDate,
                             onAppointmentSelect,
                             onDateSelect,
                             weekStart,
                             setWeekStart,
                             selectedAppointment,
                             professionalSchedules,
                             appointmentDuration,
                             onContextMenu,
                             onOvercrowdedSelect,
                             // Patient management now handled by PatientContext
                             doctorId,
                             medicalCenterId,
                             onFreeSlotSelect,
                             selectedSlot,
                             setSelectedSlot,
                             onWeekChange,
                         }: WeekViewProps) {
    const [localSelectedAppointment, setLocalSelectedAppointment] = useState<ProfessionalAppointment | null>(null)
    const [overcrowdedMenu, setOvercrowdedMenu] = useState<{
        x: number;
        y: number;
        appointments: ProfessionalAppointment[]
    } | null>(null)
    const weekEnd = endOfWeek(weekStart, {weekStartsOn: 1})
    // State to track current time
    const [currentTime, setCurrentTime] = useState(new Date());
    const {currentUser, logout} = useAuth();
    const MENU_WIDTH = 150
    const MENU_HEIGHT = 120

    const getDaysInWeek = (start: Date) => {
        const week = []
        for (let i = 0; i < 7; i++) {
            const day = new Date(start)
            day.setDate(start.getDate() + i)
            week.push(day)
        }
        return week
    }

    const weekDays = useMemo(() => getDaysInWeek(weekStart), [weekStart]);

    const appointmentSchedulesByDayOfWeek: Record<string, AppointmentSchedule[]> = useMemo(() => {
        const weekEnd: Date = endOfWeek(weekStart, {weekStartsOn: 1})
        const appointmentSchedulesForWeek = professionalSchedules?.getAppointmentSchedulesByRange(weekStart, weekEnd) || [];
        return groupAppointmentSchedulesByDayOfWeek(appointmentSchedulesForWeek);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [professionalSchedules?.appointmentSchedules, weekStart]);

    const datesWithVacationSchedules: Set<Date> = useMemo(() => {
        return professionalSchedules?.datesWithVacationSchedulesFromDates(weekDays) || new Set<Date>();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [professionalSchedules?.vacationSchedules, weekStart]);

    const specialSchedulesByDate: Record<string, SpecialSchedule[]> = useMemo(() => {
        const weekEnd: Date = endOfWeek(weekStart, {weekStartsOn: 1});
        const specialSchedulesForWeek = professionalSchedules?.getSpecialSchedulesByRange(weekStart, weekEnd) || [];
        return groupSpecialSchedulesByDate(specialSchedulesForWeek);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [professionalSchedules?.specialSchedules, weekStart]);

    // Update current time every minute
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date());
        }, 60000); // Update every minute

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        setLocalSelectedAppointment(selectedAppointment)
    }, [selectedAppointment, localSelectedAppointment])


    const getDayTimeSlots = (day: Date) => {
        if (datesWithVacationSchedules.has(day)) {
            return [];
        }
        const slots: string[] = []

        const specialSchedulesForDay: SpecialSchedule[] = specialSchedulesByDate[getDateKey(day)] || [];
        if (specialSchedulesForDay.length > 0) {
            specialSchedulesForDay.forEach(specialSchedule => {
                slots.concat(generateTimeSlots(specialSchedule.startTime, specialSchedule.endTime, appointmentDuration))
            })
        }
        const dayOfWeek: string = day.toLocaleDateString('en-US', {weekday: 'long'}).toLowerCase();
        const appointmentSchedulesForDay: AppointmentSchedule[] = appointmentSchedulesByDayOfWeek[dayOfWeek] || [];
        if (appointmentSchedulesForDay.length > 0) {
            appointmentSchedulesForDay.forEach(schedule => {
                slots.concat(generateTimeSlots(schedule.startTime, schedule.endTime, appointmentDuration))
            })
        }
        return slots;
    }

    const getHourRange = () => {
        let minHour = 23
        let maxHour = 0
        weekDays.forEach(day => {
            const slots = getDayTimeSlots(day)
            if (slots.length > 0) {
                const startHour = parseInt(slots[0].split(":")[0])
                const endHour = parseInt(slots[slots.length - 1].split(":")[0])
                minHour = Math.min(minHour, startHour)
                maxHour = Math.max(maxHour, endHour + 1) // +1 to include the last hour block
            }
        })
        return {minHour: minHour === 23 ? 8 : minHour, maxHour: maxHour === 0 ? 23 : maxHour}
    }

    const {minHour, maxHour} = getHourRange()
    const hours = Array.from({length: maxHour - minHour}, (_, i) => i + minHour)

    const isDayAvailable = createIsDayAvailableChecker(datesWithVacationSchedules, specialSchedulesByDate, appointmentSchedulesByDayOfWeek);

    const handlePrevWeek = () => {
        const newWeekStart = new Date(weekStart)
        newWeekStart.setDate(newWeekStart.getDate() - 7)
        setWeekStart(newWeekStart)
        if (onWeekChange) {
            onWeekChange(newWeekStart); // Notify parent to sync sidebar
        }
    }

    const handleNextWeek = () => {
        const newWeekStart = new Date(weekStart)
        newWeekStart.setDate(newWeekStart.getDate() + 7)
        setWeekStart(newWeekStart)
        if (onWeekChange) {
            onWeekChange(newWeekStart); // Notify parent to sync sidebar
        }
    }

    const adjustMenuPosition = (x: number, y: number) => {
        const adjustedX = Math.min(x, window.innerWidth - MENU_WIDTH)
        const adjustedY = Math.min(y, window.innerHeight - MENU_HEIGHT)
        return {x: Math.max(0, adjustedX), y: Math.max(0, adjustedY)}
    }

    const handleAppointmentClick = (e: React.MouseEvent, appointment: ProfessionalAppointment) => {
        e.stopPropagation()
        e.preventDefault()

        // Check if the appointment is already selected, if so, deselect it
        if (localSelectedAppointment?.id === appointment.id &&
            localSelectedAppointment?.date === appointment.date) {
            setLocalSelectedAppointment(null)
            setSelectedSlot(null)
            onAppointmentSelect(null)
            return
        }

        const dateStr = appointment.date
        const hourAppointments = appointmentsByDate[dateStr]?.filter(apt => {
            const [aptHour] = apt.startTime.split(":")
            return Number.parseInt(aptHour) === Number.parseInt(appointment.startTime.split(":")[0])
        }) || []
        const sameTimeAppointments = hourAppointments
            .filter(apt => apt.startTime === appointment.startTime)


        const {x: adjustedClientX, y: adjustedClientY} = adjustMenuPosition(e.clientX, e.clientY)

        if (sameTimeAppointments.length > 1 && onOvercrowdedSelect) {
            setLocalSelectedAppointment(null)
            setSelectedSlot(null)
            onAppointmentSelect(null)
            setOvercrowdedMenu({x: adjustedClientX, y: adjustedClientY, appointments: sameTimeAppointments})
        } else if (onContextMenu) {
            setLocalSelectedAppointment(appointment)
            setSelectedSlot(null)
            onAppointmentSelect(appointment)
            const syntheticEvent = {
                pageX: adjustedClientX,
                pageY: adjustedClientY,
                stopPropagation: () => {
                },
                preventDefault: () => {
                },
            } as React.MouseEvent
            onContextMenu(syntheticEvent, appointment)
        }
    }

    const handleOvercrowdedSelect = (appointment: ProfessionalAppointment) => {
        if (onContextMenu && overcrowdedMenu) {
            // Check if the appointment is already selected, if so, deselect it
            if (localSelectedAppointment?.id === appointment.id &&
                localSelectedAppointment?.date === appointment.date) {
                setLocalSelectedAppointment(null)
                setSelectedSlot(null)
                onAppointmentSelect(null)
                setOvercrowdedMenu(null)
                return
            }

            const {x: adjustedX, y: adjustedY} = adjustMenuPosition(overcrowdedMenu.x, overcrowdedMenu.y)
            const syntheticEvent = {
                pageX: adjustedX,
                pageY: adjustedY,
                stopPropagation: () => {
                },
                preventDefault: () => {
                },
            } as React.MouseEvent
            setLocalSelectedAppointment(appointment)
            setSelectedSlot(null)
            onAppointmentSelect(appointment)
            onContextMenu(syntheticEvent, appointment)
            setOvercrowdedMenu(null)
        }
    }

    const handleFreeSlotClick = (e: React.MouseEvent, date: string, time: string) => {
        // Check if the click was on the padlock icon or its container
        const target = e.target as HTMLElement;
        const isPadlockClick = target.closest('.padlock-icon-container');
        if (isPadlockClick) {
            // Don't do anything if the click was on the padlock
            return;
        }

        e.stopPropagation()
        const newSelectedSlot = `${date}:${time}`

        // Check if the clicked slot is already selected, if so, deselect it
        if (selectedSlot === newSelectedSlot) {

            setSelectedSlot(null)
            setLocalSelectedAppointment(null)
            onAppointmentSelect(null)
        } else {
            // Otherwise, select the new slot

            setSelectedSlot(newSelectedSlot)
            setLocalSelectedAppointment(null)
            onAppointmentSelect(null)
            if (onFreeSlotSelect) {
                onFreeSlotSelect(date, time)
            }
        }
    }

    // Function to toggle blocked slot
    const handleToggleBlockedSlot = (e: React.MouseEvent, dateString: string, timeString: string) => {
        e.stopPropagation();
        e.preventDefault();
        const employeeUserId = getEmployeeUserId(currentUser)
        const isCurrentlyBlocked =
            isSlotBlocked(blockedSlots, dateString, timeString, appointmentDuration);


        const toggleBlockedSlotRequest: ToggleBlockedSlotRequest = {
            date: dateString,
            startTime: timeString,
            appointmentSlotDuration: appointmentDuration,
            reason: "",
            professionalId: doctorId,
            medicalCenterId: medicalCenterId,
            employeeUserId: employeeUserId
        }
        toggleBlockedSlot(toggleBlockedSlotRequest, isCurrentlyBlocked);


        const slotKey = `${dateString}:${timeString}`;
        if (selectedSlot === slotKey && !isCurrentlyBlocked) {
            setSelectedSlot(null);
            setLocalSelectedAppointment(null);
            onAppointmentSelect(null);
        }
    }

    const handleOutsideClick = (event: React.MouseEvent) => {
        if ((event.target as HTMLElement).closest(".appointment-slot") === null) {
            setLocalSelectedAppointment(null)
            setSelectedSlot(null)
            onAppointmentSelect(null)
            setOvercrowdedMenu(null)
        }
    }

    const getAppointmentPosition = (time: string) => {
        const [, minutes] = time.split(":").map(Number)
        return `${(minutes / 60) * 100}%`
    }

    const getAppointmentHeight = () => {
        return `${(appointmentDuration / 60) * 100}%`
    }

    const getHourSlotHeight = () => {
        const slotsPerHour = 60 / appointmentDuration
        return `${slotsPerHour * 24}px`
    }

    const getSlotsNeeded = (appointment: ProfessionalAppointment): number => {
        return appointment.appointmentIntervalAmount;
    };

    // Helper to check if a time slot is in the past (only allow previous appointment slot)
    const isPastTimeSlot = (date: string, time: string): boolean => {
        const now = new Date();
        const [year, month, day] = date.split('-').map(Number);
        const [hours, minutes] = time.split(':').map(Number);
        const slotDate = new Date(year, month - 1, day, hours, minutes);

        // Only show current time indicator for today
        if (slotDate.toDateString() !== now.toDateString()) {
            // For dates other than today, all past slots are disabled
            return slotDate.getTime() < now.getTime();
        }

        // For today, find the current or next upcoming slot
        const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();
        const slotTimeMinutes = hours * 60 + minutes;

        // Get the time slots for this specific day
        const dayDate = new Date(year, month - 1, day);
        const dayTimeSlots = getDayTimeSlots(dayDate);

        // Find the index of the current slot in the day's time slots
        const currentSlotIndex = dayTimeSlots.findIndex(slot => {
            const [slotHour, slotMinute] = slot.split(':').map(Number);
            const slotTime = slotHour * 60 + slotMinute;
            return slotTime >= currentTimeMinutes;
        });

        // If we found a current/upcoming slot, allow only the previous slot to be available
        if (currentSlotIndex > 0) {
            const timeSlotIndex = dayTimeSlots.indexOf(time);
            // Allow only the immediately previous slot (currentSlotIndex - 1) and future slots
            return timeSlotIndex < currentSlotIndex - 1;
        } else if (currentSlotIndex === 0) {
            // If current time is before the first slot, no past slots to allow
            return slotTimeMinutes < currentTimeMinutes;
        } else {
            // If current time is after all slots, allow only the last slot
            const timeSlotIndex = dayTimeSlots.indexOf(time);
            return timeSlotIndex < dayTimeSlots.length - 1;
        }
    };

    const isSlotCoveredByMultiSlot = (time: string, appointments: ProfessionalAppointment[]): boolean => {
        return appointments.some(appointment => {
            return appointment.collidesWithTime(time, appointmentDuration)
        });
    };

    // Helper to calculate the current time position for the time indicator line
    const getCurrentTimePosition = (): { dayIndex: number; position: number; hasSobreturno: boolean } | null => {
        const now = new Date();

        // Find which day in the week matches today
        const todayIndex = weekDays.findIndex(day => isSameDay(day, now));
        if (todayIndex === -1) {
            return null; // Today is not in the displayed week
        }

        // Get current hours and minutes
        const hours = currentTime.getHours();
        const minutes = currentTime.getMinutes();

        // Check if current time is within the hour range we're displaying
        if (hours < minHour || hours >= maxHour) {
            return null;
        }

        // Calculate position based on hour and minute
        // Each hour block has a height defined by getHourSlotHeight()
        // We need to calculate the percentage through the current hour
        const hourPosition = hours - minHour; // Position in terms of full hours from the top
        const minutePercentage = minutes / 60; // How far through the current hour (0-1)

        // Calculate the pixel position
        // We use the hourSlotHeight to determine the height of each hour block
        const hourSlotHeight = parseInt(getHourSlotHeight().replace('px', ''));
        const position = (hourPosition + minutePercentage) * hourSlotHeight;

        // Check if the current time is over a slot with sobreturnos
        const day = weekDays[todayIndex];
        const year = day.getFullYear();
        const month = String(day.getMonth() + 1).padStart(2, '0');
        const dayOfMonth = String(day.getDate()).padStart(2, '0');
        const dateStr = `${year}-${month}-${dayOfMonth}`;

        // Get all appointments for this day (excluding cancelled ones)
        const dayAppointments = (appointmentsByDate[dateStr] || []).filter(apt => apt.state !== "CANCELLED");

        // Get appointments for the current hour
        const hourAppointments = dayAppointments.filter((apt) => {
            const [aptHour] = apt.startTime.split(":");
            return Number.parseInt(aptHour) === hours;
        });

        // Check if there are multiple appointments at the current time
        const sameTimeAppointments = hourAppointments.filter(apt => {
            const [aptHour, aptMinute] = apt.startTime.split(':').map(Number);
            const aptTimeInMinutes = aptHour * 60 + aptMinute;
            const currentTimeInMinutes = hours * 60 + minutes;
            return aptTimeInMinutes <= currentTimeInMinutes &&
                currentTimeInMinutes < aptTimeInMinutes + (apt.appointmentIntervalAmount * appointmentDuration);
        });

        // If there are multiple appointments at this time, it's a sobreturno
        const hasSobreturno = sameTimeAppointments.length > 1;

        return {dayIndex: todayIndex, position, hasSobreturno};
    };

    return (
        <div className="flex-1 overflow-auto" onClick={handleOutsideClick}>
            <div className="bg-white rounded-lg border">
                <div className="flex items-center justify-between p-4 border-b">
                    <Button onClick={handlePrevWeek} variant="outline" size="sm">
                        <ChevronLeft className="h-4 w-4"/>
                    </Button>
                    <h2 className="text-lg font-semibold text-center">
                        {format(weekStart, "d 'de' MMMM", {locale: es})} - {format(weekEnd, "d 'de' MMMM yyyy", {locale: es})}
                    </h2>
                    <Button onClick={handleNextWeek} variant="outline" size="sm">
                        <ChevronRight className="h-4 w-4"/>
                    </Button>
                </div>

                <div className="grid grid-cols-[80px_repeat(7,1fr)] border-b sticky top-0 bg-white z-10">
                    <div className="p-4 text-sm font-medium text-gray-500">Hora</div>
                    {weekDays.map((day, i) => (
                        <div
                            key={i}
                            className={`p-4 text-sm font-medium text-center border-l ${
                                isDayAvailable(day)
                                    ? "text-black-500 hover:cursor-pointer hover:bg-blue-100"
                                    : "text-gray-300 bg-gray-100 cursor-not-allowed"
                            } ${day.toDateString() === currentDate.toDateString() ? "bg-blue-50" : ""}`}
                            onClick={isDayAvailable(day) ? () => onDateSelect(day) : undefined}
                        >
                            {day
                                .toLocaleDateString("es-ES", {weekday: "long", day: "numeric"})
                                .split(", ")
                                .map((part, index) => (index === 0 ? part.charAt(0).toUpperCase() + part.slice(1) : part))
                                .join(", ")}
                        </div>
                    ))}
                </div>

                <div className="grid grid-cols-[80px_repeat(7,1fr)] relative">
                    {/* Current time indicator line */}
                    {(() => {
                        const timePosition = getCurrentTimePosition();
                        if (!timePosition) return null;

                        const {dayIndex, position, hasSobreturno} = timePosition;
                        // Calculate the left position based on the day index
                        // First column (80px) is for hours, then we need to find the correct day column
                        const dayColumnWidth = `calc((100% - 80px) / 7)`;

                        return (
                            <div
                                className="absolute pointer-events-none"
                                style={{
                                    top: `${position}px`,
                                    left: `calc(80px + (${dayIndex} * ${dayColumnWidth}))`,
                                    width: dayColumnWidth,
                                    zIndex: 10 // Above appointment elements (which have z-index 1-2)
                                }}
                            >
                                <div
                                    className={`absolute left-0 right-0 h-[2px] ${hasSobreturno ? 'bg-white/90' : 'bg-blue-500/80'}`}
                                    style={{
                                        boxShadow: hasSobreturno ? '0 0 4px rgba(255, 255, 255, 0.7)' : '0 0 4px rgba(59, 130, 246, 0.7)'
                                    }}
                                />
                            </div>
                        );
                    })()}

                    {hours.map((hour) => (
                        <React.Fragment key={hour}>
                            <div
                                className="p-4 text-sm text-gray-500 border-b"
                                style={{height: getHourSlotHeight()}}
                            >{`${hour.toString().padStart(2, '0')}:00`}</div>
                            {weekDays.map((day, dayIndex) => {
                                const year = day.getFullYear()
                                const month = String(day.getMonth() + 1).padStart(2, '0')
                                const dayOfMonth = String(day.getDate()).padStart(2, '0')
                                const dateStr = `${year}-${month}-${dayOfMonth}`
                                const dayAppointments = appointmentsByDate[dateStr] || []
                                const hourAppointments = dayAppointments.filter((apt) => {
                                    const [aptHour] = apt.startTime.split(":")
                                    return Number.parseInt(aptHour) === hour
                                })

                                const dayTimeSlots = getDayTimeSlots(day)
                                const startTime = `${hour.toString().padStart(2, '0')}:00`
                                const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`
                                const timeString = `${startTime}`;
                                const allSlots = appointmentDuration && generateTimeSlots(startTime, endTime, appointmentDuration) || []
                                const freeSlots = allSlots.filter(slot =>
                                        !hourAppointments.some(apt => apt.startTime === slot) &&
                                        !isSlotCoveredByMultiSlot(slot, dayAppointments) &&
                                        dayTimeSlots.includes(slot)
                                    // Note: We don't filter blocked slots here because we want to show them with different styling
                                    // We'll handle the blocked state in the rendering part
                                );

                                return (
                                    <div
                                        key={`${hour}-${dayIndex}`}
                                        className={`border-l border-b relative ${isDayAvailable(day) ? "" : "bg-gray-100 cursor-not-allowed"}`}
                                        style={{height: getHourSlotHeight()}}
                                    >


                                        {appointmentDuration && isDayAvailable(day) && (
                                            <div className="absolute inset-0">
                                                {hourAppointments.map((appointment) => {
                                                    const sameTimeAppointments = hourAppointments.filter(
                                                        apt => apt.startTime === appointment.startTime
                                                    );
                                                    const isOvercrowded = sameTimeAppointments.length > 1;
                                                    const slotsNeeded = getSlotsNeeded(appointment);

                                                    // Get appointment position more precisely
                                                    const [aptHour, aptMinute] = appointment.startTime.split(':').map(Number);
                                                    const hourStartInMinutes = hour * 60;
                                                    const appointmentStartInMinutes = aptHour * 60 + aptMinute;
                                                    const offsetInHour = appointmentStartInMinutes - hourStartInMinutes;
                                                    const offsetPercentage = (offsetInHour / 60) * 100;

                                                    // Check if there's a subsequent appointment that would overlap with this one
                                                    const appointmentEndTime = appointmentStartInMinutes + (appointment.appointmentIntervalAmount || appointmentDuration);
                                                    const hasSubsequentAppointment = dayAppointments.some(apt => {
                                                        // Convert apt time to minutes
                                                        const [nextAptHour, nextAptMinute] = apt.startTime.split(':').map(Number);
                                                        const nextAptStartMinutes = nextAptHour * 60 + nextAptMinute;

                                                        // Check if this appointment starts after current one but before it would end
                                                        return nextAptStartMinutes > appointmentStartInMinutes &&
                                                            nextAptStartMinutes < appointmentEndTime;
                                                    });

                                                    const appointmentHeight = hasSubsequentAppointment ?
                                                        getAppointmentHeight() :
                                                        `${(slotsNeeded * appointmentDuration / 60) * 100}%`;

                                                    return (
                                                        <button
                                                            key={appointment.id}
                                                            onClick={(e) => handleAppointmentClick(e, appointment)}
                                                            onContextMenu={(e) => handleAppointmentClick(e, appointment)}
                                                            className={`appointment-slot absolute left-0 right-0 p-2 text-xs rounded-sm truncate flex items-center ${
                                                                isOvercrowded
                                                                    ? "bg-blue-500 text-white border border-blue-300"
                                                                    : appointment.state === "PENDING"
                                                                        ? "bg-yellow-100 text-yellow-700 border border-yellow-300"
                                                                        : appointment.state === "NO_SHOW"
                                                                            ? "bg-red-100 text-red-700 border border-red-300"
                                                                            : appointment.state === "IN_WAITING_ROOM"
                                                                                ? "bg-violet-200 text-violet-700 border border-violet-300"
                                                                                : appointment.state === "IN_CONSULTATION"
                                                                                    ? "bg-blue-100 text-blue-700 border border-blue-300"
                                                                                    : appointment.state === "COMPLETE"
                                                                                        ? "bg-green-100 text-green-700 border border-green-300"
                                                                                        : "bg-gray-100 text-gray-700 border border-gray-300"
                                                            }
                          ${
                                                                localSelectedAppointment?.id === appointment.id &&
                                                                localSelectedAppointment?.date === appointment.date
                                                                    ? "ring-2 ring-inset ring-blue-500"
                                                                    : ""
                                                            }`}
                                                            style={{
                                                                top: `${offsetPercentage}%`,
                                                                height: appointmentHeight,
                                                                minHeight: '24px',
                                                                zIndex: slotsNeeded > 1 ? 2 : 1,
                                                            }}
                                                        >
                          <span className="truncate">
                            {isOvercrowded
                                ? `${appointment.startTime} - ${sameTimeAppointments.length} pacientes`
                                : `${appointment.startTime} - ${appointment.patientName}${slotsNeeded > 1 ? ` (${appointment.appointmentIntervalAmount}min)` : ''}`}
                          </span>
                                                        </button>
                                                    );
                                                })}
                                                {freeSlots.map((time) => {
                                                    const slotKey = `${dateStr}:${time}`
                                                    const isPast = isPastTimeSlot(dateStr, time);
                                                    const isBlocked = isSlotBlocked(blockedSlots, dateStr, time, appointmentDuration);
                                                    return (
                                                        <button
                                                            key={`${dateStr}-${time}-free`}
                                                            onClick={(e) => !isPast && !isBlocked && handleFreeSlotClick(e, dateStr, time)}
                                                            disabled={isPast} // Don't disable for blocked slots, we handle that in the click handler
                                                            className={`appointment-slot absolute left-0 right-0 p-2 text-xs rounded-sm truncate min-h-[24px] flex items-center
                                ${isPast ? 'bg-gray-50 text-gray-300 cursor-not-allowed' :
                                                                isBlocked ? 'bg-gray-300 text-gray-500 cursor-default' :
                                                                    'bg-gray-50 text-gray-500 hover:bg-gray-200'}
                                border border-gray-300
                                ${selectedSlot === slotKey ? "ring-2 ring-inset ring-blue-500" : ""}`}
                                                            style={{
                                                                top: getAppointmentPosition(time),
                                                                height: getAppointmentHeight(),
                                                            }}
                                                        >
                                                            <div className="flex w-full items-center relative group">
                                                                <span className="min-w-[2rem] text-left">{time}</span>
                                                                {!isPast && !isBlocked && (
                                                                    <>
                                                                        <span className="pl-1">-</span>
                                                                        <span
                                                                            className="pl-1 truncate group-hover:hidden">Disponible</span>
                                                                        <span
                                                                            className="pl-1 hidden group-hover:block truncate pr-4">Disponible</span>
                                                                    </>
                                                                )}
                                                                {!isPast && isBlocked && (
                                                                    <>
                                                                        <span className="pl-1">-</span>
                                                                        <span
                                                                            className="pl-1 truncate pr-4">Bloqueado</span>
                                                                    </>
                                                                )}

                                                                {/* Padlock icon - only show for non-past slots */}
                                                                {!isPast && (
                                                                    <div
                                                                        className={`absolute right-1 p-1 transition-colors cursor-pointer ${isBlocked || selectedSlot === slotKey ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} padlock-icon-container`}
                                                                        onClick={(e) => handleToggleBlockedSlot(e, dateStr, time)}
                                                                    >
                                                                        {isBlocked ?
                                                                            <Lock
                                                                                className="h-3 w-3 text-red-600 hover:text-green-600"/> :
                                                                            <Unlock
                                                                                className="h-3 w-3 text-gray-400 hover:text-red-600"/>}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </button>
                                                    )
                                                })}
                                            </div>
                                        )}
                                    </div>
                                )
                            })}
                        </React.Fragment>
                    ))}
                </div>
            </div>
            {overcrowdedMenu && (
                <OvercrowdedContextMenu
                    x={overcrowdedMenu.x}
                    y={overcrowdedMenu.y}
                    appointments={overcrowdedMenu.appointments}
                    onSelect={handleOvercrowdedSelect}
                    onClose={() => setOvercrowdedMenu(null)}
                />
            )}
        </div>
    )
}