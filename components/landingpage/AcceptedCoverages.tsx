import { DEFAULT_COVERAGES } from "@/data/coverages"
import Image from "next/image"

const coverageLogos: { [key: string]: string } = {
  "OSDE": "/images/logos/osde.png",
  "Swiss Medical": "/images/logos/swiss-medical.svg",
  "Medifé": "/images/logos/medife.png",
  "Galeno": "/images/logos/galeno.png",
  "Omint": "/images/logos/omint.png",
  "Accord Salud": "/images/logos/accord-salud.png",
  "Medicus": "/images/logos/medicus.png",
};

const LogosList = () => (
  <div className="flex-shrink-0 flex items-center">
    {DEFAULT_COVERAGES.filter(c => coverageLogos[c.name]).map((coverage, index) => (
      <div key={index} className="flex-shrink-0 mx-8 flex items-center justify-center h-16 w-32">
        <Image
          src={coverageLogos[coverage.name]}
          alt={`${coverage.name} logo`}
          width={120}
          height={40}
          className="object-contain grayscale"
        />
      </div>
    ))}
  </div>
);

const AcceptedCoverages = () => {
  return (
    <div id="coberturas" className="pt-12 md:pt-4 pb-20 bg-blue-50 scroll-mt-20 md:scroll-mt-24">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl md:text-3xl font-bold text-center text-gray-800 mb-12 font-recoleta">
          Coberturas disponibles
        </h2>
        <div className="relative group">
          <div className="overflow-x-hidden">
            <div className="flex w-max animate-scroll group-hover:pause">
              <LogosList />
              <LogosList />
            </div>
          </div>
          <div className="absolute top-0 left-0 w-32 h-full bg-gradient-to-r from-blue-50 to-transparent" />
          <div className="absolute top-0 right-0 w-32 h-full bg-gradient-to-l from-blue-50 to-transparent" />
        </div>
      </div>
    </div>
  )
}

export default AcceptedCoverages 