import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>r, <PERSON>, <PERSON>, <PERSON> } from "lucide-react"

const specialties = [
  { name: "Medicina Familiar", icon: Stethoscope, description: "Atención integral para todas las edades." },
  { name: "<PERSON><PERSON><PERSON><PERSON>", icon: Baby, description: "Cuidado especializado para niños y adolescentes." },
  { name: "Salud Mental", icon: <PERSON>, description: "Apoyo para tu bienestar emocional y psicológico." },
  { name: "Traumatología", icon: <PERSON>, description: "Tratamiento de lesiones y afecciones musculoesqueléticas." },
  { name: "Clínica Médica", icon: User, description: "Diagnóstico y tratamiento de enfermedades comunes." },
]

export default function PopularSpecialties() {
  return (
    <section id="especialidades" className="py-16 bg-blue-50">
      <div className="container mx-auto px-4 md:px-8 max-w-7xl">
        <h2 className="text-3xl md:text-4xl font-recoleta font-bold text-center mb-14"><PERSON>reas Médicas Destacadas</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {specialties.map((specialty, index) => (
            <Card key={index} className="hover:shadow-xl transition-shadow duration-300 rounded-xl bg-white">
              <CardContent className="flex flex-col items-center text-center p-8">
                <div className="p-4 bg-blue-100 rounded-full mb-5">
                  <specialty.icon className="h-10 w-10 text-blue-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2 text-gray-800">{specialty.name}</h3>
                <p className="text-gray-600 text-sm">{specialty.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

