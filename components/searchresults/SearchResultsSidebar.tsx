import {useContext, useEffect, useState} from "react"
import {Button} from "@/components/ui/button"
import {Label} from "@/components/ui/label"
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {AlertTriangle, Filter, MapPin, X} from "lucide-react"
import {CoverageContext} from "@/contexts/CoverageContext"
import {usePathname, useRouter, useSearchParams} from "next/navigation"
import {Doctor} from "@/types/doctor"
import {doctors as staticDoctors} from "@/data/doctors"
import {initialMedicalCenters as staticMedicalCenters} from "@/data/medicalCenters"
import {
  getCABALocationsFromMedicalCenters,
  getGBALocationsFromMedicalCenters,
  getSearchLocationOptionsFromMedicalCenters
} from "@/data/locations"

interface SearchResultsSidebarProps {
    open: boolean
    onClose: () => void
    noInsurance: boolean
    setNoInsurance: (value: boolean) => void
    onApplyFilters?: (coverageData: {
        noInsurance: boolean,
        coverageId: string,
        coverageName: string,
        plan: string
    }) => void
    onTimeFilterChange?: (value: string) => void
    onSortChange?: (value: "date" | "distance") => void
    sortBy?: "date" | "distance"
    searchType?: string
    searchQuery?: string
    locationQuery?: string
    renderMode?: "drawer" | "popover"
    selectedTime?: string
}

export default function SearchResultsSidebar({
                                                 open,
                                                 onClose,
                                                 noInsurance,
                                                 setNoInsurance,
                                                 onApplyFilters,
                                                 onTimeFilterChange,
                                                 onSortChange,
                                                 sortBy = "date",
                                                 searchType = "",
                                                 searchQuery = "",
                                                  locationQuery = "",
                                                  renderMode = "drawer",
                                                  selectedTime
                                             }: SearchResultsSidebarProps) {
    const [selectedRegion, setSelectedRegion] = useState<"all" | "caba" | "gba">("all")
    const [selectedBarrio, setSelectedBarrio] = useState<string>("")
    const locationOptions = getSearchLocationOptionsFromMedicalCenters()

    const {medicalCoverages, isDoctorCoverageExcluded} = useContext(CoverageContext)
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    // Initialize from URL parameters on component mount
    useEffect(() => {
        const zoneParam = searchParams.get("zone")
        const locParam = searchParams.get("loc")
        const locationParam = locParam || zoneParam

        if (locationParam) {
            if (locationParam === "all") {
                setSelectedRegion("all")
                setSelectedBarrio("")
            } else if (locationParam === "caba" || locationParam === "Capital Federal") {
                setSelectedRegion("caba")
                setSelectedBarrio("all")
            } else if (locationParam === "gba" || locationParam === "Gran Buenos Aires") {
                setSelectedRegion("gba")
                setSelectedBarrio("all")
            } else {
                const cabaLocation = getCABALocationsFromMedicalCenters().find(loc =>
                    loc.id === locationParam || loc.name === locationParam
                )
                if (cabaLocation) {
                    setSelectedRegion("caba")
                    setSelectedBarrio(cabaLocation.id)
                } else {
                    const gbaLocation = getGBALocationsFromMedicalCenters().find(loc =>
                        loc.id === locationParam || loc.name === locationParam
                    )
                    if (gbaLocation) {
                        setSelectedRegion("gba")
                        setSelectedBarrio(gbaLocation.id)
                    }
                }
            }
        }
    }, [searchParams])

    // Update a simple filter parameter and apply filters
    const updateSimpleFilterParam = (name: string, value: string) => {
        const params = new URLSearchParams(searchParams.toString());
        params.set(name, value);
        router.replace(`${pathname}?${params.toString()}`);

        if (name === "time" && onTimeFilterChange) {
            onTimeFilterChange(value);
        }
    }

    const timeValue = selectedTime ?? (searchParams.get("time") || "all");

    return (
        <div
            className={
                renderMode === "popover"
                    ? "w-full md:w-[56rem] bg-white rounded-lg border border-gray-200 shadow-lg overflow-y-auto max-h-[80vh]"
                    : `fixed inset-y-0 left-0 transform ${open ? "translate-x-0" : "-translate-x-full"} w-64 bg-white shadow-lg transition-transform duration-300 ease-in-out z-[80] md:z-20 md:sticky md:top-20 md:translate-x-0 md:h-fit rounded-xl md:rounded-lg border md:border-gray-200 border-[#0070F3]/30 overflow-y-auto`
            }>
            <div className="p-4 md:p-5">
                <div className="flex justify-between items-center mb-6 md:hidden">
                    <div className="flex items-center">
                        <Filter className="w-5 h-5 mr-2 text-[#0070F3]"/>
                        <h2 className="text-lg text-[#1c2533] font-semibold font-sans">Filtros</h2>
                    </div>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={onClose}
                        className="hover:bg-[#0070F3]/5 hover:text-[#0070F3]"
                    >
                        <X className="w-5 h-5"/>
                    </Button>
                </div>

                <div className="space-y-4 md:space-y-5">
                    <div>
                        <Label htmlFor="sort" className="text-sm font-medium text-gray-700 mb-1 block">Ordenar por</Label>
                        <Select
                            value={sortBy}
                            onValueChange={(value: "date" | "distance") => {
                                updateSimpleFilterParam("sort", value);
                                if (onSortChange) {
                                    onSortChange(value);
                                }
                            }}
                        >
                            <SelectTrigger id="sort"
                                           className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                                <SelectValue placeholder="Seleccionar orden"/>
                            </SelectTrigger>
                            <SelectContent className="z-[90]">
                                <SelectItem value="date">Primer turno disponible</SelectItem>
                                <SelectItem value="distance">Menor distancia</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-4">
                        <div className="flex items-center mb-1">
                            <MapPin className="h-4 w-4 mr-1.5 text-[#0070F3]"/>
                            <Label htmlFor="region" className="text-sm font-medium text-gray-700">Ubicación</Label>
                        </div>

                        <div>
                            <Select
                                value={selectedRegion}
                                onValueChange={(value: "all" | "caba" | "gba") => {
                                    setSelectedRegion(value);
                                    if (value !== "all") {
                                        setSelectedBarrio("all");
                                    } else {
                                        setSelectedBarrio("");
                                    }

                                    const params = new URLSearchParams(searchParams.toString());
                                    params.delete("zone");

                                    if (value === "all") {
                                        params.set("loc", "all");
                                    } else if (value === "caba") {
                                        params.set("loc", "Capital Federal");
                                    } else if (value === "gba") {
                                        params.set("loc", "Gran Buenos Aires");
                                    }

                                    router.replace(`${pathname}?${params.toString()}`);
                                }}
                            >
                                <SelectTrigger id="region"
                                               className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                                    <SelectValue placeholder="Seleccionar ubicación"/>
                                </SelectTrigger>
                                <SelectContent className="z-[90]">
                                    <SelectItem value="all">Todas las ubicaciones</SelectItem>
                                    {locationOptions.some(loc => loc.region === "caba" && loc.type === "region") && (
                                        <SelectItem value="caba">Capital Federal</SelectItem>
                                    )}
                                    {locationOptions.some(loc => loc.region === "gba" && loc.type === "region") && (
                                        <SelectItem value="gba">Gran Buenos Aires</SelectItem>
                                    )}
                                </SelectContent>
                            </Select>
                        </div>

                        {(selectedRegion === "caba" || selectedRegion === "gba") && (
                            <div>
                                <Select
                                    value={selectedBarrio}
                                    onValueChange={(value) => {
                                        setSelectedBarrio(value);

                                        const params = new URLSearchParams(searchParams.toString());
                                        params.delete("zone");

                                        if (value === "all") {
                                            if (selectedRegion === "caba") {
                                                params.set("loc", "Capital Federal");
                                            } else if (selectedRegion === "gba") {
                                                params.set("loc", "Gran Buenos Aires");
                                            }
                                        } else {
                                            const locations = selectedRegion === "caba"
                                                ? getCABALocationsFromMedicalCenters()
                                                : getGBALocationsFromMedicalCenters();

                                            const location = locations.find(loc => loc.id === value);
                                            if (location) {
                                                params.set("loc", location.name);
                                            } else {
                                                params.set("loc", value);
                                            }
                                        }
                                        router.replace(`${pathname}?${params.toString()}`);
                                    }}
                                >
                                    <SelectTrigger
                                        className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                                        <SelectValue placeholder={selectedRegion === "caba" ? "Barrio" : "Municipio"}/>
                                    </SelectTrigger>
                                    <SelectContent className="z-[90] max-h-60 overflow-auto">
                                        <SelectItem value="all">
                                            {selectedRegion === "caba" ? "Todos los barrios" : "Todos los municipios"}
                                        </SelectItem>
                                        {(selectedRegion === "caba"
                                                ? getCABALocationsFromMedicalCenters()
                                                : getGBALocationsFromMedicalCenters()
                                        ).map((location) => (
                                            <SelectItem key={location.id} value={location.id}>
                                                {location.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        )}
                    </div>

                    <div className="border-t border-gray-100 pt-4">
                        <Label className="text-sm font-medium text-gray-700 mb-3 block">Horario</Label>
                        <RadioGroup
                            value={timeValue}
                            className="space-y-3"
                            onValueChange={(value) => updateSimpleFilterParam("time", value)}
                        >
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="all" id="all" className="text-[#0070F3]"/>
                                <Label htmlFor="all" className="text-sm text-gray-600 cursor-pointer">Todos</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="morning" id="morning" className="text-[#0070F3]"/>
                                <Label htmlFor="morning" className="text-sm text-gray-600 cursor-pointer">Mañana (hasta
                                    12:00)</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="afternoon" id="afternoon" className="text-[#0070F3]"/>
                                <Label htmlFor="afternoon" className="text-sm text-gray-600 cursor-pointer">Tarde (12:00
                                    a 17:00)</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="evening" id="evening" className="text-[#0070F3]"/>
                                <Label htmlFor="evening" className="text-sm text-gray-600 cursor-pointer">Noche (desde
                                    17:00)</Label>
                            </div>
                        </RadioGroup>
                    </div>

                    {/* Coverage UI removed; handled by UnifiedSearch */}
                </div>
            </div>
        </div>
    )
}

