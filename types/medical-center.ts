// Define the OpeningHours interface with specific day keys
export interface OpeningHours {
  monday: { start: string; end: string; enabled: boolean };
  tuesday: { start: string; end: string; enabled: boolean };
  wednesday: { start: string; end: string; enabled: boolean };
  thursday: { start: string; end: string; enabled: boolean };
  friday: { start: string; end: string; enabled: boolean };
  saturday: { start: string; end: string; enabled: boolean };
  sunday: { start: string; end: string; enabled: boolean };
}

// Define the Billing interface
export interface Billing {
  businessName: string;
  taxId: string;
  address: string;
  businessType: string;
}

// Define the Location interface with enhanced fields
export interface Location {
  latitude?: number;
  longitude?: number;
  formattedAddress?: string;
  placeId?: string;
  streetNumber?: string;
  route?: string;
  locality?: string;
  administrativeArea?: string;
  postalCode?: string;
  country?: string;
}

// Main MedicalCenter interface that includes all fields
export interface MedicalCenter {
  id: string;
  name: string;
  doctors: string[];

  // Original working days format (numeric keys)
  workingDays: {
    [key: string]: {
      enabled: boolean;
      hours: Array<{
        start: string;
        end: string;
      }>;
      weeksFrequency?: number;
    };
  };
  dateExceptions?: {
    [key: string]: {
      enabled: boolean;
      hours?: { start: string; end: string }[]
    }
  };

  // New fields for the configuration form
  address?: string;
  streetAddress?: string; // Structured street address (street name + number)
  floor?: string; // Floor number (replacing referencePoint)
  apartment?: string; // Apartment or office number (replacing referencePoint)
  phone?: string;
  email?: string;
  website?: string;
  logoUrl?: string;
  openingHours?: OpeningHours;
  billing?: Billing;
  location?: Location;

  // Accepted medical coverages
  acceptedCoverages?: string[]; // IDs of accepted coverages

  // Location-related properties
  locationId?: string; // ID of the location from locations.ts
  locationRegion?: "caba" | "gba"; // Region of the location
  locationType?: "barrio" | "municipio"; // Type of the location
}