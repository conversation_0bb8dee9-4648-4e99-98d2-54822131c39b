"use client"

import { useContext } from "react"
import { <PERSON><PERSON>ontex<PERSON> } from "@/contexts/DoctorContext" // New context
import { AppStateContext } from "@/contexts/AppStateContext" // New context
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle } from "lucide-react"

export default function DoctorDisassociateTab() {
  const { editedConfig, removeDoctor: handleDisassociateDoctor } = useContext(DoctorContext) // Renamed to match DoctorContext
  const { setActiveConfigTab } = useContext(AppStateContext)

  if (!editedConfig) return null

  const handleConfirmDisassociate = () => {
    handleDisassociateDoctor(editedConfig.id)
  }

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="p-0 pb-4">
        <CardTitle className="text-lg text-red-700">Desasociar Profesional</CardTitle>
      </CardHeader>
      <CardContent className="p-0 space-y-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3">
          <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
          <div>
            <p className="text-red-800 font-medium">¿Estás seguro?</p>
            <p className="text-red-700">
              Esto desasociará a <span className="font-semibold">{editedConfig.name}</span> del establecimiento. El
              profesional no será eliminado, pero ya no estará vinculado a este establecimiento.
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setActiveConfigTab("info")}
            className="border-gray-300 text-gray-700 hover:bg-gray-100"
          >
            Cancelar
          </Button>
          <Button onClick={handleConfirmDisassociate} className="bg-red-600 hover:bg-red-700 text-white">
            Desasociar Profesional
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}