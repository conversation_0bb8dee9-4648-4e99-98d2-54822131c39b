export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api',
  timeout: 10000, // 10 seconds
  headers: {
    'Content-Type': 'application/json',
  },
} as const;

export const API_ENDPOINTS = {
  // Medical Centers
  MEDICAL_CENTERS: '/medical-centers',
  MEDICAL_CENTER: (id: string) => `/medical-centers/${id}`,
  
  // Doctors
  DOCTORS: (medicalCenterId: string) => `/medical-centers/${medicalCenterId}/doctors`,
  DOCTOR: (id: string) => `/doctors/${id}`,
  
  // Appointments
  APPOINTMENTS: (medicalCenterId: string) => `/medical-centers/${medicalCenterId}/appointments`,
  APPOINTMENT: (id: string) => `/appointments/${id}`,
  
  // Health Insurance
  HEALTH_INSURANCE: '/health-insurance',
  HEALTH_INSURANCE_PLAN: (id: string) => `/health-insurance/${id}`,
  
  // Schedule
  DOCTOR_SCHEDULE: (doctorId: string) => `/doctors/${doctorId}/schedule`,

  // Users
  USERS: '/users',
  USER: (id: string) => `/users/${id}`,
} as const; 