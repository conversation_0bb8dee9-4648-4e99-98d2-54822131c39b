import { MedicalCoverage } from "@/types/doctor"

export const DEFAULT_COVERAGES: MedicalCoverage[] = [
  { id: "1", name: "<PERSON>DE", plans: ["210", "310", "410", "510"] },
  { id: "2", name: "Swiss Medical", plans: ["S1", "S2", "SMG02", "SMG20", "SMG30", "SMG40", "SMG50", "SMG60", "SMG70"] },
  { id: "3", name: "Medifé", plans: ["Medifé +", "Bronce Classic", "Bronce", "Plata", "Oro", "Platinum"] },
  { id: "4", name: "<PERSON><PERSON>", plans: ["Plata", "Oro", "Azul", "Plata 220", "Oro 330", "Azul 440", "Plata 550"] },
  { id: "5", name: "Omin<PERSON>", plans: ["Classic", "Premium", "Excellence"] },
  { id: "6", name: "Accord Salud", plans: ["Plan 1", "Plan 2", "Plan 3"] },
  { id: "7", name: "<PERSON><PERSON><PERSON>", plans: ["Plan 1", "Plan 2", "Plan 3"] },
  { id: "8", name: "Sin Cobertura", plans: [] },
]

// Helper function to find a coverage by ID
export const findCoverageById = (id: string): MedicalCoverage | undefined => {
  return DEFAULT_COVERAGES.find(coverage => coverage.id === id)
}

// Helper function to find a coverage by name
export const findCoverageByName = (name: string): MedicalCoverage | undefined => {
  return DEFAULT_COVERAGES.find(coverage => coverage.name.toLowerCase() === name.toLowerCase())
}
