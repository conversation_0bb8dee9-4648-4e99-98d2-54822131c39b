import type {NextConfig} from "next";

const nextConfig: NextConfig = {
    /* config options here */
    webpack: (config) => {
        config.externals.push({
            'utf-8-validate': 'commonjs utf-8-validate',
            'bufferutil': 'commonjs bufferutil',
        });
        return config;
    },
    // Allow connections from any IP address
    async headers() {
        return [
            {
                source: '/:path*',
                headers: [
                    {key: 'Access-Control-Allow-Origin', value: '*'},
                    {key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT'},
                    {key: 'Access-Control-Allow-Headers', value: 'X-Requested-With, Content-Type, Authorization'},
                ],
            },
        ];
    },
    eslint: {
        ignoreDuringBuilds: true,
    },
    typescript: {
        // ¡¡ADVERTENCIA!!
        // Permite que la compilación en producción se complete exitosamente
        // incluso si tu proyecto tiene errores de TypeScript.
        // ¡¡ADVERTENCIA!!
        ignoreBuildErrors: true,
    },
};

export default nextConfig;
