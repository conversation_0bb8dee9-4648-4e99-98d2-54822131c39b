// Types for the professional schedules API response

import {convertTimeStringToMinutes} from "@/utils/dateUtils";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {getWeeksDifference} from "@/utils/scheduleUtils";

export enum AppointmentState {
    CANCELLED = "CANCELLED",
    PENDING = "PENDING",
    NO_SHOW = "NO_SHOW",
    IN_WAITING_ROOM = "IN_WAITING_ROOM",
    IN_CONSULTATION = "IN_CONSULTATION",
    COMPLETE = "COMPLETE",
    PROCESSING = "PROCESSING",
    SETTLED = "SETTLED"
}

export enum AppointmentSource {
    TURNERA = "TURNERA",
    PHONE = "PHONE",
    IN_PERSON = "IN_PERSON",
}

export enum BlockedSlotType {
    DOCTOR = "DOCTOR",
    PATIENT = "PATIENT"
}


abstract class Schedule {
    abstract startTime: string;
    abstract endTime: string;


    includesTime(time: string) {
        const startTime = convertTimeStringToMinutes(this.startTime)
        const endTime = convertTimeStringToMinutes(this.endTime)
        const timeInMinutes = convertTimeStringToMinutes(time);
        return timeInMinutes >= startTime && timeInMinutes < endTime;

    }
}

export class AppointmentSchedule extends Schedule {

    constructor(
        public id: number,
        public dayOfWeek: string,
        public startingAt: string,
        public startTime: string,
        public endTime: string,
        public weeklyFrequency: number,
        public endingAt: string | null) {
        super()
    }

    get getStartingAt(): Date {
        return new Date(this.startingAt);
    }

    static fromJSON(json: Record<string, unknown>): AppointmentSchedule {
        return new AppointmentSchedule(
            json.id as number,
            json.dayOfWeek as string,
            json.startingAt as string,
            json.startTime as string,
            json.endTime as string,
            json.weeklyFrequency as number,
            json.endingAt as string | null
        );
    }

    isForDate(date: Date, dayOfWeek: string): boolean {

        return this.isForDayOfWeek(dayOfWeek) && this.isActiveInDate(date);
    };

    isForDayOfWeek(dayOfWeek: string): boolean {
        return this.dayOfWeek.toLowerCase() === dayOfWeek.toLowerCase();
    };

    isForDateRange(startDate: Date, endDate: Date): boolean {
        const scheduleStartDate = new Date(this.startingAt);
        const scheduleEndDate = this.endingAt ? new Date(this.endingAt) : null;
        return (scheduleStartDate <= endDate) && (!scheduleEndDate || scheduleEndDate >= startDate);
    }

    isActiveInDate(date: Date): boolean {
        const frequency = this.weeklyFrequency
        const weeksSinceReference = getWeeksDifference(date, this.getStartingAt)
        const startDate = new Date(this.startingAt);
        const endDate = this.endingAt ? new Date(this.endingAt) : null;
        return date >= startDate && (!endDate || date <= endDate) && weeksSinceReference % frequency === 0;
    };
}

export class SpecialSchedule extends Schedule {
    constructor(
        public id: number,
        public date: string,
        public startTime: string,
        public endTime: string
    ) {
        super()
    }

    static fromJSON(json: Record<string, unknown>): SpecialSchedule {
        return new SpecialSchedule(
            json.id as number,
            json.date as string,
            json.startTime as string,
            json.endTime as string
        );
    }

    isForDate(date: Date): boolean {
        const scheduleDate = new Date(this.date);
        return date === scheduleDate;
    }

    isForDateRange(startDate: Date, endDate: Date): boolean {
        const scheduleDate = new Date(this.date);
        return scheduleDate >= startDate && scheduleDate <= endDate;
    }

}

export class VacationSchedule {
    constructor(
        public id: number,
        public fromDate: string,
        public toDate: string
    ) {

    }

    static fromJSON(json: Record<string, unknown>): VacationSchedule {
        return new VacationSchedule(
            json.id as number,
            json.fromDate as string,
            json.toDate as string
        );
    }

    isForDate(date: Date): boolean {
        const fromDate = new Date(this.fromDate);
        const toDate = new Date(this.toDate);
        return date >= fromDate && date <= toDate;
    }


    isForWeek(weekStart: Date, weekEnd: Date) {
        const fromDate = new Date(this.fromDate);
        const toDate = new Date(this.toDate);
        return (fromDate <= weekEnd && toDate >= weekStart);
    }
}

export class BlockedSlot {
    constructor(
        public id: number,
        public date: string,
        public slotType: BlockedSlotType,
        public patientId: number,
        public startTime: string,
        public appointmentIntervalAmount: number
    ) {
    }

    static fromJSON(json: Record<string, unknown>): BlockedSlot {
        return new BlockedSlot(
            json.id as number,
            json.date as string,
            json.slotType as BlockedSlotType,
            json.patientId as number,
            json.startTime as string,
            json.appointmentIntervalAmount as number
        );
    }

    isForDate(date: Date): boolean {
        return this.date === date.toDateString();
    }
}

export class ProfessionalSchedulesResponse {
    constructor(
        public month: string,
        public appointmentSchedules: AppointmentSchedule[],
        public specialSchedules: SpecialSchedule[],
        public vacationSchedules: VacationSchedule[],
        public appointments: ProfessionalAppointment[],
        public blockedSlots: BlockedSlot[]
    ) {
    }

    static fromJSON(json: Record<string, unknown>): ProfessionalSchedulesResponse {
        const asArray = (v: unknown): unknown[] => Array.isArray(v) ? v : [];

        const month = (json.month as string) ?? "";

        const appointmentSchedules: AppointmentSchedule[] = asArray(json["appointmentSchedules"]).map((raw) => {
            return AppointmentSchedule.fromJSON(raw as Record<string, unknown>);
        });

        const specialSchedules: SpecialSchedule[] = asArray(json["specialSchedules"]).map((raw) => {
            return SpecialSchedule.fromJSON(raw as Record<string, unknown>);
        });

        const vacationSchedules: VacationSchedule[] = asArray(json["vacationSchedules"]).map((raw) => {
            return VacationSchedule.fromJSON(raw as Record<string, unknown>);
        });

        const appointments: ProfessionalAppointment[] = asArray(json["appointments"]).map((raw) => {
            return ProfessionalAppointment.fromJSON(raw as Record<string, unknown>);
        });

        const blockedSlots: BlockedSlot[] = asArray(json["blockedSlots"]).map((raw) => {
            return BlockedSlot.fromJSON(raw as Record<string, unknown>);
        });

        return new ProfessionalSchedulesResponse(
            month,
            appointmentSchedules,
            specialSchedules,
            vacationSchedules,
            appointments,
            blockedSlots
        );
    }

    getAppointmentSchedulesByDate(date: Date): AppointmentSchedule[] {
        const dayOfWeek = date.toLocaleDateString('en-US', {weekday: 'long'}).toLowerCase();
        return this.appointmentSchedules.filter(schedule =>
            schedule.isForDate(date, dayOfWeek)
        );
    }

    getAppointmentSchedulesByRange(startDate: Date, endDate: Date): AppointmentSchedule[] {
        return this.appointmentSchedules.filter(schedule => schedule.isForDateRange(startDate, endDate));
    }

    getVacationScheduleByDate(date: Date): VacationSchedule | undefined {
        return this.vacationSchedules.find(schedule => schedule.isForDate(date));
    }

    getSpecialSchedulesByDate(date: Date): SpecialSchedule[] {
        return this.specialSchedules.filter(schedule => schedule.isForDate(date));
    }

    getSpecialSchedulesByRange(startDate: Date, endDate: Date): SpecialSchedule[] {
        return this.specialSchedules.filter(schedule => schedule.isForDateRange(startDate, endDate));
    }

    getBlockedSlotsForDoctorByDate(date: Date): BlockedSlot[] {
        return this.blockedSlots.filter(slot => slot.isForDate(date)) || [];
    }

    datesWithVacationSchedulesFromDates(days: Date[]): Set<Date> {
        const datesWithVacationSchedules = new Set<Date>();
        days.forEach((day) => {
            if (this.getVacationScheduleByDate(day)) {
                datesWithVacationSchedules.add(day);
            }
        });
        return datesWithVacationSchedules;
    }


}


export function groupAppointmentSchedulesByDayOfWeek(appointmentSchedules: AppointmentSchedule[]): Record<string, AppointmentSchedule[]> {
    if (appointmentSchedules.length == 0) return {};
    const appointmentSchedulesByDayOfWeek: Record<string, AppointmentSchedule[]> = {};
    appointmentSchedules.forEach((schedule) => {
        if (!appointmentSchedulesByDayOfWeek[schedule.dayOfWeek]) {
            appointmentSchedulesByDayOfWeek[schedule.dayOfWeek] = [];
        }
        appointmentSchedulesByDayOfWeek[schedule.dayOfWeek].push(schedule);
    });
    return appointmentSchedulesByDayOfWeek;
}

export function groupSpecialSchedulesByDate(specialSchedules: SpecialSchedule[]): Record<string, SpecialSchedule[]> {
    if (specialSchedules.length == 0) return {};
    const specialSchedulesByDate: Record<string, SpecialSchedule[]> = {};
    specialSchedules.forEach((schedule) => {
        if (!specialSchedulesByDate[schedule.date]) {
            specialSchedulesByDate[schedule.date] = [];
        }
        specialSchedulesByDate[schedule.date].push(schedule);
    });
    return specialSchedulesByDate;
}
