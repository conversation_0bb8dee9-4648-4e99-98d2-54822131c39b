import {useEffect, useRef, useState} from "react";
import {Doctor} from "@/types/doctor";
import {DAYS} from "@/utils/constants";
import {generateDoctorId} from "@/utils/idGenerator";

// TODO FACU: Remove storage usage and implement proper API calls
// const UNASSIGNED_DOCTORS_KEY = 'medical-scheduler-unassigned-doctors';

export function useDoctors(medicalCenterId: string) {
    const [doctors, setDoctors] = useState<Doctor[]>([]);
    const [loading, setLoading] = useState(true);

    // Track if we're already initialized for this medical center
    const initializedCenterRef = useRef<string | null>(null);

    useEffect(() => {
        if (medicalCenterId && initializedCenterRef.current !== medicalCenterId) {
            setLoading(true);

            console.log(`Initializing useDoctors hook for medical center ${medicalCenterId}`);

            const loadDoctors = () => {
                try {
                    // TODO FACU: Replace with API call to load doctors
                    const storedDoctors: Doctor[] = [];
                    console.log(`Loaded ${storedDoctors.length} doctors for medical center ${medicalCenterId}`);

                    // TODO FACU: Replace with proper medical center validation
                    setDoctors(storedDoctors);

                    setLoading(false);

                    // Mark this center as initialized
                    initializedCenterRef.current = medicalCenterId;
                } catch (error) {
                    console.error(`Error loading doctors for ${medicalCenterId}:`, error);
                    setDoctors([]);
                    setLoading(false);
                }
            };

            loadDoctors();
        } else if (!medicalCenterId) {
            // If no medical center ID, try to load unassigned doctors
            setLoading(true);

            try {
                // TODO FACU: Replace with API call to load unassigned doctors
                const unassignedDoctors: Doctor[] = [];
                console.log(`Loaded ${unassignedDoctors.length} unassigned doctors`);
                setDoctors(unassignedDoctors);

                setLoading(false);
            } catch (error) {
                console.error('Error loading unassigned doctors:', error);
                setDoctors([]);
                setLoading(false);
            }
        }
    }, [medicalCenterId]);

    const addDoctor = (doctor: Omit<Doctor, "id">) => {
        // TODO FACU: Replace storage calls with API calls
        let doctorId: string;
        const existingId = null; // TODO FACU: Implement proper existing doctor check

        let newDoctor: Doctor;

        if (existingId) {
            // TODO FACU: Implement proper existing doctor handling
            doctorId = existingId;
            newDoctor = {
                id: existingId,
                ...doctor,
                initial: doctor.name.charAt(0).toUpperCase(),
                consultationTypes: doctor.consultationTypes || [],
                workingDays: doctor.workingDays || DAYS.reduce((acc, day) => ({
                    ...acc,
                    [day.id]: {enabled: false, hours: [], weeksFrequency: 1},
                }), {}),
                onlineBookingAdvanceDays: doctor.onlineBookingAdvanceDays || 60,
                onlineBookingMinHours: doctor.onlineBookingMinHours || 2,
                appointmentSlotDuration: doctor.appointmentSlotDuration || 15,
            };
        } else {
            // Generate a new ID for a new doctor using short-unique-id with 'd-' prefix
            doctorId = generateDoctorId();

            newDoctor = {
                id: doctorId,
                ...doctor,
                initial: doctor.name.charAt(0).toUpperCase(),
                consultationTypes: doctor.consultationTypes || [],
                workingDays: doctor.workingDays || DAYS.reduce((acc, day) => ({
                    ...acc,
                    [day.id]: {enabled: false, hours: [], weeksFrequency: 1},
                }), {}),
                onlineBookingAdvanceDays: doctor.onlineBookingAdvanceDays || 60,
                onlineBookingMinHours: doctor.onlineBookingMinHours || 2,
                appointmentSlotDuration: doctor.appointmentSlotDuration || 15,
            };
        }

        // Update state with the new doctor if we're looking at the unassigned doctors
        if (!medicalCenterId) {
            const updatedDoctors = [...doctors, newDoctor];
            setDoctors(updatedDoctors);
        }

        // TODO FACU: Replace with API call to save doctor
        console.log('Saving doctor to unassigned doctors:', newDoctor);

        return newDoctor;
    };

    const editDoctor = (doctor: Doctor) => {
        // Create a deep copy of the doctor to ensure all nested objects are properly saved
        const doctorCopy = JSON.parse(JSON.stringify(doctor));

        // Update the doctors array with the new doctor
        const updatedDoctors = doctors.map((d) => (d.id === doctorCopy.id ? doctorCopy : d));
        setDoctors(updatedDoctors);

        // TODO FACU: Replace with API call to update doctor
        if (medicalCenterId) {
            console.log("Doctor updated:", doctorCopy);
        }
    };

    const removeDoctor = (doctorId: string) => {
        const updatedDoctors = doctors.filter((d) => d.id !== doctorId);
        setDoctors(updatedDoctors);

        // TODO FACU: Replace with API call to delete doctor
        if (medicalCenterId) {
            console.log(`Doctor ${doctorId} removed`);
        }
    };

    // Track if we're currently refreshing to prevent infinite loops
    const isRefreshingRef = useRef(false);

    // Allow forcing a refresh of doctors from storage
    const refreshDoctors = () => {
        if (medicalCenterId && !isRefreshingRef.current) {
            console.log(`useDoctors: Refreshing doctors for medical center ${medicalCenterId}`);
            isRefreshingRef.current = true;

            try {
                // Reset the initialization status to force a reload
                initializedCenterRef.current = null;

                setLoading(true);

                // TODO FACU: Replace with API call to refresh doctors
                const storedDoctors: Doctor[] = [];

                setDoctors(storedDoctors);
                setLoading(false);

                // Mark as initialized again
                initializedCenterRef.current = medicalCenterId;

                console.log(`useDoctors: Successfully refreshed ${storedDoctors.length} doctors for medical center ${medicalCenterId}`);
            } catch (error) {
                console.error(`useDoctors: Error refreshing doctors for medical center ${medicalCenterId}:`, error);
            } finally {
                // Reset the refreshing flag after a short delay
                setTimeout(() => {
                    isRefreshingRef.current = false;
                }, 100);
            }
        } else if (isRefreshingRef.current) {
            console.log(`useDoctors: Already refreshing doctors for medical center ${medicalCenterId}, skipping`);
        }
    };

    return {
        doctors,
        loading,
        addDoctor,
        editDoctor,
        removeDoctor,
        refreshDoctors,
    };
}