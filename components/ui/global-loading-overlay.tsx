"use client"

import { useEffect, useState } from "react"
import ConfigurationPageSkeleton from "@/components/configuration/ConfigurationPageSkeleton"

interface GlobalLoadingOverlayProps {
  children: React.ReactNode
}

export default function GlobalLoadingOverlay({ children }: GlobalLoadingOverlayProps) {
  const [isLoading, setIsLoading] = useState(false)

  // Listen for the custom event to show the loading overlay
  useEffect(() => {
    const handleShowLoading = () => {
      setIsLoading(true)
    }

    window.addEventListener('show-loading-overlay', handleShowLoading)

    return () => {
      window.removeEventListener('show-loading-overlay', handleShowLoading)
    }
  }, [])

  if (isLoading) {
    return <ConfigurationPageSkeleton />
  }

  return <>{children}</>
}
