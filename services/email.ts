// No storage import needed for the email service
import {v4 as uuidv4} from 'uuid';

// Email service interface
export interface EmailProvider {
    sendEmail(to: string, subject: string, htmlContent: string): Promise<boolean>;
}

// Mock email interface for storage
export interface MockEmail {
    id: string;
    to: string;
    subject: string;
    htmlContent: string;
    sentAt: string;
    read: boolean;
}

// Storage key for mock emails
const MOCK_EMAILS_STORAGE_KEY = 'medical-scheduler-mock-emails';

// Mock email provider for development
class MockEmailProvider implements EmailProvider {
    async sendEmail(to: string, subject: string, htmlContent: string): Promise<boolean> {
        console.log(`[MOCK EMAIL] To: ${to}, Subject: ${subject}`);

        // Store the email in local storage for preview
        const emails = this.getMockEmails();
        const newEmail: MockEmail = {
            id: uuidv4(),
            to,
            subject,
            htmlContent,
            sentAt: new Date().toISOString(),
            read: false
        };

        this.saveMockEmails([...emails, newEmail]);

        // In development, always return true to simulate successful sending
        return true;
    }

    // Helper methods to manage mock emails
    getMockEmails(): MockEmail[] {
        if (typeof localStorage === 'undefined') return [];

        const data = localStorage.getItem(MOCK_EMAILS_STORAGE_KEY);
        if (!data) return [];

        try {
            return JSON.parse(data);
        } catch (e) {
            console.error("Error parsing mock emails from localStorage:", e);
            return [];
        }
    }

    saveMockEmails(emails: MockEmail[]): void {
        if (typeof localStorage === 'undefined') return;
        localStorage.setItem(MOCK_EMAILS_STORAGE_KEY, JSON.stringify(emails));
    }

    markEmailAsRead(emailId: string): void {
        if (typeof localStorage === 'undefined') return;

        const emails = this.getMockEmails();
        const updatedEmails = emails.map(email =>
            email.id === emailId ? {...email, read: true} : email
        );

        this.saveMockEmails(updatedEmails);
    }

    clearAllEmails(): void {
        if (typeof localStorage === 'undefined') return;
        localStorage.setItem(MOCK_EMAILS_STORAGE_KEY, JSON.stringify([]));
    }
}

// Default provider - will be replaced with a real provider in production
let emailProvider: EmailProvider = new MockEmailProvider();

// Function to set a different email provider
export function setEmailProvider(provider: EmailProvider) {
    emailProvider = provider;
}

// Export the mock provider for direct access to its methods
export const mockEmailProvider = new MockEmailProvider();

// Helper function to send doctor invitation emails
// Helper function to send password reset verification code
export async function sendPasswordResetEmail(
    email: string,
    userName: string,
    verificationCode: string
): Promise<boolean> {
    const subject = "Recuperación de contraseña - Turnera";

    // Get the base URL for the application
    const baseUrl = typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_BASE_URL || '';

    const htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recuperación de contraseña</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #333333; background-color: #f5f5f5; line-height: 1.6;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <!-- Header with Logo -->
    <tr>
      <td style="padding: 30px 40px; text-align: center; background-color: #f8fafc;">
        <img src="${baseUrl}/images/turnera-logo.svg" alt="Turnera Logo" width="150" style="max-width: 100%;">
      </td>
    </tr>

    <!-- Main Content -->
    <tr>
      <td style="padding: 40px 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td>
              <h1 style="margin: 0 0 20px; font-size: 24px; line-height: 1.2; color: #1e293b; font-weight: 700;">Recuperación de contraseña</h1>
              <p style="margin-bottom: 20px; line-height: 1.6; font-size: 16px; color: #4B5563;">Estimado/a ${userName},</p>
              <p style="margin-bottom: 24px; line-height: 1.6; font-size: 16px; color: #4B5563;">Recibimos una solicitud para restablecer la contraseña de su cuenta. Para continuar, utilice el siguiente código de verificación:</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Verification Code -->
    <tr>
      <td style="padding: 0 40px 30px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #f1f5f9; border-radius: 8px; padding: 20px; text-align: center;">
              <p style="margin: 0 0 10px; font-size: 14px; color: #64748b;">Su código de verificación es:</p>
              <p style="margin: 0; font-size: 32px; letter-spacing: 5px; font-weight: 700; color: #0070F3; font-family: monospace;">${verificationCode}</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Instructions -->
    <tr>
      <td style="padding: 0 40px 40px;">
        <p style="margin: 0 0 20px; font-size: 16px; color: #4B5563;">Este código es válido por 10 minutos. Si no solicitó restablecer su contraseña, puede ignorar este mensaje.</p>
        <p style="margin: 0; font-size: 16px; color: #4B5563;">Para completar el proceso, ingrese este código en la página de recuperación de contraseña.</p>
      </td>
    </tr>

    <!-- Footer -->
    <tr>
      <td style="padding: 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #F1F5F9; padding: 24px 40px; text-align: center; border-top: 1px solid #E2E8F0;">
              <p style="margin: 0; font-size: 14px; color: #64748B;">Este es un correo electrónico automático. Por favor, no responda a este mensaje.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
  `;

    try {
        return await emailProvider.sendEmail(email, subject, htmlContent);
    } catch (error) {
        console.error("Error sending password reset email:", error);
        return false;
    }
}

// Helper function to send email verification code to patients
export async function sendVerificationEmail(
    email: string,
    patientName: string,
    verificationCode: string
): Promise<boolean> {
    const subject = "Verificación de correo electrónico - Turnera";

    // Get the base URL for the application
    const baseUrl = typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_BASE_URL || '';

    const loginUrl = `${baseUrl}/plataforma/paciente/login`;

    const htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verificación de correo electrónico</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #333333; background-color: #f5f5f5; line-height: 1.6;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <!-- Header with Logo -->
    <tr>
      <td style="padding: 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #ffffff; padding: 30px 40px; text-align: center; border-bottom: 1px solid #E2E8F0;">
              <img src="/images/turnera-logo.svg" alt="Turnera Logo" width="150" height="auto" style="max-width: 80%; height: auto;">
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Welcome Banner -->
    <tr>
      <td style="padding: 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #3B82F6; padding: 30px 40px; text-align: center; position: relative;">
              <!-- Pattern Background -->
              <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('data:image/svg+xml,%3Csvg width=\'100%25\' height=\'100%25\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cdefs%3E%3Cpattern id=\'smallGrid\' width=\'20\' height=\'20\' patternUnits=\'userSpaceOnUse\'%3E%3Cpath d=\'M 20 0 L 0 0 0 20\' fill=\'none\' stroke=\'rgba(255, 255, 255, 0.08)\' stroke-width=\'0.5\'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\'100%25\' height=\'100%25\' fill=\'url(%23smallGrid)\'/%3E%3C/svg%3E'); opacity: 0.4;"></div>
              <h1 style="color: #ffffff; margin: 0; font-size: 26px; font-weight: 600; position: relative;">Verificación de correo electrónico</h1>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Main Content -->
    <tr>
      <td style="padding: 40px 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td>
              <p style="margin-bottom: 20px; line-height: 1.6; font-size: 16px; color: #4B5563;">Estimado/a ${patientName},</p>
              <p style="margin-bottom: 24px; line-height: 1.6; font-size: 16px; color: #4B5563;">Gracias por registrarse en Turnera. Para completar su registro, por favor utilice el siguiente código de verificación:</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Verification Code Box -->
    <tr>
      <td style="padding: 0 40px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #EFF6FF; border-radius: 12px; border-left: 4px solid #3B82F6; margin-bottom: 30px;">
          <tr>
            <td style="padding: 24px; text-align: center;">
              <p style="margin: 0 0 12px; font-size: 16px; color: #2563EB; font-weight: 500;">Su código de verificación:</p>
              <p style="margin: 0; font-size: 32px; letter-spacing: 8px; color: #1E40AF; background-color: rgba(59, 130, 246, 0.1); padding: 12px; border-radius: 8px; font-family: monospace; display: inline-block;">${verificationCode}</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Security Note -->
    <tr>
      <td style="padding: 0 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td>
              <div style="display: flex; align-items: center; background-color: #FFFBEB; border-radius: 12px; padding: 16px; margin-bottom: 30px; border-left: 4px solid #F59E0B;">
                <div style="margin-right: 16px; color: #F59E0B; font-size: 24px;">&#9888;</div>
                <p style="margin: 0; line-height: 1.6; font-size: 15px; color: #92400E;">Este código es válido por 10 minutos. No comparta este código con nadie.</p>
              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Buttons -->
    <tr>
      <td style="padding: 0 40px 30px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="text-align: center;">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                <tr>
                  <td style="border-radius: 8px; background: #3B82F6; text-align: center;">
                    <a href="${loginUrl}" style="background: #3B82F6; border-radius: 8px; color: #ffffff; display: inline-block; font-size: 16px; font-weight: 500; line-height: 1.1; padding: 16px 24px; text-decoration: none; text-align: center;">Ir a iniciar sesión</a>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Footer Message -->
    <tr>
      <td style="padding: 0 40px 30px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td>
              <hr style="border: none; height: 1px; background-color: #E2E8F0; margin: 0 0 24px;">
              <p style="margin-bottom: 10px; line-height: 1.6; font-size: 15px; color: #4B5563;">Si no ha solicitado este código, puede ignorar este correo electrónico.</p>
              <p style="margin-bottom: 0; line-height: 1.6; font-size: 15px; color: #4B5563;">Saludos cordiales,<br><strong style="color: #3B82F6;">El equipo de Turnera</strong></p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Footer -->
    <tr>
      <td style="padding: 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #F1F5F9; padding: 24px 40px; text-align: center; border-top: 1px solid #E2E8F0;">
              <p style="margin: 0; font-size: 14px; color: #64748B;">Este es un correo electrónico automático. Por favor, no responda a este mensaje.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
  `

    try {
        return await emailProvider.sendEmail(email, subject, htmlContent);
    } catch (error) {
        console.error("Error sending email verification code:", error);
        return false;
    }
}

// Helper function to generate a 6-digit verification code
function generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
}

// Helper function to send doctor invitation emails
export async function sendDoctorInvitationEmail(
    email: string,
    tempPassword: string,
    doctorName: string
): Promise<boolean> {
    const subject = "Bienvenido a Turnera - Su cuenta de profesional ha sido creada";

    // Get the base URL for the application
    const baseUrl = typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NEXT_PUBLIC_BASE_URL || '';

    // Generate verification code for direct password reset
    const verificationCode = generateVerificationCode();
    const verificationCodeExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24 hours from now

    // Store verification code for the user
    const user = storage.getUserByEmail(email);
    if (user) {
        const updatedUser = {
            ...user,
            verificationCode,
            verificationCodeExpiry
        };
        storage.saveUser(updatedUser);
    }

    const loginUrl = `${baseUrl}/plataforma/profesional/login`;
    const resetPasswordUrl = `/plataforma/recuperar-contrasena/reset?email=${encodeURIComponent(email)}&code=${verificationCode}`;
    const fullResetPasswordUrl = `${baseUrl}${resetPasswordUrl}`;

    console.log('Email service - Generated URLs:', {
        loginUrl,
        resetPasswordUrl,
        fullResetPasswordUrl,
        verificationCode
    });


    const htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Turnera te da la bienvenida</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body style="margin: 0; padding: 0; font-family: 'Inter', 'Helvetica Neue', Helvetica, Arial, sans-serif; color: #333333; background-color: #f5f5f5; line-height: 1.6;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);">
    <!-- Header with Logo -->
    <tr>
      <td style="padding: 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #ffffff; padding: 30px 40px; text-align: center; border-bottom: 1px solid #E2E8F0;">
              <img src="/images/turnera-logo.svg" alt="Turnera Logo" width="150" height="auto" style="max-width: 80%; height: auto;">
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Welcome Banner -->
    <tr>
      <td style="padding: 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #3B82F6; padding: 30px 40px; text-align: center; position: relative;">
              <!-- Pattern Background -->
              <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: url('data:image/svg+xml,%3Csvg width=\'100%25\' height=\'100%25\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cdefs%3E%3Cpattern id=\'smallGrid\' width=\'20\' height=\'20\' patternUnits=\'userSpaceOnUse\'%3E%3Cpath d=\'M 20 0 L 0 0 0 20\' fill=\'none\' stroke=\'rgba(255, 255, 255, 0.08)\' stroke-width=\'0.5\'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width=\'100%25\' height=\'100%25\' fill=\'url(%23smallGrid)\'/%3E%3C/svg%3E'); opacity: 0.4;"></div>
              <h1 style="color: #ffffff; margin: 0; font-size: 26px; font-weight: 600; position: relative;">¡Turnera te da la bienvenida!</h1>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Main Content -->
    <tr>
      <td style="padding: 40px 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td>
              <p style="margin-bottom: 20px; line-height: 1.6; font-size: 16px; color: #4B5563;">Estimado/a Dr/a. ${doctorName},</p>
              <p style="margin-bottom: 24px; line-height: 1.6; font-size: 16px; color: #4B5563;">Su cuenta de profesional ha sido creada en nuestro sistema de gestión de turnos médicos. A continuación, encontrará sus credenciales temporales:</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Credentials Box -->
    <tr>
      <td style="padding: 0 40px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #EFF6FF; border-radius: 12px; border-left: 4px solid #3B82F6; margin-bottom: 30px;">
          <tr>
            <td style="padding: 24px;">
              <p style="margin: 0 0 12px; font-size: 16px;"><strong style="color: #2563EB; font-weight: 500;">Email:</strong> <span style="color: #4B5563;">${email}</span></p>
              <p style="margin: 0; font-size: 16px;"><strong style="color: #2563EB; font-weight: 500;">Contraseña temporal:</strong> <span style="color: #4B5563; background-color: rgba(59, 130, 246, 0.1); padding: 4px 8px; border-radius: 4px; font-family: monospace; letter-spacing: 0.5px;">${tempPassword}</span></p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Security Note -->
    <tr>
      <td style="padding: 0 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td>
              <div style="display: flex; align-items: center; background-color: #FFFBEB; border-radius: 12px; padding: 16px; margin-bottom: 30px; border-left: 4px solid #F59E0B;">
                <div style="margin-right: 16px; color: #F59E0B; font-size: 24px;">&#9888;</div>
                <p style="margin: 0; line-height: 1.6; font-size: 15px; color: #92400E;">Por razones de seguridad, le recomendamos cambiar su contraseña después de iniciar sesión por primera vez.</p>
              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Buttons -->
    <tr>
      <td style="padding: 0 40px 30px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="text-align: center;">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
                <tr>
                  <td style="border-radius: 8px; background: #3B82F6; text-align: center;">
                    <a href="${loginUrl}" style="background: #3B82F6; border-radius: 8px; color: #ffffff; display: inline-block; font-size: 16px; font-weight: 500; line-height: 1.1; padding: 16px 24px; text-decoration: none; text-align: center;">Iniciar sesión</a>
                  </td>
                  <td width="16">&nbsp;</td>
                  <td style="border-radius: 8px; background: #10b981; text-align: center;">
                    <a href="${fullResetPasswordUrl}" style="background: #10b981; border-radius: 8px; color: #ffffff; display: inline-block; font-size: 16px; font-weight: 500; line-height: 1.1; padding: 16px 24px; text-decoration: none; text-align: center;">Cambiar contraseña</a>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Instructions -->
    <tr>
      <td style="padding: 0 40px 30px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #F8FAFC; border-radius: 12px; padding: 20px;">
              <div style="display: flex; align-items: flex-start;">
                <div style="background-color: #DBEAFE; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; margin-right: 12px; margin-top: 2px; flex-shrink: 0;">
                  <span style="color: #3B82F6; font-weight: bold; font-size: 14px;">i</span>
                </div>
                <p style="margin: 0; font-size: 15px; color: #64748B; line-height: 1.5;">
                  Puede iniciar sesión con las credenciales temporales proporcionadas o usar el botón "Cambiar contraseña" para establecer una nueva contraseña directamente sin necesidad de ingresar un código de verificación.
                </p>
              </div>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Footer Message -->
    <tr>
      <td style="padding: 0 40px 30px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td>
              <hr style="border: none; height: 1px; background-color: #E2E8F0; margin: 0 0 24px;">
              <p style="margin-bottom: 10px; line-height: 1.6; font-size: 15px; color: #4B5563;">Si tiene alguna pregunta o necesita asistencia, no dude en contactar al administrador del sistema.</p>
              <p style="margin-bottom: 0; line-height: 1.6; font-size: 15px; color: #4B5563;">Saludos cordiales,<br><strong style="color: #3B82F6;">El equipo de Turnera</strong></p>
            </td>
          </tr>
        </table>
      </td>
    </tr>

    <!-- Footer -->
    <tr>
      <td style="padding: 0;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
          <tr>
            <td style="background-color: #F1F5F9; padding: 24px 40px; text-align: center; border-top: 1px solid #E2E8F0;">
              <p style="margin: 0; font-size: 14px; color: #64748B;">Este es un correo electrónico automático. Por favor, no responda a este mensaje.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
  `

    try {
        return await emailProvider.sendEmail(email, subject, htmlContent);
    } catch (error) {
        console.error("Error sending doctor invitation email:", error);
        return false;
    }
}
