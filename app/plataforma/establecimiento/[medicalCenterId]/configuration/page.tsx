"use client"

import { useState, use<PERSON>ontext, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useAuth } from "@/hooks/useAuth"
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext"
import { <PERSON><PERSON>ontex<PERSON> } from "@/contexts/DoctorContext"
import { NewDoctorContext } from "@/contexts/NewDoctorContext"
import ConfigurationHeader from "@/components/configuration/configuration-header"
import ConfigurationNav from "@/components/configuration/configuration-nav"
import Sidebar from "@/components/configuration/configuration-sidebar"
import DoctorCard from "@/components/configuration/DoctorCard"
import DoctorDialog from "@/components/configuration/DoctorDialog"
import NewDoctorDialog from "@/components/configuration/NewDoctorDialog"
import CoverageCard from "@/components/configuration/CoverageCard"
import AddCoverageDialog from "@/components/configuration/AddCoverageDialog"
import NotificationsCard from "@/components/configuration/NotificationsCard"
import TabContent from "@/components/configuration/TabContent"
import { <PERSON>, <PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, CardDes<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { UserPlus, AlertTriangle, HelpCircle, Calendar } from "lucide-react"
import MedicalCenterConfigCard from "@/components/configuration/MedicalCenterConfigCard"
import UsersCard from "@/components/configuration/UsersCard"
import ConfigurationPageSkeleton from "@/components/configuration/ConfigurationPageSkeleton"
import GlobalLoadingOverlay from "@/components/ui/global-loading-overlay"
import GlobalFontStyles from "@/components/ui/global-font-styles"

interface AgendasTabProps {
  medicalCenterId: string
}

function AgendasTab({ medicalCenterId }: AgendasTabProps) {
  const { setIsNewDoctorDialogOpen } = useContext(NewDoctorContext)

  return (
    <>
      <Card className="mb-6 bg-white shadow-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <CardTitle className="text-[1.3rem] font-semibold text-gray-900">
                  Configuración de agendas
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Configure los días, horarios y tipos de atención para cada profesional en el establecimiento.
                </CardDescription>
              </div>
            </div>
            <Button
              onClick={() => setIsNewDoctorDialogOpen(true)}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Agregar Profesional
            </Button>
          </div>
        </CardHeader>
      </Card>
      <DoctorCard medicalCenterId={medicalCenterId} />
    </>
  )
}

export default function ConfigurationPage() {
  const params = useParams()
  const router = useRouter()
  const medicalCenterId = params.medicalCenterId as string
  const [selectedTab, setSelectedTab] = useState("agendas")
  const [isLoading, setIsLoading] = useState(true)
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const { currentUser, logout } = useAuth()

  const { medicalCenters, activeMedicalCenterId, setActiveMedicalCenterId, syncWithUrl } = useContext(MedicalCenterContext)
  // Get refreshDoctorsFromStorage to ensure we have the latest data
  const { refreshDoctorsFromStorage } = useContext(DoctorContext)

  // Derive activeMedicalCenter from medicalCenters and activeMedicalCenterId
  const activeMedicalCenter = medicalCenters.find((mc) => mc.id === activeMedicalCenterId) || null

  // Force refresh state to trigger re-renders
  const [forceRefresh, setForceRefresh] = useState(0);

  // Listen for doctor update events
  useEffect(() => {
    const handleDoctorConfigSaved = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.medicalCenterId === medicalCenterId) {
        console.log("ConfigurationPage: Doctor config saved event detected", customEvent.detail);

        // Force a refresh of the page data
        setForceRefresh(prev => prev + 1);

        // Refresh doctors from storage
        setTimeout(() => {
          refreshDoctorsFromStorage();
        }, 50);
      }
    };

    window.addEventListener('doctor-config-saved', handleDoctorConfigSaved);

    return () => {
      window.removeEventListener('doctor-config-saved', handleDoctorConfigSaved);
    };
  }, [medicalCenterId, refreshDoctorsFromStorage]);





  // Set active medical center based on URL parameter
  useEffect(() => {
    console.log("ConfigurationPage - useEffect triggered:", {
      medicalCenterId,
      activeMedicalCenterId,
      medicalCentersCount: medicalCenters.length,
      activeMedicalCenter: activeMedicalCenter ? activeMedicalCenter.id : null,
      forceRefresh
    });

    // First, ensure the active center in context matches URL
    if (medicalCenterId && medicalCenters.length > 0) {
      // Make sure the URL-provided center exists in our list
      const centerExists = medicalCenters.some(center => center.id === medicalCenterId);
      if (centerExists) {
        // Direct localStorage update to ensure it persists
        if (typeof window !== 'undefined') {
          // Import the storage keys without importing the whole module
          const ACTIVE_MEDICAL_CENTER_KEY = 'medical-scheduler-active-center';
          console.log(`ConfigurationPage: Direct localStorage update for active center: ${medicalCenterId}`);
          localStorage.setItem(ACTIVE_MEDICAL_CENTER_KEY, medicalCenterId);
        }

        // This will sync localStorage too via the context
        setActiveMedicalCenterId(medicalCenterId);
        // Use new method to sync with URL
        syncWithUrl(medicalCenterId);
      }
    }

    // Import storage dynamically to force consistency checks on direct page load
    const initializeConfiguration = async () => {
      try {
        // Only run this when directly accessing the configuration page
        if (typeof window !== 'undefined') {
          // Prevent infinite loops with a localStorage flag
          const configInitKey = `config-init-${medicalCenterId}`;
          const hasInitialized = localStorage.getItem(configInitKey);

          if (hasInitialized) {
            console.log(`Configuration for ${medicalCenterId} already initialized this session`);
            setIsLoading(false);
            return;
          }

          // Dynamically import the storage service

          // Force validation of storage to ensure data consistency
          storage.validateStorage();

          // Set the initialized flag
          localStorage.setItem(configInitKey, 'true');

          // Set the active medical center based on URL parameter
          console.log(`Setting ${medicalCenterId} as active medical center`);
          setActiveMedicalCenterId(medicalCenterId);

          // Refresh doctors data from storage to ensure we have the latest data
          console.log(`Refreshing doctors data for ${medicalCenterId}`);
          refreshDoctorsFromStorage();

          // Set loading to false after a short delay to allow context updates
          setTimeout(() => {
            setIsLoading(false);
          }, 300);
        }
      } catch (error) {
        console.error("Error initializing configuration page:", error);
        setIsLoading(false);
      }
    };

    // Run the initialization function
    initializeConfiguration();

    // Start a very short timeout to stop showing the loading state
    // This ensures we don't get stuck forever on the loading screen
    if (initTimeoutRef.current) {
      clearTimeout(initTimeoutRef.current);
    }

    initTimeoutRef.current = setTimeout(() => {
      console.log("Timeout reached, forcing loading to complete");
      setIsLoading(false);
    }, 2000);

    return () => {
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current);
      }
    };
  }, [medicalCenterId, medicalCenters, activeMedicalCenterId, setActiveMedicalCenterId, activeMedicalCenter, syncWithUrl, refreshDoctorsFromStorage, forceRefresh]);

  // Show skeleton loading for all loading states
  if (!medicalCenters.length || isLoading) {
    return <ConfigurationPageSkeleton />;
  }

  // Even if the active medical center doesn't match, we'll still render the page
  // after the timeout, but with a warning
  if (!activeMedicalCenter || activeMedicalCenter.id !== medicalCenterId) {
    console.warn(`Warning: Active medical center (${activeMedicalCenter?.id}) doesn't match the requested one (${medicalCenterId})`);
  }

  // Get the medical center from the URL parameter directly
  const displayedMedicalCenter = medicalCenters.find(mc => mc.id === medicalCenterId) || activeMedicalCenter;

  if (!displayedMedicalCenter) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white">
        <div className="text-center bg-white p-8 rounded-xl shadow-lg border border-red-100">
          <div className="relative">
            <div className="rounded-full h-16 w-16 bg-red-50 mx-auto mb-6 flex items-center justify-center">
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">Establecimiento no encontrado</h3>
          <p className="text-gray-500 mb-4">No pudimos encontrar el establecimiento solicitado.</p>
          <Button
            variant="outline"
            className="border-blue-200 text-blue-600 hover:bg-blue-50"
            onClick={() => router.push('/plataforma/establecimiento')}
          >
            Volver a establecimientos
          </Button>
        </div>
      </div>
    );
  }

  return (
    <GlobalLoadingOverlay>
      <div className="min-h-screen bg-blue-50 flex flex-col">
        <GlobalFontStyles />

        <ConfigurationHeader
          medicalCenter={displayedMedicalCenter}
          currentUser={currentUser}
          logout={() => logout ? logout() : null}
        />
        <ConfigurationNav medicalCenterId={medicalCenterId} />

      <main className="container mx-auto px-[6rem] pt-[0.25rem] pb-[1.5rem] flex">
        <Sidebar selectedTab={selectedTab} setSelectedTab={setSelectedTab} />

        <div className="flex-1">
          {selectedTab === "agendas" && <AgendasTab medicalCenterId={medicalCenterId} />}
          {selectedTab === "coberturas" && <CoverageCard />}
          {selectedTab === "notificaciones" && <NotificationsCard />}
          {selectedTab === "establecimiento" && <MedicalCenterConfigCard />}
          {selectedTab === "usuarios" && <UsersCard medicalCenterId={medicalCenterId} />}
          {selectedTab === "ayuda" && (
            <TabContent
              title="Ayuda"
              description="Recursos y soporte técnico para el uso de la plataforma"
              icon={<HelpCircle className="h-5 w-5" />}
            >
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-blue-800 mb-2">Centro de ayuda</h3>
                  <p className="text-gray-700 mb-4">
                    Encuentre respuestas a preguntas frecuentes y tutoriales sobre cómo utilizar la plataforma.
                  </p>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    Visitar centro de ayuda
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Soporte técnico</h3>
                    <p className="text-sm text-gray-600 mb-2">
                      ¿Tiene problemas técnicos? Contáctenos para recibir asistencia.
                    </p>
                    <Button variant="outline" className="w-full">
                      Contactar soporte
                    </Button>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h3 className="font-medium text-gray-900 mb-2">Tutoriales en video</h3>
                    <p className="text-sm text-gray-600 mb-2">
                      Aprenda a utilizar todas las funciones con nuestros videos explicativos.
                    </p>
                    <Button variant="outline" className="w-full">
                      Ver tutoriales
                    </Button>
                  </div>
                </div>
              </div>
            </TabContent>
          )}
        </div>
      </main>

      <DoctorDialog />
      <NewDoctorDialog />
      <AddCoverageDialog />
    </div>
    </GlobalLoadingOverlay>
  )
}