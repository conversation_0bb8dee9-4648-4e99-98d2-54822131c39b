"use client"

import { useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { useAuth } from "@/hooks/useAuth"
import { UserRole } from "@/types/users"

export default function EstablishmentLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const router = useRouter()
  const { currentUser, isAuthenticated, isLoading } = useAuth()

  // Authentication and authorization check
  useEffect(() => {
    // Skip authentication check for login page and admin page
    if (pathname === "/plataforma/establecimiento/login" || pathname.startsWith("/admin")) {
      return
    }

    if (!isLoading && !isAuthenticated) {
      // Not authenticated, redirect to login
      router.push('/')
      return
    }

    if (!isLoading && isAuthenticated && currentUser) {
      // Check if user has EMPLOYEE_USER role
      const hasEmployeeUserRole = currentUser.roles.includes(UserRole.EMPLOYEE_USER)

      if (!hasEmployeeUserRole) {
        // User doesn't have EMPL<PERSON>YEE_USER role, redirect to unauthorized page
        router.push('/')
        return
      }
    }
  }, [isAuthenticated, isLoading, currentUser, router, pathname])

  return (
    <>
      {children}
    </>
  )
}
