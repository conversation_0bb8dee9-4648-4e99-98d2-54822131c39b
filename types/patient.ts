export interface Patient {
    id: number
    name: string
    dni: string
    phone: string
    email: string
    coverage: string // Current coverage for the appointment (can be overridden)
    defaultCoverage?: string // The patient's default coverage that will be pre-selected when booking
    userId?: string // Reference to the user account if the patient has one
    isDefault?: boolean // Whether this is the default patient for a user account
    associatedPatientIds?: string[] // IDs of patients associated with this patient (for family members)
    phoneVerified?: boolean // Whether the patient's phone has been verified
    emailVerified?: boolean // Whether the patient's email has been verified
    createdByMedicalCenterId?: string // ID of the medical center that created this patient
    notes?: string // Additional notes about the patient (e.g., fraud detection, special circumstances)
}

// Consultation note for a specific appointment - immutable once saved
export interface ConsultationNote {
    id: string
    appointmentId: string
    date: string // YYYY-MM-DD format
    notes: string
    createdAt: string // ISO timestamp when note was created
    isLocked: boolean // Whether the note can be edited (false for active appointments, true for completed)
    consultationType: string // Type of consultation (e.g., "Consulta general", "Control")
    medicalCenterId?: string // ID of the medical center where consultation took place
}

// Medical data that a doctor maintains for a specific patient
export interface DoctorPatientMedicalData {
    id: string // Unique identifier for this medical data record
    doctorId: string
    patientId: string
    createdAt: string // ISO timestamp when this medical data was first created
    updatedAt: string // ISO timestamp when this medical data was last modified

    // Personal medical information (doctor-specific)
    personalData: {
        height?: number // in centimeters
        weight?: number // in kilograms
        dateOfBirth?: string // YYYY-MM-DD format
        bloodType?: string
        allergies?: string
        chronicConditions?: string
        medications?: string
        emergencyContact?: {
            name: string
            phone: string
            relationship: string
        }
    }

    // Consultation notes for appointments
    consultationNotes: ConsultationNote[]
}

// Type for creating new medical data
export interface CreateDoctorPatientMedicalData {
    doctorId: string
    patientId: string
    personalData?: Partial<DoctorPatientMedicalData['personalData']>
}

