"use client"

import {use<PERSON>tate, use<PERSON>ffe<PERSON>, use<PERSON><PERSON><PERSON>} from "react"
import {<PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter} from "@/components/ui/card"
import {Button} from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {
    User,
    UserRole,
    AdminPermissions,
    ReceptionistPermissions,
    defaultAdminPermissions,
    defaultReceptionistPermissions
} from "@/types/users"
import {toast, Toaster} from "sonner"
import {Edit, Trash2, UserCog, UserPlus, X, Search, CheckCircle2, AlertTriangle, Check} from "lucide-react"
import {generateUserId} from "@/utils/idGenerator"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger} from "@/components/ui/tabs"
import {useAuth} from "@/hooks/useAuth"
import {Checkbox} from "@/components/ui/checkbox"
import {PhoneInput} from "react-international-phone"
import "react-international-phone/style.css"
import "@/styles/phone-input.css"
import {getSpanishCountries} from "@/data/phoneCountries"
import {PhoneNumberUtil} from 'google-libphonenumber'
import {PatientContext} from "@/contexts/PatientContext"
import {useContext} from "react"

// Initialize phone number utility
const phoneUtil = PhoneNumberUtil.getInstance()

interface UsersCardProps {
    medicalCenterId: string
}

// Helper type for combined permission keys
type AllPermissionKeys = keyof AdminPermissions | keyof ReceptionistPermissions;

// Helper function to get role badge color
const getRoleBadgeColor = (role: UserRole): string => {
    switch (role) {
        case UserRole.SUPERUSER:
            return "bg-purple-100 text-purple-800";
        case UserRole.ADMIN:
            return "bg-blue-100 text-blue-800";
        case UserRole.RECEPTIONIST:
            return "bg-green-100 text-green-800";
        case UserRole.DOCTOR:
            return "bg-amber-100 text-amber-800";
        default:
            return "bg-gray-100 text-gray-800";
    }
};

// Helper function to get role for a specific medical center
const getRoleForMedicalCenter = (user: User, medicalCenterId: string): UserRole => {
    // Check if user has specific roles for medical centers
    if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
        const centerRole = user.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
        if (centerRole) {
            return centerRole.role;
        }
    }

    // Fall back to main role
    return user.roles;
};

export default function UsersCard({medicalCenterId}: UsersCardProps) {
    const [users, setUsers] = useState<User[]>([])
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
    const [searchQuery, setSearchQuery] = useState("")
    const [formError, setFormError] = useState<string | null>(null)
    const [phoneError, setPhoneError] = useState("")
    const {currentUser} = useAuth()
    const patientContext = useContext(PatientContext)

    // New user form state
    const [newUser, setNewUser] = useState({
        name: "",
        email: "",
        password: "",
        phone: "",
        dni: "",
        role: UserRole.ADMIN, // Default role
        doctorId: "", // For doctor users
    })

    // State for existing user search
    const [existingUser, setExistingUser] = useState<User | null>(null)

    // Selected user for editing or deletion
    const [selectedUser, setSelectedUser] = useState<User | null>(null)

    // State for name editing
    const [isEditingName, setIsEditingName] = useState(false)

    // State for available medical centers
    const [availableMedicalCenters, setAvailableMedicalCenters] = useState<Array<{ id: string; name: string }>>([]);

    // Load all medical centers
    useEffect(() => {
        const allCenters = storage.getMedicalCenters();
        setAvailableMedicalCenters(allCenters.map(center => ({id: center.id, name: center.name})));
    }, []);

    // Load users for this medical center
    useEffect(() => {
        if (medicalCenterId) {
            const medicalCenterUsers = storage.getUsersByMedicalCenter(medicalCenterId);
            // Ensure all loaded users have a permissions object if they are admin/receptionist
            const usersWithPermissions = medicalCenterUsers.map(user => {
                if ((user.roles === UserRole.ADMIN || user.roles === UserRole.RECEPTIONIST) && !user.permissions) {
                    console.warn(`User ${user.email} loaded without permissions. Assigning defaults.`);
                    if (user.roles === UserRole.ADMIN) {
                        return {...user, permissions: {...defaultAdminPermissions}};
                    } else if (user.roles === UserRole.RECEPTIONIST) {
                        return {...user, permissions: {...defaultReceptionistPermissions}};
                    }
                }
                return user;
            });
            setUsers(usersWithPermissions);
        }
    }, [medicalCenterId])

    // Filter users based on search query
    const filteredUsers = users.filter(user =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase())
    )

    // Create a new user or add existing user to this medical center
    const handleCreateUser = () => {
        setFormError(null)

        // If we have an existing user, add this medical center to their list
        if (existingUser) {
            // Validate role selection
            if (!newUser.role) {
                setFormError("Por favor, seleccione un rol para este establecimiento")
                return
            }

            // Check if user already has access to this medical center
            const userMedicalCenters = existingUser.medicalCenterIds || [existingUser.medicalCenterId]
            if (userMedicalCenters.includes(medicalCenterId)) {
                // User already has access to this medical center, show error
                setFormError("Este usuario ya está registrado en este establecimiento")
                return
            }

            // User doesn't have access to this center yet, add it
            const updatedUser = {...existingUser}

            // Ensure medicalCenterIds exists and includes this center
            if (!updatedUser.medicalCenterIds) {
                updatedUser.medicalCenterIds = [updatedUser.medicalCenterId]
            }
            updatedUser.medicalCenterIds.push(medicalCenterId)

            // Initialize or update medicalCenterRoles
            if (!updatedUser.medicalCenterRoles) {
                // Initialize with default role for primary center
                updatedUser.medicalCenterRoles = [{
                    medicalCenterId: updatedUser.medicalCenterId,
                    role: updatedUser.roles,
                    permissions: updatedUser.permissions
                }]
            }

            // Add role for this center with the selected role
            updatedUser.medicalCenterRoles.push({
                medicalCenterId,
                role: newUser.role, // Use the selected role for this medical center
                permissions: newUser.role === UserRole.ADMIN ? {...defaultAdminPermissions} :
                    newUser.role === UserRole.RECEPTIONIST ? {...defaultReceptionistPermissions} :
                        undefined
            })

            // Save updated user
            storage.saveUser(updatedUser)

            // Update local state
            setUsers(prevUsers => [...prevUsers, updatedUser])

            // Reset form and close dialog
            setNewUser({
                name: "",
                email: "",
                password: "",
                phone: "",
                dni: "",
                role: UserRole.ADMIN,
                doctorId: ""
            })
            setExistingUser(null)
            setIsAddDialogOpen(false)

            // Show success message
            toast.success(`Usuario ${updatedUser.name} agregado a este establecimiento`)
            return
        }

        // Creating a new user
        // Validate input
        if (!newUser.name || !newUser.email || !newUser.password || !newUser.phone) {
            setFormError("Por favor, complete todos los campos obligatorios")
            return
        }

        // Check for phone validation errors
        if (phoneError) {
            setFormError("Por favor, corrija los errores en el formulario")
            return
        }

        // Double-check the email is not already in use
        const emailCheck = storage.getUserByEmail(newUser.email)
        if (emailCheck) {
            setFormError(`El email "${newUser.email}" ya está registrado. Use la función de búsqueda.`)
            return
        }

        // Create new user
        const user: User = {
            id: generateUserId(),
            name: newUser.name,
            email: newUser.email,
            password: newUser.password,
            phone: newUser.phone,
            dni: newUser.dni || "00000000", // Default DNI if not provided
            roles: newUser.role,
            medicalCenterId: medicalCenterId,
            // Initialize medicalCenterIds with the current medical center
            medicalCenterIds: [medicalCenterId],
            // Initialize medicalCenterRoles
            medicalCenterRoles: [{
                medicalCenterId: medicalCenterId,
                role: newUser.role,
                permissions: newUser.role === UserRole.ADMIN
                    ? {...defaultAdminPermissions}
                    : newUser.role === UserRole.RECEPTIONIST
                        ? {...defaultReceptionistPermissions}
                        : undefined
            }],
            // Assign default permissions based on role (for backward compatibility)
            permissions: newUser.role === UserRole.ADMIN
                ? {...defaultAdminPermissions}
                : newUser.role === UserRole.RECEPTIONIST
                    ? {...defaultReceptionistPermissions}
                    : undefined
        }

        // Save user to storage
        storage.saveUser(user)

        // Create patient profile if phone and DNI are provided
        if (user.phone && user.dni && patientContext) {
            const patientId = patientContext.createPatientForProfessional(user);
            if (patientId) {
                // Update the user with the default patient ID
                const updatedUser = {...user, defaultPatientId: patientId};
                storage.saveUser(updatedUser);
                console.log(`Created patient profile ${patientId} for user ${user.name}`);
            }
        }

        // Update local state
        setUsers(prevUsers => [...prevUsers, user])

        // Reset form and close dialog
        setNewUser({
            name: "",
            email: "",
            password: "",
            phone: "",
            dni: "",
            role: UserRole.ADMIN,
            doctorId: ""
        })
        setExistingUser(null)
        setIsAddDialogOpen(false)
        setPhoneError("")

        // Show success message
        const hasPatientProfile = user.phone && user.dni;
        toast.success(
            <div>
                <p>Usuario {user.name} creado exitosamente</p>
                {hasPatientProfile && (
                    <p className="text-xs mt-1 text-green-600">Perfil de paciente creado automáticamente</p>
                )}
            </div>
        )
    }

    // Edit an existing user
    const handleEditUser = () => {
        if (!selectedUser) return

        setFormError(null)

        // Validate input
        if (!selectedUser.name || !selectedUser.email) {
            setFormError("Por favor, complete los campos requeridos")
            return
        }

        // Check if the email is already in use by another user
        const existingUser = storage.getUserByEmail(selectedUser.email)
        if (existingUser && existingUser.id !== selectedUser.id) {
            setFormError(`El email "${selectedUser.email}" ya está registrado por otro usuario`)
            return
        }

        // Create a copy of the user to update
        const updatedUser = {...selectedUser}

        // Only allow password changes if the current user is editing their own account
        if (currentUser?.id !== selectedUser.id) {
            // Remove any password changes
            delete updatedUser.password
        }

        // Check if this user has medicalCenterRoles
        if (!updatedUser.medicalCenterRoles) {
            // Initialize medicalCenterRoles with the current role for the primary center
            updatedUser.medicalCenterRoles = [{
                medicalCenterId: updatedUser.medicalCenterId,
                role: updatedUser.roles,
                permissions: updatedUser.permissions
            }]

            // If this is not the primary center, add a role for this center too
            if (medicalCenterId !== updatedUser.medicalCenterId) {
                updatedUser.medicalCenterRoles.push({
                    medicalCenterId: medicalCenterId,
                    role: updatedUser.roles,
                    permissions: updatedUser.roles === UserRole.ADMIN ? {...defaultAdminPermissions} :
                        updatedUser.roles === UserRole.RECEPTIONIST ? {...defaultReceptionistPermissions} :
                            undefined
                })
            }
        }

        // The role for this medical center is already updated in the Select onValueChange handler
        // We just need to make sure it's properly saved
        const centerRoleIndex = updatedUser.medicalCenterRoles.findIndex(
            mcr => mcr.medicalCenterId === medicalCenterId
        )

        // Just in case, if we don't find the role for this center, add it
        if (centerRoleIndex < 0) {
            // Get the role from the UI selection
            const roleForThisCenter = (() => {
                if (updatedUser.medicalCenterRoles) {
                    const centerRole = updatedUser.medicalCenterRoles.find(
                        mcr => mcr.medicalCenterId === medicalCenterId
                    );
                    if (centerRole) {
                        return centerRole.role;
                    }
                }
                return updatedUser.roles;
            })();

            // Add a new role for this center
            updatedUser.medicalCenterRoles.push({
                medicalCenterId: medicalCenterId,
                role: roleForThisCenter,
                permissions: roleForThisCenter === UserRole.ADMIN ? {...defaultAdminPermissions} :
                    roleForThisCenter === UserRole.RECEPTIONIST ? {...defaultReceptionistPermissions} :
                        undefined
            })
        }

        // If this is the primary medical center, update the main role for backward compatibility
        if (medicalCenterId === updatedUser.medicalCenterId) {
            // Update permissions for backward compatibility
            if (updatedUser.roles === UserRole.ADMIN || updatedUser.roles === UserRole.RECEPTIONIST) {
                updatedUser.permissions = updatedUser.roles === UserRole.ADMIN
                    ? {...defaultAdminPermissions}
                    : {...defaultReceptionistPermissions}
            } else if (updatedUser.roles === UserRole.SUPERUSER) {
                delete updatedUser.permissions // Superusers don't store permissions this way
            }
        }

        // Get the role for this medical center
        const roleForThisCenter = (() => {
            if (updatedUser.medicalCenterRoles) {
                const centerRole = updatedUser.medicalCenterRoles.find(
                    mcr => mcr.medicalCenterId === medicalCenterId
                );
                if (centerRole) {
                    return centerRole.role;
                }
            }
            return updatedUser.roles;
        })();

        // Save updated user to storage
        storage.saveUser(updatedUser)

        // Update local state
        setUsers(prevUsers =>
            prevUsers.map(user =>
                user.id === updatedUser.id ? updatedUser : user
            )
        )

        // Close dialog
        setIsEditDialogOpen(false)

        // Show success message
        toast.success(`Usuario ${updatedUser.name} actualizado exitosamente con rol ${getRoleName(roleForThisCenter)} en este centro`)
    }

    // Delete a user from the current medical center
    const handleDeleteUser = () => {
        if (!selectedUser) return

        // Create a copy of the user to update
        const updatedUser = {...selectedUser}

        // Check if user has multiple medical centers
        const userMedicalCenters = updatedUser.medicalCenterIds || [updatedUser.medicalCenterId]

        // If user only has this medical center, delete them completely
        if (userMedicalCenters.length === 1 && userMedicalCenters[0] === medicalCenterId) {
            // Delete user from storage
            storage.deleteUser(selectedUser.id)

            // Update local state
            setUsers(prevUsers => prevUsers.filter(user => user.id !== selectedUser.id))

            // Close dialog
            setIsDeleteDialogOpen(false)

            // Show success message
            toast.success(`Usuario ${selectedUser.name} eliminado exitosamente`)
            return
        }

        // User has multiple medical centers, just remove this one

        // Remove this medical center from medicalCenterIds
        if (updatedUser.medicalCenterIds) {
            updatedUser.medicalCenterIds = updatedUser.medicalCenterIds.filter(id => id !== medicalCenterId)
        }

        // Remove this medical center from medicalCenterRoles
        if (updatedUser.medicalCenterRoles) {
            updatedUser.medicalCenterRoles = updatedUser.medicalCenterRoles.filter(
                mcr => mcr.medicalCenterId !== medicalCenterId
            )
        }

        // If this was the primary medical center, update the primary to the first remaining one
        if (updatedUser.medicalCenterId === medicalCenterId && updatedUser.medicalCenterIds && updatedUser.medicalCenterIds.length > 0) {
            const newPrimaryCenter = updatedUser.medicalCenterIds[0]
            updatedUser.medicalCenterId = newPrimaryCenter

            // Update the primary role to match the role in the new primary center
            if (updatedUser.medicalCenterRoles) {
                const newPrimaryRole = updatedUser.medicalCenterRoles.find(mcr => mcr.medicalCenterId === newPrimaryCenter)
                if (newPrimaryRole) {
                    updatedUser.roles = newPrimaryRole.role
                    updatedUser.permissions = newPrimaryRole.permissions
                }
            }
        }

        // Save updated user
        storage.saveUser(updatedUser)

        // Update local state - remove user from this medical center's list
        setUsers(prevUsers => prevUsers.filter(user => user.id !== selectedUser.id))

        // Close dialog
        setIsDeleteDialogOpen(false)

        // Show success message
        toast.success(`Usuario ${selectedUser.name} eliminado de este establecimiento exitosamente`)
    }

    // Function to determine if current user can edit a specific user
    const canModifyUser = (user: User): boolean => {
        if (!currentUser) return false

        // Users can always edit themselves
        if (user.id === currentUser.id) return true;

        // Super users can modify anyone including other superusers
        if (currentUser.roles === UserRole.SUPERUSER) {
            // If trying to modify another superuser, check if there are enough superusers
            if (user.roles === UserRole.SUPERUSER) {
                // Check if there are other superusers in this medical center
                const superusersInCenter = storage.getUsersByMedicalCenter(medicalCenterId)
                    .filter(u => {
                        // Check if user has SUPERUSER role for this specific medical center
                        if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                            const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
                            if (centerRole) {
                                return centerRole.role === UserRole.SUPERUSER;
                            }
                        }
                        // Fall back to default role
                        return u.roles === UserRole.SUPERUSER;
                    });

                // Allow modifying superusers if there are at least 2 (including the current one)
                return superusersInCenter.length >= 2;
            }

            // Superusers can always modify non-superusers
            return true;
        }

        // Admins can modify receptionists
        if (currentUser.roles === UserRole.ADMIN) {
            return user.roles === UserRole.RECEPTIONIST
        }

        return false
    }

    // Function to determine if current user can delete a specific user
    const canDeleteUser = (user: User): boolean => {
        if (!currentUser) return false

        // Get the role for this specific medical center
        const getUserRoleForCenter = (u: User): UserRole => {
            if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
                if (centerRole) {
                    return centerRole.role;
                }
            }
            return u.roles;
        };

        const userRole = getUserRoleForCenter(user);
        const currentUserRole = getUserRoleForCenter(currentUser);

        // Users cannot delete themselves, except for superusers in certain conditions
        if (user.id === currentUser.id) {
            // Only superusers can delete themselves, and only if there's another superuser
            if (userRole === UserRole.SUPERUSER) {
                // Count superusers in this medical center
                const superusersInCenter = storage.getUsersByMedicalCenter(medicalCenterId)
                    .filter(u => {
                        // Check if user has SUPERUSER role for this specific medical center
                        if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                            const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
                            if (centerRole) {
                                return centerRole.role === UserRole.SUPERUSER;
                            }
                        }
                        // Fall back to default role
                        return u.roles === UserRole.SUPERUSER;
                    });

                // Allow deletion only if there are at least 2 superusers (including the current one)
                return superusersInCenter.length >= 2;
            }

            // Admin and receptionist users cannot delete themselves
            return false;
        }

        // Superusers cannot delete other superusers
        if (userRole === UserRole.SUPERUSER && currentUserRole !== UserRole.SUPERUSER) {
            return false;
        }

        // Admins can delete receptionists
        if (userRole === UserRole.RECEPTIONIST) {
            return currentUserRole === UserRole.ADMIN || currentUserRole === UserRole.SUPERUSER;
        }

        // Only superusers can delete admins
        if (userRole === UserRole.ADMIN) {
            return currentUserRole === UserRole.SUPERUSER;
        }

        // For other roles, use the same rules as for editing
        return canModifyUser(user);
    }

    // Helper function to get friendly names for permissions
    const getPermissionDisplayName = (key: AllPermissionKeys): string => {
        switch (key) {
            // Admin permissions
            case 'canManageAdmins':
                return 'Gestionar Administradores';
            case 'canManageReceptionists':
                return 'Gestionar Recepcionistas';
            case 'canViewGeneralSettings':
                return 'Ver Configuración General';
            case 'canViewDoctors':
                return 'Ver Médicos';
            case 'canViewCoverage':
                return 'Ver Cobertura';
            case 'canViewUsers':
                return 'Ver Usuarios';
            case 'canEditMedicalCenterInfo':
                return 'Editar Info del Establecimiento';
            case 'canEditDoctorInfo':
                return 'Editar Info de Médicos';
            case 'canManageCoverage':
                return 'Gestionar Cobertura';
            // Receptionist permissions
            case 'canManageAppointments':
                return 'Gestionar Turnos';
            default:
                return key; // Fallback to key name
        }
    }

    const getRoleName = useCallback((role: UserRole): string => {
        switch (role) {
            case UserRole.SUPERUSER:
                return "Super Usuario"
            case UserRole.ADMIN:
                return "Administrador"
            case UserRole.RECEPTIONIST:
                return "Recepcionista"
            default:
                return "Usuario"
        }
    }, [])

    return (
        <Card className="bg-white shadow-md border-2 border-transparent">
            <CardHeader>
                <div className="flex items-center gap-2">
                    <UserCog className="h-5 w-5 text-blue-600"/>
                    <div>
                        <CardTitle className="text-[1.3rem] font-semibold text-gray-900">
                            Gestión de usuarios
                        </CardTitle>
                        <CardDescription className="text-gray-600">
                            Administre los usuarios y sus permisos en el establecimiento.
                        </CardDescription>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                    <div className="relative w-72">
                        <Input
                            placeholder="Buscar usuarios..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-9"
                        />
                        <UserCog className="w-4 h-4 text-gray-500 absolute left-3 top-3"/>
                    </div>

                    <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                        <DialogTrigger asChild>
                            <Button
                                className="flex items-center gap-2 bg-blue-600"
                                onClick={() => {
                                    setNewUser({
                                        name: "",
                                        email: "",
                                        password: "",
                                        phone: "",
                                        dni: "",
                                        role: UserRole.ADMIN,
                                        doctorId: ""
                                    })
                                    setFormError(null)
                                    setPhoneError("")
                                }}
                            >
                                <UserPlus className="w-4 h-4"/>
                                Agregar Usuario
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-3xl">
                            <DialogHeader>
                                <DialogTitle>Agregar Nuevo Usuario</DialogTitle>
                                <DialogDescription>
                                    Cree un nuevo usuario para el establecimiento
                                </DialogDescription>
                            </DialogHeader>

                            {formError && (
                                <div className="bg-red-50 border-l-4 border-red-400 p-4 my-2">
                                    <div className="flex">
                                        <div className="flex-shrink-0">
                                            <X className="h-5 w-5 text-red-500"/>
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-sm text-red-700">{formError}</p>
                                        </div>
                                    </div>
                                </div>
                            )}

                            <div className="grid gap-4 py-4">
                                {/* Email search section */}
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="email" className="text-right">
                                        Email
                                    </Label>
                                    <div className="col-span-3 flex gap-2">
                                        <Input
                                            id="email"
                                            type="email"
                                            placeholder="<EMAIL>"
                                            className="flex-1 w-56"
                                            value={newUser.email}
                                            onChange={(e) => {
                                                setNewUser({...newUser, email: e.target.value})
                                                // Clear existing user when email changes
                                                if (existingUser && e.target.value !== existingUser.email) {
                                                    setExistingUser(null)
                                                }
                                            }}
                                        />
                                        <Button
                                            type="button"
                                            variant="outline"
                                            className="flex items-center gap-1"
                                            onClick={() => {
                                                // Search for existing user
                                                const foundUser = storage.getUserByEmail(newUser.email)
                                                if (foundUser) {
                                                    setExistingUser(foundUser)
                                                    // Pre-fill name from existing user
                                                    setNewUser(prev => ({
                                                        ...prev,
                                                        name: foundUser.name,
                                                        // Don't set password for existing user
                                                    }))
                                                } else {
                                                    setExistingUser(null)
                                                    toast.error(`No se encontró ningún usuario con el email ${newUser.email}`)
                                                }
                                            }}
                                        >
                                            <Search className="h-4 w-4"/>
                                            Buscar
                                        </Button>
                                    </div>
                                </div>

                                {/* Show existing user info if found */}
                                {existingUser && (
                                    existingUser.medicalCenterIds?.includes(medicalCenterId) ? (
                                        <div
                                            className="col-span-4 bg-amber-50 p-4 rounded-md border border-amber-200 mb-2">
                                            <div className="flex items-center gap-2 mb-2">
                                                <AlertTriangle className="h-5 w-5 text-amber-600"/>
                                                <span className="font-medium">Usuario ya registrado en este establecimiento</span>
                                            </div>
                                            <p className="text-sm mb-2">
                                                Este usuario ya está registrado en este establecimiento. Si desea
                                                modificar su rol,
                                                utilice la opción de editar usuario en la lista de usuarios.
                                            </p>
                                            <div className="grid grid-cols-2 gap-2 text-sm">
                                                <div><span className="font-medium">Nombre:</span> {existingUser.name}
                                                </div>
                                                <div><span className="font-medium">Email:</span> {existingUser.email}
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div
                                            className="col-span-4 bg-blue-50 p-4 rounded-md border border-blue-200 mb-2">
                                            <div className="flex items-center gap-2 mb-2">
                                                <CheckCircle2 className="h-5 w-5 text-green-600"/>
                                                <span className="font-medium">Usuario existente encontrado</span>
                                            </div>
                                            <p className="text-sm mb-2">Este usuario ya existe en el sistema. Se le
                                                asignará un rol en este establecimiento.</p>
                                            <div className="grid grid-cols-2 gap-2 text-sm">
                                                <div><span className="font-medium">Nombre:</span> {existingUser.name}
                                                </div>
                                                <div><span className="font-medium">Email:</span> {existingUser.email}
                                                </div>
                                            </div>
                                        </div>
                                    )
                                )}

                                {/* Name field - disabled for existing users */}
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="name" className="text-right">
                                        Nombre
                                    </Label>
                                    <Input
                                        id="name"
                                        placeholder="Nombre completo"
                                        className="col-span-3"
                                        value={newUser.name}
                                        onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                                        disabled={!!existingUser} // Disable if existing user
                                    />
                                </div>

                                {/* Password field - only for new users */}
                                {!existingUser && (
                                    <div className="grid grid-cols-4 items-center gap-4">
                                        <Label htmlFor="password" className="text-right">
                                            Contraseña
                                        </Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            placeholder="Contraseña segura"
                                            className="col-span-3"
                                            value={newUser.password}
                                            onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                                        />
                                    </div>
                                )}

                                {/* Phone and DNI fields - only for new users */}
                                {!existingUser && (
                                    <>
                                        <div className="grid grid-cols-4 items-center gap-4">
                                            <Label htmlFor="phone" className="text-right">
                                                Teléfono *
                                            </Label>
                                            <div className="col-span-3">
                                                <div className="custom-phone-input">
                                                    <PhoneInput
                                                        defaultCountry="ar"
                                                        value={newUser.phone}
                                                        onChange={(phone) => {
                                                            setNewUser({...newUser, phone});
                                                            setPhoneError("");

                                                            try {
                                                                // Parse the phone number using Google's libphonenumber
                                                                const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                                                                const isValid = phoneUtil.isValidNumber(phoneNumber);

                                                                if (!isValid && phone !== "+" && phone.length > 5) {
                                                                    setPhoneError("El número de teléfono no es válido");
                                                                }
                                                            } catch {
                                                                if (phone !== "+" && phone.length > 5) {
                                                                    setPhoneError("El número de teléfono no es válido");
                                                                }
                                                            }
                                                        }}
                                                        inputStyle={{
                                                            width: '100%',
                                                            height: '2.5rem'
                                                        }}
                                                        className="w-full custom-phone-input with-dial-code-preview"
                                                        placeholder="Teléfono"
                                                        countrySelectorStyleProps={{
                                                            buttonStyle: {
                                                                paddingLeft: '10px',
                                                                paddingRight: '5px'
                                                            }
                                                        }}
                                                        hideDropdown={false}
                                                        disableDialCodeAndPrefix={true}
                                                        showDisabledDialCodeAndPrefix={true}
                                                        disableFormatting={false}
                                                        preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                                                        countries={getSpanishCountries()}
                                                    />
                                                    {phoneError && (
                                                        <div className="text-xs text-red-600 mt-1">
                                                            {phoneError}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-4 items-center gap-4">
                                            <Label htmlFor="dni" className="text-right">
                                                DNI (Opcional)
                                            </Label>
                                            <Input
                                                id="dni"
                                                type="text"
                                                placeholder="12345678"
                                                className="col-span-3"
                                                value={newUser.dni}
                                                onChange={(e) => setNewUser({...newUser, dni: e.target.value})}
                                            />
                                        </div>
                                    </>
                                )}

                                {/* Role selection */}
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="role" className="text-right">
                                        Rol en este establecimiento
                                    </Label>
                                    <Select
                                        value={newUser.role}
                                        onValueChange={(value) => setNewUser({
                                            ...newUser,
                                            role: value as UserRole,
                                            doctorId: value === UserRole.DOCTOR ? newUser.doctorId : ""
                                        })}
                                    >
                                        <SelectTrigger className="col-span-3">
                                            <SelectValue placeholder="Seleccione un rol"/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            {/* Only superusers can create other superusers and admins */}
                                            {currentUser?.roles === UserRole.SUPERUSER && (
                                                <>
                                                    <SelectItem value={UserRole.SUPERUSER}>Superusuario</SelectItem>
                                                    <SelectItem value={UserRole.ADMIN}>Administrador</SelectItem>
                                                </>
                                            )}
                                            <SelectItem value={UserRole.RECEPTIONIST}>Recepcionista</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            <DialogFooter>
                                <Button variant="outline" onClick={() => {
                                    setIsAddDialogOpen(false)
                                    setExistingUser(null)
                                    setFormError(null)
                                }}>Cancelar</Button>
                                <Button
                                    onClick={handleCreateUser}
                                    disabled={existingUser
                                        ? existingUser.medicalCenterIds?.includes(medicalCenterId) || !newUser.role // Disable if user already in center or no role selected
                                        : !newUser.name || !newUser.email || !newUser.password || !newUser.phone || !!phoneError // For new users, all fields are required
                                    }
                                >
                                    {existingUser
                                        ? (existingUser.medicalCenterIds?.includes(medicalCenterId)
                                            ? "Usuario Ya Registrado"
                                            : "Agregar a Centro")
                                        : "Crear Usuario"
                                    }
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>

                <Tabs defaultValue="all" className="w-full">
                    <TabsList className="mb-4">
                        <TabsTrigger value="all">Todos</TabsTrigger>
                        <TabsTrigger value="superuser">Superusuarios</TabsTrigger>
                        <TabsTrigger value="admin">Administradores</TabsTrigger>
                        <TabsTrigger value="receptionist">Recepcionistas</TabsTrigger>

                    </TabsList>

                    <TabsContent value="all" className="mt-0">
                        <UsersList
                            users={filteredUsers}
                            onEditUser={(user) => {
                                // Ensure the user being edited has the correct permission structure
                                const userWithEnsuredPermissions = {...user};
                                if ((user.roles === UserRole.ADMIN || user.roles === UserRole.RECEPTIONIST) && !user.permissions) {
                                    console.warn(`User ${user.email} opened for edit without permissions. Assigning defaults.`);
                                    userWithEnsuredPermissions.permissions = user.roles === UserRole.ADMIN
                                        ? {...defaultAdminPermissions}
                                        : {...defaultReceptionistPermissions};
                                }
                                setSelectedUser(userWithEnsuredPermissions);
                                setIsEditDialogOpen(true);
                                setIsEditingName(false); // Reset editing state when a new user is selected
                                setFormError(null);
                            }}
                            onDeleteUser={(user) => {
                                setSelectedUser(user)
                                setIsDeleteDialogOpen(true)
                            }}
                            canModifyUser={canModifyUser}
                            canDeleteUser={canDeleteUser}
                            getRoleName={getRoleName}
                            medicalCenterId={medicalCenterId}
                        />
                    </TabsContent>

                    <TabsContent value="superuser" className="mt-0">
                        <UsersList
                            users={filteredUsers.filter(user => {
                                // Check if user has SUPERUSER role in this medical center
                                if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                    const centerRole = user.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
                                    if (centerRole) {
                                        return centerRole.role === UserRole.SUPERUSER;
                                    }
                                }
                                // Fall back to default role
                                return user.roles === UserRole.SUPERUSER;
                            })}
                            onEditUser={(user) => {
                                // Ensure the user being edited has the correct permission structure
                                const userWithEnsuredPermissions = {...user};
                                if ((user.roles === UserRole.ADMIN || user.roles === UserRole.RECEPTIONIST) && !user.permissions) {
                                    console.warn(`User ${user.email} opened for edit without permissions. Assigning defaults.`);
                                    userWithEnsuredPermissions.permissions = user.roles === UserRole.ADMIN
                                        ? {...defaultAdminPermissions}
                                        : {...defaultReceptionistPermissions};
                                }
                                setSelectedUser(userWithEnsuredPermissions);
                                setIsEditDialogOpen(true);
                                setFormError(null);
                            }}
                            onDeleteUser={(user) => {
                                setSelectedUser(user)
                                setIsDeleteDialogOpen(true)
                            }}
                            canModifyUser={canModifyUser}
                            canDeleteUser={canDeleteUser}
                            getRoleName={getRoleName}
                            medicalCenterId={medicalCenterId}
                        />
                    </TabsContent>

                    <TabsContent value="admin" className="mt-0">
                        <UsersList
                            users={filteredUsers.filter(user => {
                                // Check if user has ADMIN role in this medical center
                                if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                    const centerRole = user.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
                                    if (centerRole) {
                                        return centerRole.role === UserRole.ADMIN;
                                    }
                                }
                                // Fall back to default role
                                return user.roles === UserRole.ADMIN;
                            })}
                            onEditUser={(user) => {
                                // Ensure the user being edited has the correct permission structure
                                const userWithEnsuredPermissions = {...user};
                                if ((user.roles === UserRole.ADMIN || user.roles === UserRole.RECEPTIONIST) && !user.permissions) {
                                    console.warn(`User ${user.email} opened for edit without permissions. Assigning defaults.`);
                                    userWithEnsuredPermissions.permissions = user.roles === UserRole.ADMIN
                                        ? {...defaultAdminPermissions}
                                        : {...defaultReceptionistPermissions};
                                }
                                setSelectedUser(userWithEnsuredPermissions);
                                setIsEditDialogOpen(true);
                                setFormError(null);
                            }}
                            onDeleteUser={(user) => {
                                setSelectedUser(user)
                                setIsDeleteDialogOpen(true)
                            }}
                            canModifyUser={canModifyUser}
                            canDeleteUser={canDeleteUser}
                            getRoleName={getRoleName}
                            medicalCenterId={medicalCenterId}
                        />
                    </TabsContent>

                    <TabsContent value="receptionist" className="mt-0">
                        <UsersList
                            users={filteredUsers.filter(user => {
                                // Check if user has RECEPTIONIST role in this medical center
                                if (user.medicalCenterRoles && user.medicalCenterRoles.length > 0) {
                                    const centerRole = user.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
                                    if (centerRole) {
                                        return centerRole.role === UserRole.RECEPTIONIST;
                                    }
                                }
                                // Fall back to default role
                                return user.roles === UserRole.RECEPTIONIST;
                            })}
                            onEditUser={(user) => {
                                // Ensure the user being edited has the correct permission structure
                                const userWithEnsuredPermissions = {...user};
                                if ((user.roles === UserRole.ADMIN || user.roles === UserRole.RECEPTIONIST) && !user.permissions) {
                                    console.warn(`User ${user.email} opened for edit without permissions. Assigning defaults.`);
                                    userWithEnsuredPermissions.permissions = user.roles === UserRole.ADMIN
                                        ? {...defaultAdminPermissions}
                                        : {...defaultReceptionistPermissions};
                                }
                                setSelectedUser(userWithEnsuredPermissions);
                                setIsEditDialogOpen(true);
                                setFormError(null);
                            }}
                            onDeleteUser={(user) => {
                                setSelectedUser(user)
                                setIsDeleteDialogOpen(true)
                            }}
                            canModifyUser={canModifyUser}
                            canDeleteUser={canDeleteUser}
                            getRoleName={getRoleName}
                            medicalCenterId={medicalCenterId}
                        />
                    </TabsContent>


                </Tabs>

                {/* Edit User Dialog */}
                <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
                    setIsEditDialogOpen(open);
                    if (!open) setIsEditingName(false); // Reset editing state when dialog closes
                }}>
                    <DialogContent className="sm:max-w-3xl w-full">
                        <DialogHeader>
                            <DialogTitle>Editar Usuario</DialogTitle>
                            <DialogDescription>
                                Modifique los datos y permisos del usuario
                            </DialogDescription>
                        </DialogHeader>

                        {formError && (
                            <div className="bg-red-50 border-l-4 border-red-400 p-4 my-2">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <X className="h-5 w-5 text-red-500"/>
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm text-red-700">{formError}</p>
                                    </div>
                                </div>
                            </div>
                        )}

                        {selectedUser && (
                            <div className="grid gap-4 py-4">
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit-name" className="text-right">
                                        Nombre
                                    </Label>
                                    {currentUser?.id === selectedUser.id ? (
                                        <div className="col-span-3 flex gap-2">
                                            {isEditingName ? (
                                                <>
                                                    <Input
                                                        id="edit-name"
                                                        placeholder="Nombre completo"
                                                        className="flex-1"
                                                        value={selectedUser.name}
                                                        onChange={(e) => setSelectedUser({
                                                            ...selectedUser,
                                                            name: e.target.value
                                                        })}
                                                        autoFocus
                                                    />
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => setIsEditingName(false)}
                                                    >
                                                        <Check className="h-4 w-4"/>
                                                        Aceptar
                                                    </Button>
                                                </>
                                            ) : (
                                                <>
                                                    <div className="flex-1 flex items-center">
                                                        <span className="text-gray-700">{selectedUser.name}</span>
                                                    </div>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => setIsEditingName(true)}
                                                    >
                                                        <Edit className="h-4 w-4"/>
                                                        Editar
                                                    </Button>
                                                </>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="col-span-3 flex items-center">
                                            <span className="text-gray-700">{selectedUser.name}</span>
                                            <span className="ml-2 text-xs text-gray-500 italic">(Solo el usuario puede cambiar su nombre)</span>
                                        </div>
                                    )}
                                </div>
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit-email" className="text-right">
                                        Email
                                    </Label>
                                    <div className="col-span-3 flex items-center">
                                        <span className="text-gray-700">{selectedUser.email}</span>
                                        <span className="ml-2 text-xs text-gray-500 italic">(No se puede cambiar)</span>
                                    </div>
                                </div>
                                {/* Password field - only editable by the user themselves */}
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit-password" className="text-right">
                                        Contraseña
                                    </Label>
                                    {currentUser?.id === selectedUser.id ? (
                                        <div className="col-span-3">
                                            <Input
                                                id="edit-password"
                                                type="password"
                                                placeholder="Dejar en blanco para mantener la misma"
                                                className="w-full"
                                                value={selectedUser.password || ""}
                                                onChange={(e) => setSelectedUser({
                                                    ...selectedUser,
                                                    password: e.target.value
                                                })}
                                            />
                                        </div>
                                    ) : (
                                        <div className="col-span-3 flex items-center text-gray-500 italic">
                                            <span>Solo el usuario puede cambiar su propia contraseña</span>
                                        </div>
                                    )}
                                </div>
                                <div className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor="edit-role" className="text-right">
                                        Rol en este establecimiento
                                    </Label>
                                    <Select
                                        value={(() => {
                                            // Get role for this specific medical center if available
                                            if (selectedUser.medicalCenterRoles) {
                                                const centerRole = selectedUser.medicalCenterRoles.find(
                                                    mcr => mcr.medicalCenterId === medicalCenterId
                                                );
                                                if (centerRole) {
                                                    return centerRole.role;
                                                }
                                            }
                                            // Fall back to main role
                                            return selectedUser.roles;
                                        })()}
                                        onValueChange={(value) => {
                                            const newRole = value as UserRole;
                                            // Create a copy of the user
                                            const updatedUser = {...selectedUser};

                                            // Reset permissions to default when role changes
                                            const defaultPerms = newRole === UserRole.ADMIN
                                                ? {...defaultAdminPermissions}
                                                : newRole === UserRole.RECEPTIONIST
                                                    ? {...defaultReceptionistPermissions}
                                                    : undefined;

                                            // Initialize medicalCenterRoles if it doesn't exist
                                            if (!updatedUser.medicalCenterRoles) {
                                                updatedUser.medicalCenterRoles = [{
                                                    medicalCenterId: updatedUser.medicalCenterId,
                                                    role: updatedUser.role,
                                                    permissions: updatedUser.permissions
                                                }];
                                            }

                                            // Find or create the role for this medical center
                                            const centerRoleIndex = updatedUser.medicalCenterRoles.findIndex(
                                                mcr => mcr.medicalCenterId === medicalCenterId
                                            );

                                            if (centerRoleIndex >= 0) {
                                                // Update existing role for this center
                                                updatedUser.medicalCenterRoles[centerRoleIndex].role = newRole;
                                                updatedUser.medicalCenterRoles[centerRoleIndex].permissions = defaultPerms;
                                            } else {
                                                // Add new role for this center
                                                updatedUser.medicalCenterRoles.push({
                                                    medicalCenterId,
                                                    role: newRole,
                                                    permissions: defaultPerms
                                                });
                                            }

                                            // Only update the main role if this is the primary medical center
                                            if (medicalCenterId === updatedUser.medicalCenterId) {
                                                updatedUser.role = newRole;
                                                updatedUser.permissions = defaultPerms;
                                            }

                                            setSelectedUser(updatedUser);
                                        }}
                                        disabled={
                                            // Admins can't modify other admins
                                            (currentUser?.roles === UserRole.ADMIN && selectedUser.roles === UserRole.ADMIN) ||
                                            // Superusers can only change their role if there are other superusers
                                            (selectedUser.roles === UserRole.SUPERUSER && currentUser?.id === selectedUser.id &&
                                                storage.getUsersByMedicalCenter(medicalCenterId)
                                                    .filter(u => {
                                                        // Check if user has SUPERUSER role for this specific medical center
                                                        if (u.medicalCenterRoles && u.medicalCenterRoles.length > 0) {
                                                            const centerRole = u.medicalCenterRoles.find(mcr => mcr.medicalCenterId === medicalCenterId);
                                                            if (centerRole) {
                                                                return centerRole.role === UserRole.SUPERUSER;
                                                            }
                                                        }
                                                        // Fall back to default role
                                                        return u.roles === UserRole.SUPERUSER;
                                                    }).length < 2) ||
                                            // Non-superusers can't modify superusers
                                            (selectedUser.roles === UserRole.SUPERUSER && currentUser?.roles !== UserRole.SUPERUSER)
                                        }
                                    >
                                        <SelectTrigger className="col-span-3">
                                            <SelectValue placeholder="Seleccione un rol"/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            {currentUser?.roles === UserRole.SUPERUSER && (
                                                <>
                                                    <SelectItem value={UserRole.SUPERUSER}>Superusuario</SelectItem>
                                                    <SelectItem value={UserRole.ADMIN}>Administrador</SelectItem>
                                                </>
                                            )}
                                            <SelectItem value={UserRole.RECEPTIONIST}>Recepcionista</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Multiple Medical Centers section removed */}

                                {/* Permissions Section - Only visible to superusers editing admin/receptionist */}
                                {currentUser?.roles === UserRole.SUPERUSER &&
                                    (selectedUser.roles === UserRole.ADMIN || selectedUser.roles === UserRole.RECEPTIONIST) &&
                                    selectedUser.permissions && (
                                        <div className="col-span-4 space-y-3 border-t pt-4 mt-2">
                                            <Label className="text-base font-medium">Permisos Específicos</Label>
                                            <div className="grid grid-cols-2 gap-x-4 gap-y-2 pl-2">
                                                {Object.keys(selectedUser.permissions).map((permKey) => (
                                                    <div key={permKey} className="flex items-center space-x-2">
                                                        <Checkbox
                                                            id={`perm-${permKey}`}
                                                            checked={Boolean(selectedUser.permissions?.[permKey as keyof typeof selectedUser.permissions])}
                                                            onCheckedChange={(checked) => {
                                                                setSelectedUser({
                                                                    ...selectedUser,
                                                                    permissions: selectedUser.permissions ? {
                                                                        ...selectedUser.permissions,
                                                                        [permKey]: checked === true,
                                                                    } : selectedUser.role === UserRole.ADMIN ? {
                                                                        ...defaultAdminPermissions,
                                                                        [permKey]: checked === true
                                                                    } : {
                                                                        ...defaultReceptionistPermissions,
                                                                        [permKey]: checked === true
                                                                    },
                                                                });
                                                            }}
                                                        />
                                                        <Label htmlFor={`perm-${permKey}`}
                                                               className="text-sm font-normal">
                                                            {getPermissionDisplayName(permKey as AllPermissionKeys)}
                                                        </Label>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                            </div>
                        )}

                        <DialogFooter>
                            <Button variant="outline" onClick={() => {
                                setIsEditDialogOpen(false);
                                setFormError(null);
                            }}>Cancelar</Button>
                            <Button
                                onClick={handleEditUser}
                                disabled={!selectedUser?.name || !selectedUser?.email}
                            >
                                Guardar Cambios
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {/* Delete User Confirmation Dialog */}
                <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Eliminar Usuario</DialogTitle>
                            <DialogDescription>
                                {selectedUser && (
                                    (selectedUser.medicalCenterIds?.length || 0) > 1 ?
                                        `¿Está seguro que desea eliminar este usuario de este establecimiento? El usuario seguirá existiendo en otros establecimientos.` :
                                        `¿Está seguro que desea eliminar este usuario completamente? Esta acción no se puede deshacer.`
                                )}
                            </DialogDescription>
                        </DialogHeader>

                        {selectedUser && (
                            <div className="p-4 bg-gray-50 rounded-md my-2">
                                <p><strong>Nombre:</strong> {selectedUser.name}</p>
                                <p><strong>Email:</strong> {selectedUser.email}</p>
                                <p>
                                    <strong>Rol:</strong> {getRoleName(getRoleForMedicalCenter(selectedUser, medicalCenterId))}
                                </p>
                                {(selectedUser.medicalCenterIds?.length || 0) > 1 && (
                                    <p className="mt-2 text-amber-600 text-sm">
                                        <strong>Nota:</strong> Este usuario será eliminado
                                        de {availableMedicalCenters.find(mc => mc.id === medicalCenterId)?.name || 'este centro'}.
                                    </p>
                                )}
                            </div>
                        )}

                        <DialogFooter>
                            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancelar</Button>
                            <Button variant="destructive" onClick={handleDeleteUser}>
                                {selectedUser && (selectedUser.medicalCenterIds?.length || 0) > 1
                                    ? "Eliminar de este centro"
                                    : "Eliminar Usuario"}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

            </CardContent>

            <CardFooter className="border-t pt-6 flex justify-between">
                <div className="text-sm text-gray-500">
                    Total: {filteredUsers.length} usuarios encontrados
                </div>
                <Toaster/>
            </CardFooter>
        </Card>
    )
}

// Users list component
interface UsersListProps {
    users: User[]
    onEditUser: (user: User) => void
    onDeleteUser: (user: User) => void
    canModifyUser: (user: User) => boolean
    canDeleteUser: (user: User) => boolean
    getRoleName: (role: UserRole) => string
    medicalCenterId: string
}

function UsersList({
                       users,
                       onEditUser,
                       onDeleteUser,
                       canModifyUser,
                       canDeleteUser,
                       getRoleName,
                       medicalCenterId
                   }: UsersListProps) {
    if (users.length === 0) {
        return (
            <div className="text-center py-8 text-gray-500">
                No se encontraron usuarios
            </div>
        )
    }

    return (
        <div className="divide-y">
            {users.map((user) => (
                <div key={user.id} className="py-4 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <UserCog className="h-5 w-5 text-blue-600"/>
                        </div>
                        <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                    </div>

                    <div className="flex items-center">
            <span
                className={`px-2 py-1 text-xs rounded-full mr-4 ${getRoleBadgeColor(getRoleForMedicalCenter(user, medicalCenterId))}`}>
              {getRoleName(getRoleForMedicalCenter(user, medicalCenterId))}
            </span>

                        <div className="flex space-x-2">
                            {canModifyUser(user) && (
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onEditUser(user)}
                                    className="text-blue-600 hover:text-blue-800"
                                >
                                    <Edit className="h-4 w-4"/>
                                </Button>
                            )}
                            {canDeleteUser(user) && (
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => onDeleteUser(user)}
                                    className="text-red-600 hover:text-red-800"
                                >
                                    <Trash2 className="h-4 w-4"/>
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    )
}
