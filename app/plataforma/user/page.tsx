"use client"

import React from "react"
import {useRouter} from "next/navigation"
import {Building2, Steth<PERSON>cope, User} from "lucide-react"
import {UserRole} from "@/types/users"
import {Button} from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import {useAuth} from "@/contexts/AuthContext"
import {getProfessionalUserId} from "@/utils/userUtils";

export default function UserPage() {
    const router = useRouter()
    const {currentUser, isLoading, logout, isAuthenticated} = useAuth()

    // Redirect if not authenticated
    React.useEffect(() => {
        if (isLoading) return
        if (!isAuthenticated || !currentUser) {
            router.push("/plataforma/login")
        }
    }, [isAuthenticated, isLoading, currentUser, router])

    if (!currentUser) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-blue-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>Cargando...</p>
                </div>
            </div>
        )
    }

    const handleRoleSelection = (role: UserRole) => {
        const hasRole = currentUser.roles.includes(role)
        if (!hasRole) return

        switch (role) {
            case UserRole.PROFESSIONAL_USER:
                const doctorId = getProfessionalUserId(currentUser)
                if (doctorId) {
                    router.push(`/plataforma/profesional/${doctorId}`)
                }
                break
            case UserRole.EMPLOYEE_USER:
                router.push("/plataforma/establecimiento")
                break
            case UserRole.TURNERA_USER:
                router.push("/")
                break
        }
    }

    const getRoleConfig = (role: UserRole) => {
        switch (role) {
            case UserRole.TURNERA_USER:
                return {
                    icon: User,
                    label: "Paciente",
                    description: "Reservar y gestionar turnos médicos",
                    color: "text-[#0070F3]",
                    bgColor: "bg-[#0070F3]/5",
                    borderColor: "border-[#0070F3]",
                    hoverColor: "hover:bg-[#0070F3]/10",
                    buttonBg: "bg-[#0070F3]",
                    buttonHover: "hover:bg-[#0056CC]"
                }
            case UserRole.PROFESSIONAL_USER:
                return {
                    icon: Stethoscope,
                    label: "Profesional",
                    description: "Gestionar agenda profesional",
                    color: "text-[#089ca4]",
                    bgColor: "bg-[#089ca4]/5",
                    borderColor: "border-[#089ca4]",
                    hoverColor: "hover:bg-[#089ca4]/10",
                    buttonBg: "bg-[#089ca4]",
                    buttonHover: "hover:bg-[#067176]"
                }
            case UserRole.EMPLOYEE_USER:
                return {
                    icon: Building2,
                    label: "Establecimiento",
                    description: "Administrar establecimiento médico",
                    color: "text-[#0a7c82]",
                    bgColor: "bg-[#0a7c82]/5",
                    borderColor: "border-[#0a7c82]",
                    hoverColor: "hover:bg-[#0a7c82]/10",
                    buttonBg: "bg-[#0a7c82]",
                    buttonHover: "hover:bg-[#085c61]"
                }
            default:
                return null
        }
    }

    return (
        <div className="min-h-screen bg-blue-50 flex flex-col">
            <header className="py-3">
                <div className="container mx-auto flex justify-between items-center px-4">
                    <Link href="/">
                        <Image
                            src="/images/turnera-logo.svg"
                            alt="Turnera Logo"
                            width={120}
                            height={36}
                            className="h-8 w-auto"
                            priority
                        />
                    </Link>
                    <Button
                        variant="outline"
                        onClick={logout}
                        className="text-gray-600 hover:text-red-600 hover:border-red-600"
                    >
                        Cerrar sesión
                    </Button>
                </div>
            </header>

            <main className="flex-grow container mx-auto px-4 py-8 flex items-center justify-center">
                <div className="w-full max-w-4xl">
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold text-[#1c2533] mb-2">
                            Bienvenido, {currentUser.name} {currentUser.surname}
                        </h1>
                        <p className="text-gray-600 text-lg">
                            Seleccione el tipo de cuenta con la que desea acceder
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {currentUser.roles.map((role) => {
                            const config = getRoleConfig(role)
                            if (!config) return null

                            const Icon = config.icon

                            return (
                                <div
                                    key={role}
                                    className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-all duration-300 cursor-pointer group flex flex-col h-full"
                                    onClick={() => handleRoleSelection(role)}
                                >
                                    <div className="text-center flex flex-col flex-grow">
                                        <div
                                            className={`inline-flex p-4 rounded-full ${config.bgColor} mb-4 group-hover:scale-110 transition-transform duration-300 self-center`}>
                                            <Icon className={`h-8 w-8 ${config.color}`}/>
                                        </div>
                                        <h3 className={`text-xl font-semibold mb-2 ${config.color}`}>
                                            {config.label}
                                        </h3>
                                        <p className="text-gray-600 text-sm mb-6 flex-grow">
                                            {config.description}
                                        </p>
                                        <Button
                                            className={`w-full text-white ${config.buttonBg} ${config.buttonHover} transition-colors duration-200 mt-auto`}
                                            onClick={(e) => {
                                                e.stopPropagation()
                                                handleRoleSelection(role)
                                            }}
                                        >
                                            Acceder como {config.label}
                                        </Button>
                                    </div>
                                </div>
                            )
                        })}
                    </div>

                    {currentUser.roles.length === 0 && (
                        <div className="text-center py-12">
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
                                <p className="text-yellow-800">
                                    No se encontraron roles asignados a su cuenta.
                                    Por favor, contacte al administrador.
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </main>

            <footer className="bg-white border-t border-gray-200 py-6 mt-auto">
                <div className="container mx-auto px-4">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="mb-4 md:mb-0">
                            <Image
                                src="/images/turnera-logo.svg"
                                alt="Turnera Logo"
                                width={120}
                                height={36}
                                className="h-6 w-auto mb-2"
                            />
                            <p className="text-gray-500 text-sm">
                                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
                            </p>
                        </div>
                        <div className="flex space-x-4">
                            <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3]">
                                Términos y Condiciones
                            </Button>
                            <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3]">
                                Privacidad
                            </Button>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    )
}
