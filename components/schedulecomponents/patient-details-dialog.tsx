"use client"

import {useEffect, useState} from "react"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Tit<PERSON>} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Switch} from "@/components/ui/switch"
import {Check, Pencil} from "lucide-react"
import {PhoneInput} from "react-international-phone"
import {PhoneNumberUtil} from 'google-libphonenumber'
import "react-international-phone/style.css"
import "@/styles/phone-input.css"
import {getSpanishCountries} from "@/data/phoneCountries"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {PatientResponse} from "@/types/patient/patientResponse";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {PatientAppointment} from "@/types/patient/patientAppointment";

interface PatientDetailsDialogProps {
    patient: PatientResponse | null
    isOpen: boolean
    onClose: () => void
    onAppointmentSelect?: (appointment: ProfessionalAppointment | PatientAppointment | null) => void
}

export function PatientDetailsDialog({
                                         patient,
                                         isOpen,
                                         onClose,
                                         onAppointmentSelect,
                                     }: PatientDetailsDialogProps) {
    // Initialize Google's libphonenumber utility
    const phoneUtil = PhoneNumberUtil.getInstance()

    // State for editable fields
    const [editingPhone, setEditingPhone] = useState(false)
    const [editingEmail, setEditingEmail] = useState(false)

    // State for edited values
    const [coverageValue, setCoverageValue] = useState("")
    const [planValue, setPlanValue] = useState("")
    const [noCoverage, setNoCoverage] = useState(false)
    const [phoneValue, setPhoneValue] = useState("")
    const [emailValue, setEmailValue] = useState("")

    // State for current patient data (to update UI immediately)
    const [currentCoverage, setCurrentCoverage] = useState("")
    const [currentPhone, setCurrentPhone] = useState("")
    const [currentEmail, setCurrentEmail] = useState("")

    // State for dropdown open
    const [isCoverageOpen, setIsCoverageOpen] = useState(false)
    const [isPlanOpen, setIsPlanOpen] = useState(false)

    // State for coverage edit dialog
    const [coverageDialogOpen, setCoverageDialogOpen] = useState(false)

    // State for validation errors
    const [phoneError, setPhoneError] = useState("")
    const [emailError, setEmailError] = useState("")
    const [successMessage, setSuccessMessage] = useState("")

    // Dynamically create plansByCoverage from DEFAULT_COVERAGES
    const plansByCoverage = DEFAULT_COVERAGES.reduce((acc, coverage) => {
        acc[coverage.name] = coverage.plans || [];
        return acc;
    }, {} as Record<string, string[]>);

    // Helper function to parse coverage string into coverage and plan values
    const parseCoverageFromString = (coverageString: string) => {
        if (coverageString === "Sin Cobertura") {
            return {coverage: "", plan: "", noCoverage: true};
        }

        // Find the coverage in DEFAULT_COVERAGES
        let foundCoverage = false;
        let coverage = "";
        let plan = "";

        for (const coverageItem of DEFAULT_COVERAGES) {
            if (coverageString.startsWith(coverageItem.name)) {
                coverage = coverageItem.name;
                plan = coverageString.substring(coverageItem.name.length).trim();
                foundCoverage = true;
                break;
            }
        }

        // If no matching coverage found, use the first word as coverage
        if (!foundCoverage) {
            const coverageParts = coverageString.split(' ');
            if (coverageParts.length > 1) {
                coverage = coverageParts[0];
                plan = coverageParts.slice(1).join(' ');
            } else {
                coverage = coverageString;
                plan = "";
            }
        }

        return {coverage, plan, noCoverage: false};
    };

    // Helper function to initialize coverage form values
    const initializeCoverageValues = (coverageString: string) => {
        const {coverage, plan, noCoverage} = parseCoverageFromString(coverageString);
        setCoverageValue(coverage);
        setPlanValue(plan);
        setNoCoverage(noCoverage);
    };

    // Helper function to show success message with auto-clear
    const showSuccessMessage = (message: string) => {
        setSuccessMessage(message);
        setTimeout(() => {
            setSuccessMessage("");
        }, 3000);
    };

    useEffect(() => {
        if (patient) {
            // Initialize editable fields with current values
            initializeCoverageValues(patient.cobertura);

            // Set current values for immediate UI updates
            setCurrentCoverage(patient.cobertura)
            setCurrentPhone(patient.phone)
            setCurrentEmail(patient.email || "")

            setPhoneValue(patient.phone)
            setEmailValue(patient.email || "")
        }
    }, [patient])

    // Reset editing states when dialog closes
    useEffect(() => {
        if (!isOpen) {
            setEditingPhone(false)
            setEditingEmail(false)
            setCoverageDialogOpen(false)
            setSuccessMessage("")
            setPhoneError("")
            setEmailError("")
        }
    }, [isOpen])

    if (!patient) return null

    const getAppointmentDateTime = (apt: { date: string; startTime: string }) => {
        return new Date(`${apt.date}T${apt.startTime}`);
    };

    const cleanLocalDateTimeFromJava: (dirtyDateTime: string) => string = (dirtyDateTime) => {
        return dirtyDateTime.substring(0, 5);
    }

    const pastAppointments = patient.pastAppointments
        .sort((a, b) => getAppointmentDateTime(b).getTime() - getAppointmentDateTime(a).getTime());
    const futureAppointments = patient.futureAppointments
        .filter((apt) => apt.state !== "CANCELLED")
        .sort((a, b) => getAppointmentDateTime(a).getTime() - getAppointmentDateTime(b).getTime());

    // Function to check if coverage has changed
    const hasCoverageChanged = () => {
        if (!patient) return false;

        let newCoverage;
        if (noCoverage) {
            newCoverage = "Sin Cobertura";
        } else if (coverageValue) {
            newCoverage = planValue ? `${coverageValue} ${planValue}` : coverageValue;
        } else {
            newCoverage = "Sin Cobertura";
        }

        return newCoverage !== patient.cobertura;
    };

    // Function to save coverage changes
    const saveCoverageChanges = () => {
        if (!patient || !hasCoverageChanged()) return;

        let newCoverage;
        if (noCoverage) {
            newCoverage = "Sin Cobertura";
        } else if (coverageValue) {
            newCoverage = planValue ? `${coverageValue} ${planValue}` : coverageValue;
        } else {
            // If no coverage is selected, default to "Sin Cobertura"
            newCoverage = "Sin Cobertura";
        }

        // TODO: Implement API call to update patient coverage
        setCurrentCoverage(newCoverage);
        showSuccessMessage("Cobertura actualizada correctamente");
    };

    // Function to check if phone has changed
    const hasPhoneChanged = () => {
        if (!patient) return false;
        return phoneValue !== patient.phone;
    };

    // Function to save phone changes
    const savePhoneChanges = () => {
        if (!patient || !hasPhoneChanged()) return;

        // Validate phone
        try {
            const phoneNumber = phoneUtil.parseAndKeepRawInput(phoneValue);
            const isValid = phoneUtil.isValidNumber(phoneNumber);

            if (!isValid) {
                setPhoneError("El número de teléfono no es válido");
                return;
            }
        } catch {
            setPhoneError("El número de teléfono no es válido");
            return;
        }

        // TODO: Implement API call to update patient phone
        setCurrentPhone(phoneValue);
        setEditingPhone(false);
        setPhoneError("");
        showSuccessMessage("Teléfono actualizado correctamente");
    };

    // Function to check if email has changed
    const hasEmailChanged = () => {
        if (!patient) return false;
        return emailValue !== (patient.email || "");
    };

    // Function to save email changes
    const saveEmailChanges = () => {
        if (!patient || !hasEmailChanged()) return;

        // Validate email if provided
        if (emailValue && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
            setEmailError("El correo electrónico no es válido");
            return;
        }

        // TODO: Implement API call to update patient email
        setCurrentEmail(emailValue);
        setEditingEmail(false);
        setEmailError("");
        showSuccessMessage("Email actualizado correctamente");
    };

    // Function to cancel phone editing
    const cancelPhoneEdit = () => {
        setEditingPhone(false);
        setPhoneError("");
        setPhoneValue(patient.phone);
    };

    // Function to cancel email editing
    const cancelEmailEdit = () => {
        setEditingEmail(false);
        setEmailError("");
        setEmailValue(patient.email || "");
    };

    return (
        <>
            {isOpen && <div className="fixed inset-0"/>}
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="sm:max-w-[800px] max-h-[70vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Detalles del Paciente</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-6">
                        {successMessage && (
                            <div className="bg-green-50 p-3 rounded-md flex items-center text-green-600 text-sm">
                                <Check className="h-4 w-4 mr-2"/>
                                {successMessage}
                            </div>
                        )}
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium mb-2">Nombre Completo:</h3>
                                    <p>{patient.name}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">DNI:</h3>
                                    <p>{patient.identificationNumber}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2 flex items-center">
                                        Cobertura:
                                        {!coverageDialogOpen && (
                                            <button
                                                className="ml-2 text-blue-500 hover:text-blue-700"
                                                onClick={() => {
                                                    // Initialize values before opening dialog
                                                    initializeCoverageValues(patient.cobertura);
                                                    setCoverageDialogOpen(true);
                                                }}
                                                title="Editar cobertura"
                                            >
                                                <Pencil className="h-3.5 w-3.5"/>
                                            </button>
                                        )}
                                    </h3>
                                    <p>{currentCoverage || patient.cobertura}</p>
                                </div>

                                {/* Coverage Edit Dialog */}
                                <Dialog open={coverageDialogOpen} onOpenChange={setCoverageDialogOpen}>
                                    <DialogContent className="sm:max-w-[425px]">
                                        <DialogHeader>
                                            <DialogTitle>Editar Cobertura</DialogTitle>
                                        </DialogHeader>
                                        <div className="space-y-4 py-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="coverage">Cobertura</Label>
                                                <Select
                                                    open={isCoverageOpen}
                                                    onOpenChange={setIsCoverageOpen}
                                                    value={coverageValue}
                                                    onValueChange={(value) => {
                                                        setCoverageValue(value);
                                                        setPlanValue("");
                                                    }}
                                                    disabled={noCoverage}
                                                >
                                                    <SelectTrigger id="coverage" className="w-full">
                                                        <SelectValue placeholder="Seleccionar Cobertura"/>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                                                            <SelectItem key={cov.id}
                                                                        value={cov.name}>{cov.name}</SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="plan">Plan</Label>
                                                <Select
                                                    open={isPlanOpen}
                                                    onOpenChange={setIsPlanOpen}
                                                    value={planValue}
                                                    onValueChange={(value) => {
                                                        setPlanValue(value);
                                                    }}
                                                    disabled={!coverageValue || noCoverage}
                                                >
                                                    <SelectTrigger id="plan" className="w-full">
                                                        <SelectValue placeholder="Seleccionar Plan"/>
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {coverageValue &&
                                                            plansByCoverage[coverageValue]?.map((plan) => (
                                                                <SelectItem key={plan} value={plan}>
                                                                    {plan}
                                                                </SelectItem>
                                                            ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="flex items-center space-x-2">
                                                <Switch
                                                    id="no-coverage"
                                                    checked={noCoverage}
                                                    onCheckedChange={(checked) => {
                                                        setNoCoverage(checked);
                                                        if (checked) {
                                                            setCoverageValue("");
                                                            setPlanValue("");
                                                        } else if (patient.cobertura !== "Sin Cobertura") {
                                                            // Restore original coverage when toggling off
                                                            initializeCoverageValues(patient.cobertura);
                                                        }
                                                    }}
                                                />
                                                <Label htmlFor="no-coverage">Sin Cobertura</Label>
                                            </div>
                                        </div>
                                        <DialogFooter>
                                            <Button
                                                variant="outline"
                                                onClick={() => {
                                                    setCoverageDialogOpen(false);
                                                    // Reset to original values
                                                    initializeCoverageValues(patient.cobertura);
                                                }}
                                            >
                                                Cancelar
                                            </Button>
                                            <Button
                                                className="bg-blue-500 hover:bg-blue-600"
                                                onClick={() => {
                                                    saveCoverageChanges();
                                                    setCoverageDialogOpen(false);
                                                }}
                                                disabled={!hasCoverageChanged()}
                                            >
                                                Guardar
                                            </Button>
                                        </DialogFooter>
                                    </DialogContent>
                                </Dialog>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium mb-2 flex items-center">
                                        Email:
                                        {!editingEmail && (
                                            <button
                                                className="ml-2 text-blue-500 hover:text-blue-700"
                                                onClick={() => setEditingEmail(true)}
                                                title="Editar email"
                                            >
                                                <Pencil className="h-3.5 w-3.5"/>
                                            </button>
                                        )}
                                    </h3>
                                    {editingEmail ? (
                                        <div className="space-y-2">
                                            <Input
                                                value={emailValue}
                                                onChange={(e) => {
                                                    setEmailValue(e.target.value);
                                                    setEmailError("");
                                                }}
                                                placeholder="Email (opcional)"
                                            />
                                            {emailError && (
                                                <div className="text-xs text-red-600">
                                                    {emailError}
                                                </div>
                                            )}

                                            <div className="flex space-x-2 mt-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={cancelEmailEdit}
                                                >
                                                    Cancelar
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    className="bg-blue-500 hover:bg-blue-600"
                                                    onClick={saveEmailChanges}
                                                    disabled={!hasEmailChanged()}
                                                >
                                                    Guardar
                                                </Button>
                                            </div>
                                        </div>
                                    ) : (
                                        <p>{currentEmail || patient.email || "-"}</p>
                                    )}
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2 flex items-center">
                                        Teléfono:
                                        {!editingPhone && (
                                            <button
                                                className="ml-2 text-blue-500 hover:text-blue-700"
                                                onClick={() => setEditingPhone(true)}
                                                title="Editar teléfono"
                                            >
                                                <Pencil className="h-3.5 w-3.5"/>
                                            </button>
                                        )}
                                    </h3>
                                    {editingPhone ? (
                                        <div className="space-y-2">
                                            <div className="custom-phone-input">
                                                <PhoneInput
                                                    defaultCountry="ar"
                                                    value={phoneValue}
                                                    onChange={(phone) => {
                                                        setPhoneValue(phone);
                                                        setPhoneError("");

                                                        try {
                                                            // Parse the phone number using Google's libphonenumber
                                                            const phoneNumber = phoneUtil.parseAndKeepRawInput(phone);
                                                            const isValid = phoneUtil.isValidNumber(phoneNumber);

                                                            if (!isValid && phone !== "+" && phone.length > 5) {
                                                                setPhoneError("El número de teléfono no es válido");
                                                            }
                                                        } catch {
                                                            if (phone !== "+" && phone.length > 5) {
                                                                setPhoneError("El número de teléfono no es válido");
                                                            }
                                                        }
                                                    }}
                                                    inputStyle={{
                                                        width: '100%',
                                                        height: '2.5rem'
                                                    }}
                                                    className="w-full custom-phone-input with-dial-code-preview"
                                                    placeholder="Teléfono"
                                                    countrySelectorStyleProps={{
                                                        buttonStyle: {
                                                            paddingLeft: '10px',
                                                            paddingRight: '5px'
                                                        }
                                                    }}
                                                    hideDropdown={false}
                                                    disableDialCodeAndPrefix={true}
                                                    showDisabledDialCodeAndPrefix={true}
                                                    disableFormatting={false}
                                                    preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                                                    countries={getSpanishCountries()}
                                                />
                                                {phoneError && (
                                                    <div className="text-xs text-red-600 mt-1">
                                                        {phoneError}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="flex space-x-2 mt-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={cancelPhoneEdit}
                                                >
                                                    Cancelar
                                                </Button>
                                                <Button
                                                    size="sm"
                                                    className="bg-blue-500 hover:bg-blue-600"
                                                    onClick={savePhoneChanges}
                                                    disabled={!!phoneError || !hasPhoneChanged()}
                                                >
                                                    Guardar
                                                </Button>
                                            </div>
                                        </div>
                                    ) : (
                                        <p>{currentPhone || patient.phone}</p>
                                    )}
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Atenciones en tu establecimiento:</h3>
                                    <p>{patient.attentionsInMedicalCenter} turnos</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Porcentaje de Asistencia:</h3>
                                    <p>{patient.attendancePercentage.toFixed(0)}%</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 className="font-medium mt-2 mb-2">Próximos Turnos:</h3>
                            {futureAppointments.length > 0 ? (
                                <ul className="space-y-2 text-[0.875rem]">
                                    {futureAppointments.map((apt, index) => (
                                        <li key={index}>
                                            <Button
                                                variant="link"
                                                className="p-0 h-auto"
                                                onClick={() => {
                                                    if (onAppointmentSelect)
                                                        onAppointmentSelect(apt)
                                                }}
                                            >
                                                {getAppointmentDateTime(apt).toLocaleDateString("es-ES", {
                                                    day: "numeric",
                                                    month: "numeric",
                                                    year: "numeric"
                                                })} - {cleanLocalDateTimeFromJava(apt.startTime)} - {apt.consultationType}
                                            </Button>
                                        </li>
                                    ))}
                                </ul>
                            ) : (
                                <p className="text-[0.875rem]">No hay turnos futuros programados.</p>
                            )}
                        </div>
                        <div>
                            <h3 className="font-medium mb-2">Historial de Turnos:</h3>
                            {pastAppointments.length > 0 ? (
                                <ul className="space-y-2 text-[0.9rem]">
                                    {pastAppointments.map((apt, index) => (
                                        <li key={index}>
                                            {new Date(apt.date + "T00:00:00").toLocaleDateString()} - {apt.startTime} - {apt.consultationType} - {apt.state}
                                        </li>
                                    ))}
                                </ul>
                            ) : (
                                <p className="text-[0.875rem]">No hay historial de turnos.</p>
                            )}
                        </div>
                        <Button onClick={onClose} className="w-full bg-blue-500 hover:bg-blue-600">
                            Cerrar
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    )
}