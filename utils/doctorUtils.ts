import { ConsultationType, Doctor } from "@/types/doctor"
import type { MedicalCenter } from "@/types/medical-center"
import { CONSULTATION_TYPES } from "@/data/consultationTypes"

export const initialMedicalCenters: MedicalCenter[] = [
  {
    id: "medical-center-1",
    name: "Centro Médico Verdi",
    doctors: ["dr-favaloro", "dra-grierson"],
    workingDays: {
      "1": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "2": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "3": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "4": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "5": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "6": { enabled: false, hours: [] },
      "0": { enabled: false, hours: [] },
    },
  },
]

const getConsultationType = (name: string, overrides: Partial<ConsultationType> = {}): ConsultationType => {
  const baseType = CONSULTATION_TYPES.find(t => t.name === name)
  if (!baseType) {

    return {
      name,
      availableOnline: false,
      onlineBookingHoursByDay: {},
      requiresMedicalOrder: false,
      duration: "default",
      dailyLimit: "unlimited",
      basePrice: 0,
      copays: [],
      excludedCoverages: [],
      acceptsPrivatePay: true,
      ...overrides,
    }
  }
  // If baseType has onlineBookingHoursByDay, ensure enabled is added
  const updatedOnlineBookingHoursByDay = baseType.onlineBookingHoursByDay
    ? Object.fromEntries(
        Object.entries(baseType.onlineBookingHoursByDay).map(([dayId, ranges]) => [
          dayId,
          ranges.map(range => ({ ...range, enabled: true })),
        ])
      )
    : undefined

  return { ...baseType, onlineBookingHoursByDay: updatedOnlineBookingHoursByDay, ...overrides }
}

export const initialDoctors: Doctor[] = [
  {
    id: "dr-favaloro",
    name: "Rene Favaloro",
    specialties: ["Cardiología"],
    initial: "F",
    consultationTypes: [
      getConsultationType("Primera Consulta", {
        onlineBookingHoursByDay: {
          "1": [],
          "4": [],
          "5": [],
        },
        basePrice: 500,
        acceptsPrivatePay: true,
      }),
    ],
    workingDays: {
      "1": { enabled: true, hours: [{ start: "09:00", end: "16:00" }] },
      "2": { enabled: false, hours: [] },
      "3": { enabled: false, hours: [] },
      "4": { enabled: true, hours: [{ start: "09:00", end: "15:30" }] },
      "5": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "6": { enabled: false, hours: [] },
      "0": { enabled: false, hours: [] },
    },
    mn: "38555",
    email: "<EMAIL>",
    onlineBookingAdvanceDays: 60,
    onlineBookingMinHours: 2,
    appointmentSlotDuration: 15,
  },
  {
    id: "dra-grierson",
    name: "Cecilia Grierson",
    specialties: ["Obstetricia"],
    initial: "G",
    consultationTypes: [
      getConsultationType("Primera Consulta", {
        onlineBookingHoursByDay: {
          "1": [],
          "3": [],
          "5": [],
        },
        basePrice: 450,
        acceptsPrivatePay: true,
      }),
    ],
    workingDays: {
      "1": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "2": { enabled: false, hours: [] },
      "3": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "4": { enabled: false, hours: [] },
      "5": { enabled: true, hours: [{ start: "12:00", end: "19:00" }] },
      "6": { enabled: false, hours: [] },
      "0": { enabled: false, hours: [] },
    },
    mn: "20444",
    email: "",
    onlineBookingAdvanceDays: 60,
    onlineBookingMinHours: 2,
    appointmentSlotDuration: 15,
  },
]