"use client"

import {AppointmentState} from "@/types/professional-schedules";
import {AppointmentModificationRequest} from "@/app/api/requestBodies/AppointmentModificationRequest";

export const cancelAppointment = async (appointmentId: number, employeeUserId: number) => {
    try {
        const requestBody: AppointmentModificationRequest = {
            appointmentId,
            employeeUserId,
            newAppointmentStatus: AppointmentState.CANCELLED
        };
        const response = await fetch('/api/appointments/modify-request', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`Failed to cancel appointment: ${errorData.error || 'Unknown error'}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Error cancelling appointment:', error);
        throw error;
    }
}

export class ToggleBlockedSlotRequest {
    constructor(
        public date: string,
        public startTime: string,
        public appointmentSlotDuration: number,
        public reason: string,
        public professionalId: number,
        public medicalCenterId: number,
        public employeeUserId: number | undefined
    ) {
    }
}


export const toggleBlockedSlot = async (request: ToggleBlockedSlotRequest, isCurrentlyBlocked: boolean) => {
    // TODO : implement logic to block slot / unblock slot
}
