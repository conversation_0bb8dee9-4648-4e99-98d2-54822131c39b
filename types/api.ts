// todo por abajo de esto es temp

export interface CreateMedicalCenterRequest {
    mail: string;
    phone: string;
    imageUrl?: string;
    name: string;
    address: string;
    postalCode: number;
    province: string;
    city: string;
    acceptsSelfPaidPatients: boolean;
    floorNumber: number;
    departmentNumber: string;
    latitude: number;
    longitude: number;
}

export interface UpdateMedicalCenterRequest extends Partial<CreateMedicalCenterRequest> {
    id: string;
}


export interface Doctor {
    id: string;
    name: string;
    email: string;
    specialty: string;
    medicalCenterId: string;
    workingDays: {
        [key: string]: {
            enabled: boolean;
            hours: Array<{ start: string; end: string }>;
        };
    };
    appointmentSlotDuration: number;
    consultationTypes: Array<{
        name: string;
        requiresMedicalOrder: boolean;
        duration: string;
        dailyLimit: string;
        basePrice: number;
        availableOnline: boolean;
        acceptsPrivatePay: boolean;
    }>;
}

export interface CreateDoctorRequest {
    name: string;
    email: string;
    specialty: string;
    medicalCenterId: string;
}

export interface UpdateDoctorRequest extends Partial<CreateDoctorRequest> {
    id: string;
}

// Schedule Types
export interface Schedule {
    id: string;
    doctorId: string;
    workingDays: WorkingDay[];
    exceptions: ScheduleException[];
}

export interface WorkingDay {
    dayOfWeek: number; // 0-6 (Sunday-Saturday)
    enabled: boolean;
    hours: Array<{ start: string; end: string }>;
}

export interface ScheduleException {
    date: string; // YYYY-MM-DD format
    enabled: boolean;
    hours: Array<{ start: string; end: string }>;
}

// Appointment Types
export interface Appointment {
    id: string;
    medicalCenterId: string;
    doctorId: string;
    patientId: string;
    date: string; // YYYY-MM-DD format
    time: string; // HH:mm format
    status: AppointmentStatus;
    type: AppointmentType;
    notes?: string;
}

export enum AppointmentStatus {
    SCHEDULED = "scheduled",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    NO_SHOW = "no_show"
}

export enum AppointmentType {
    GENERAL = "general",
    FOLLOW_UP = "follow_up",
    SPECIALIST = "specialist",
    EMERGENCY = "emergency"
}

export interface CreateAppointmentRequest {
    medicalCenterId: string;
    doctorId: string;
    patientId: string;
    date: string;
    time: string;
    type: AppointmentType;
    notes?: string;
}

export interface UpdateAppointmentRequest extends Partial<CreateAppointmentRequest> {
    id: string;
    status?: AppointmentStatus;
}

// Health Insurance Types
export interface HealthInsurance {
    id: string;
    name: string;
    plans: InsurancePlan[];
}

export interface InsurancePlan {
    id: string;
    name: string;
    coverage: Coverage[];
}

export interface Coverage {
    id: string;
    name: string;
    percentage: number;
}

export interface CreateHealthInsuranceRequest {
    name: string;
    plans: Array<{
        name: string;
        coverage: Array<{
            name: string;
            percentage: number;
        }>;
    }>;
}

export interface UpdateHealthInsuranceRequest extends Partial<CreateHealthInsuranceRequest> {
    id: string;
}

// User Types
export interface User {
    id: string;
    name: string;
    email: string;
    password: string;
    role: UserRole;
    medicalCenterId?: string;
    medicalCenterIds?: string[];
    medicalCenterRoles?: Array<{
        medicalCenterId: string;
        role: UserRole;
    }>;
}

export enum UserRole {
    ADMIN = "admin",
    SUPERUSER = "superuser",
    RECEPTIONIST = "receptionist",
    DOCTOR = "doctor"
}

// Medical Center Types
export interface MedicalCenter {
    id: string;
    name: string;
    address: string;
    phone: string;
    email: string;
    doctors: string[]; // Array of doctor IDs
    workingDays: {
        [key: string]: {
            enabled: boolean;
            hours: Array<{ start: string; end: string }>;
        };
    };
    openingHours: {
        [key: string]: {
            start: string;
            end: string;
            enabled: boolean;
        };
    };
    acceptedCoverages: string[];
}