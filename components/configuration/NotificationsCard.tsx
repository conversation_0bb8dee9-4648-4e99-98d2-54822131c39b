"use client"

import { use<PERSON>ontext, useState, useMemo } from "react"
import { <PERSON><PERSON>ontex<PERSON> } from "@/contexts/DoctorContext"
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Bell, Package, RefreshCw, AlertCircle, Mail, CreditCard, Check, CalendarClock } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface ReminderPack {
  quantity: number
  price: number
  remaining: number
  autoRenew: boolean
}

export default function NotificationsCard() {
  const { doctors } = useContext(DoctorContext)
  const [isDailySummaryEnabled, setIsDailySummaryEnabled] = useState(false)
  const [selectedDoctors, setSelectedDoctors] = useState<string[]>([])
  const [reminderPack, setReminderPack] = useState<ReminderPack>({
    quantity: 0,
    price: 0,
    remaining: 0,
    autoRenew: true
  })
  const [isPurchaseDialogOpen, setIsPurchaseDialogOpen] = useState(false)
  const [selectedPackIndex, setSelectedPackIndex] = useState(2) // Default to 100 pack (index 2)
  const [isProcessing, setIsProcessing] = useState(false)
  const [purchaseComplete, setPurchaseComplete] = useState(false)

  // Move pricingConfig into its own useMemo to fix dependency issues
  const pricingConfig = useMemo(() => ({
    packs: [
      { quantity: 20, price: 5500 },
      { quantity: 50, price: 11000 },
      { quantity: 100, price: 19000 },
      { quantity: 200, price: 32000 },
      { quantity: 500, price: 50000 },
      { quantity: 1000, price: 85000 },
      { quantity: 2000, price: 140000 },
    ],
    newPatientFee: 500,
    individualReminderPrice: 300,
  }), []);

  // Calculate pricing details dynamically
  const calculatedPricing = useMemo(() => {
    const basePrice = pricingConfig.individualReminderPrice;
    
    return pricingConfig.packs.map(pack => {
      const unitPrice = Math.round(pack.price / pack.quantity);
      const discountPercent = Math.round((1 - unitPrice / basePrice) * 100);
      return {
        ...pack,
        unitPrice,
        discountPercent,
        displayPrice: pack.price.toLocaleString(),
      };
    });
  }, [pricingConfig]);

  const handleAutoRenewToggle = (checked: boolean) => {
    setReminderPack(prev => ({ ...prev, autoRenew: checked }))
  }

  const handleDoctorToggle = (doctorId: string) => {
    if (!isDailySummaryEnabled) return

    setSelectedDoctors(prev => 
      prev.includes(doctorId)
        ? prev.filter(id => id !== doctorId)
        : [...prev, doctorId]
    )
  }

  const handleDailySummaryToggle = (checked: boolean) => {
    setIsDailySummaryEnabled(checked)
    if (!checked) {
      setSelectedDoctors([])
    }
  }

  const selectAllDoctors = () => {
    if (!isDailySummaryEnabled) return
    setSelectedDoctors(doctors.map(doctor => doctor.id))
  }

  const clearAllDoctors = () => {
    if (!isDailySummaryEnabled) return
    setSelectedDoctors([])
  }

  const handleCheckboxClick = (e: React.MouseEvent, doctorId: string) => {
    e.stopPropagation()
    
    if (!isDailySummaryEnabled) return
    
    handleDoctorToggle(doctorId)
  }

  const openPurchaseDialog = () => {
    setIsPurchaseDialogOpen(true)
    setPurchaseComplete(false)
  }

  const processPurchase = () => {
    setIsProcessing(true)
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false)
      setPurchaseComplete(true)
      
      // Update the pack after successful purchase (CUMULATIVE)
      setTimeout(() => {
        const newPack = calculatedPricing[selectedPackIndex]
        setReminderPack(prevPack => ({
          quantity: prevPack.quantity + newPack.quantity,
          price: prevPack.price + newPack.price,
          remaining: prevPack.remaining + newPack.quantity,
          autoRenew: prevPack.autoRenew
        }))
        setIsPurchaseDialogOpen(false)
      }, 1500)
    }, 2000)
  }

  return (
    <div className="space-y-6">
      {/* Doctor-based Notification Settings */}
      <Card className="bg-white shadow-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5 text-blue-600" />
            <div>
              <CardTitle className="text-[1.3rem] font-semibold text-gray-900">
                Resumen diario de turnos
              </CardTitle>
              <CardDescription className="text-gray-600">
                Envíe notificaciones automáticas con el resumen de turnos del día.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-100">
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <Label className="text-base font-medium text-gray-900">Enviar Resumen por Email</Label>
                  <p className="text-sm text-gray-600 mt-1">
                    Cada profesional recibirá un email a las 20:00hs con todos sus turnos agendados para el día siguiente.
                  </p>
                </div>
              </div>
              <Switch 
                checked={isDailySummaryEnabled}
                onCheckedChange={handleDailySummaryToggle}
              />
            </div>

            <div className={`space-y-3 transition-opacity duration-200 ${isDailySummaryEnabled ? 'opacity-100' : 'opacity-50'}`}>
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Seleccionar profesionales</Label>
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={selectAllDoctors}
                    disabled={!isDailySummaryEnabled}
                    className="text-xs h-7"
                  >
                    Seleccionar todos
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={clearAllDoctors}
                    disabled={!isDailySummaryEnabled || selectedDoctors.length === 0}
                    className="text-xs h-7"
                  >
                    Limpiar
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-60 overflow-y-auto p-2 bg-gray-50 rounded-lg">
                {doctors.map((doctor) => (
                  <div 
                    key={doctor.id} 
                    className={`flex items-center gap-3 p-2 rounded hover:bg-gray-100 ${!isDailySummaryEnabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                    onClick={() => handleDoctorToggle(doctor.id)}
                  >
                    <Checkbox
                      id={doctor.id}
                      checked={selectedDoctors.includes(doctor.id)}
                      disabled={!isDailySummaryEnabled}
                      className={!isDailySummaryEnabled ? 'opacity-50' : ''}
                      onClick={(e) => handleCheckboxClick(e, doctor.id)}
                    />
                    <Label 
                      htmlFor={doctor.id} 
                      className={`font-normal ${!isDailySummaryEnabled ? 'text-gray-400' : 'text-gray-700'}`}
                    >
                      {doctor.name}
                    </Label>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-between items-center text-sm text-gray-600 pt-1">
                <span>{selectedDoctors.length} profesionales seleccionados</span>
                {isDailySummaryEnabled && selectedDoctors.length === 0 && (
                  <span className="text-amber-600">Seleccione al menos un profesional</span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reminder Pack Management */}
      <Card className="bg-white shadow-md">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Package className="h-5 w-5 text-green-600" />
            <div>
              <CardTitle className="text-[1.3rem] font-semibold text-gray-900">
                Pack de Recordatorios
              </CardTitle>
              <CardDescription className="text-gray-600">
                Gestione su pack de recordatorios para pacientes y configure la renovación automática. Los recordatorios no tienen vencimiento.
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-base font-medium">Recordatorios Restantes</Label>
                <RefreshCw className="h-4 w-4 text-gray-500" />
              </div>
              <div className="text-2xl font-bold text-blue-600">{reminderPack.remaining}</div>
              <div className="text-sm text-gray-600">de {reminderPack.quantity} totales</div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-base font-medium">Renovación Automática</Label>
                <Switch
                  checked={reminderPack.autoRenew}
                  onCheckedChange={handleAutoRenewToggle}
                />
              </div>
              <div className="text-sm text-gray-600">
                {reminderPack.autoRenew 
                  ? "Se renovará automáticamente cuando se agoten los recordatorios."
                  : "No se renovará automáticamente."}
              </div>
            </div>
          </div>

          <div className="flex items-start gap-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <CalendarClock className="h-5 w-5 text-blue-600 mt-1 shrink-0" />
            <div>
              <p className="text-sm text-gray-800">
                <span className="font-medium">Recordatorios automáticos por reservas online:</span> Cuando un paciente reserva un turno online, se envía automáticamente un recordatorio un día antes de su turno.
              </p>
              <p className="text-sm text-gray-600 mt-1">
                Cada recordatorio cuesta ${pricingConfig.individualReminderPrice} o se descuenta de tu pack activo. Sólo pagás cuando el recordatorio es enviado.
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <Button 
              className="w-full bg-blue-600 hover:bg-blue-700"
              onClick={openPurchaseDialog}
            >
              Comprar Nuevo Pack
            </Button>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Información Importante</AlertTitle>
              <AlertDescription>
                Los recordatorios se envían por WhatsApp, SMS y Email. El envío por los tres medios cuenta como un solo recordatorio.
                Los recordatorios se descuentan cuando se envían, no cuando se agendan los turnos.
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>

      {/* Purchase Dialog */}
      <Dialog open={isPurchaseDialogOpen} onOpenChange={setIsPurchaseDialogOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Comprar Pack de Recordatorios</DialogTitle>
            <DialogDescription>
              Seleccione el pack que desea adquirir.
            </DialogDescription>
          </DialogHeader>

          {!purchaseComplete ? (
            <>
              <div className="space-y-4">
                {/* Highlight Individual Price */}
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-blue-600" />
                    <span className="text-lg font-semibold text-blue-800">
                      Precio individual: ${pricingConfig.individualReminderPrice}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Ahorre hasta un 77% comprando packs de recordatorios para pacientes.
                  </p>
                </div>

                {/* Pack Options */}
                <RadioGroup
                  value={selectedPackIndex.toString()}
                  onValueChange={(value) => setSelectedPackIndex(parseInt(value))}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {calculatedPricing.map((pack, index) => (
                      <div
                        key={index}
                        className={`relative flex flex-col border rounded-lg p-4 ${
                          selectedPackIndex === index
                            ? 'border-blue-600 bg-blue-50/50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <RadioGroupItem
                          value={index.toString()}
                          id={`pack-${index}`}
                          className="sr-only"
                        />
                        <Label
                          htmlFor={`pack-${index}`}
                          className="cursor-pointer space-y-2"
                        >
                          <div className="flex items-center justify-between">
                            <span className="text-xl font-bold text-gray-900">
                              {pack.quantity}
                            </span>
                            <span className="text-lg font-semibold text-gray-900">
                              ${pack.displayPrice}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              ${pack.unitPrice}/recordatorio
                            </span>
                            {pack.discountPercent > 0 && (
                              <span className="text-sm font-medium text-green-600">
                                Ahorro {pack.discountPercent}%
                              </span>
                            )}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>

                {/* Payment Information */}
                <div className="bg-amber-50 border border-amber-100 rounded-lg p-4 text-sm flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-amber-500 shrink-0 mt-0.5" />
                  <div>
                    <p className="text-gray-800 font-medium">Información sobre el pago</p>
                    <p className="text-gray-600 mt-1">
                      Pago seguro a través de MercadoPago. Su pack estará disponible inmediatamente.
                    </p>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsPurchaseDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button
                  onClick={processPurchase}
                  className="bg-blue-600 hover:bg-blue-700"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Procesando...
                    </>
                  ) : (
                    <>
                      <CreditCard className="h-4 w-4 mr-2" />
                      Pagar con MercadoPago
                    </>
                  )}
                </Button>
              </DialogFooter>
            </>
          ) : (
            <div className="py-8 text-center">
              <div className="flex justify-center mb-4">
                <div className="rounded-full bg-green-100 p-3">
                  <Check className="h-8 w-8 text-green-600" />
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                ¡Compra Exitosa!
              </h3>
              <p className="text-gray-600 mb-6">
                Su pack de {calculatedPricing[selectedPackIndex].quantity} recordatorios ha sido agregado a su cuenta.
              </p>
              <Button 
                onClick={() => setIsPurchaseDialogOpen(false)}
                className="bg-green-600 hover:bg-green-700"
              >
                Volver a la Configuración
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 