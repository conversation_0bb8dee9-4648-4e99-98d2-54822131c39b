"use client"

import { useContext, useState, useEffect, useRef } from "react"
import { Doctor<PERSON>ontext } from "@/contexts/DoctorContext"
import { ConsultationContext } from "@/contexts/ConsultationContext"
import { CoverageContext } from "@/contexts/CoverageContext"
import { useConsultationTypeActions } from "@/hooks/useConsultationTypeActions"
import { ConsultationType, DoctorConfiguration } from "@/types/doctor"
import { CONSULTATION_TYPES } from "@/data/consultationTypes"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from "@/components/ui/collapsible"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON> } from "@/components/ui/dialog"
import { X, Trash, ChevronDown, Search, Pencil, Check, DollarSign } from "lucide-react"
import { Dispatch, SetStateAction } from "react"
import { DAYS } from "@/utils/constants"
import { ConsultationTypeCollapsible } from "./ConsultationTypeCollapsible"

export default function DoctorConsultationTypesTab() {
  const { editedConfig, setEditedConfig } = useContext(DoctorContext)
  const { removeConsultationType } = useContext(ConsultationContext)
  const { medicalCoverages } = useContext(CoverageContext)
  const { updateConsultationType, addCopay } = useConsultationTypeActions(
    editedConfig,
    setEditedConfig as Dispatch<SetStateAction<DoctorConfiguration | null>>
  )

  const [copayDialogOpen, setCopayDialogOpen] = useState(false)
  const [excludeDialogOpen, setExcludeDialogOpen] = useState(false)
  const [selectedCoverageId, setSelectedCoverageId] = useState<string | null>(null)
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null)
  const [copayAmount, setCopayAmount] = useState<number>(0)
  const [currentTypeName, setCurrentTypeName] = useState<string>("")

  // Price adjustment state
  const [priceAdjustmentDialogOpen, setPriceAdjustmentDialogOpen] = useState(false)
  const [priceAdjustmentPercentage, setPriceAdjustmentPercentage] = useState<number>(0)

  const [typeSearchTerm, setTypeSearchTerm] = useState("")
  const [showTypeDropdown, setShowTypeDropdown] = useState(false)
  const [pendingTypes, setPendingTypes] = useState<string[]>([])
  const typeSearchRef = useRef<HTMLDivElement>(null)
  const typeInputRef = useRef<HTMLInputElement>(null)

  // State for time range editing
  const [editingDay, setEditingDay] = useState<{typeName: string, dayId: string} | null>(null)
  const [originalRanges, setOriginalRanges] = useState<{[dayId: string]: {start: string, end: string, enabled: boolean}[]}>({})

  // Track open sections for each consultation type
  const [openSections, setOpenSections] = useState<{
    [typeName: string]: {
      onlineBooking: boolean;
      appointmentSettings: boolean;
      pricingAndCoverage: boolean;
    }
  }>({})

  // Track whether we've synced onlineBookingHoursByDay with workingDays
  const hasSyncedRef = useRef(false)

  // --- Updated synchronization effect ---
  // This effect syncs the consultation types' online booking ranges (CT) with the doctor's working schedule (WS)
  // For each day, it iterates through the WS ranges and:
  //   • Preserves an existing CT range if it's fully contained within the WS range,
  //   • Otherwise, it uses the WS range as default.
  // This ensures:
  //   1. New WS ranges get added to CT,
  //   2. Modified WS ranges update out-of-bound CT ranges,
  //   3. CT ranges that are still valid (modified but within WS) are kept,
  //   4. Deleted WS ranges remove the corresponding CT ranges.
  useEffect(() => {
    if (!editedConfig) return;

    const updatedConsultationTypes: ConsultationType[] = editedConfig.consultationTypes.map(type => {
      if (!type.availableOnline) {
        return { ...type, onlineBookingHoursByDay: type.onlineBookingHoursByDay || {} };
      }

      const updatedHours: { [dayId: string]: { start: string; end: string; enabled: boolean }[] } = {};

      DAYS.forEach(day => {
        const weeklyHours = editedConfig.workingDays[day.id]?.hours || [];
        const currentOnlineHours = type.onlineBookingHoursByDay?.[day.id] || [];

        if (weeklyHours.length === 0) {
          // If no working schedule, remove any CT ranges for that day.
          updatedHours[day.id] = [];
        } else {
          const newRanges = weeklyHours.map(wsRange => {
            // Find a CT range that is fully contained within this WS range.
            const matchingCTRange = currentOnlineHours.find(range => {
              const wsStart = new Date(`2000-01-01T${wsRange.start}:00`).getTime();
              const wsEnd = new Date(`2000-01-01T${wsRange.end}:00`).getTime();
              const ctStart = new Date(`2000-01-01T${range.start}:00`).getTime();
              const ctEnd = new Date(`2000-01-01T${range.end}:00`).getTime();
              return ctStart >= wsStart && ctEnd <= wsEnd;
            });
            return matchingCTRange ? matchingCTRange : { start: wsRange.start, end: wsRange.end, enabled: true };
          });
          updatedHours[day.id] = newRanges;
        }
      });

      return { ...type, onlineBookingHoursByDay: updatedHours };
    });

    if (JSON.stringify(updatedConsultationTypes) !== JSON.stringify(editedConfig.consultationTypes)) {
      setEditedConfig(prev => prev ? { ...prev, consultationTypes: updatedConsultationTypes } : null);
    }
}, [editedConfig, setEditedConfig]);
  // --- End updated effect ---

  // Reset sync flag when editedConfig changes (e.g., new doctor selected)
  useEffect(() => {
    hasSyncedRef.current = false;
  }, [editedConfig]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (typeSearchRef.current && !typeSearchRef.current.contains(e.target as Node)) {
        setShowTypeDropdown(false);
        setTypeSearchTerm("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const openCopayDialog = (typeName: string, coverageId: string) => {
    setCurrentTypeName(typeName);
    setSelectedCoverageId(coverageId);
    setSelectedPlanId(null);
    setCopayAmount(0);
    setCopayDialogOpen(true);
  };

  const openExcludeDialog = (typeName: string, coverageId: string) => {
    setCurrentTypeName(typeName);
    setSelectedCoverageId(coverageId);
    setSelectedPlanId(null);
    setExcludeDialogOpen(true);
  };

  const handleAddCopay = () => {
    if (selectedCoverageId) {
      addCopay(currentTypeName, selectedCoverageId, copayAmount, selectedPlanId);
      setCopayDialogOpen(false);
    }
  };

  const handleAddExclusion = () => {
    if (selectedCoverageId) {
      updateConsultationType(currentTypeName, "excludedCoverages", [
        ...(editedConfig?.consultationTypes.find((t) => t.name === currentTypeName)?.excludedCoverages || []),
        { coverageId: selectedCoverageId, planId: selectedPlanId },
      ]);
      setExcludeDialogOpen(false);
    }
  };

  // Handle price adjustment for all consultation types that accept private patients
  const handlePriceAdjustment = () => {
    if (!editedConfig || priceAdjustmentPercentage === 0) return;

    const updatedConsultationTypes = editedConfig.consultationTypes.map(type => {
      // Only adjust prices for types that accept private patients
      if (type.acceptsPrivatePay) {
        // Calculate new price based on percentage
        const adjustmentFactor = 1 + (priceAdjustmentPercentage / 100);
        const newPrice = Math.round(type.basePrice * adjustmentFactor);

        return {
          ...type,
          basePrice: newPrice
        };
      }
      return type;
    });

    setEditedConfig({
      ...editedConfig,
      consultationTypes: updatedConsultationTypes
    });

    setPriceAdjustmentDialogOpen(false);
    setPriceAdjustmentPercentage(0);
  };

  const getEffectiveDuration = (duration: string) => {
    if (!editedConfig) return "N/A";
    const baseSlot = editedConfig.appointmentSlotDuration;
    const multiplier = duration === "default" ? 1 : parseInt(duration.replace("slots", ""));
    return `${baseSlot * multiplier} minutos`;
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        const hourStr = hour.toString().padStart(2, "0");
        const minuteStr = minute.toString().padStart(2, "0");
        options.push(`${hourStr}:${minuteStr}`);
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();

  const getOnlineTimeOptionsForDay = (dayId: string) => {
    if (!editedConfig || !editedConfig.workingDays || !editedConfig.workingDays[dayId]?.enabled) {
      return [];
    }
    const doctorHours = editedConfig.workingDays[dayId].hours || [];
    if (!doctorHours.length) return [];

    return timeOptions.filter(time => {
      const timeDate = new Date(`2000-01-01T${time}:00`);
      return doctorHours.some(hour => {
        const start = new Date(`2000-01-01T${hour.start}:00`);
        const end = new Date(`2000-01-01T${hour.end}:00`);
        return timeDate >= start && timeDate <= end;
      });
    });
  };

  const getNextOnlineTime = (currentTime: string, dayId: string, slotDuration: number): string => {
    const dayOptions = getOnlineTimeOptionsForDay(dayId);
    const currentIndex = dayOptions.indexOf(currentTime);
    if (currentIndex === -1) return dayOptions[0] || "09:00";
    const slotsNeeded = Math.ceil(slotDuration / 5);
    return dayOptions[Math.min(currentIndex + slotsNeeded, dayOptions.length - 1)] || currentTime;
  };

  const getPreviousOnlineTime = (currentTime: string, dayId: string, slotDuration: number): string => {
    const dayOptions = getOnlineTimeOptionsForDay(dayId);
    const currentIndex = dayOptions.indexOf(currentTime);
    if (currentIndex === -1) return dayOptions[0] || "09:00";
    const slotsNeeded = Math.ceil(slotDuration / 5);
    return dayOptions[Math.max(currentIndex - slotsNeeded, 0)] || currentTime;
  };

  const handleOnlineHoursUpdate = (
    typeName: string,
    dayId: string,
    rangeIndex: number,
    field: "start" | "end",
    value: string
  ) => {
    const currentType = editedConfig?.consultationTypes.find(t => t.name === typeName);
    if (!currentType || !editedConfig) return;

    const currentHours = currentType.onlineBookingHoursByDay || {};
    const dayRanges = currentHours[dayId] || [];
    const currentRange = dayRanges[rangeIndex] || { start: "09:00", end: "10:00", enabled: true };
    const slotDuration = editedConfig.appointmentSlotDuration;

    let newStart = currentRange.start;
    let newEnd = currentRange.end;

    if (field === "start") {
      newStart = value;
      const duration = (new Date(`2000-01-01T${newEnd}:00`).getTime() - new Date(`2000-01-01T${newStart}:00`).getTime()) / (1000 * 60);
      if (duration < slotDuration) {
        newEnd = getNextOnlineTime(newStart, dayId, slotDuration);
      }
    } else {
      newEnd = value;
      const duration = (new Date(`2000-01-01T${newEnd}:00`).getTime() - new Date(`2000-01-01T${newStart}:00`).getTime()) / (1000 * 60);
      if (duration < slotDuration) {
        newStart = getPreviousOnlineTime(newEnd, dayId, slotDuration);
      }
    }

    const updatedRanges = [...dayRanges];
    updatedRanges[rangeIndex] = { start: newStart, end: newEnd, enabled: currentRange.enabled };

    setEditedConfig(prev => prev ? {
      ...prev,
      consultationTypes: prev.consultationTypes.map(t =>
        t.name === typeName
          ? { ...t, onlineBookingHoursByDay: { ...currentHours, [dayId]: updatedRanges } }
          : t
      )
    } : null);
  };

  const toggleRangeOnlineBooking = (typeName: string, dayId: string, rangeIndex: number, enabled: boolean) => {
    const currentHours = editedConfig?.consultationTypes.find(t => t.name === typeName)?.onlineBookingHoursByDay || {};
    const dayRanges = currentHours[dayId] || [];

    const updatedRanges = [...dayRanges];
    if (updatedRanges[rangeIndex]) {
      updatedRanges[rangeIndex] = { ...updatedRanges[rangeIndex], enabled };
    }

    setEditedConfig(prev => ({
      ...prev!,
      consultationTypes: prev!.consultationTypes.map(t =>
        t.name === typeName
          ? {
              ...t,
              onlineBookingHoursByDay: {
                ...currentHours,
                [dayId]: updatedRanges
              }
            }
          : t
      )
    }));
  };

  // Rest of the component remains unchanged...
  const availableTypes = CONSULTATION_TYPES.filter(
    type => !editedConfig?.consultationTypes.some(t => t.name === type.name)
  );

  const filteredTypes = availableTypes.filter(type =>
    type.name.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "")
      .includes(typeSearchTerm.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, ""))
  );

  const handleTypeSelect = (typeName: string) => {
    setPendingTypes(prev =>
      prev.includes(typeName) ? prev.filter(t => t !== typeName) : [...prev, typeName]
    );
  };

  const handleConfirmTypes = () => {
    if (pendingTypes.length > 0 && editedConfig) {
      const newTypes = pendingTypes.map(name => {
        const type = CONSULTATION_TYPES.find(t => t.name === name);
        return (
          type || {
            name,
            availableOnline: false,
            onlineBookingHoursByDay: {},
            requiresMedicalOrder: false,
            hasInstructions: false,
            instructions: "",
            duration: "default" as const,
            dailyLimit: "unlimited" as const,
            basePrice: 0,
            copays: [],
            excludedCoverages: [],
            acceptsPrivatePay: true,
          }
        );
      });
      setEditedConfig({
        ...editedConfig,
        consultationTypes: [...editedConfig.consultationTypes, ...newTypes],
      });
      setPendingTypes([]);
      setShowTypeDropdown(false);
      setTypeSearchTerm("");
    }
  };

  // Toggle section open/closed state
  const toggleSection = (typeName: string, section: 'onlineBooking' | 'appointmentSettings' | 'pricingAndCoverage') => {
    setOpenSections(prev => {
      const typeSettings = prev[typeName] || {
        onlineBooking: false,
        appointmentSettings: false,
        pricingAndCoverage: false
      };

      return {
        ...prev,
        [typeName]: {
          ...typeSettings,
          [section]: !typeSettings[section]
        }
      };
    });
  };

  // Generate summary for online booking section
  const getOnlineBookingSummary = (type: ConsultationType) => {
    if (!type.availableOnline) return "No disponible online";

    // Count enabled days
    const enabledDays = DAYS.filter(day => {
      const dayRanges = type.onlineBookingHoursByDay?.[day.id] || [];
      return dayRanges.some(range => range.enabled);
    }).length;

    let summary = `Disponible online: ${enabledDays === 1 ? '1 día' : `${enabledDays} días`}`;
    
    // Add info about extraordinary dates
    if (type.availableOnExtraordinaryDates !== false) {
      summary += ", Disponible en fechas extraordinarias";
    }
    
    return summary;
  };

  // Generate summary for appointment settings section
  const getAppointmentSettingsSummary = (type: ConsultationType) => {
    const duration = getEffectiveDuration(type.duration);
    const requiresOrder = type.requiresMedicalOrder ? "Requiere orden médica" : "No requiere orden médica";
    const limit = type.dailyLimit === "unlimited" ? "Sin límite" : `Límite: ${type.dailyLimit}`;
    const hasInstructions = type.hasInstructions && type.instructions ? "Con instrucciones" : "Sin instrucciones";

    return `${duration}, ${requiresOrder}, ${hasInstructions}, ${limit}`;
  };

  // Generate summary for pricing and coverage section
  const getPricingAndCoverageSummary = (type: ConsultationType) => {
    const formattedPrice = type.basePrice.toLocaleString('es-AR', { minimumFractionDigits: 0 }).replace(/,/g, '.');
    const acceptsPrivate = type.acceptsPrivatePay ? `Particular: $${formattedPrice}` : "No acepta particulares";
    const coverageCount = (type.copays || []).length;
    const excludedCount = (type.excludedCoverages || []).length;

    let coverageInfo = "";
    if (coverageCount > 0) {
      coverageInfo = `, Copago en ${coverageCount === 1 ? '1 cobertura' : `${coverageCount} coberturas`}`;
    }

    if (excludedCount > 0) {
      coverageInfo += `, ${excludedCount === 1 ? '1 cobertura excluída' : `${excludedCount} coberturas excluídas`}`;
    }

    return `${acceptsPrivate}${coverageInfo}`;
  };

  if (!editedConfig) return null;

  return (
    <Card className="border-0 shadow-none min-h-[600px] flex flex-col">
      {/* Add global styles to hide number input arrows */}
      <style jsx global>{`
        /* Hide arrows for Chrome, Safari, Edge, Opera */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
        /* Hide arrows for Firefox */
        input[type=number] {
          -moz-appearance: textfield;
        }
      `}</style>
      <CardHeader className="p-0 pb-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg text-gray-900">Tipos de Atención</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={() => setPriceAdjustmentDialogOpen(true)}
          >
            <DollarSign className="h-4 w-4" />
            Actualizar precios
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0 space-y-4 flex-grow">
        <div className="space-y-2" ref={typeSearchRef}>
          <div className="relative">
            <Input
              placeholder="Buscar tipos de atención"
              value={typeSearchTerm}
              onChange={(e) => {
                setTypeSearchTerm(e.target.value);
                setShowTypeDropdown(true);
              }}
              onFocus={() => setShowTypeDropdown(true)}
              ref={typeInputRef}
            />
            <Search
              className="absolute right-3 top-1/2 transform -translate-y-1/2 opacity-50 w-4 h-4 cursor-pointer"
              onClick={() => {
                typeInputRef.current?.focus();
                setShowTypeDropdown(true);
              }}
            />
            {showTypeDropdown && (
              <div className="absolute z-50 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg">
                <div className="max-h-96 overflow-auto"> {/* Increased height from max-h-60 to max-h-96 */}
                  {filteredTypes.map(type => (
                    <div
                      key={type.name}
                      className={`p-2 text-sm cursor-pointer transition-colors ${
                        pendingTypes.includes(type.name)
                          ? "bg-blue-500 text-white hover:bg-blue-600"
                          : "hover:bg-gray-100"
                      }`}
                      onClick={() => handleTypeSelect(type.name)}
                    >
                      {type.name}
                    </div>
                  ))}
                  {filteredTypes.length === 0 && (
                    <div className="p-2 text-sm text-gray-500">No se encontraron tipos</div>
                  )}
                </div>
                <div className="border-t p-2 flex justify-end sticky bottom-0 bg-white">
                  <Button
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={handleConfirmTypes}
                    disabled={pendingTypes.length === 0}
                  >
                    Confirmar
                  </Button>
                </div>
              </div>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            {pendingTypes.map(type => (
              <Badge key={type} className="flex items-center gap-1 bg-blue-100 text-blue-800">
                {type}
                <button
                  onClick={() => setPendingTypes(prev => prev.filter(t => t !== type))}
                  className="ml-1 rounded-full hover:bg-blue-200 p-1"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          {editedConfig.consultationTypes.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No hay tipos de atención configurados<br />
              <span className="text-sm">Agregue tipos usando el buscador</span>
            </div>
          ) : (
            [...editedConfig.consultationTypes]
              .sort((a, b) => a.name.localeCompare(b.name))
              .map((type) => (
                <div key={type.name} className="relative border rounded-lg">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => removeConsultationType(type.name)}
                    className="absolute right-2 top-2 z-10 h-8 w-8 border-red-200 hover:bg-red-50"
                  >
                    <Trash className="h-3.5 w-3.5 text-red-500" />
                  </Button>
                  <Collapsible className="w-full">
                    <CollapsibleTrigger className="w-full p-3 flex items-center bg-blue-50 text-blue-800 hover:bg-blue-100 group">
                      <ChevronDown className="h-4 w-4 mr-2 transition-transform flex-shrink-0 group-data-[state=open]:rotate-180" />
                      <span className="font-medium">{type.name}</span>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="p-4 space-y-4 bg-white">

                  {/* Online Booking Settings */}
                  <ConsultationTypeCollapsible
                    title="Reservas online"
                    isOpen={openSections[type.name]?.onlineBooking || false}
                    onOpenChange={() => toggleSection(type.name, 'onlineBooking')}
                    summary={getOnlineBookingSummary(type)}
                    isComplete={type.availableOnline}
                  >
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={type.availableOnline}
                          onCheckedChange={(checked) => updateConsultationType(type.name, "availableOnline", checked)}
                        />
                        <Label>Disponible online</Label>
                      </div>

                      {type.availableOnline && (
                        <div className="mt-2">
                          {/* Add toggle for extraordinary dates */}
                          <div className="flex items-center gap-2 mb-4">
                            <Switch
                              checked={type.availableOnExtraordinaryDates !== false} 
                              onCheckedChange={(checked) => updateConsultationType(type.name, "availableOnExtraordinaryDates", checked)}
                            />
                            <Label>Disponible en fechas extraordinarias</Label>
                          </div>

                          {/* Day selector with compact grid layout */}
                          <div className="grid grid-cols-7 gap-1 mb-3 text-xs">
                            {DAYS.map(day => {
                              const hasWorkingHours = editedConfig.workingDays[day.id]?.hours?.length > 0;
                              const dayRanges = type.onlineBookingHoursByDay?.[day.id] || [];
                              const isEnabled = hasWorkingHours && dayRanges.some(range => range.enabled);

                              return (
                                <div
                                  key={day.id}
                                  className={`text-center py-1 px-1 rounded cursor-pointer border ${
                                    !hasWorkingHours ? 'opacity-40 bg-gray-100 border-gray-200 text-gray-500' :
                                    isEnabled ? 'bg-blue-100 border-blue-300 text-blue-800 font-medium' :
                                    'bg-gray-50 border-gray-200 hover:bg-gray-100'
                                  }`}
                                  onClick={() => {
                                    if (hasWorkingHours && dayRanges.length > 0) {
                                      // Toggle all ranges for this day
                                      const allEnabled = dayRanges.every(range => range.enabled);
                                      dayRanges.forEach((_, index) => {
                                        toggleRangeOnlineBooking(type.name, day.id, index, !allEnabled);
                                      });
                                    }
                                  }}
                                >
                                  {day.name.substring(0, 3)}
                                </div>
                              );
                            })}
                          </div>

                          {/* Time ranges in a more compact format */}
                          <div className="space-y-2 border rounded-md p-2 bg-gray-50">
                            <div className="text-xs text-gray-500 mb-1">Horarios disponibles para reserva online:</div>

                            {DAYS.filter(day => {
                              const hasWorkingHours = editedConfig.workingDays[day.id]?.hours?.length > 0;
                              return hasWorkingHours;
                            }).map(day => {
                              const dayRanges = type.onlineBookingHoursByDay?.[day.id] || [];
                              const enabledRanges = dayRanges.filter(range => range.enabled);
                              const isEditing = editingDay?.typeName === type.name && editingDay?.dayId === day.id;

                              return (
                                <div key={day.id} className="flex items-center text-sm mb-2">
                                  <span className="font-medium w-12">{day.name.substring(0, 3)}:</span>

                                  {!isEditing ? (
                                    // View mode
                                    <div className="flex flex-wrap gap-1 items-center">
                                      {enabledRanges.length > 0 ? (
                                        <>
                                          {enabledRanges.map((range, index) => (
                                            <div key={index} className="bg-white border border-blue-200 rounded px-1 py-0.5 text-xs flex items-center gap-1">
                                              <span>{range.start}</span>
                                              <span className="text-gray-400">-</span>
                                              <span>{range.end}</span>
                                              <button
                                                className="ml-1 text-gray-400 hover:text-red-500"
                                                onClick={() => toggleRangeOnlineBooking(type.name, day.id, dayRanges.indexOf(range), false)}
                                              >
                                                <X className="h-3 w-3" />
                                              </button>
                                            </div>
                                          ))}

                                          <button
                                            className="ml-2 text-gray-400 hover:text-blue-500 p-1 rounded-full hover:bg-gray-100"
                                            onClick={() => {
                                              // Store original ranges for potential reversion
                                              setOriginalRanges(prev => ({
                                                ...prev,
                                                [day.id]: JSON.parse(JSON.stringify(dayRanges))
                                              }));
                                              setEditingDay({ typeName: type.name, dayId: day.id });
                                            }}
                                            title="Editar horarios"
                                          >
                                            <Pencil className="h-3 w-3" />
                                          </button>
                                        </>
                                      ) : (
                                        <span className="text-xs text-gray-500">No disponible</span>
                                      )}
                                    </div>
                                  ) : (
                                    // Edit mode
                                    <div className="flex items-center">
                                      {dayRanges.map((range, index) => (
                                        <div key={index} className="inline-flex items-center gap-2 bg-gray-50 p-1 rounded border">
                                          <div className="flex items-center gap-1">
                                            <Select
                                              value={range.start}
                                              onValueChange={(value) => handleOnlineHoursUpdate(type.name, day.id, index, "start", value)}
                                              disabled={!range.enabled}
                                            >
                                              <SelectTrigger className="w-20 h-7 text-xs">
                                                <SelectValue placeholder="Inicio" />
                                              </SelectTrigger>
                                              <SelectContent className="z-50">
                                                {getOnlineTimeOptionsForDay(day.id).map(time => (
                                                  <SelectItem key={time} value={time}>{time}</SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                            <span className="text-xs text-gray-500">a</span>
                                            <Select
                                              value={range.end}
                                              onValueChange={(value) => handleOnlineHoursUpdate(type.name, day.id, index, "end", value)}
                                              disabled={!range.enabled}
                                            >
                                              <SelectTrigger className="w-20 h-7 text-xs">
                                                <SelectValue placeholder="Fin" />
                                              </SelectTrigger>
                                              <SelectContent className="z-50">
                                                {getOnlineTimeOptionsForDay(day.id)
                                                  .filter(time => time > range.start)
                                                  .map(time => (
                                                    <SelectItem key={time} value={time}>{time}</SelectItem>
                                                  ))
                                                }
                                              </SelectContent>
                                            </Select>

                                            <div className="flex ml-1">
                                              <button
                                                className="text-gray-400 hover:text-red-500 p-1 rounded-full hover:bg-gray-100"
                                                onClick={() => {
                                                  // Revert changes for this day
                                                  if (originalRanges[day.id]) {
                                                    setEditedConfig(prev => {
                                                      if (!prev) return prev;

                                                      return {
                                                        ...prev,
                                                        consultationTypes: prev.consultationTypes.map(t =>
                                                          t.name === type.name ? {
                                                            ...t,
                                                            onlineBookingHoursByDay: {
                                                              ...t.onlineBookingHoursByDay,
                                                              [day.id]: originalRanges[day.id]
                                                            }
                                                          } : t
                                                        )
                                                      };
                                                    });
                                                  }
                                                  setEditingDay(null);
                                                }}
                                                title="Cancelar"
                                              >
                                                <X className="h-3 w-3" />
                                              </button>
                                              <button
                                                className="text-gray-400 hover:text-green-500 p-1 rounded-full hover:bg-gray-100"
                                                onClick={() => setEditingDay(null)}
                                                title="Confirmar"
                                              >
                                                <Check className="h-3 w-3" />
                                              </button>
                                            </div>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  </ConsultationTypeCollapsible>

                  {/* Appointment Settings */}
                  <ConsultationTypeCollapsible
                    title="Configuración de atención"
                    isOpen={openSections[type.name]?.appointmentSettings || false}
                    onOpenChange={() => toggleSection(type.name, 'appointmentSettings')}
                    summary={getAppointmentSettingsSummary(type)}
                    isComplete={true}
                  >
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={type.requiresMedicalOrder}
                          onCheckedChange={(checked) => updateConsultationType(type.name, "requiresMedicalOrder", checked)}
                        />
                        <Label>Requiere orden médica</Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={type.hasInstructions || false}
                          onCheckedChange={(checked) => {
                            updateConsultationType(type.name, "hasInstructions", checked);
                            if (!checked) {
                              updateConsultationType(type.name, "instructions", "");
                            }
                          }}
                        />
                        <Label>Instrucciones para el paciente</Label>
                      </div>
                      {type.hasInstructions && (
                        <div className="mt-2">
                          <textarea
                            className="w-full border border-gray-300 rounded-md p-2 text-sm"
                            rows={3}
                            maxLength={256}
                            placeholder="Ingrese instrucciones para el paciente (máx. 256 caracteres)"
                            value={type.instructions || ""}
                            onChange={(e) => updateConsultationType(type.name, "instructions", e.target.value)}
                          />
                          <div className="text-xs text-gray-500 mt-1 text-right">
                            {(type.instructions?.length || 0)}/256 caracteres
                          </div>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Label className="w-24">Duración</Label>
                        <Select
                          value={type.duration}
                          onValueChange={(value) =>
                            updateConsultationType(type.name, "duration", value as "default" | "2slots" | "3slots" | "4slots")
                          }
                        >
                          <SelectTrigger className="w-48">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="z-50">
                            <SelectItem value="default">1 turno ({getEffectiveDuration("default")})</SelectItem>
                            <SelectItem value="2slots">2 turnos ({getEffectiveDuration("2slots")})</SelectItem>
                            <SelectItem value="3slots">3 turnos ({getEffectiveDuration("3slots")})</SelectItem>
                            <SelectItem value="4slots">4 turnos ({getEffectiveDuration("4slots")})</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center gap-2">
                        <Label className="w-24">Límite diario</Label>
                        <Select
                          value={type.dailyLimit === "unlimited" ? "unlimited" : "limited"}
                          onValueChange={(value) =>
                            updateConsultationType(type.name, "dailyLimit", value === "unlimited" ? "unlimited" : 1)
                          }
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent className="z-50">
                            <SelectItem value="unlimited">Ilimitado</SelectItem>
                            <SelectItem value="limited">Limitado</SelectItem>
                          </SelectContent>
                        </Select>
                        {type.dailyLimit !== "unlimited" && (
                          <Input
                            type="number"
                            value={type.dailyLimit as number}
                            onChange={(e) => updateConsultationType(type.name, "dailyLimit", parseInt(e.target.value) || 1)}
                            className="w-20"
                            min={1}
                          />
                        )}
                      </div>
                    </div>
                  </ConsultationTypeCollapsible>

                  {/* Pricing and Coverage Settings */}
                  <ConsultationTypeCollapsible
                    title="Tarifas y coberturas"
                    isOpen={openSections[type.name]?.pricingAndCoverage || false}
                    onOpenChange={() => toggleSection(type.name, 'pricingAndCoverage')}
                    summary={getPricingAndCoverageSummary(type)}
                    isComplete={true}
                  >
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={type.acceptsPrivatePay}
                          onCheckedChange={(checked) => updateConsultationType(type.name, "acceptsPrivatePay", checked)}
                        />
                        <Label>Acepta pacientes particulares</Label>
                      </div>
                      {type.acceptsPrivatePay && (
                        <div className="flex items-center gap-2">
                          <Label className="w-28">Precio particular</Label>
                          <div className="relative w-32">
                            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                            <Input
                              type="text"
                              inputMode="numeric"
                              value={type.basePrice === 0 ? "" : type.basePrice.toLocaleString('es-AR', { minimumFractionDigits: 0 }).replace(/,/g, '.')}
                              onChange={(e) => {
                                // If the input is empty, set to 0
                                if (!e.target.value) {
                                  updateConsultationType(type.name, "basePrice", 0);
                                  return;
                                }

                                // Only allow numbers, dots, and commas
                                const value = e.target.value.replace(/[^0-9.,]/g, '');

                                // Convert to a number by removing dots and replacing comma with dot
                                const numericValue = parseFloat(value.replace(/\./g, '').replace(',', '.')) || 0;

                                // Update the consultation type with the parsed value
                                updateConsultationType(type.name, "basePrice", numericValue);
                              }}
                              className="pl-8"
                              placeholder="0"
                            />
                          </div>
                        </div>
                      )}
                      <div className="space-y-2">
                        <Label>Copagos por cobertura</Label>
                        <div className="flex flex-wrap gap-2">
                          {(type.copays || []).map((copay, index) => (
                            <Badge key={`${copay.coverageId}-${copay.planId || "all"}-${index}`} className="flex items-center gap-1 bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100">
                              {medicalCoverages.find((c) => c.id === copay.coverageId)?.name}
                              {copay.planId ? ` (${copay.planId})` : " (Todos los planes)"}: ${copay.amount.toLocaleString('es-AR', { minimumFractionDigits: 0 }).replace(/,/g, '.')}
                              <button
                                onClick={() =>
                                  updateConsultationType(type.name, "copays", (type.copays || []).filter((c) => c.coverageId !== copay.coverageId || c.planId !== copay.planId))
                                }
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                        <Select onValueChange={(coverageId) => openCopayDialog(type.name, coverageId)} value="">
                          <SelectTrigger className="w-40 mt-2">
                            <SelectValue placeholder="Agregar copago" />
                          </SelectTrigger>
                          <SelectContent className="z-50">
                            {medicalCoverages.filter(coverage => coverage.name !== "Sin Cobertura").map((coverage) => (
                              <SelectItem key={coverage.id} value={coverage.id}>{coverage.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>Coberturas excluidas</Label>
                        <div className="flex flex-wrap gap-2">
                          {(type.excludedCoverages || []).map((exclusion, index) => (
                            <Badge
                              key={`${exclusion.coverageId}-${exclusion.planId || "all"}-${index}`}
                              className="flex items-center gap-1 bg-red-50 text-red-700 border-red-200 hover:bg-red-100"
                            >
                              {medicalCoverages.find((c) => c.id === exclusion.coverageId)?.name}
                              {exclusion.planId ? ` (${exclusion.planId})` : " (Todos los planes)"}
                              <button
                                onClick={() =>
                                  updateConsultationType(type.name, "excludedCoverages", (type.excludedCoverages || []).filter((e) => e.coverageId !== exclusion.coverageId || e.planId !== exclusion.planId))
                                }
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                        <Select onValueChange={(coverageId) => openExcludeDialog(type.name, coverageId)} value="">
                          <SelectTrigger className="w-40 mt-2">
                            <SelectValue placeholder="Excluir cobertura" />
                          </SelectTrigger>
                          <SelectContent className="z-50">
                            {medicalCoverages.filter(coverage => coverage.name !== "Sin Cobertura").map((coverage) => (
                              <SelectItem key={coverage.id} value={coverage.id}>{coverage.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </ConsultationTypeCollapsible>
                    </CollapsibleContent>
                  </Collapsible>
                </div>
              ))
          )}
        </div>

        <Dialog open={copayDialogOpen} onOpenChange={setCopayDialogOpen}>
          <DialogContent className="max-w-md p-6">
            <DialogHeader>
              <DialogTitle>Agregar Copago</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Cobertura</Label>
                <Input value={medicalCoverages.find((c) => c.id === selectedCoverageId)?.name || ""} disabled />
              </div>
              <div className="space-y-2">
                <Label>Plan</Label>
                <Select value={selectedPlanId || ""} onValueChange={(value) => setSelectedPlanId(value === "all" ? null : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todos los planes" />
                  </SelectTrigger>
                  <SelectContent className="z-50">
                    <SelectItem value="all">Todos los planes</SelectItem>
                    {medicalCoverages.find((c) => c.id === selectedCoverageId)?.plans.map((plan) => (
                      <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Monto del Copago</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <Input
                    type="text"
                    inputMode="numeric"
                    value={copayAmount === 0 ? "" : copayAmount.toLocaleString('es-AR', { minimumFractionDigits: 0 }).replace(/,/g, '.')}
                    placeholder="0"
                    onChange={(e) => {
                      // If the input is empty, set to 0
                      if (!e.target.value) {
                        setCopayAmount(0);
                        return;
                      }

                      // Only allow numbers, dots, and commas
                      const value = e.target.value.replace(/[^0-9.,]/g, '');

                      // Convert to a number by removing dots and replacing comma with dot
                      const numericValue = parseFloat(value.replace(/\./g, '').replace(',', '.')) || 0;

                      // Update the copay amount
                      setCopayAmount(numericValue);
                    }}
                    className="pl-8"
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setCopayDialogOpen(false)}>
                Cancelar
              </Button>
              <Button
                onClick={handleAddCopay}
                disabled={copayAmount <= 0 || (selectedPlanId === "" && selectedPlanId !== null)}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Agregar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <Dialog open={excludeDialogOpen} onOpenChange={setExcludeDialogOpen}>
          <DialogContent className="max-w-md p-6">
            <DialogHeader>
              <DialogTitle>Excluir Cobertura</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Cobertura</Label>
                <Input value={medicalCoverages.find((c) => c.id === selectedCoverageId)?.name || ""} disabled />
              </div>
              <div className="space-y-2">
                <Label>Plan</Label>
                <Select value={selectedPlanId || ""} onValueChange={(value) => setSelectedPlanId(value === "all" ? null : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Todos los planes" />
                  </SelectTrigger>
                  <SelectContent className="z-50">
                    <SelectItem value="all">Todos los planes</SelectItem>
                    {medicalCoverages.find((c) => c.id === selectedCoverageId)?.plans.map((plan) => (
                      <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setExcludeDialogOpen(false)}>
                Cancelar
              </Button>
              <Button onClick={handleAddExclusion} className="bg-blue-600 hover:bg-blue-700 text-white">Excluir</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Price Adjustment Dialog */}
        <Dialog open={priceAdjustmentDialogOpen} onOpenChange={setPriceAdjustmentDialogOpen}>
          <DialogContent className="max-w-md p-6">
            <DialogHeader>
              <DialogTitle>Actualizar Precios</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                Esta actualización modificará los precios de todos los tipos de atención que acepten pacientes particulares.
              </div>
              <div className="space-y-2">
                <Label>Porcentaje de actualización</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="text"
                    inputMode="numeric"
                    value={priceAdjustmentPercentage === 0 ? "" : priceAdjustmentPercentage.toString()}
                    onChange={(e) => {
                      // If the input is empty, set to 0
                      if (!e.target.value) {
                        setPriceAdjustmentPercentage(0);
                        return;
                      }

                      // Only allow numbers and a single decimal point
                      const value = e.target.value.replace(/[^0-9.-]/g, '');

                      // Parse as float and limit to 2 decimal places
                      const numericValue = parseFloat(value);
                      if (!isNaN(numericValue)) {
                        setPriceAdjustmentPercentage(Math.round(numericValue * 100) / 100);
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && priceAdjustmentPercentage !== 0) {
                        e.preventDefault();
                        handlePriceAdjustment();
                      }
                    }}
                    placeholder="0"
                    className="w-24"
                  />
                  <span className="text-gray-500">%</span>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Ejemplo: 10% para aumentar, -10% para disminuir
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setPriceAdjustmentDialogOpen(false);
                setPriceAdjustmentPercentage(0);
              }}>
                Cancelar
              </Button>
              <Button
                onClick={handlePriceAdjustment}
                disabled={priceAdjustmentPercentage === 0}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Aplicar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

      </CardContent>
    </Card>
  );
}