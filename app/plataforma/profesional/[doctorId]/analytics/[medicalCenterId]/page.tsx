"use client"

import {use<PERSON><PERSON>back, use<PERSON>ontext, useEffect, useMemo, useRef, useState} from "react"
import {use<PERSON><PERSON><PERSON>, useRouter} from "next/navigation"
import Image from "next/image"
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import {DoctorContext} from "@/contexts/DoctorContext"
import {useAppointments} from "@/contexts/AppointmentContext"
import {
    addDays,
    eachDayOfInterval,
    endOfMonth,
    endOfYear,
    format,
    parseISO,
    startOfDay,
    startOfMonth,
    startOfWeek,
    startOfYear,
    subDays
} from "date-fns"
import {es} from "date-fns/locale"
import {Button} from "@/components/ui/button"
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card"
import {
    CartesianGrid,
    Cell,
    Line,
    LineChart,
    Pie,
    PieChart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue,} from "@/components/ui/select"
import {Popover, PopoverContent, PopoverTrigger,} from "@/components/ui/popover"
import {Input} from "@/components/ui/input"
import {Calendar, ChevronRight, Clock, LogOut, Settings, TrendingUp, User, UserCheck, Users} from "lucide-react"
import {useAuth} from "@/hooks/useAuth"
import {UserRole} from "@/types/users"
import {MedicalCenter} from "@/types/medical-center"

type Period = "day" | "week" | "month" | "year" | "custom"

export default function DoctorAnalytics() {
    // Track component mounting to prevent problems with Next.js strict mode
    const isMountedRef = useRef(false)
    const shouldUpdateStateRef = useRef(true)

    // Get route params
    const params = useParams()
    const router = useRouter()
    const doctorId = params.doctorId as string
    const medicalCenterId = params.medicalCenterId as string

    // Get data from contexts
    const {medicalCenters, setActiveMedicalCenterId} = useContext(MedicalCenterContext)
    const {doctors} = useContext(DoctorContext)
    const {appointments} = useAppointments()
    const {currentUser, logout, isAuthenticated, isLoading} = useAuth()

    // Authentication check
    useEffect(() => {
        if (!isLoading && !isAuthenticated) {
            // Not authenticated, redirect to login
            router.push('/plataforma/login')
            return
        }

        if (isAuthenticated && currentUser) {
            // Check if user is a doctor
            if (currentUser.roles === UserRole.DOCTOR) {
                // Check if doctor is accessing their own analytics
                if (currentUser.doctorId !== doctorId) {
                    console.warn(`Doctor ${currentUser.name} attempted to access analytics for doctor ${doctorId}`)
                    // Redirect to their own selection page
                    router.push(`/plataforma/profesional/${currentUser.doctorId}`)
                    return
                }

                // Check if the doctor works at this medical center
                const allMedicalCenters = storage.getMedicalCenters()
                const medicalCenter = allMedicalCenters.find((center: MedicalCenter) => center.id === medicalCenterId)

                if (!medicalCenter || !medicalCenter.doctors.includes(doctorId)) {
                    console.warn(`Doctor ${currentUser.name} attempted to access analytics for medical center ${medicalCenterId} where they don't work`)
                    // Redirect to their selection page
                    router.push(`/plataforma/profesional/${currentUser.doctorId}`)
                    return
                }
            } else if (currentUser.roles !== UserRole.ADMIN && currentUser.roles !== UserRole.SUPERUSER) {
                // Non-admin, non-doctor users should not access this page
                console.warn(`User ${currentUser.name} with role ${currentUser.roles} attempted to access doctor analytics`)
                router.push(`/plataforma/establecimiento/${currentUser.medicalCenterId}`)
                return
            }
        }
    }, [isLoading, isAuthenticated, currentUser, doctorId, medicalCenterId, router])

    // Only set the active medical center once per mount
    useEffect(() => {
        if (!isMountedRef.current) {
            isMountedRef.current = true

            if (medicalCenterId) {
                setActiveMedicalCenterId(medicalCenterId)
            }
        }
    }, [medicalCenterId, setActiveMedicalCenterId])

    // Create a stable doctor reference
    const doctor = useMemo(() =>
            doctors.find(d => d.id === doctorId) || null,
        [doctors, doctorId]
    )

    // Static selections
    const [selectedMedicalCenterId, setSelectedMedicalCenterId] = useState(medicalCenterId)
    const [period, setPeriod] = useState<Period>("month")
    const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
        from: subDays(new Date(), 30),
        to: new Date(),
    })

    const medicalCenter = useMemo(() =>
            medicalCenters.find((mc) => mc.id === selectedMedicalCenterId),
        [medicalCenters, selectedMedicalCenterId]
    )

    const doctorMedicalCenters = useMemo(() =>
            medicalCenters.filter(mc => mc.doctors.includes(doctorId)),
        [medicalCenters, doctorId]
    )

    const getStartDate = useCallback(() => {
        const today = new Date()
        switch (period) {
            case "day":
                return startOfDay(today)
            case "week":
                return startOfWeek(today, {weekStartsOn: 1})
            case "month":
                return startOfMonth(today)
            case "year":
                return startOfYear(today)
            case "custom":
                return startOfDay(dateRange.from)
            default:
                return startOfMonth(today)
        }
    }, [period, dateRange])

    const getEndDate = useCallback(() => {
        const today = new Date()
        switch (period) {
            case "day":
                return startOfDay(today)
            case "week":
                return addDays(startOfWeek(today, {weekStartsOn: 1}), 6)
            case "month":
                return endOfMonth(today)
            case "year":
                return endOfYear(today)
            case "custom":
                return dateRange.to
            default:
                return endOfMonth(today)
        }
    }, [period, dateRange])

    const filteredAppointments = useMemo(() => {
        const startDate = getStartDate()
        const endDate = getEndDate()
        return Object.entries(appointments)
            .filter(([date]) => {
                const appointmentDate = parseISO(date)
                return appointmentDate >= startDate && appointmentDate <= endDate
            })
            .flatMap(([, appts]) => appts)
            .filter(apt => apt.doctorId === doctorId)
    }, [appointments, doctorId, getStartDate, getEndDate])

    const totalAppointments = filteredAppointments.length
    const attendedAppointments = filteredAppointments.filter(apt => apt.status === "Atendido" || apt.status === "En Atención").length
    const attendanceRate = totalAppointments > 0 ? ((attendedAppointments / totalAppointments) * 100).toFixed(1) : "0"

    const uniquePatientIds = useMemo(() => new Set(filteredAppointments.map(apt => apt.patient)), [filteredAppointments])
    const activePatientsCount = uniquePatientIds.size

    const calculateFillRate = useCallback(() => {
        if (period === "year") return "N/A"
        const slotsPerDoctorPerDay = 12
        const today = new Date()
        const daysInPeriod = period === "day" ? 1 : period === "week" ? 7 : format(endOfMonth(today), "d", {locale: es})
        const totalPossibleSlots = slotsPerDoctorPerDay * Number(daysInPeriod)
        return totalPossibleSlots > 0 ? ((totalAppointments / totalPossibleSlots) * 100).toFixed(1) : "0"
    }, [period, totalAppointments])

    const calculatePatientTypes = useCallback(() => {
        const startDate = getStartDate()
        const previousAppointments = Object.entries(appointments)
            .filter(([date]) => parseISO(date) < startDate)
            .flatMap(([, appts]) => appts)
            .filter(apt => apt.doctorId === doctorId)
        const previousPatientIds = new Set(previousAppointments.map(apt => apt.patient))
        let newPatients = 0
        let recurringPatients = 0
        uniquePatientIds.forEach(patientId => {
            if (previousPatientIds.has(patientId)) recurringPatients++
            else newPatients++
        })
        return {newPatients, recurringPatients}
    }, [getStartDate, appointments, doctorId, uniquePatientIds])

    const appointmentTrend = useMemo(() => {
        const trend: { name: string; appointments: number }[] = []
        const today = new Date()
        if (period === "day") {
            const hours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]
            hours.forEach(hour => {
                const count = filteredAppointments.filter(apt => {
                    const [h] = apt.time.split(":").map(Number)
                    return h === hour
                }).length
                trend.push({name: `${hour}:00`, appointments: count})
            })
        } else if (period === "week") {
            const days = ["Lunes", "Martes", "Miércoles", "Jueves", "Viernes", "Sábado", "Domingo"]
            const startOfWeekDate = startOfWeek(today, {weekStartsOn: 1})
            days.forEach((day, index) => {
                const currentDateInWeek = new Date(startOfWeekDate)
                currentDateInWeek.setDate(startOfWeekDate.getDate() + index)
                const formattedDate = format(currentDateInWeek, "yyyy-MM-dd")
                const count = (appointments[formattedDate] || []).filter(apt => apt.doctorId === doctorId).length
                trend.push({name: day, appointments: count})
            })
        } else if (period === "month") {
            const start = startOfMonth(today)
            const end = endOfMonth(today)
            const daysInMonth = eachDayOfInterval({start, end})
            daysInMonth.forEach(day => {
                const formattedDate = format(day, "yyyy-MM-dd")
                const count = (appointments[formattedDate] || []).filter(apt => apt.doctorId === doctorId).length
                trend.push({name: format(day, "d"), appointments: count})
            })
        } else if (period === "year") {
            const start = startOfYear(today)
            const end = endOfYear(today)
            const monthsInPeriod = eachDayOfInterval({start, end}).reduce((acc, day) => {
                const monthYear = format(day, "MMM yyyy", {locale: es})
                acc[monthYear] = (acc[monthYear] || 0) + ((appointments[format(day, "yyyy-MM-dd")] || []).filter(apt => apt.doctorId === doctorId).length)
                return acc
            }, {} as Record<string, number>)
            Object.entries(monthsInPeriod).forEach(([month, count]) => trend.push({name: month, appointments: count}))
        } else { // Custom
            const monthlyData = new Map<string, number>()
            Object.entries(appointments)
                .filter(([date]) => {
                    const appointmentDate = parseISO(date)
                    return appointmentDate >= getStartDate() && appointmentDate <= getEndDate()
                })
                .forEach(([date, appts]) => {
                    const monthYear = format(parseISO(date), "MMM yyyy", {locale: es})
                    const count = monthlyData.get(monthYear) || 0
                    monthlyData.set(monthYear, count + appts.filter(apt => apt.doctorId === doctorId).length)
                })
            Array.from(monthlyData.entries())
                .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())
                .forEach(([month, count]) => trend.push({name: month, appointments: count}))
        }
        return trend
    }, [period, filteredAppointments, appointments, doctorId, getStartDate, getEndDate])

    const periodLabels: Record<Period, string> = {
        day: "Hoy",
        week: "Esta semana",
        month: "Este mes",
        year: "Este año",
        custom: "Personalizado"
    }

    const handleMedicalCenterChange = useCallback((value: string) => {
        shouldUpdateStateRef.current = false // Prevent re-initialization on doctor change
        setSelectedMedicalCenterId(value)
        router.push(`/plataforma/profesional/${doctorId}/analytics/${value}`)
    }, [doctorId, router])

    // Check if user has admin, receptionist, or superuser role
    const hasAdminRole = currentUser && (
        currentUser.roles === 'admin' ||
        currentUser.roles === 'receptionist' ||
        currentUser.roles === 'superuser' ||
        (currentUser.medicalCenterRoles &&
            currentUser.medicalCenterRoles.some(mcr =>
                mcr.role === 'admin' || mcr.role === 'receptionist' || mcr.role === 'superuser'
            ))
    );

    const handleGoToAdminView = () => {
        router.push("/plataforma/establecimiento");
    };

    // Add error and loading states
    if (!doctor || !medicalCenter) {
        return (
            <div className="min-h-screen bg-blue-50 flex flex-col items-center justify-center">
                <div className="text-blue-500">Cargando estadísticas...</div>
            </div>
        )
    }

    const fillRate = calculateFillRate()
    const {newPatients, recurringPatients} = calculatePatientTypes()

    return (
        <div className="min-h-screen bg-blue-50 flex flex-col">
            <style jsx global>{`
                html {
                    font-size: 13px;
                }

                @media (min-width: 1500px) {
                    html {
                        font-size: 15px;
                    }
                }

                @media (min-width: 2560px) {
                    html {
                        font-size: 17px;
                    }
                }

                .container {
                    max-width: 1400px;
                }
            `}</style>

            <header className="bg-white shadow-md border-b border-blue-100">
                <div
                    className="container mx-auto px-6 py-4 flex flex-col lg:flex-row items-center justify-between gap-4">
                    <div className="flex items-center justify-between w-full lg:w-auto">
                        <Image
                            src="/images/turnera-logo.svg"
                            alt="Turnera Logo"
                            width={120}
                            height={40}
                            className="h-8 w-auto"
                            priority
                        />

                    </div>
                    <div className="flex flex-col lg:flex-row items-center gap-4 w-full lg:w-auto">
                        {hasAdminRole && (
                            <Button
                                variant="outline"
                                onClick={handleGoToAdminView}
                                className="border-purple-200 text-purple-700 hover:bg-purple-50 hover:text-purple-800 transition-colors duration-200"
                            >
                                <Settings className="h-4 w-4 mr-2"/>
                                <span className="hidden sm:inline">Vista Administrador</span>
                            </Button>
                        )}
                        <Select value={selectedMedicalCenterId} onValueChange={handleMedicalCenterChange}>
                            <SelectTrigger className="w-full lg:w-[200px] bg-blue-50 border-blue-200">
                                <SelectValue placeholder="Select Medical Center"/>
                            </SelectTrigger>
                            <SelectContent>
                                {doctorMedicalCenters.map(mc => (
                                    <SelectItem key={mc.id} value={mc.id}>{mc.name}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <div
                            className="flex rounded-lg bg-white border border-blue-200 shadow-sm p-1 gap-1 w-full lg:w-auto">
                            <Button
                                variant="ghost"
                                className="flex-1 lg:flex-none px-3 py-1 text-sm font-medium rounded-md text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                                onClick={() => router.push(`/plataforma/profesional/${doctorId}/agenda/${selectedMedicalCenterId}`)}
                            >
                                Agenda
                            </Button>
                            <Button
                                variant="default"
                                className="flex-1 lg:flex-none px-3 py-1 text-sm font-medium rounded-md bg-blue-500 text-white hover:bg-blue-600 shadow-sm"
                            >
                                Estadísticas
                            </Button>
                        </div>
                    </div>
                    <div
                        className="bg-blue-50 rounded-lg px-4 py-2 border border-blue-100 w-full lg:w-auto text-center lg:text-left flex justify-between items-center">
                        <div>
                            <h1 className="text-lg font-semibold text-blue-800">{medicalCenter.name}</h1>
                            <div className="flex items-center justify-center lg:justify-start text-blue-600 mt-1">
                                <User size={14} className="mr-1"/>
                                <p className="text-sm">{doctor.name}</p>
                            </div>
                        </div>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={logout}
                            className="text-gray-600 hover:text-red-600 flex items-center gap-1 ml-4"
                        >
                            <LogOut size={16}/>
                            <span className="hidden sm:inline">Salir</span>
                        </Button>
                    </div>
                </div>
            </header>

            <main className="container mx-auto px-6 py-6 flex-1">
                <div className="flex justify-between items-center mb-8 flex-col lg:flex-row gap-4">
                    <h2 className="text-2xl font-bold text-gray-800">Análisis y Estadísticas</h2>
                    <div className="flex items-center space-x-4 flex-col lg:flex-row gap-4">
                        <div
                            className="flex rounded-lg bg-white border border-blue-200 shadow-sm p-1 gap-1 flex-wrap justify-center">
                            {["day", "week", "month", "year", "custom"].map(p => (
                                <Button
                                    key={p}
                                    variant={period === p ? "default" : "ghost"}
                                    className={`px-3 py-1 text-sm font-medium rounded-md transition-all duration-200 ${
                                        period === p ? "bg-blue-500 text-white hover:bg-blue-600 shadow-sm" : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                                    }`}
                                    onClick={() => setPeriod(p as Period)}
                                >
                                    {periodLabels[p as Period]}
                                </Button>
                            ))}
                        </div>
                        {period === "custom" && (
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="outline"
                                            className="flex items-center gap-2 bg-white border-gray-200 rounded-full shadow-sm hover:bg-gray-50 transition-colors duration-200 w-full lg:w-auto">
                                        <Calendar className="h-5 w-5 text-gray-600"/>
                                        <span className="text-sm font-medium text-gray-700">
                      {format(dateRange.from, "dd/MM/yyyy")} - {format(dateRange.to, "dd/MM/yyyy")}
                    </span>
                                        <ChevronRight className="h-5 w-5 text-gray-400"/>
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent
                                    className="w-auto p-4 bg-white border border-gray-200 rounded-lg shadow-xl">
                                    <div className="space-y-3">
                                        <Input
                                            type="date"
                                            value={format(dateRange.from, "yyyy-MM-dd")}
                                            onChange={(e) => setDateRange({
                                                ...dateRange,
                                                from: parseISO(e.target.value)
                                            })}
                                            className="border-gray-200 rounded-md focus:ring-blue-300"
                                        />
                                        <Input
                                            type="date"
                                            value={format(dateRange.to, "yyyy-MM-dd")}
                                            onChange={(e) => setDateRange({...dateRange, to: parseISO(e.target.value)})}
                                            className="border-gray-200 rounded-md focus:ring-blue-300"
                                        />
                                    </div>
                                </PopoverContent>
                            </Popover>
                        )}
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card
                        className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
                        <CardHeader className="pb-2 flex flex-row justify-between items-center">
                            <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Total de
                                Turnos</CardTitle>
                            <TrendingUp className="w-5 h-5 text-blue-600"/>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-600">{totalAppointments}</div>
                            <p className="text-sm text-gray-500 mt-1">{periodLabels[period]}</p>
                        </CardContent>
                    </Card>
                    <Card
                        className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
                        <CardHeader className="pb-2 flex flex-row justify-between items-center">
                            <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Porcentaje
                                de Ocupación</CardTitle>
                            <Clock className="w-5 h-5 text-green-600"/>
                        </CardHeader>
                        <CardContent>
                            <div
                                className="text-3xl font-bold text-green-600">{fillRate === "N/A" ? "N/A" : `${fillRate}%`}</div>
                            <p className="text-sm text-gray-500 mt-1">{period === "year" ? "No aplicable en este periodo" : periodLabels[period]}</p>
                        </CardContent>
                    </Card>
                    <Card
                        className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
                        <CardHeader className="pb-2 flex flex-row justify-between items-center">
                            <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Tasa de
                                Presencialidad</CardTitle>
                            <UserCheck className="w-5 h-5 text-purple-600"/>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-600">{attendanceRate}%</div>
                            <p className="text-sm text-gray-500 mt-1">{attendedAppointments} de {totalAppointments} {totalAppointments === 1 ? "turno" : "turnos"}</p>
                        </CardContent>
                    </Card>
                    <Card
                        className="bg-white border border-gray-100 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-200">
                        <CardHeader className="pb-2 flex flex-row justify-between items-center">
                            <CardTitle className="text-sm font-semibold text-gray-600 uppercase tracking-wider">Pacientes
                                Activos</CardTitle>
                            <Users className="w-5 h-5 text-orange-600"/>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-orange-600">{activePatientsCount}</div>
                            <p className="text-sm text-gray-500 mt-1">Activos en este periodo</p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                    <Card className="bg-white border border-gray-100 shadow-lg rounded-xl overflow-hidden">
                        <CardHeader className="bg-gray-50 border-b border-gray-200">
                            <CardTitle className="text-xl font-bold text-gray-800">Tendencia de Turnos</CardTitle>
                        </CardHeader>
                        <CardContent className="p-6 h-[24rem]">
                            <ResponsiveContainer width="100%" height="100%">
                                <LineChart data={appointmentTrend} margin={{top: 10, right: 30, left: 0, bottom: 10}}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb"/>
                                    <XAxis dataKey="name" stroke="#6b7280"/>
                                    <YAxis stroke="#6b7280"/>
                                    <Tooltip
                                        formatter={(value: number) => [`${value} ${value === 1 ? "turno" : "turnos"}`, ""]}
                                        contentStyle={{
                                            backgroundColor: "#fff",
                                            border: "1px solid #e5e7eb",
                                            borderRadius: "8px"
                                        }}/>
                                    <Line type="monotone" dataKey="appointments"
                                          name={totalAppointments === 1 ? "Turno" : "Turnos"} stroke="#3b82f6"
                                          strokeWidth={2} activeDot={{r: 8}}/>
                                </LineChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </Card>
                    <Card className="bg-white border border-gray-100 shadow-lg rounded-xl overflow-hidden">
                        <CardHeader className="bg-gray-50 border-b border-gray-200">
                            <CardTitle className="text-xl font-bold text-gray-800">Nuevos vs. Recurrentes</CardTitle>
                        </CardHeader>
                        <CardContent className="p-6 h-[24rem] flex flex-col">
                            <div className="flex-1 flex items-center justify-center">
                                <ResponsiveContainer width="100%" height="80%">
                                    <PieChart>
                                        <Pie
                                            data={[{
                                                name: "Pacientes nuevos",
                                                value: newPatients
                                            }, {name: "Pacientes recurrentes", value: recurringPatients}]}
                                            cx="50%"
                                            cy="50%"
                                            labelLine={false}
                                            label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                            outerRadius={90}
                                            fill="#8884d8"
                                            dataKey="value"
                                        >
                                            <Cell fill="#3b82f6"/>
                                            <Cell fill="#22c55e"/>
                                        </Pie>
                                        <Tooltip
                                            formatter={(value: number) => [`${value} ${value === 1 ? "paciente" : "pacientes"}`, ""]}/>
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>
                            <div className="flex justify-center space-x-8 mt-4">
                                <div className="flex items-center">
                                    <div className="w-4 h-4 bg-blue-600 rounded-full mr-2"></div>
                                    <span className="text-sm font-medium text-gray-700">Nuevos: {newPatients}</span>
                                </div>
                                <div className="flex items-center">
                                    <div className="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                                    <span
                                        className="text-sm font-medium text-gray-700">Recurrentes: {recurringPatients}</span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </main>

            <footer className="bg-white border-t border-blue-100 py-3 mt-auto">
                <div className="container mx-auto px-6 text-center text-sm text-blue-500">
                    Turnera © {new Date().getFullYear()} - Sistema de Gestión de Turnos
                </div>
            </footer>
        </div>
    )
}