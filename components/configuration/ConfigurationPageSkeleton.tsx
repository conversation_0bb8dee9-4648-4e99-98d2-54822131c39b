"use client"

import DoctorCardSkeleton from "./DoctorCardSkeleton"
import { Calendar, ArrowLeft, UserPlus } from "lucide-react"
import GlobalFontStyles from "@/components/ui/global-font-styles"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import Image from "next/image"
import Sidebar from "./configuration-sidebar"

export default function ConfigurationPageSkeleton() {
  // Create a dummy selectedTab state for the Sidebar
  const selectedTab = "agendas"
  const setSelectedTab = () => {}
  return (
    <div className="min-h-screen bg-blue-50 flex flex-col">
      <GlobalFontStyles />

      {/* Header - Exact match with ConfigurationHeader */}
      <header className="border-b bg-white shadow-sm">
        <div className="container mx-auto px-[6rem] py-[1.2rem] flex items-center justify-between">
          <div className="flex items-center">
            <Image
              src="/images/turnera-logo.svg"
              alt="Turnera Logo"
              width={120}
              height={40}
              className="h-[1.8rem] w-auto"
              priority
            />
          </div>
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse"></div>
            <div className="h-5 w-40 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </header>

      {/* Nav - Exact match with ConfigurationNav */}
      <nav className="bg-blue-50">
        <div className="container mx-auto px-[6rem] py-[1rem]">
          <div className="flex items-center">
            <Button
              variant="outline"
              className="flex items-center gap-[0.375rem] transition-all duration-300 bg-white border-blue-300/50 hover:bg-blue-100/70 hover:border-blue-400 px-[0.75rem] py-[0.375rem] rounded-lg shadow-sm hover:shadow-md"
            >
              <ArrowLeft className="w-[1rem] h-[1rem] text-blue-600 group-hover:text-blue-800 transition-colors" />
              <span className="text-blue-700 group-hover:text-blue-900 font-medium text-[0.875rem]">Atrás</span>
            </Button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container mx-auto px-[6rem] pt-[0.25rem] pb-[1.5rem] flex">
        {/* Sidebar - Using the actual Sidebar component */}
        <Sidebar selectedTab={selectedTab} setSelectedTab={setSelectedTab} />

        {/* Content Area */}
        <div className="flex-1">
          {/* Card Header - Using the actual Card components */}
          <Card className="mb-6 bg-white shadow-md">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <div>
                    <CardTitle className="text-[1.3rem] font-semibold text-gray-900">
                      Configuración de agendas
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      Configure los días, horarios y tipos de atención para cada profesional en el establecimiento.
                    </CardDescription>
                  </div>
                </div>
                <Button
                  className="bg-blue-600 text-white hover:bg-blue-700"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Agregar Profesional
                </Button>
              </div>
            </CardHeader>
          </Card>

          {/* Doctor Cards */}
          <div className="grid grid-cols-1 gap-4">
            {Array(3).fill(0).map((_, index) => (
              <DoctorCardSkeleton key={index} />
            ))}
          </div>
        </div>
      </main>
    </div>
  )
}
