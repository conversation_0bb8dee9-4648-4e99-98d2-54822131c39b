import {AppointmentState} from "@/types/professional-schedules";


export function getSpanishNameFromAppointmentState(state: AppointmentState) {
    switch (state) {
        case AppointmentState.CANCELLED:
            return "Cancelado";
        case AppointmentState.PENDING:
            return "Agendado";
        case AppointmentState.NO_SHOW:
            return "No se presentó";
        case AppointmentState.IN_WAITING_ROOM:
            return "Recepcionado";
        case AppointmentState.IN_CONSULTATION:
            return "En Atención";
        case AppointmentState.COMPLETE || AppointmentState.PROCESSING || AppointmentState.SETTLED:
            return "Atendido";
        default:
            return "Estado desconocido";
    }
}