"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowR<PERSON>, Calendar, Clock, Bell } from "lucide-react"

export default function CallToAction() {
  return (
    <section className="py-16 md:py-20 relative overflow-hidden">
      {/* Background with gradient and shapes */}
      <div className="absolute inset-0 bg-gradient-to-tr from-blue-600 to-blue-500 z-0"></div>
      <div className="absolute -right-12 -top-12 w-64 h-64 bg-blue-500 rounded-full opacity-30 blur-3xl z-0"></div>
      <div className="absolute -left-12 -bottom-12 w-64 h-64 bg-indigo-500 rounded-full opacity-30 blur-3xl z-0"></div>

      {/* Background pattern */}
      <div className="absolute inset-0 bg-[url('/images/pattern.svg')] opacity-10 z-0"></div>

      <div className="container mx-auto px-6 md:px-8 max-w-7xl relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          {/* Content */}
          <h2 className="text-3xl md:text-4xl font-semibold mb-2 md:mb-3 text-white font-recoleta max-w-2xl mx-auto">
            Reservá tus turnos médicos de forma fácil y rápida
          </h2>
          <p className="text-lg mb-6 md:mb-8 max-w-xl mx-auto text-white/90">
            Registrate en Turnera y encontrá los mejores profesionales para cuidar tu salud.
          </p>

          <div className="flex flex-wrap gap-3 mb-8 justify-center">
            <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm">
              <Calendar className="h-4 w-4 mr-2" /> Turnos disponibles al instante
            </div>
            <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm">
              <Clock className="h-4 w-4 mr-2" /> Reservas 24/7
            </div>
            <div className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 text-white text-sm">
              <Bell className="h-4 w-4 mr-2" /> Recordatorios automáticos
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              size="lg"
              className="bg-white text-blue-600 hover:bg-blue-50 rounded-[12px] shadow-lg hover:shadow-xl transition-all duration-300"
              asChild
            >
              <Link href="/plataforma/paciente/registro">
                Registrarme como Paciente <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button
              size="lg"
              className="bg-white text-blue-600 hover:bg-blue-50 rounded-[12px] shadow-lg hover:shadow-xl transition-all duration-300"
              asChild
            >
              <Link href="/para-profesionales">Soy Profesional Médico</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
