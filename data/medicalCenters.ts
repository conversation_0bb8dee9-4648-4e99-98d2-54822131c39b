import { MedicalCenter } from "@/types/medical-center";

// Export as initialMedicalCenters (preferred new name)
export const initialMedicalCenters: MedicalCenter[] = [
  {
    id: "medical-center-1",
    name: "Centro Médico Verdi",
    doctors: ["dr-favaloro", "dra-grierson"],
    workingDays: {
      "1": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "2": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "3": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "4": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "5": { enabled: true, hours: [{ start: "09:00", end: "20:00" }] },
      "6": { enabled: false, hours: [] },
      "0": { enabled: false, hours: [] },
    },
    address: "Av. del Libertador 123, CABA",
    phone: "+54 11 1234-5678",
    email: "<EMAIL>",
    website: "https://www.centroverdi.com",
    logoUrl: "/logos/centro-verdi.png",
    openingHours: {
      monday: { start: '09:00', end: '20:00', enabled: true },
      tuesday: { start: '09:00', end: '20:00', enabled: true },
      wednesday: { start: '09:00', end: '20:00', enabled: true },
      thursday: { start: '09:00', end: '20:00', enabled: true },
      friday: { start: '09:00', end: '20:00', enabled: true },
      saturday: { start: '09:00', end: '13:00', enabled: false },
      sunday: { start: '00:00', end: '00:00', enabled: false }
    },
    billing: {
      businessName: "Centro Médico Verdi S.A.",
      taxId: "30-12345678-9",
      address: "Av. del Libertador 123, CABA",
      businessType: "company"
    },
    // Default accepted coverages (matching the IDs in CoverageContext)
    acceptedCoverages: ["1", "2", "3"]
  },
];

// Also export as medicalCenters for backward compatibility
export const medicalCenters = initialMedicalCenters;

