"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Inbox, MessageSquare, MessageCircle, UserPlus, ArrowLeft, TestTube, Building2 } from "lucide-react"
import Image from "next/image"

export default function DevPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <div className="flex items-center">
            <Image
              src="/images/turnera-logo.svg"
              alt="Turnera Logo"
              width={140}
              height={40}
              className="h-8 w-auto mr-4"
              priority
            />
            <h1 className="text-2xl font-semibold text-gray-900">Developer Tools</h1>
          </div>
          <div className="flex items-center space-x-4">
            <Link href="/admin" passHref>
              <Button variant="outline" size="sm" className="flex items-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Admin
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <h2 className="text-xl font-semibold mb-6">Development Tools</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            <Link href="/dev/emails" passHref className="block">
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Inbox className="h-5 w-5 mr-2 text-blue-500" />
                    Email Preview
                  </CardTitle>
                  <CardDescription>
                    Preview email templates and test email functionality
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">
                    View and test all email templates used in the application.
                  </p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dev/phone" passHref className="block">
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2 text-green-500" />
                    Phone Messages
                  </CardTitle>
                  <CardDescription>
                    Preview SMS templates and test messaging functionality
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">
                    View and test all SMS message templates used in the application.
                  </p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dev/whatsapp" passHref className="block">
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MessageCircle className="h-5 w-5 mr-2 text-green-600" />
                    WhatsApp Messages
                  </CardTitle>
                  <CardDescription>
                    Vista previa de plantillas de WhatsApp y prueba de funcionalidad de mensajería
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">
                    Ver y probar todas las plantillas de mensajes de WhatsApp usadas en la aplicación.
                  </p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dev/professional-registrations" passHref className="block">
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <UserPlus className="h-5 w-5 mr-2 text-purple-500" />
                    Professional Registrations
                  </CardTitle>
                  <CardDescription>
                    View professional registration form submissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">
                    View all professional registration form submissions from the public registration forms.
                  </p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dev/test-notifications" passHref className="block">
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TestTube className="h-5 w-5 mr-2 text-orange-500" />
                    Test Notifications
                  </CardTitle>
                  <CardDescription>
                    Probar funcionalidad de notificaciones por email y WhatsApp
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">
                    Enviar notificaciones de prueba para verificar plantillas de email y WhatsApp.
                  </p>
                </CardContent>
              </Card>
            </Link>

            <Link href="/dev/medical-center-info" passHref className="block">
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building2 className="h-5 w-5 mr-2 text-red-500" />
                    Medical Center Info
                  </CardTitle>
                  <CardDescription>
                    View and manage medical center information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-500">
                    Access and update the information of the medical center.
                  </p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}
