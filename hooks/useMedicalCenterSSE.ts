import {useEffect, useRef, useState} from 'react'

interface MedicalCenterSSEOptions {
    medicalCenterId: number
    employeeUserId: number
}

export interface SSEEvent {
    type: string
    data: unknown
    timestamp: Date
}

// TODO IMPORTANT : remove logging when prod
export const useMedicalCenterSSE = (options: MedicalCenterSSEOptions) => {
    const {medicalCenterId, employeeUserId} = options
    const [isConnected, setIsConnected] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null)

    const eventSourceRef = useRef<EventSource | null>(null)
    const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const eventListenersRef = useRef<Map<string, ((data: unknown) => void)[]>>(new Map())


    const addEventListener = (eventType: string, callback: (data: unknown) => void) => {
        const listeners = eventListenersRef.current.get(eventType) || []
        listeners.push(callback)
        eventListenersRef.current.set(eventType, listeners)

        // Return cleanup function
        return () => {
            const updatedListeners = eventListenersRef.current.get(eventType)?.filter(cb => cb !== callback) || []
            if (updatedListeners.length === 0) {
                eventListenersRef.current.delete(eventType)
            } else {
                eventListenersRef.current.set(eventType, updatedListeners)
            }
        }
    }


    const emitEvent = (eventType: string, data: unknown) => {
        const listeners = eventListenersRef.current.get(eventType) || []
        const event: SSEEvent = {
            type: eventType,
            data,
            timestamp: new Date()
        }
        setLastEvent(event)
        listeners.forEach(callback => {
            try {
                callback(data)
            } catch (error) {
                console.error(`Error in event listener for ${eventType}:`, error)
            }
        })
    }

    const connect = () => {
        try {
            if (!medicalCenterId) return

            if (eventSourceRef.current) {
                eventSourceRef.current.close()
                eventSourceRef.current = null
            }

            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current)
                reconnectTimeoutRef.current = null
            }

            const url = `/api/sse?medical-center-id=${medicalCenterId}&employee-user-id=${employeeUserId}`
            const eventSource = new EventSource(url)
            eventSourceRef.current = eventSource

            eventSource.onopen = () => {
                console.log('Medical Center SSE connection opened')
                setIsConnected(true)
                setError(null)
                emitEvent('connection', {status: 'connected'})
            }

            eventSource.addEventListener('patients', (event) => {
                console.log('Patients event received:', event.data)
                try {
                    const data = JSON.parse(event.data)
                    emitEvent('patients', data)
                } catch (e) {
                    emitEvent('patients', event.data)
                }
            })

            // Listen for appointments events
            eventSource.addEventListener('appointments', (event) => {
                console.log('Appointments event received:', event.data)
                try {
                    const data = JSON.parse(event.data)
                    emitEvent('appointments', data)
                } catch (e) {
                    emitEvent('appointments', event.data)
                }
            })

            eventSource.addEventListener('schedules', (event) => {
                console.log('Schedules event received:', event.data)
                try {
                    const data = JSON.parse(event.data)
                    emitEvent('schedules', data)
                } catch (e) {
                    emitEvent('schedules', event.data)
                }
            })

            eventSource.addEventListener('doctors', (event) => {
                console.log('Doctors event received:', event.data)
                try {
                    const data = JSON.parse(event.data)
                    emitEvent('doctors', data)
                } catch (e) {
                    emitEvent('doctors', event.data)
                }
            })

            eventSource.addEventListener('heartbeat', () => {
            })


            eventSource.onmessage = (event) => {
                console.log('Generic SSE message received:', event.data)
                try {
                    const data = JSON.parse(event.data)
                    emitEvent('message', data)
                } catch (e) {
                    emitEvent('message', event.data)
                }
            }

            eventSource.onerror = (event) => {
                console.error('Medical Center SSE error:', event)
                setIsConnected(false)
                setError('Connection error')
                emitEvent('error', {error: 'Connection error'})

                
                reconnectTimeoutRef.current = setTimeout(() => {
                    console.log('Attempting to reconnect Medical Center SSE...')
                    connect()
                }, 3000)
            }

        } catch (err) {
            console.error('Failed to create Medical Center SSE connection:', err)
            setError('Failed to connect')
            emitEvent('error', {error: 'Failed to connect'})
        }
    }

    const disconnect = () => {
        if (eventSourceRef.current) {
            eventSourceRef.current.close()
            eventSourceRef.current = null
        }
        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current)
            reconnectTimeoutRef.current = null
        }
        setIsConnected(false)
        emitEvent('connection', {status: 'disconnected'})
    }

    // Handle page unload events
    const handlePageUnload = () => {
        console.log('Page is being unloaded, disconnecting Medical Center SSE...')
        disconnect()
    }

    useEffect(() => {
        if (medicalCenterId) {
            connect()
            // Add event listeners for page unload events
            window.addEventListener('beforeunload', handlePageUnload)
            window.addEventListener('unload', handlePageUnload)
            window.addEventListener('pagehide', handlePageUnload)

            return () => {
                // Clean up event listeners and disconnect
                window.removeEventListener('beforeunload', handlePageUnload)
                window.removeEventListener('unload', handlePageUnload)
                window.removeEventListener('pagehide', handlePageUnload)
                disconnect()
            }
        }
    }, [medicalCenterId, employeeUserId])

    return {
        isConnected,
        error,
        lastEvent,
        addEventListener,
        disconnect,
        reconnect: connect
    }
}
