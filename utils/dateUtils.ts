export function generateTimeSlots(startTime: string, endTime: string, duration: number) {
    const slots: string[] = []
    const start = new Date(`1970-01-01T${startTime}`)
    const end = new Date(`1970-01-01T${endTime}`)

    while (start < end) {
        slots.push(start.toLocaleTimeString("es-ES", {hour: "2-digit", minute: "2-digit"}))
        start.setMinutes(start.getMinutes() + duration)
    }

    return slots
}

export function getDateKey(day: Date) {
    return day.toISOString().split('T')[0]
}

export function amountOfTimeSlots(startTime: string, endTime: string, duration: number) {
    const start = convertTimeStringToMinutes(startTime)
    const end = convertTimeStringToMinutes(endTime)
    return (end - start) / duration;
}

export function getMonthData(year: number, month: number) {
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDay = firstDay.getDay()

    const days = Array.from({length: daysInMonth}, (_, i) => (
        new Date(year, month, i + 1))
    ).sort((a, b) =>
        a.getTime() - b.getTime())

    return {days, startingDay}
}


export function formatMonthYear(date: Date) {
    return date.toLocaleDateString("es-ES", {month: "long", year: "numeric"})
}

export function getDateFromString(dateString: string): Date | null {
    const dateParts = dateString.split('-')
    if (dateParts.length !== 3) return null;
    const [year, month, day] = dateParts.map(part => parseInt(part, 10))
    if (isNaN(year) || isNaN(month) || isNaN(day)) return null;
    return new Date(year, month - 1, day) // month is 0-indexed in JavaScript
}


export function convertTimeStringToMinutes(timeString: string | undefined): number {
    if (!timeString) return 15; // default fallback

    // Split the time string (handles both "HH:MM" and "HH:MM:SS" formats)
    const parts = timeString.split(':');

    if (parts.length < 2) return 15; // invalid format, return default

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    // Convert to total minutes
    return (hours * 60) + minutes;
}

export function convertMinutesToTimeString(minutes: number): string {
    const hrs = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}