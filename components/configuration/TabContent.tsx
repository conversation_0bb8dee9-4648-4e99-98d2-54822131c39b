import { Card, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"
import { ReactNode } from "react"

interface TabContentProps {
  title: string
  description: string
  children: ReactNode
  icon?: ReactNode
}

export default function TabContent({ title, description, children, icon }: TabContentProps) {
  return (
    <Card className="bg-white shadow-md">
      <CardHeader>
        <div className="flex items-center gap-2">
          {icon && <div className="text-blue-600">{icon}</div>}
          <div>
            <CardTitle className="text-[1.3rem] font-semibold text-gray-900">{title}</CardTitle>
            <CardDescription className="text-gray-600">{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>{children}</CardContent>
    </Card>
  )
}