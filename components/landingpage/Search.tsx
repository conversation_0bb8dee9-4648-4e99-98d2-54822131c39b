"use client"

import {useEffect, useRef, useState} from "react"
import {useRouter} from "next/navigation"
import {Input} from "@/components/ui/input"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {SearchIcon, X} from "lucide-react"
import {Label} from "@/components/ui/label"
import {Switch} from "@/components/ui/switch"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {SPECIALTIES} from "@/data/specialties"
import {CONSULTATION_TYPES} from "@/data/consultationTypes"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {
    getCABALocationsFromMedicalCenters,
    getGBALocationsFromMedicalCenters,
    getSearchLocationOptionsFromMedicalCenters
} from "@/data/locations"
import {Doctor} from "@/types/doctor"
import {MedicalCenter} from "@/types/medical-center"
import {useAuth} from "@/contexts/AuthContext"
import {User<PERSON><PERSON>} from "@/types/users"
import {getTurneraUserId} from "@/utils/userUtils";

const searchTypes = [
    {value: "especialidad", label: "Especialidad", placeholder: "Buscar por especialidad"},
    {value: "estudio", label: "Estudio", placeholder: "Buscar por tipo de estudio"},
    {value: "profesional", label: "Profesional", placeholder: "Buscar por profesional"},
    {value: "establecimiento", label: "Establecimiento", placeholder: "Buscar por establecimiento"},
]

// Helper function to normalize strings for search (remove accents)
const normalizeString = (str: string) => {
    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "")
}

// Helper function to highlight matching text
const highlightMatch = (text: string, query: string) => {
    if (!query.trim()) return <span>{text}</span>

    const normalizedText = normalizeString(text.toLowerCase())
    const normalizedQuery = normalizeString(query.toLowerCase())

    if (!normalizedText.includes(normalizedQuery)) return <span>{text}</span>

    const startIndex = normalizedText.indexOf(normalizedQuery)

    return (
        <>
            {text.substring(0, startIndex)}
            <span className="font-semibold">{text.substring(startIndex, startIndex + query.length)}</span>
            {text.substring(startIndex + query.length)}
        </>
    )
}

export default function Search() {
    const router = useRouter()
    const {currentUser, isLoading} = useAuth()

    const [searchType, setSearchType] = useState("especialidad")
    const [searchTerm, setSearchTerm] = useState("")
    const locationTermRef = useRef("Todas las ubicaciones")
    const setLocationTerm = (value: string) => {
        locationTermRef.current = value
    }
    const [selectedRegion, setSelectedRegion] = useState<"all" | "caba" | "gba">("all")
    const [selectedBarrio, setSelectedBarrio] = useState<string>("")
    const [selectedCoverage, setSelectedCoverage] = useState<string>("")
    const [selectedPlan, setSelectedPlan] = useState<string>("")
    const [noInsurance, setNoInsurance] = useState(true)
    const [showSpecialtyDropdown, setShowSpecialtyDropdown] = useState(false)
    const [showConsultationDropdown, setShowConsultationDropdown] = useState(false)
    const [showDoctorDropdown, setShowDoctorDropdown] = useState(false)
    const [showMedicalCenterDropdown, setShowMedicalCenterDropdown] = useState(false)

    // New state for doctors, medical centers, and locations
    const [allDoctors, setAllDoctors] = useState<Doctor[]>([])
    const [allMedicalCenters, setAllMedicalCenters] = useState<MedicalCenter[]>([])
    const locationsRef = useRef<{ id: string, name: string, type: string, region: string }[]>([
        {id: "all", name: "Todas las ubicaciones", type: "all", region: "all"}
    ])
    const setLOCATIONS = (value: { id: string, name: string, type: string, region: string }[]) => {
        locationsRef.current = value
    }

    const specialtySearchRef = useRef<HTMLDivElement>(null)
    const consultationSearchRef = useRef<HTMLDivElement>(null)
    const doctorSearchRef = useRef<HTMLDivElement>(null)
    const medicalCenterSearchRef = useRef<HTMLDivElement>(null)
    const searchInputRef = useRef<HTMLDivElement>(null)

    // Load static data on component mount
    useEffect(() => {
        // This needs to run only on the client side
        if (typeof window === 'undefined') return;

        try {
            // Get doctors and medical centers from storage
            const doctors: Doctor[] = []
            const medicalCenters: MedicalCenter[] = []

            setAllDoctors(doctors)
            setAllMedicalCenters(medicalCenters)

            // Get locations from medical centers
            try {
                const locationOptions = getSearchLocationOptionsFromMedicalCenters();
                setLOCATIONS(locationOptions);

                // No need to extract regions separately, we'll filter them directly in the render
            } catch (error) {
                console.error('Error loading location options:', error);
                // Set default with just the "all" option if there's an error
                setLOCATIONS([{id: "all", name: "Todas las ubicaciones", type: "all", region: "all"}]);
            }
        } catch (error) {
            console.error('Error initializing search component:', error);
            // Set default values if there's an error
            setAllDoctors([]);
            setAllMedicalCenters([]);
            setLOCATIONS([]);
        }
    }, [])


    useEffect(() => {

        if (isLoading) return;

        if (currentUser && currentUser.roles.some(user => user === UserRole.TURNERA_USER)) {
            const patients = currentUser.turneraPatientInformation;
            const currentPatient = patients && patients.length > 0 ? patients[0] : null;
            if (currentPatient) {
                console.log('Setting coverage from patient:', currentPatient);

                if (currentPatient.plan) {

                    const foundCoverage = DEFAULT_COVERAGES.find(cov =>
                        currentPatient.plan.includes(cov.name)
                    );

                    if (foundCoverage) {
                        console.log('Found coverage:', foundCoverage.name);

                        // Use setTimeout to ensure the state updates happen after any other pending updates
                        setTimeout(() => {
                            setSelectedCoverage(foundCoverage.name)
                            setNoInsurance(false)

                            // Extract plan if present (everything after the coverage name)
                            if (currentPatient.plan !== foundCoverage.name) {
                                const planPart = foundCoverage.plans.find(plan =>
                                    currentPatient.plan.includes(plan))
                                if (planPart) {
                                    console.log('Found plan:', planPart);
                                    setSelectedPlan(planPart);
                                }
                            }
                        }, 0);
                    }
                } else {
                    setTimeout(() => {
                        setSelectedCoverage("");
                        setSelectedPlan("");
                        setNoInsurance(true);
                    }, 0);
                }
            }
        } else {
            // No user or non-patient user
            setTimeout(() => {
                setSelectedCoverage("");
                setSelectedPlan("");
                setNoInsurance(true);
            }, 0);
        }
    }, [isLoading, currentUser])

    // Filter doctors based on search term
    const filteredDoctors = allDoctors.filter(doctor =>
        normalizeString(doctor.name.toLowerCase()).includes(normalizeString(searchTerm.toLowerCase()))
    )

    // Filter medical centers based on search term
    const filteredMedicalCenters = allMedicalCenters.filter(center =>
        normalizeString(center.name.toLowerCase()).includes(normalizeString(searchTerm.toLowerCase()))
    )

    // Filter specialties based on search term
    const filteredSpecialties = SPECIALTIES.filter(specialty =>
        normalizeString(specialty.toLowerCase()).includes(normalizeString(searchTerm.toLowerCase()))
    )

    // Filter consultation types based on search term
    const filteredConsultationTypes = CONSULTATION_TYPES.filter(type =>
        normalizeString(type.name.toLowerCase()).includes(normalizeString(searchTerm.toLowerCase()))
    )

    // We don't need filteredLocations since we're not using a location search dropdown

    // Handle coverage change
    const handleCoverageChange = (value: string) => {
        setSelectedCoverage(value)
        setSelectedPlan("")
    }

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (specialtySearchRef.current && !specialtySearchRef.current.contains(e.target as Node)) {
                setShowSpecialtyDropdown(false)
            }
            if (consultationSearchRef.current && !consultationSearchRef.current.contains(e.target as Node)) {
                setShowConsultationDropdown(false)
            }
            if (doctorSearchRef.current && !doctorSearchRef.current.contains(e.target as Node)) {
                setShowDoctorDropdown(false)
            }
            if (medicalCenterSearchRef.current && !medicalCenterSearchRef.current.contains(e.target as Node)) {
                setShowMedicalCenterDropdown(false)
            }
            // We don't need to handle location dropdown anymore
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()

        // Build coverage parameters
        let coverageParams = ''
        if (noInsurance || (!selectedCoverage && !selectedPlan)) {
            coverageParams = '&noCoverage=true'
        } else if (selectedCoverage) {
            coverageParams = `&coverage=${encodeURIComponent(selectedCoverage)}`
            if (selectedPlan) {
                coverageParams += `&plan=${encodeURIComponent(selectedPlan)}`
            }
        }

        // Build zone parameter based on selected region and barrio
        let zoneParam = '&loc=all';

        if (selectedRegion !== 'all') {
            if (selectedBarrio && selectedBarrio !== 'all') {
                // If a specific barrio is selected, use its ID
                zoneParam = `&loc=${selectedBarrio}`;
            } else {
                // If only the region is selected, use the region ID
                zoneParam = `&loc=${selectedRegion}`;
            }
        }

        if (searchType === "profesional") {
            // Find the doctor by name to get the ID
            const selectedDoctor = allDoctors.find(doctor =>
                doctor.name.toLowerCase() === searchTerm.toLowerCase()
            )

            if (selectedDoctor) {
                // Redirect to the doctor profile page with the correct ID
                // Don't include coverage parameters in URL for doctor pages
                router.push(`/plataforma/reservar/d/${selectedDoctor.id}`)
            } else {
                // If no exact match, navigate to search results with the search term as a query
                // Include coverage parameters for search results, not direct doctor page
                router.push(`/plataforma/buscar/profesional?q=${encodeURIComponent(searchTerm)}${coverageParams}`)
            }
        } else if (searchType === "establecimiento") {
            // Find the medical center by name to get the ID
            const selectedCenter = allMedicalCenters.find(center =>
                center.name.toLowerCase() === searchTerm.toLowerCase()
            )

            if (selectedCenter) {
                // Redirect to the medical center page with the correct ID
                // Include coverage parameters in URL for medical center pages
                router.push(`/plataforma/reservar/cm/${selectedCenter.id}?${coverageParams.substring(1)}`)
            } else {
                // Since we removed the search results page for medical centers,
                // just go to the home page with a more specific search term message
                alert("No se encontró el establecimiento. Por favor intente con otro término.")
            }
        } else if (searchType === "especialidad") {
            // Redirect to specialty search results
            // Use source=search instead of medicalCenter for subsequent booking
            router.push(`/plataforma/buscar/especialidad?q=${encodeURIComponent(searchTerm)}${zoneParam}${coverageParams}&source=search&searchType=especialidad`)
        } else if (searchType === "estudio") {
            // Redirect to consultation type search results
            // Use source=search instead of medicalCenter for subsequent booking
            router.push(`/plataforma/buscar/estudio?q=${encodeURIComponent(searchTerm)}${zoneParam}${coverageParams}&source=search&searchType=estudio`)
        }
    }

    // Check if search button should be disabled
    const isSearchButtonDisabled = !searchTerm.trim() ||
        // If Sin Cobertura is OFF, require both coverage and plan
        (!noInsurance && (selectedCoverage === "" || selectedPlan === ""))

    return (
        <section id="buscar" className="py-8 md:pb-14 md:pt-20 bg-blue-50 scroll-mt-20 md:scroll-mt-24">
            <div className="container mx-auto px-6 md:px-8 max-w-7xl">
                <h2 className="text-4xl md:text-3xl font-recoleta text-center mb-6 px-2">
                    Buscar <span className="font-bold">turnos disponibles</span> por:
                </h2>
                <form className="max-w-4xl mx-auto border-black border-2 rounded-xl p-8 md:p-6" onSubmit={handleSubmit}>
                    <div className="flex flex-wrap justify-center gap-2 mb-6 px-4">
                        {searchTypes.map((type) => (
                            <button
                                key={type.value}
                                type="button"
                                onClick={() => {
                                    setSearchType(type.value)
                                    setSearchTerm("")
                                    setShowSpecialtyDropdown(false)
                                    setShowConsultationDropdown(false)
                                    setShowDoctorDropdown(false)
                                    setShowMedicalCenterDropdown(false)
                                }}
                                className={`px-4 py-2 text-base md:text-sm transition-colors rounded-[12px] ${
                                    searchType === type.value
                                        ? "bg-blue-600 text-white"
                                        : "bg-white text-gray-600 hover:bg-gray-100 border border-gray-300"
                                }`}
                            >
                                {type.label}
                            </button>
                        ))}
                    </div>
                    <div className="flex flex-col gap-4 mb-4">
                        {/* SPECIALTY/CONSULTATION/PROFESSIONAL/ESTABLISHMENT SEARCH INPUT */}
                        <div
                            ref={
                                searchType === "especialidad" ? specialtySearchRef :
                                    searchType === "estudio" ? consultationSearchRef :
                                        searchType === "profesional" ? doctorSearchRef :
                                            searchType === "establecimiento" ? medicalCenterSearchRef : null
                            }
                            className="relative"
                        >
                            <div className="relative" ref={searchInputRef}>
                                <Input
                                    type="text"
                                    placeholder={searchTypes.find((t) => t.value === searchType)?.placeholder}
                                    value={searchTerm}
                                    onChange={(e) => {
                                        setSearchTerm(e.target.value)
                                        if (searchType === "especialidad") {
                                            setShowSpecialtyDropdown(true)
                                        } else if (searchType === "estudio") {
                                            setShowConsultationDropdown(true)
                                        } else if (searchType === "profesional") {
                                            setShowDoctorDropdown(true)
                                        } else if (searchType === "establecimiento") {
                                            setShowMedicalCenterDropdown(true)
                                        }
                                    }}
                                    onFocus={() => {
                                        if (searchType === "especialidad") {
                                            setShowSpecialtyDropdown(true)
                                        } else if (searchType === "estudio") {
                                            setShowConsultationDropdown(true)
                                        } else if (searchType === "profesional") {
                                            setShowDoctorDropdown(true)
                                        } else if (searchType === "establecimiento") {
                                            setShowMedicalCenterDropdown(true)
                                        }
                                    }}
                                    className="w-full text-sm md:text-sm rounded-full border-gray-300 bg-white py-5 md:py-4 placeholder:text-sm"
                                />
                                {searchTerm && (
                                    <X
                                        className="absolute right-12 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 cursor-pointer hover:text-gray-700"
                                        onClick={() => {
                                            setSearchTerm("")
                                            if (searchType === "especialidad") {
                                                setShowSpecialtyDropdown(true)
                                            } else if (searchType === "estudio") {
                                                setShowConsultationDropdown(true)
                                            } else if (searchType === "profesional") {
                                                setShowDoctorDropdown(true)
                                            } else if (searchType === "establecimiento") {
                                                setShowMedicalCenterDropdown(true)
                                            }
                                        }}
                                    />
                                )}
                                <SearchIcon
                                    className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500"/>
                            </div>

                            {/* Specialty dropdown */}
                            {searchType === "especialidad" && showSpecialtyDropdown && (
                                <div
                                    className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg">
                                    <div className="max-h-[15rem] overflow-auto">
                                        {filteredSpecialties.length > 0 ? (
                                            filteredSpecialties.map((specialty) => (
                                                <div
                                                    key={specialty}
                                                    className="py-[0.4rem] px-[0.75rem] text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                                                    onClick={() => {
                                                        setSearchTerm(specialty)
                                                        setShowSpecialtyDropdown(false)
                                                    }}
                                                >
                                                    {highlightMatch(specialty, searchTerm)}
                                                </div>
                                            ))
                                        ) : (
                                            <div className="py-[0.4rem] px-[0.75rem] text-sm text-gray-500">
                                                No se encontraron especialidades
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Consultation type dropdown */}
                            {searchType === "estudio" && showConsultationDropdown && (
                                <div
                                    className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg">
                                    <div className="max-h-[15rem] overflow-auto">
                                        {filteredConsultationTypes.length > 0 ? (
                                            filteredConsultationTypes.map((type) => (
                                                <div
                                                    key={type.name}
                                                    className="py-[0.4rem] px-[0.75rem] text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                                                    onClick={() => {
                                                        setSearchTerm(type.name)
                                                        setShowConsultationDropdown(false)
                                                    }}
                                                >
                                                    {highlightMatch(type.name, searchTerm)}
                                                </div>
                                            ))
                                        ) : (
                                            <div className="py-[0.4rem] px-[0.75rem] text-sm text-gray-500">
                                                No se encontraron tipos de consulta
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Doctor dropdown */}
                            {searchType === "profesional" && showDoctorDropdown && (
                                <div
                                    className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg">
                                    <div className="max-h-[15rem] overflow-auto">
                                        {filteredDoctors.length > 0 ? (
                                            filteredDoctors.map((doctor) => (
                                                <div
                                                    key={doctor.id}
                                                    className="py-[0.4rem] px-[0.75rem] text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                                                    onClick={() => {
                                                        setSearchTerm(doctor.name)
                                                        setShowDoctorDropdown(false)
                                                    }}
                                                >
                                                    <div className="flex flex-col">
                                                        <span>{highlightMatch(doctor.name, searchTerm)}</span>
                                                        {doctor.specialties && doctor.specialties.length > 0 && (
                                                            <span
                                                                className="text-xs text-gray-500">{doctor.specialties.join(", ")}</span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="py-[0.4rem] px-[0.75rem] text-sm text-gray-500">
                                                No se encontraron profesionales
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* Medical center dropdown */}
                            {searchType === "establecimiento" && showMedicalCenterDropdown && (
                                <div
                                    className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg">
                                    <div className="max-h-[15rem] overflow-auto">
                                        {filteredMedicalCenters.length > 0 ? (
                                            filteredMedicalCenters.map((center) => (
                                                <div
                                                    key={center.id}
                                                    className="py-[0.4rem] px-[0.75rem] text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                                                    onClick={() => {
                                                        setSearchTerm(center.name)
                                                        setShowMedicalCenterDropdown(false)
                                                    }}
                                                >
                                                    <div className="flex flex-col">
                                                        <span>{highlightMatch(center.name, searchTerm)}</span>
                                                        {center.address && (
                                                            <span
                                                                className="text-xs text-gray-500">{center.address}</span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="py-[0.4rem] px-[0.75rem] text-sm text-gray-500">
                                                No se encontraron establecimientos
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* LOCATION INPUT (not shown for profesional and establecimiento) */}
                        {searchType !== "profesional" && searchType !== "establecimiento" && (
                            <div className="space-y-4">
                                {/* Main region selection */}
                                <div className="relative">
                                    <Select
                                        value={selectedRegion}
                                        onValueChange={(value: "all" | "caba" | "gba") => {
                                            setSelectedRegion(value);

                                            // Set default barrio to "all" when a region is selected
                                            if (value !== "all") {
                                                setSelectedBarrio("all");
                                            } else {
                                                setSelectedBarrio("");
                                            }

                                            if (value === "all") {
                                                setLocationTerm("Todas las ubicaciones");
                                            } else if (value === "caba") {
                                                setLocationTerm("Capital Federal");
                                            } else if (value === "gba") {
                                                setLocationTerm("Gran Buenos Aires");
                                            }
                                        }}
                                    >
                                        <SelectTrigger
                                            className="w-full text-sm md:text-sm rounded-full border-gray-300 bg-white py-5 md:py-4">
                                            <SelectValue placeholder="Ubicación"/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            {locationsRef.current.filter(location =>
                                                location.type === 'all' || location.type === 'region'
                                            ).map((region) => (
                                                <SelectItem key={region.id} value={region.id}>
                                                    {region.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Barrio selection - only shown when CABA or GBA is selected */}
                                {(selectedRegion === "caba" || selectedRegion === "gba") && (
                                    <div className="relative">
                                        <Select
                                            value={selectedBarrio}
                                            onValueChange={(value) => {
                                                setSelectedBarrio(value);

                                                if (value === "all") {
                                                    // Keep the main region name
                                                    if (selectedRegion === "caba") {
                                                        setLocationTerm("Capital Federal");
                                                    } else {
                                                        setLocationTerm("Gran Buenos Aires");
                                                    }
                                                } else {
                                                    // Set to the specific barrio name
                                                    const locations = selectedRegion === "caba"
                                                        ? getCABALocationsFromMedicalCenters()
                                                        : getGBALocationsFromMedicalCenters();
                                                    const location = locations.find(loc => loc.id === value);
                                                    if (location) {
                                                        setLocationTerm(location.name);
                                                    }
                                                }
                                            }}
                                        >
                                            <SelectTrigger
                                                className="w-full text-sm md:text-sm rounded-full border-gray-300 bg-white py-5 md:py-4">
                                                <SelectValue
                                                    placeholder={selectedRegion === "caba" ? "Barrio" : "Municipio"}/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">
                                                    {selectedRegion === "caba" ? "Todos los barrios" : "Todos los municipios"}
                                                </SelectItem>
                                                {(selectedRegion === "caba"
                                                        ? getCABALocationsFromMedicalCenters()
                                                        : getGBALocationsFromMedicalCenters()
                                                ).map((location) => (
                                                    <SelectItem key={location.id} value={location.id}>
                                                        {location.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* COVERAGE SELECTION */}
                    <div className="flex flex-col gap-4 mb-4">
                        <div>
                            <Select
                                key={`coverage-${getTurneraUserId(currentUser) || 'no-user'}-${isLoading ? 'loading' : 'loaded'}`}
                                disabled={noInsurance}
                                value={selectedCoverage}
                                onValueChange={handleCoverageChange}
                            >
                                <SelectTrigger
                                    className="w-full text-sm md:text-sm rounded-[12px] border-gray-300 bg-white py-5 md:py-4">
                                    <SelectValue placeholder="Seleccionar Obra Social/Prepaga"/>
                                </SelectTrigger>
                                <SelectContent>
                                    {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map((coverage) => (
                                        <SelectItem key={coverage.id} value={coverage.name}
                                                    className="text-sm md:text-sm">
                                            {coverage.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Select
                                key={`plan-${getTurneraUserId(currentUser) || 'no-user'}-${selectedCoverage}-${isLoading ? 'loading' : 'loaded'}`}
                                disabled={!selectedCoverage || noInsurance}
                                value={selectedPlan}
                                onValueChange={setSelectedPlan}
                            >
                                <SelectTrigger
                                    className="w-full text-sm md:text-sm rounded-[12px] border-gray-300 bg-white py-5 md:py-4">
                                    <SelectValue placeholder="Seleccionar Plan"/>
                                </SelectTrigger>
                                <SelectContent>
                                    {selectedCoverage && DEFAULT_COVERAGES.find(c => c.name === selectedCoverage)?.plans.map((plan) => (
                                        <SelectItem key={plan} value={plan} className="text-sm md:text-sm">
                                            {plan}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Switch
                                id="no-insurance"
                                checked={noInsurance}
                                onCheckedChange={(checked) => {
                                    setNoInsurance(checked)
                                    if (checked) {
                                        setSelectedCoverage("")
                                        setSelectedPlan("")
                                    }
                                }}
                                className="rounded-full"
                            />
                            <Label htmlFor="no-insurance" className="text-sm md:text-sm">
                                Sin Cobertura
                            </Label>
                        </div>
                    </div>
                    <div className="flex justify-center mt-6">
                        <Button
                            type="submit"
                            className="w-full rounded-[12px] bg-blue-600 hover:bg-blue-700 text-white text-base md:text-base py-5 md:py-4"
                            disabled={isSearchButtonDisabled}
                        >
                            <SearchIcon className="mr-2 h-4 w-4"/> Buscar turnos
                        </Button>
                    </div>
                </form>
            </div>
        </section>
    )
}

