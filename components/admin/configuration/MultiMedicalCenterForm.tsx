"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Trash2, Save, X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Doctor } from "@/types/doctor"
import { MedicalCenter } from "@/types/medical-center"
import { toast } from "react-toastify"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { generateMedicalCenterId, generateCustomId } from "@/utils/idGenerator"

interface MedicalCenterFormData {
  id: string
  name: string
  doctorIds: string[]
  isValid: boolean
}

// Simple table components
const Table = ({ className, children }: { className?: string, children: React.ReactNode }) => (
  <table className={`w-full ${className || ''}`}>{children}</table>
)

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead className="bg-gray-50">{children}</thead>
)

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody>{children}</tbody>
)

const TableRow = ({ className, children }: { className?: string, children: React.ReactNode }) => (
  <tr className={`border-b hover:bg-gray-50 ${className || ''}`}>{children}</tr>
)

const TableHead = ({ className, children }: { className?: string, children: React.ReactNode }) => (
  <th className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${className || ''}`}>{children}</th>
)

const TableCell = ({ className, children }: { className?: string, children: React.ReactNode }) => (
  <td className={`px-4 py-3 ${className || ''}`}>{children}</td>
)

export function MultiMedicalCenterForm() {
  const [medicalCenters, setMedicalCenters] = useState<MedicalCenterFormData[]>([
    {
      id: generateCustomId('form'),
      name: "Centro Médico Uno",
      doctorIds: [],
      isValid: true,
    },
    {
      id: generateCustomId('form'),
      name: "Centro Médico Dos",
      doctorIds: [],
      isValid: true,
    },
    {
      id: generateCustomId('form'),
      name: "Centro Médico Tres",
      doctorIds: [],
      isValid: true,
    },
  ])
  const [allDoctors, setAllDoctors] = useState<Doctor[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Load all doctors on component mount
  useEffect(() => {
    const loadDoctors = async () => {
      try {
        setIsLoading(true)
        const doctors = storage.getAllDoctorsInSystem()
        setAllDoctors(doctors)
      } catch (error) {
        console.error("Error loading doctors:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadDoctors()
  }, [])

  function createEmptyMedicalCenterForm(): MedicalCenterFormData {
    return {
      id: generateCustomId('form'),
      name: "",
      doctorIds: [],
      isValid: false,
    }
  }

  // Calculate valid medical centers - derived during render
  const validMedicalCenters = medicalCenters.filter(center =>
    center.name.trim() !== ""
  )

  // Add a new row
  const handleAddRow = () => {
    setMedicalCenters([...medicalCenters, createEmptyMedicalCenterForm()])
  }

  // Remove a row
  const handleRemoveRow = (idToRemove: string) => {
    setMedicalCenters(medicalCenters.filter(center => center.id !== idToRemove))
  }

  // Update medical center field
  const handleMedicalCenterChange = (id: string, field: keyof MedicalCenterFormData, value: string | string[]) => {
    setMedicalCenters(
      medicalCenters.map(center => {
        if (center.id !== id) return center

        const updatedCenter = { ...center, [field]: value }

        // Update the isValid flag right when the center changes
        updatedCenter.isValid = updatedCenter.name.trim() !== ""

        return updatedCenter
      })
    )
  }

  // Add a doctor to a medical center
  const handleAddDoctor = (centerId: string, doctorId: string) => {
    if (!doctorId) return

    setMedicalCenters(
      medicalCenters.map(center => {
        if (center.id !== centerId || center.doctorIds.includes(doctorId)) return center

        return {
          ...center,
          doctorIds: [...center.doctorIds, doctorId],
          isValid: center.name.trim() !== ""
        }
      })
    )
  }

  // Remove a doctor from a medical center
  const handleRemoveDoctor = (centerId: string, doctorId: string) => {
    setMedicalCenters(
      medicalCenters.map(center => {
        if (center.id !== centerId) return center

        return {
          ...center,
          doctorIds: center.doctorIds.filter(id => id !== doctorId),
          isValid: center.name.trim() !== ""
        }
      })
    )
  }

  // Submit all valid medical centers
  const handleSubmit = async () => {
    if (validMedicalCenters.length === 0) {
      toast.error("No hay Establecimientos válidos para crear")
      return
    }

    setIsSubmitting(true)

    try {
      // Get existing medical centers
      const existingCenters = storage.getMedicalCenters()
      const newCenters: MedicalCenter[] = []

      // Create a mapping of center IDs to their doctor IDs for later use
      const centerDoctorMap: Record<string, string[]> = {}

      // Create each medical center
      for (const centerForm of validMedicalCenters) {
        // Create the medical center with default values
        const newCenter: MedicalCenter = {
          id: generateMedicalCenterId(),
          name: centerForm.name,
          doctors: [], // Initialize with empty doctors array
          workingDays: {
            "0": { enabled: false, hours: [] },
            "1": { enabled: true, hours: [{ start: "09:00", end: "18:00" }] },
            "2": { enabled: true, hours: [{ start: "09:00", end: "18:00" }] },
            "3": { enabled: true, hours: [{ start: "09:00", end: "18:00" }] },
            "4": { enabled: true, hours: [{ start: "09:00", end: "18:00" }] },
            "5": { enabled: true, hours: [{ start: "09:00", end: "18:00" }] },
            "6": { enabled: false, hours: [] },
          },
          openingHours: {
            monday: { start: '09:00', end: '18:00', enabled: true },
            tuesday: { start: '09:00', end: '18:00', enabled: true },
            wednesday: { start: '09:00', end: '18:00', enabled: true },
            thursday: { start: '09:00', end: '18:00', enabled: true },
            friday: { start: '09:00', end: '18:00', enabled: true },
            saturday: { start: '00:00', end: '00:00', enabled: false },
            sunday: { start: '00:00', end: '00:00', enabled: false }
          },
          acceptedCoverages: ["8"], // Sin Cobertura by default
          address: "",
          phone: "",
          email: "",
        }

        // Store the doctor IDs in our mapping
        centerDoctorMap[newCenter.id] = [...centerForm.doctorIds]

        newCenters.push(newCenter)
        console.log(`Created medical center: ${newCenter.id} - ${newCenter.name}`)
      }

      // First save all medical centers to storage
      storage.saveMedicalCenters([...existingCenters, ...newCenters])

      // After saving centers, now add doctors to each center
      for (const center of newCenters) {
        const doctorIds = centerDoctorMap[center.id] || []

        // Add each assigned doctor to this medical center
        for (const doctorId of doctorIds) {
          const success = storage.addDoctorToMedicalCenter(doctorId, center.id)
          if (!success) {
            console.warn(`Failed to add doctor ${doctorId} to medical center ${center.id}`)
          }
        }
      }

      toast.success(`${validMedicalCenters.length} ${validMedicalCenters.length === 1 ? 'establecimiento creado' : 'Establecimientos creados'} correctamente`)

      // Reset the form with a single empty medical center
      setMedicalCenters([createEmptyMedicalCenterForm()])
    } catch (error) {
      console.error("Error creating medical centers:", error)
      toast.error("Error al crear Establecimientos")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get doctor name by ID
  const getDoctorName = (doctorId: string): string => {
    const doctor = allDoctors.find(d => d.id === doctorId)
    return doctor ? doctor.name : 'Doctor no encontrado'
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Crear Múltiples Establecimientos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <Button variant="outline" size="sm" onClick={handleAddRow} className="mr-2">
            <Plus className="h-4 w-4 mr-2" />
            Añadir Fila
          </Button>
          <Button
            size="sm"
            onClick={handleSubmit}
            disabled={isSubmitting || medicalCenters.every(c => !c.isValid) || isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "Guardando..." : "Guardar Establecimientos"}
          </Button>
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px]">Nombre del Establecimiento</TableHead>
                <TableHead>Profesionales</TableHead>
                <TableHead className="w-[100px]">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {medicalCenters.map((center) => (
                <TableRow key={center.id}>
                  <TableCell>
                    <Input
                      placeholder="Nombre del establecimiento"
                      value={center.name}
                      onChange={(e) => handleMedicalCenterChange(center.id, "name", e.target.value)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-1 mb-1">
                        {center.doctorIds.map((doctorId) => (
                          <Badge key={doctorId} className="flex items-center gap-1">
                            {getDoctorName(doctorId)}
                            <X
                              className="h-3 w-3 cursor-pointer"
                              onClick={() => handleRemoveDoctor(center.id, doctorId)}
                            />
                          </Badge>
                        ))}
                      </div>
                      <div className="flex">
                        <Select
                          onValueChange={(value) => handleAddDoctor(center.id, value)}
                          disabled={isLoading || allDoctors.length === 0}
                        >
                          <SelectTrigger className="w-full mr-2">
                            <SelectValue placeholder="Seleccionar doctor" />
                          </SelectTrigger>
                          <SelectContent>
                            {allDoctors
                              .filter(doctor => !center.doctorIds.includes(doctor.id))
                              .map((doctor) => (
                                <SelectItem key={doctor.id} value={doctor.id}>
                                  {doctor.name} ({doctor.specialties.join(', ')})
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleRemoveRow(center.id)}
                      disabled={medicalCenters.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}