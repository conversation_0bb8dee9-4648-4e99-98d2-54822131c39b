import type { Metadata } from 'next'
import { ToastContainer } from "react-toastify"
import "react-toastify/dist/ReactToastify.css"
import { AppProvider } from "@/contexts/AppContext"
import { ToastProvider } from '@/components/ui/toast'

export const metadata: Metadata = {
  title: "Admin Dashboard | Turnera",
  description: "Administration dashboard for Turnera platform",
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <AppProvider>
      <ToastProvider>
        {children}
      </ToastProvider>
      <ToastContainer />
    </AppProvider>
  )
} 