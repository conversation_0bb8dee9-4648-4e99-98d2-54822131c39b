"use client"

import { use<PERSON>ffe<PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>, useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { SearchIcon, X } from "lucide-react"
// removed Switch/Label; headings use native labels and coverage uses a single progressive dropdown
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ChevronLeft } from "lucide-react"
import { toast } from "sonner"
import { SPECIALTIES } from "@/data/specialties"
import { CONSULTATION_TYPES } from "@/data/consultationTypes"
import { DEFAULT_COVERAGES } from "@/data/coverages"
import { getCABALocationsFromMedicalCenters, getGBALocationsFromMedicalCenters, getSearchLocationOptionsFromMedicalCenters } from "@/data/locations"
import { Doctor } from "@/types/doctor"
import { Medical<PERSON>enter } from "@/types/medical-center"
import { useAuth } from "@/contexts/AuthContext"
import { UserRole } from "@/types/users"
import { getTurneraUserId } from "@/utils/userUtils"

const searchTypes = [
  { value: "especialidad", label: "Especialidad", placeholder: "Ej: Pediatría, Dermatología" },
  { value: "estudio", label: "Estudio", placeholder: "Ej: Resonancia magnética, Radiografía" },
  { value: "profesional", label: "Profesional", placeholder: "Ej: Cecilia Grierson, René Favaloro" },
  { value: "establecimiento", label: "Establecimiento", placeholder: "Ej: Centro Médico Verdi, Clínica San..." },
]

const normalizeString = (str: string) => str.normalize("NFD").replace(/[\u0300-\u036f]/g, "")

const highlightMatch = (text: string, query: string) => {
  if (!query.trim()) return <span>{text}</span>
  const normalizedText = normalizeString(text.toLowerCase())
  const normalizedQuery = normalizeString(query.toLowerCase())
  if (!normalizedText.includes(normalizedQuery)) return <span>{text}</span>
  const startIndex = normalizedText.indexOf(normalizedQuery)
  return (
    <>
      {text.substring(0, startIndex)}
      <span className="font-semibold">{text.substring(startIndex, startIndex + query.length)}</span>
      {text.substring(startIndex + query.length)}
    </>
  )
}

interface UnifiedSearchProps {
  onClose?: () => void
  initialCoverage?: string
  initialPlan?: string
}

export default function UnifiedSearch({ onClose, initialCoverage, initialPlan }: UnifiedSearchProps) {
  const router = useRouter()
  const { currentUser, isLoading } = useAuth()

  const [searchType, setSearchType] = useState("especialidad")
  const [searchTerm, setSearchTerm] = useState("")

  const [selectedRegion, setSelectedRegion] = useState<"all" | "caba" | "gba">("all")
  const [selectedBarrio, setSelectedBarrio] = useState<string>("")
  const [locationStage, setLocationStage] = useState<"region" | "barrio">("region")
  const [locationOpen, setLocationOpen] = useState(false)
  const [locationSearch, setLocationSearch] = useState("")
  const [locationActiveIndex, setLocationActiveIndex] = useState(0)
  const locationSearchInputRef = useRef<HTMLInputElement | null>(null)
  const [selectedCoverage, setSelectedCoverage] = useState<string>("Sin Cobertura")
  const [selectedPlan, setSelectedPlan] = useState<string>("")
  const [coverageStage, setCoverageStage] = useState<"coverage" | "plan">("coverage")
  const [coverageOpen, setCoverageOpen] = useState(false)
  const [coverageSearch, setCoverageSearch] = useState("")
  const [planSearch, setPlanSearch] = useState("")
  const [coverageActiveIndex, setCoverageActiveIndex] = useState(0)
  const [planActiveIndex, setPlanActiveIndex] = useState(0)
  const coverageSearchInputRef = useRef<HTMLInputElement | null>(null)
  const planSearchInputRef = useRef<HTMLInputElement | null>(null)

  const [allDoctors, setAllDoctors] = useState<Doctor[]>([])
  const [allMedicalCenters, setAllMedicalCenters] = useState<MedicalCenter[]>([])
  const locationsRef = useRef<{ id: string; name: string; type: string; region: string }[]>([
    { id: "all", name: "Todas las ubicaciones", type: "all", region: "all" },
  ])

  const specialtySearchRef = useRef<HTMLDivElement>(null)
  const consultationSearchRef = useRef<HTMLDivElement>(null)
  const doctorSearchRef = useRef<HTMLDivElement>(null)
  const medicalCenterSearchRef = useRef<HTMLDivElement>(null)

  const [showSpecialtyDropdown, setShowSpecialtyDropdown] = useState(false)
  const [showConsultationDropdown, setShowConsultationDropdown] = useState(false)
  const [showDoctorDropdown, setShowDoctorDropdown] = useState(false)
  const [showMedicalCenterDropdown, setShowMedicalCenterDropdown] = useState(false)

  // Mobile UI state
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false)
  const [mobileLocationDialogOpen, setMobileLocationDialogOpen] = useState(false)
  const [mobileCoverageDialogOpen, setMobileCoverageDialogOpen] = useState(false)

  // Debounced search term to optimize filtering
  const [debouncedTerm, setDebouncedTerm] = useState("")

  // Recent searches (local)
  const [recentSearches, setRecentSearches] = useState<string[]>([])

  useEffect(() => {
    if (typeof window === "undefined") return
    try {
      const doctors: Doctor[] = []
      const medicalCenters: MedicalCenter[] = []
      setAllDoctors(doctors)
      setAllMedicalCenters(medicalCenters)
      try {
        const locationOptions = getSearchLocationOptionsFromMedicalCenters()
        locationsRef.current = locationOptions
      } catch (error) {
        console.error("Error loading location options:", error)
        locationsRef.current = [{ id: "all", name: "Todas las ubicaciones", type: "all", region: "all" }]
      }
    } catch (error) {
      console.error("Error initializing search component:", error)
      setAllDoctors([])
      setAllMedicalCenters([])
      locationsRef.current = []
    }
  }, [])

  // Load persisted lightweight defaults
  useEffect(() => {
    try {
      const lastRegion = localStorage.getItem("unifiedSearch.selectedRegion") as "all" | "caba" | "gba" | null
      const lastBarrio = localStorage.getItem("unifiedSearch.selectedBarrio")
      const lastType = localStorage.getItem("unifiedSearch.searchType")
      const recent = localStorage.getItem("unifiedSearch.recentSearches")
      if (lastRegion) setSelectedRegion(lastRegion)
      if (lastBarrio) setSelectedBarrio(lastBarrio)
      if (lastType) setSearchType(lastType)
      if (recent) setRecentSearches(JSON.parse(recent))
    } catch {}
  }, [])

  useEffect(() => {
    try {
      localStorage.setItem("unifiedSearch.searchType", searchType)
      localStorage.setItem("unifiedSearch.selectedRegion", selectedRegion)
      localStorage.setItem("unifiedSearch.selectedBarrio", selectedBarrio)
    } catch {}
  }, [searchType, selectedRegion, selectedBarrio])

  // Debounce the search term to reduce re-renders and CPU on mobile
  useEffect(() => {
    const t = setTimeout(() => setDebouncedTerm(searchTerm), 200)
    return () => clearTimeout(t)
  }, [searchTerm])

  useEffect(() => {
    // If search page provides an initial coverage, prefer that and skip auth-based defaulting
    if (initialCoverage && initialCoverage.length > 0) return
    if (isLoading) return
    if (currentUser && currentUser.roles.some((user) => user === UserRole.TURNERA_USER)) {
      const patients = currentUser.turneraPatientInformation
      const currentPatient = patients && patients.length > 0 ? patients[0] : null
      if (currentPatient) {
        if (currentPatient.plan) {
          const foundCoverage = DEFAULT_COVERAGES.find((cov) => currentPatient.plan.includes(cov.name))
          if (foundCoverage) {
            setTimeout(() => {
              setSelectedCoverage(foundCoverage.name)
              if (currentPatient.plan !== foundCoverage.name) {
                const planPart = foundCoverage.plans.find((plan) => currentPatient.plan.includes(plan))
                if (planPart) {
                  setSelectedPlan(planPart)
                }
              }
            }, 0)
          }
        } else {
          setTimeout(() => {
            setSelectedCoverage("Sin Cobertura")
            setSelectedPlan("")
          }, 0)
        }
      }
    } else {
      setTimeout(() => {
        setSelectedCoverage("Sin Cobertura")
        setSelectedPlan("")
      }, 0)
    }
  }, [isLoading, currentUser, initialCoverage])

  // Override selection when initial values are provided (search pages)
  useEffect(() => {
    if (!initialCoverage) return
    setSelectedCoverage(initialCoverage)
    setSelectedPlan(initialPlan ?? "")
  }, [initialCoverage, initialPlan])

  const filteredDoctors = useMemo(
    () =>
      allDoctors.filter((doctor) =>
        normalizeString(doctor.name.toLowerCase()).includes(normalizeString(debouncedTerm.toLowerCase()))
      ),
    [allDoctors, debouncedTerm]
  )
  const filteredMedicalCenters = useMemo(
    () =>
      allMedicalCenters.filter((center) =>
        normalizeString(center.name.toLowerCase()).includes(normalizeString(debouncedTerm.toLowerCase()))
      ),
    [allMedicalCenters, debouncedTerm]
  )
  const filteredSpecialties = useMemo(
    () =>
      SPECIALTIES.filter((specialty) =>
        normalizeString(specialty.toLowerCase()).includes(normalizeString(debouncedTerm.toLowerCase()))
      ),
    [debouncedTerm]
  )
  const filteredConsultationTypes = useMemo(
    () =>
      CONSULTATION_TYPES.filter((type) =>
        normalizeString(type.name.toLowerCase()).includes(normalizeString(debouncedTerm.toLowerCase()))
      ),
    [debouncedTerm]
  )

  const handleCoverageChange = (value: string) => {
    setSelectedCoverage(value)
    setSelectedPlan("")
  }

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (specialtySearchRef.current && !specialtySearchRef.current.contains(e.target as Node)) setShowSpecialtyDropdown(false)
      if (consultationSearchRef.current && !consultationSearchRef.current.contains(e.target as Node)) setShowConsultationDropdown(false)
      if (doctorSearchRef.current && !doctorSearchRef.current.contains(e.target as Node)) setShowDoctorDropdown(false)
      if (medicalCenterSearchRef.current && !medicalCenterSearchRef.current.contains(e.target as Node)) setShowMedicalCenterDropdown(false)
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    ;(document.activeElement as HTMLElement | null)?.blur()
    setShowSpecialtyDropdown(false)
    setShowConsultationDropdown(false)
    setShowDoctorDropdown(false)
    setShowMedicalCenterDropdown(false)

    let coverageParams = ""
    if (selectedCoverage === "Sin Cobertura") {
      coverageParams = "&noCoverage=true"
    } else if (selectedCoverage) {
      coverageParams = `&coverage=${encodeURIComponent(selectedCoverage)}`
      if (selectedPlan) {
        coverageParams += `&plan=${encodeURIComponent(selectedPlan)}`
      }
    }

    let zoneParam = "&loc=all"
    if (selectedRegion !== "all") {
      if (selectedBarrio && selectedBarrio !== "all") {
        zoneParam = `&loc=${selectedBarrio}`
      } else {
        zoneParam = `&loc=${selectedRegion}`
      }
    }

    if (searchType === "profesional") {
      const selectedDoctor = allDoctors.find((doctor) => doctor.name.toLowerCase() === searchTerm.toLowerCase())
      if (selectedDoctor) {
        router.push(`/plataforma/reservar/d/${selectedDoctor.id}`)
      } else {
        router.push(`/plataforma/buscar/profesional?q=${encodeURIComponent(searchTerm)}${coverageParams}`)
      }
    } else if (searchType === "establecimiento") {
      const selectedCenter = allMedicalCenters.find((center) => center.name.toLowerCase() === searchTerm.toLowerCase())
      if (selectedCenter) {
        router.push(`/plataforma/reservar/cm/${selectedCenter.id}?${coverageParams.substring(1)}`)
      } else {
        toast.error("No se encontró el establecimiento. Prueba otro nombre o filtra por zona/cobertura.")
      }
    } else if (searchType === "especialidad") {
      router.push(`/plataforma/buscar/especialidad?q=${encodeURIComponent(searchTerm)}${zoneParam}${coverageParams}&source=search&searchType=especialidad`)
    } else if (searchType === "estudio") {
      router.push(`/plataforma/buscar/estudio?q=${encodeURIComponent(searchTerm)}${zoneParam}${coverageParams}&source=search&searchType=estudio`)
    }

    // Save recent searches (keep last 6 unique, most recent first)
    try {
      const term = searchTerm.trim()
      if (term) {
        const next = [term, ...recentSearches.filter((t) => t.toLowerCase() !== term.toLowerCase())].slice(0, 6)
        setRecentSearches(next)
        localStorage.setItem("unifiedSearch.recentSearches", JSON.stringify(next))
      }
    } catch {}

    // Close parent overlay if provided
    try {
      onClose?.()
    } catch {}
  }

  const isSearchButtonDisabled = !searchTerm.trim()

  return (
    <section id="buscar" className="mt-10 md:mt-16 mb-6 md:mb-12 scroll-mt-20 md:scroll-mt-24">
      <div className="max-w-7xl mx-auto px-5 md:px-6">
        {/* Unified bar with integrated toggles */}
        <form
          onSubmit={handleSubmit}
          className="relative z-40 isolate rounded-[18px] bg-white/90 ring-1 ring-gray-200 backdrop-blur shadow-sm px-5 py-5 md:p-6"
        >
          {/* Toggle group inside bar */}
          <div className="grid grid-cols-2 gap-2 mb-3 md:flex md:flex-wrap md:items-center md:gap-2 md:mb-3">
            {searchTypes.map((type) => (
              <button
                key={type.value}
                type="button"
                onClick={() => {
                  setSearchType(type.value)
                  setSearchTerm("")
                  setShowSpecialtyDropdown(false)
                  setShowConsultationDropdown(false)
                  setShowDoctorDropdown(false)
                  setShowMedicalCenterDropdown(false)
                }}
                className={`px-4 py-2 text-base md:text-sm rounded-full transition-colors w-full text-center md:w-auto ${
                  searchType === type.value
                    ? "bg-blue-600 text-white"
                    : "bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100"
                }`}
                aria-pressed={searchType === type.value}
              >
                {type.label}
              </button>
            ))}
          </div>

          <div className="flex flex-col md:flex-row md:items-center gap-4">
            {/* What */}
            <div
              className="relative md:w-[40%] md:px-4"
              ref={
                searchType === "especialidad"
                  ? specialtySearchRef
                  : searchType === "estudio"
                  ? consultationSearchRef
                  : searchType === "profesional"
                  ? doctorSearchRef
                  : searchType === "establecimiento"
                  ? medicalCenterSearchRef
                  : null
              }
            >
              <label className="text-sm md:text-[10px] uppercase tracking-wide text-gray-500 mb-1 block">Qué</label>
              <div className="relative">
                <Input
                  type="text"
                  inputMode="search"
                  enterKeyHint="search"
                  autoComplete="off"
                  autoCapitalize="none"
                  autoCorrect="off"
                  placeholder={searchTypes.find((t) => t.value === searchType)?.placeholder}
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value)
                    if (searchType === "especialidad") setShowSpecialtyDropdown(true)
                    else if (searchType === "estudio") setShowConsultationDropdown(true)
                    else if (searchType === "profesional") setShowDoctorDropdown(true)
                    else if (searchType === "establecimiento") setShowMedicalCenterDropdown(true)
                  }}
                  onFocus={() => {
                    if (searchType === "especialidad") setShowSpecialtyDropdown(true)
                    else if (searchType === "estudio") setShowConsultationDropdown(true)
                    else if (searchType === "profesional") setShowDoctorDropdown(true)
                    else if (searchType === "establecimiento") setShowMedicalCenterDropdown(true)
                  }}
                  role="combobox"
                  aria-expanded={
                    (searchType === "especialidad" && showSpecialtyDropdown) ||
                    (searchType === "estudio" && showConsultationDropdown) ||
                    (searchType === "profesional" && showDoctorDropdown) ||
                    (searchType === "establecimiento" && showMedicalCenterDropdown)
                  }
                  aria-autocomplete="list"
                  className="w-full text-base md:text-sm rounded-[12px] border-gray-300 bg-white h-12 pr-10"
                />
                {searchTerm && (
                  <button
                    type="button"
                    aria-label="Limpiar búsqueda"
                    className="absolute right-8 top-1/2 -translate-y-1/2 h-6 w-6 grid place-items-center text-gray-500 hover:text-gray-700"
                    onClick={() => {
                      setSearchTerm("")
                      if (searchType === "especialidad") setShowSpecialtyDropdown(true)
                      else if (searchType === "estudio") setShowConsultationDropdown(true)
                      else if (searchType === "profesional") setShowDoctorDropdown(true)
                      else if (searchType === "establecimiento") setShowMedicalCenterDropdown(true)
                    }}
                  >
                    <X className="h-4 w-4" />
                  </button>
                )}
                <SearchIcon className="absolute right-2 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
              </div>

              {/* Dropdowns */}
              {searchType === "especialidad" && showSpecialtyDropdown && (
                <div className="absolute z-50 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg" role="listbox">
                  <div className="max-h-[15rem] overflow-auto text-base md:text-sm">
                    {filteredSpecialties.length > 0 ? (
                      filteredSpecialties.map((specialty) => (
                        <div
                          key={specialty}
                          className="py-[0.6rem] px-[0.9rem] text-base md:text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                          role="option"
                          onClick={() => {
                            setSearchTerm(specialty)
                            setShowSpecialtyDropdown(false)
                          }}
                        >
                          {highlightMatch(specialty, searchTerm)}
                        </div>
                      ))
                    ) : (
                      <div className="py-[0.4rem] px-[0.75rem] text-base md:text-sm text-gray-500">No se encontraron especialidades</div>
                    )}
                  </div>
                </div>
              )}

              {searchType === "estudio" && showConsultationDropdown && (
                <div className="absolute z-50 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg" role="listbox">
                  <div className="max-h-[15rem] overflow-auto text-base md:text-sm">
                    {filteredConsultationTypes.length > 0 ? (
                      filteredConsultationTypes.map((type) => (
                        <div
                          key={type.name}
                          className="py-[0.6rem] px-[0.9rem] text-base md:text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                          role="option"
                          onClick={() => {
                            setSearchTerm(type.name)
                            setShowConsultationDropdown(false)
                          }}
                        >
                          {highlightMatch(type.name, searchTerm)}
                        </div>
                      ))
                    ) : (
                      <div className="py-[0.4rem] px-[0.75rem] text-base md:text-sm text-gray-500">No se encontraron tipos de consulta</div>
                    )}
                  </div>
                </div>
              )}

              {searchType === "profesional" && showDoctorDropdown && (
                <div className="absolute z-50 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg" role="listbox">
                  <div className="max-h-[15rem] overflow-auto text-base md:text-sm">
                    {filteredDoctors.length > 0 ? (
                      filteredDoctors.map((doctor) => (
                        <div
                          key={doctor.id}
                          className="py-[0.6rem] px-[0.9rem] text-base md:text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                          role="option"
                          onClick={() => {
                            setSearchTerm(doctor.name)
                            setShowDoctorDropdown(false)
                          }}
                        >
                          <div className="flex flex-col">
                            <span>{highlightMatch(doctor.name, searchTerm)}</span>
                            {doctor.specialties && doctor.specialties.length > 0 && (
                              <span className="text-xs text-gray-500">{doctor.specialties.join(", ")}</span>
                            )}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="py-[0.4rem] px-[0.75rem] text-base md:text-sm text-gray-500">No se encontraron profesionales</div>
                    )}
                  </div>
                </div>
              )}

              {searchType === "establecimiento" && showMedicalCenterDropdown && (
                <div className="absolute z-50 w-full bg-white border border-gray-200 rounded-md mt-2 shadow-lg" role="listbox">
                  <div className="max-h-[15rem] overflow-auto text-base md:text-sm">
                    {filteredMedicalCenters.length > 0 ? (
                      filteredMedicalCenters.map((center) => (
                        <div
                          key={center.id}
                          className="py-[0.6rem] px-[0.9rem] text-base md:text-sm hover:bg-gray-100 transition-colors cursor-pointer"
                          role="option"
                          onClick={() => {
                            setSearchTerm(center.name)
                            setShowMedicalCenterDropdown(false)
                          }}
                        >
                          <div className="flex flex-col">
                            <span>{highlightMatch(center.name, searchTerm)}</span>
                            {center.address && <span className="text-xs text-gray-500">{center.address}</span>}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="py-[0.4rem] px-[0.75rem] text-base md:text-sm text-gray-500">No se encontraron establecimientos</div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Vertical divider */}
            <div className="hidden md:block w-px self-stretch bg-gray-200" />

            {/* Where (desktop) */}
            <div className="hidden md:block md:w-[30%] md:px-4">
              <label className="text-[10px] uppercase tracking-wide text-gray-500 mb-1 block">Dónde</label>
              <Popover open={locationOpen} onOpenChange={setLocationOpen}>
                <PopoverTrigger asChild>
                  <button
                    type="button"
                    className="w-full text-left text-sm rounded-[12px] border border-gray-300 bg-white h-12 px-3 whitespace-nowrap overflow-hidden text-ellipsis"
                    onClick={() => {
                      setLocationStage("region")
                      setLocationSearch("")
                      setLocationOpen(true)
                    }}
                  >
                    {selectedRegion === "all"
                      ? "Todas las ubicaciones"
                      : selectedBarrio && selectedBarrio !== "all"
                      ? `${selectedRegion === "caba" ? "Capital Federal" : "Gran Buenos Aires"} - ${(
                          selectedRegion === "caba" ? getCABALocationsFromMedicalCenters() : getGBALocationsFromMedicalCenters()
                        ).find((l) => l.id === selectedBarrio)?.name ?? ""}`
                      : selectedRegion === "caba"
                      ? "Capital Federal"
                      : "Gran Buenos Aires"}
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-[min(28rem,90vw)] p-0 z-[10000]">
                  {locationStage === "region" ? (
                    <div className="max-h-[18rem] overflow-auto py-2" role="listbox">
                      {locationsRef.current
                        .filter((location) => location.type === "all" || location.type === "region")
                        .map((region) => (
                          <div
                            key={region.id}
                            className="px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              if (region.id === "all") {
                                setSelectedRegion("all")
                                setSelectedBarrio("")
                                setLocationOpen(false)
                              } else {
                                setSelectedRegion(region.id as "caba" | "gba")
                                setSelectedBarrio("all")
                                setLocationStage("barrio")
                                setLocationSearch("")
                                setLocationActiveIndex(0)
                              }
                            }}
                          >
                            {region.name}
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div
                      className="max-h-[22rem] overflow-auto"
                      role="listbox"
                      onKeyDown={(e) => {
                        const list = [
                          { id: "all", name: selectedRegion === "caba" ? "Todos los barrios" : "Todos los municipios" },
                          ...((selectedRegion === "caba" ? getCABALocationsFromMedicalCenters() : getGBALocationsFromMedicalCenters())
                            .filter((loc) =>
                              normalizeString(loc.name.toLowerCase()).includes(normalizeString(locationSearch.toLowerCase()))
                            )),
                        ]
                        if (e.key === "ArrowDown") {
                          e.preventDefault()
                          setLocationActiveIndex((prev) => (prev + 1) % list.length)
                        } else if (e.key === "ArrowUp") {
                          e.preventDefault()
                          setLocationActiveIndex((prev) => (prev - 1 + list.length) % list.length)
                        } else if (e.key === "Enter") {
                          e.preventDefault()
                          const active = list[locationActiveIndex]
                          if (active.id === "all") {
                            setSelectedBarrio("all")
                          } else {
                            // @ts-ignore
                            setSelectedBarrio(active.id)
                          }
                          setLocationOpen(false)
                        } else if (e.key === "Escape") {
                          setLocationStage("region")
                          setLocationActiveIndex(0)
                        }
                      }}
                      tabIndex={0}
                    >
                      <div className="flex items-center gap-2 px-3 py-2 border-b text-sm font-medium sticky top-0 bg-white z-10">
                        <ChevronLeft className="h-4 w-4 cursor-pointer" onClick={() => setLocationStage("region")} />
                        {selectedRegion === "caba" ? "Capital Federal" : "Gran Buenos Aires"}
                      </div>
                      <div className="px-3 py-2">
                        <Input
                          value={locationSearch}
                          onChange={(e) => setLocationSearch(e.target.value)}
                          placeholder={selectedRegion === "caba" ? "Buscar barrio" : "Buscar municipio"}
                          className="h-9 text-sm rounded-md border-gray-300"
                          ref={locationSearchInputRef}
                        />
                      </div>
                      <div className="py-1">
                        <div
                          className={`px-3 py-2 text-sm cursor-pointer ${locationActiveIndex === 0 ? "bg-gray-100" : "hover:bg-gray-100"}`}
                          onClick={() => {
                            setSelectedBarrio("all")
                            setLocationOpen(false)
                          }}
                          onMouseEnter={() => setLocationActiveIndex(0)}
                        >
                          {selectedRegion === "caba" ? "Todos los barrios" : "Todos los municipios"}
                        </div>
                        {(selectedRegion === "caba" ? getCABALocationsFromMedicalCenters() : getGBALocationsFromMedicalCenters())
                          .filter((loc) =>
                            normalizeString(loc.name.toLowerCase()).includes(normalizeString(locationSearch.toLowerCase()))
                          )
                          .map((loc, idx) => (
                          <div
                            key={loc.id}
                            className={`px-3 py-2 text-sm cursor-pointer ${locationActiveIndex === idx + 1 ? "bg-gray-100" : "hover:bg-gray-100"}`}
                            onClick={() => {
                              setSelectedBarrio(loc.id)
                              setLocationOpen(false)
                            }}
                            onMouseEnter={() => setLocationActiveIndex(idx + 1)}
                          >
                            {highlightMatch(loc.name, locationSearch)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </PopoverContent>
              </Popover>
            </div>

            {/* Vertical divider */}
            <div className="hidden md:block w-px self-stretch bg-gray-200" />

            {/* Coverage (desktop) */}
            <div className="hidden md:block md:w-[30%] md:pl-4">
              <label className="text-[10px] uppercase tracking-wide text-gray-500 mb-1 block">Cobertura</label>
              <div className="flex gap-2 items-center">
                <Popover open={coverageOpen} onOpenChange={setCoverageOpen}>
                <PopoverTrigger asChild>
                    <button
                      type="button"
                      className={`w-full min-w-[220px] text-left text-sm rounded-[12px] border border-gray-300 bg-white h-12 px-3 whitespace-nowrap overflow-hidden text-ellipsis`}
                      onClick={() => {
                        setCoverageStage("coverage")
                        setCoverageSearch("")
                        setPlanSearch("")
                        setCoverageActiveIndex(0)
                        setCoverageOpen(true)
                      }}
                    >
                      {selectedCoverage
                        ? selectedPlan
                          ? `${selectedCoverage} ${selectedPlan}`
                          : selectedCoverage
                        : "Sin Cobertura"}
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[min(28rem,90vw)] p-0 z-[10000]">
                    {coverageStage === "coverage" ? (
                      <div className="max-h-[22rem] overflow-auto" role="listbox"
                           onKeyDown={(e) => {
                             const list = [
                               { id: 'none', name: 'Sin Cobertura' },
                               ...DEFAULT_COVERAGES.filter((cov) => cov.name !== 'Sin Cobertura')
                                 .filter((coverage) =>
                                   normalizeString(coverage.name.toLowerCase()).includes(
                                     normalizeString(coverageSearch.toLowerCase())
                                   )
                                 )
                                 .map((c) => ({ id: c.id, name: c.name })),
                             ]
                             if (e.key === 'ArrowDown') {
                               e.preventDefault()
                               setCoverageActiveIndex((prev) => (prev + 1) % list.length)
                             } else if (e.key === 'ArrowUp') {
                               e.preventDefault()
                               setCoverageActiveIndex((prev) => (prev - 1 + list.length) % list.length)
                             } else if (e.key === 'Enter') {
                               e.preventDefault()
                               const active = list[coverageActiveIndex]
                               if (active.id === 'none') {
                                 setSelectedCoverage('Sin Cobertura')
                                 setSelectedPlan('')
                                 setCoverageOpen(false)
                               } else {
                                 setSelectedCoverage(active.name)
                                 setSelectedPlan('')
                                 setCoverageStage('plan')
                                 setPlanActiveIndex(0)
                               }
                             } else if (e.key === 'Escape') {
                               setCoverageOpen(false)
                               setCoverageActiveIndex(0)
                             }
                           }}
                           tabIndex={0}
                      >
                        <div className="px-3 py-2 border-b sticky top-0 bg-white z-10">
                          <Input
                            value={coverageSearch}
                            onChange={(e) => setCoverageSearch(e.target.value)}
                            placeholder="Buscar cobertura"
                            className="h-9 text-sm rounded-md border-gray-300"
                            ref={coverageSearchInputRef}
                          />
                        </div>
                        <div className="py-2">
                        <div
                          className={`px-3 py-2 text-sm cursor-pointer ${coverageActiveIndex === 0 ? 'bg-gray-100' : 'hover:bg-gray-100'}`}
                          onClick={() => {
                            setSelectedCoverage("Sin Cobertura")
                            setSelectedPlan("")
                            setCoverageOpen(false)
                          }}
                          onMouseEnter={() => setCoverageActiveIndex(0)}
                        >
                          Sin Cobertura
                        </div>
                        {DEFAULT_COVERAGES.filter((cov) => cov.name !== "Sin Cobertura")
                          .filter((coverage) =>
                            normalizeString(coverage.name.toLowerCase()).includes(
                              normalizeString(coverageSearch.toLowerCase())
                            )
                          )
                          .map((coverage, idx) => (
                          <div
                            key={coverage.id}
                            className={`px-3 py-2 text-sm cursor-pointer ${coverageActiveIndex === idx + 1 ? 'bg-gray-100' : 'hover:bg-gray-100'}`}
                            onClick={() => {
                              setSelectedCoverage(coverage.name)
                              setSelectedPlan("")
                              setCoverageStage("plan")
                              setPlanSearch("")
                            }}
                            onMouseEnter={() => setCoverageActiveIndex(idx + 1)}
                          >
                            {highlightMatch(coverage.name, coverageSearch)}
                          </div>
                        ))}
                        </div>
                      </div>
                    ) : (
                      <div className="max-h-[22rem] overflow-auto" role="listbox"
                           onKeyDown={(e) => {
                             const list = (DEFAULT_COVERAGES.find((c) => c.name === selectedCoverage)?.plans || [])
                               .filter((plan) =>
                                 normalizeString(plan.toLowerCase()).includes(
                                   normalizeString(planSearch.toLowerCase())
                                 )
                               )
                             if (e.key === 'ArrowDown') {
                               e.preventDefault()
                               setPlanActiveIndex((prev) => (prev + 1) % (list.length || 1))
                             } else if (e.key === 'ArrowUp') {
                               e.preventDefault()
                               setPlanActiveIndex((prev) => (prev - 1 + (list.length || 1)) % (list.length || 1))
                             } else if (e.key === 'Enter') {
                               e.preventDefault()
                               const active = list[planActiveIndex]
                               if (active) {
                                 setSelectedPlan(active)
                                 setCoverageOpen(false)
                               }
                             } else if (e.key === 'Escape') {
                               setCoverageStage('coverage')
                               setPlanActiveIndex(0)
                             }
                           }}
                           tabIndex={0}
                      >
                        <div className="flex items-center gap-2 px-3 py-2 border-b text-sm font-medium sticky top-0 bg-white z-10">
                          <ChevronLeft className="h-4 w-4 cursor-pointer" onClick={() => setCoverageStage("coverage")} />
                          {selectedCoverage}
                        </div>
                        <div className="px-3 py-2">
                          <Input
                            value={planSearch}
                            onChange={(e) => setPlanSearch(e.target.value)}
                            placeholder="Buscar plan"
                            className="h-9 text-sm rounded-md border-gray-300"
                            ref={planSearchInputRef}
                          />
                        </div>
                        <div className="py-1">
                          {DEFAULT_COVERAGES.find((c) => c.name === selectedCoverage)?.plans
                            .filter((plan) =>
                              normalizeString(plan.toLowerCase()).includes(
                                normalizeString(planSearch.toLowerCase())
                              )
                            )
                            .map((plan, idx) => (
                            <div
                              key={plan}
                              className={`px-3 py-2 text-sm cursor-pointer ${planActiveIndex === idx ? 'bg-gray-100' : 'hover:bg-gray-100'}`}
                              onClick={() => {
                                setSelectedPlan(plan)
                                setCoverageOpen(false)
                              }}
                              onMouseEnter={() => setPlanActiveIndex(idx)}
                            >
                              {highlightMatch(plan, planSearch)}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Search button (desktop) */}
            <div className="hidden md:block md:w-[16%] md:px-4">
              <label className="text-[10px] uppercase tracking-wide text-transparent mb-1 block select-none">Buscar</label>
              <div className="flex">
                <Button
                  type="submit"
                  className="w-full rounded-[12px] bg-blue-600 hover:bg-blue-700 text-white text-base h-12"
                  disabled={isSearchButtonDisabled}
                >
                  <SearchIcon className="mr-0.5 h-4 w-4" /> Buscar
                </Button>
              </div>
            </div>
          </div>
          {/* Mobile-only Filters collapsible */}
          <div className="md:hidden mt-4">
            <button
              type="button"
              className={`w-full text-left text-sm rounded-[12px] border h-12 px-3 flex items-center justify-between ${
                mobileFiltersOpen ? "bg-blue-50 border-blue-200" : "bg-white border-gray-300"
              }`}
              onClick={() => setMobileFiltersOpen((v) => !v)}
              aria-expanded={mobileFiltersOpen}
            >
              <span className="text-gray-700 text-base">Filtros</span>
              <span className="text-sm text-gray-500">
                {selectedRegion === "all"
                  ? "Todas las ubicaciones"
                  : selectedBarrio && selectedBarrio !== "all"
                  ? `${selectedRegion === "caba" ? "Capital Federal" : "Gran Buenos Aires"}`
                  : selectedRegion === "caba"
                  ? "Capital Federal"
                  : "Gran Buenos Aires"}
                {selectedCoverage ? ` • ${selectedPlan ? `${selectedCoverage} ${selectedPlan}` : selectedCoverage}` : ""}
              </span>
            </button>
            {mobileFiltersOpen && (
              <div className="mt-2 space-y-2 rounded-lg border border-blue-100 bg-blue-50/40 p-2">
                <button
                  type="button"
                  className="w-full text-left text-base rounded-[12px] border border-gray-300 bg-white h-12 px-3"
                  onClick={() => {
                    setMobileLocationDialogOpen(true)
                    setLocationStage("region")
                    setLocationSearch("")
                    setLocationActiveIndex(0)
                  }}
                >
                  Dónde: {selectedRegion === "all"
                    ? "Todas las ubicaciones"
                    : selectedBarrio && selectedBarrio !== "all"
                    ? `${selectedRegion === "caba" ? "Capital Federal" : "Gran Buenos Aires"}`
                    : selectedRegion === "caba"
                    ? "Capital Federal"
                    : "Gran Buenos Aires"}
                </button>
                <button
                  type="button"
                  className="w-full text-left text-base rounded-[12px] border border-gray-300 bg-white h-12 px-3"
                  onClick={() => {
                    setMobileCoverageDialogOpen(true)
                    setCoverageStage("coverage")
                    setCoverageSearch("")
                    setPlanSearch("")
                    setCoverageActiveIndex(0)
                    setPlanActiveIndex(0)
                  }}
                >
                  Cobertura: {selectedCoverage ? (selectedPlan ? `${selectedCoverage} ${selectedPlan}` : selectedCoverage) : "Sin Cobertura"}
                </button>

              </div>
            )}
          </div>
          {/* Mobile-only primary action (placed below filters) */}
          <div className="md:hidden mt-3">
            <Button
              type="submit"
              className="w-full rounded-[12px] bg-blue-600 hover:bg-blue-700 text-white text-lg h-12"
              disabled={isSearchButtonDisabled}
            >
              <SearchIcon className="mr-1.5 h-5 w-5" /> Buscar
            </Button>
          </div>
        </form>
      </div>
      {/* Mobile overlays (no animation) */}
      {mobileLocationDialogOpen && (
        <>
          <div className="fixed inset-0 z-50 bg-black/50 md:hidden" onClick={() => setMobileLocationDialogOpen(false)} />
          <div role="dialog" aria-modal="true" className="md:hidden fixed inset-x-0 top-24 h-[calc(100vh-96px)] z-50 rounded-t-xl bg-white shadow-lg overflow-auto">
            {locationStage === "region" ? (
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between gap-2 px-3 py-3 border-b text-lg font-semibold sticky top-0 bg-white z-10">
                <span>Seleccionar zona</span>
                <button aria-label="Cerrar" onClick={() => setMobileLocationDialogOpen(false)} className="p-2 text-gray-600">
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="flex-1 overflow-auto">
                {locationsRef.current
                  .filter((location) => location.type === "all" || location.type === "region")
                  .map((region) => (
                    <div
                      key={region.id}
                      className="px-4 py-4 text-lg hover:bg-gray-100 cursor-pointer"
                      onClick={() => {
                        if (region.id === "all") {
                          setSelectedRegion("all")
                          setSelectedBarrio("")
                          setMobileLocationDialogOpen(false)
                        } else {
                          setSelectedRegion(region.id as "caba" | "gba")
                          setSelectedBarrio("all")
                          setLocationStage("barrio")
                          setLocationSearch("")
                          setLocationActiveIndex(0)
                        }
                      }}
                    >
                      {region.name}
                    </div>
                  ))}
              </div>
            </div>
            ) : (
              <div className="flex flex-col h-full">
              <div className="flex items-center justify-between gap-2 px-3 py-3 border-b text-lg font-semibold sticky top-0 bg-white z-10">
                <div className="flex items-center gap-2">
                  <button className="mr-1" onClick={() => setLocationStage("region")}> <ChevronLeft className="h-6 w-6" /> </button>
                  <span>{selectedRegion === "caba" ? "Capital Federal" : "Gran Buenos Aires"}</span>
                </div>
                <button aria-label="Cerrar" onClick={() => setMobileLocationDialogOpen(false)} className="p-2 text-gray-600">
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="px-3 py-3">
                <Input
                  value={locationSearch}
                  onChange={(e) => setLocationSearch(e.target.value)}
                  placeholder={selectedRegion === "caba" ? "Buscar barrio" : "Buscar municipio"}
                  className="h-12 text-base rounded-md border-gray-300"
                  ref={locationSearchInputRef}
                />
              </div>
              <div className="flex-1 overflow-auto">
                <div
                  className="px-4 py-4 text-lg cursor-pointer hover:bg-gray-100"
                  onClick={() => {
                    setSelectedBarrio("all")
                    setMobileLocationDialogOpen(false)
                  }}
                >
                  {selectedRegion === "caba" ? "Todos los barrios" : "Todos los municipios"}
                </div>
                {(selectedRegion === "caba" ? getCABALocationsFromMedicalCenters() : getGBALocationsFromMedicalCenters())
                  .filter((loc) =>
                    normalizeString(loc.name.toLowerCase()).includes(normalizeString(locationSearch.toLowerCase()))
                  )
                  .map((loc) => (
                    <div
                      key={loc.id}
                      className="px-4 py-4 text-lg cursor-pointer hover:bg-gray-100"
                      onClick={() => {
                        // @ts-ignore
                        setSelectedBarrio(loc.id)
                        setMobileLocationDialogOpen(false)
                      }}
                    >
                      {highlightMatch(loc.name, locationSearch)}
                    </div>
                  ))}
              </div>
            </div>
            )}
          </div>
        </>
      )}

      {mobileCoverageDialogOpen && (
        <>
          <div className="fixed inset-0 z-50 bg-black/50 md:hidden" onClick={() => setMobileCoverageDialogOpen(false)} />
          <div role="dialog" aria-modal="true" className="md:hidden fixed inset-x-0 top-24 h-[calc(100vh-96px)] z-50 rounded-t-xl bg-white shadow-lg overflow-auto">
            {coverageStage === "coverage" ? (
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between gap-2 px-3 py-3 border-b text-lg font-semibold sticky top-0 bg-white z-10">
                <span>Seleccionar cobertura</span>
                <button aria-label="Cerrar" onClick={() => setMobileCoverageDialogOpen(false)} className="p-2 text-gray-600">
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="px-3 py-3">
                <Input
                  value={coverageSearch}
                  onChange={(e) => setCoverageSearch(e.target.value)}
                  placeholder="Buscar cobertura"
                  className="h-12 text-base rounded-md border-gray-300"
                  ref={coverageSearchInputRef}
                />
              </div>
              <div className="flex-1 overflow-auto">
                <div
                  className="px-4 py-4 text-lg cursor-pointer hover:bg-gray-100"
                  onClick={() => {
                    setSelectedCoverage("Sin Cobertura")
                    setSelectedPlan("")
                    setMobileCoverageDialogOpen(false)
                  }}
                >
                  Sin Cobertura
                </div>
                {DEFAULT_COVERAGES.filter((cov) => cov.name !== "Sin Cobertura")
                  .filter((coverage) =>
                    normalizeString(coverage.name.toLowerCase()).includes(
                      normalizeString(coverageSearch.toLowerCase())
                    )
                  )
                  .map((coverage) => (
                    <div
                      key={coverage.id}
                      className="px-4 py-4 text-lg cursor-pointer hover:bg-gray-100"
                      onClick={() => {
                        setSelectedCoverage(coverage.name)
                        setSelectedPlan("")
                        setCoverageStage("plan")
                        setPlanSearch("")
                      }}
                    >
                      {highlightMatch(coverage.name, coverageSearch)}
                    </div>
                  ))}
              </div>
            </div>
            ) : (
              <div className="flex flex-col h-full">
              <div className="flex items-center justify-between gap-2 px-3 py-3 border-b text-lg font-semibold sticky top-0 bg-white z-10">
                <div className="flex items-center gap-2">
                  <button className="mr-1" onClick={() => setCoverageStage("coverage")}> <ChevronLeft className="h-6 w-6" /> </button>
                  <span>{selectedCoverage}</span>
                </div>
                <button aria-label="Cerrar" onClick={() => setMobileCoverageDialogOpen(false)} className="p-2 text-gray-600">
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="px-3 py-3">
                <Input
                  value={planSearch}
                  onChange={(e) => setPlanSearch(e.target.value)}
                  placeholder="Buscar plan"
                  className="h-12 text-base rounded-md border-gray-300"
                  ref={planSearchInputRef}
                />
              </div>
              <div className="flex-1 overflow-auto">
                {(DEFAULT_COVERAGES.find((c) => c.name === selectedCoverage)?.plans || [])
                  .filter((plan) =>
                    normalizeString(plan.toLowerCase()).includes(
                      normalizeString(planSearch.toLowerCase())
                    )
                  )
                  .map((plan) => (
                    <div
                      key={plan}
                      className="px-4 py-4 text-lg cursor-pointer hover:bg-gray-100"
                      onClick={() => {
                        setSelectedPlan(plan)
                        setMobileCoverageDialogOpen(false)
                      }}
                    >
                      {highlightMatch(plan, planSearch)}
                    </div>
                  ))}
              </div>
            </div>
            )}
          </div>
        </>
      )}
    </section>
  )
}


