# Turnera API Documentation
#

## API Overview

The Turnera API provides endpoints for managing:

1. **Schedules**:
   - Regular appointment schedules
   - Special schedules
   - Vacation schedules

2. **Appointments**:
   - Creating appointments from user
   - Creating appointments from medical center

3. **Patients**:
   - Creating patients
   - Associating patients with users

4. **Health Insurance**:
   - Associating health insurance with medical centers

5. **Professional Details**:
   - Retrieving professional (doctor) details

## Key Endpoints

### Schedule Management
- `/schedule/set-appointment-schedule` (POST) - Create regular schedules
- `/schedule/set-special-schedule` (POST) - Create special schedules
- `/schedule/set-vacation-schedule` (POST) - Create vacation schedules
- `/schedule/modify-special-schedules` (PUT) - Modify special schedules
- `/schedule/modify-vacation-schedules` (PUT) - Modify vacation schedules
- `/schedule/delete-special-schedules` (DELETE) - Delete special schedules
- `/schedule/delete-vacation-schedules` (DELETE) - Delete vacation schedules

### Appointment Management
- `/appointment/from-user` (POST) - Create appointment from user
- `/appointment/from-medical-center/{origin}` (POST) - Create appointment from medical center

### Patient Management
- `/patients/{userId}` (POST) - Create patient by employee user
- `/patients/create-from-user/{userId}` (POST) - Create patient for user

### Professional Information
- `/schedule/professional-detail` (GET) - Get professional details

### Health Insurance
- `/health-insurance/medical-center` (POST) - Associate health insurance with medical center

## Authentication
The API uses JWT Bearer token authentication.

## Data Models

The API defines several data models including:

### Schedule Models
- `AppointmentScheduleCreationRequest` - For creating regular schedules
- `SpecialScheduleCreationRequest` - For creating special schedules
- `VacationScheduleCreationRequest` - For creating vacation schedules
- `SpecialScheduleModificationRequest` - For modifying special schedules
- `VacationScheduleModificationRequest` - For modifying vacation schedules
- `DeleteScheduleRequest` - For deleting schedules

### Appointment Models
- `AppointmentCreationRequestFromUser` - For creating appointments from user
- `AppointmentCreationRequestFromMedicalCenter` - For creating appointments from medical center

### Patient Models
- `PatientCreationRequest` - For creating patients by employee
- `PatientCreationRequestForUser` - For creating patients for user

### Professional Models
- `ProfessionalDetailResponse` - For retrieving professional details

## API Documentation
For detailed information about request/response formats and data models, visit the Swagger UI documentation.
