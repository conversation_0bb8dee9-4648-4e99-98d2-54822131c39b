"use client"

import React, { useState, useEffect, use<PERSON><PERSON>back, useRef, Suspense, useContext } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { PasswordDialog } from "@/components/dialogs/password-dialog"
import { Button } from "@/components/ui/button"
import { Calendar, ChevronLeft, CreditCard, AlertCircle, Check } from "lucide-react"
import Header from "@/components/searchresults/Header"

import { MedicalCenter } from "@/types/medical-center"
import { Doctor } from "@/types/doctor"
import { format, isSameDay, parseISO, addMonths, isSameMonth } from "date-fns"
import { Label } from "@/components/ui/label"
import { useAppointments } from "@/contexts/AppointmentContext"
import { usePatients } from "@/contexts/PatientContext"
import { useAuth } from "@/contexts/AuthContext"
import { PatientAuthForm } from "@/components/auth/patient-auth-form"
import { Appointment } from "@/types/scheduler"
import { Patient } from "@/types/patient"
import { UserRole } from "@/types/users"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { generateCustomId } from "@/utils/idGenerator"
import "react-international-phone/style.css"
import "@/styles/phone-input.css"
import { getSpanishCountries } from "@/data/phoneCountries"
import { PhoneNumberUtil } from 'google-libphonenumber'
import { PatientEditDialog } from "@/components/ui/patient-edit-dialog"
import { DEFAULT_COVERAGES } from "@/data/coverages"
import { CoverageContext } from "@/contexts/CoverageContext"
import { ConfirmationStep } from "@/components/cita/ConfirmationStep"
import { CitaFooter } from "@/components/cita/CitaFooter"
import { PatientDataAndCoverageSection } from "@/components/cita/PatientDataAndCoverageSection"
import { DateTimeSelectionStep } from "@/components/cita/DateTimeSelectionStep"
import { PatientSelectionStep } from "@/components/cita/PatientSelectionStep"
import { ReservationSummary, formatPrice } from "@/components/cita/ReservationSummary"
import { getAvailableTimeSlots, getAvailableDates } from "@/utils/appointmentUtils"

const capitalizeName = (name: string) => {
  return name
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ")
}

export default function AppointmentBookingPageWrapper() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Cargando...</div>}>
      <AppointmentBookingPage />
    </Suspense>
  )
}

function AppointmentBookingPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const doctorId = searchParams.get('doctorId')
  const medicalCenterId = searchParams.get('medicalCenterId')
  const preselectedDate = searchParams.get('date')
  const preselectedTime = searchParams.get('time')
  const source = searchParams.get('source')
  const urlCoverage = searchParams.get('coverage')
  const urlPlan = searchParams.get('plan')
  const urlNoCoverage = searchParams.get('noCoverage') === 'true'
  const urlConsultationType = searchParams.get('consultationType')
  const modifyingAppointmentId = searchParams.get('modifying')

  const coverageId = urlNoCoverage
    ? "8" // ID for "Sin Cobertura" based on DEFAULT_COVERAGES
    : urlCoverage
      ? DEFAULT_COVERAGES.find(c => c.name === urlCoverage)?.id || null
      : null

  const [doctor, setDoctor] = useState<Doctor | null>(null)
  const [medicalCenter, setMedicalCenter] = useState<MedicalCenter | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedDate, setSelectedDate] = useState<Date | null>(preselectedDate ? parseISO(preselectedDate) : null)
  const [availableDates, setAvailableDates] = useState<Date[]>([])
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([])
  const [selectedTime, setSelectedTime] = useState<string | null>(preselectedTime || null)
  const [confirmedTime, setConfirmedTime] = useState<string | null>(null)
  const [selectedConsultationType, setSelectedConsultationType] = useState<string | null>(null)
  const [selectedUrlCoverage, setSelectedUrlCoverage] = useState<string | null>(null)

  const [openSections, setOpenSections] = useState({
    patient: false,
    verification: false,
    password: false,
    coverage: false
  })

  const [searchingPatient, setSearchingPatient] = useState(false)
  const [patientDniSearch, setPatientDniSearch] = useState("")
  const [patientFound, setPatientFound] = useState<Patient | null>(null)
  const [patientName, setPatientName] = useState("")
  const [patientLastName, setPatientLastName] = useState("")
  const [patientDni, setPatientDni] = useState("")
  const [patientPhone, setPatientPhone] = useState("")
  const [patientEmail, setPatientEmail] = useState("")
  const [patientCoverage, setPatientCoverage] = useState("")
  const [patientPlan, setPatientPlan] = useState("")
  const [noCoverage, setNoCoverage] = useState(false)
  const [phoneError, setPhoneError] = useState<string>("") // For phone validation errors
  const [isCoverageAccepted, setIsCoverageAccepted] = useState<boolean | null>(null) // For coverage acceptance check
  const [isCoverageOpen, setIsCoverageOpen] = useState(false) // For coverage collapsible
  const [isPlanOpen, setIsPlanOpen] = useState(false) // For plan dropdown
  const [useDefaultCoverage, setUseDefaultCoverage] = useState(true) // Use patient's default coverage
  const [hasDefaultCoverage, setHasDefaultCoverage] = useState(false) // If patient has a default coverage
  const [selectedPatientCoverage, setSelectedPatientCoverage] = useState("") // Store the patient's default coverage
  const [overrideCoverage, setOverrideCoverage] = useState("") // For when overriding default coverage
  const [overridePlan, setOverridePlan] = useState("") // For when overriding default coverage
  const [overrideNoCoverage, setOverrideNoCoverage] = useState(false) // For when overriding with no coverage

  // Initialize Google's libphonenumber utility
  const phoneUtil = PhoneNumberUtil.getInstance()

  // Dynamically create plansByCoverage from DEFAULT_COVERAGES
  const plansByCoverage = React.useMemo(() => {
    return DEFAULT_COVERAGES.reduce((acc, coverage) => {
      acc[coverage.name] = coverage.plans || [];
      return acc;
    }, {} as Record<string, string[]>);
  }, []);

  const { appointments, addAppointment, updateAppointment, blockedSlots } = useAppointments()
  const { addPatient, patients, getPatientById, getPatientByName, getPatientByDni, createPatientUser, sendPatientVerificationCode, associatePatientWithUser } = usePatients()
  const { isAuthenticated, currentUser, hasRole, getAssociatedPatients } = useAuth()

  // Check if the user is a patient (only patients should be recognized in this page)
  const isPatientUser = isAuthenticated && hasRole(UserRole.PATIENT)

  // No longer need to track showAuthForm state as we always show auth form in step 2 when not authenticated

  // Add a useEffect to listen for patient updates
  useEffect(() => {
    // Function to reload patients from localStorage
    const reloadPatientsFromStorage = () => {
      if (isPatientUser && currentUser) {
        // Reload patients from localStorage to ensure we have the latest data
        const storedPatients = localStorage.getItem("patients");
        if (storedPatients) {
          try {
            const parsedPatients = JSON.parse(storedPatients);
            console.log('Cita page - Reloaded patients after update:', parsedPatients.length);

            // Check if the current user's patient exists in the loaded patients
            if (currentUser.defaultPatientId) {
              const patient = parsedPatients.find((p: Patient) => p.id === currentUser.defaultPatientId);
              if (patient) {
                console.log('Cita page - Found patient for current user:', patient.name);
                // Update patient data in the form
                setPatientFound(patient);
                setPatientDni(patient.dni);

                // Split the name into first and last name
                const nameParts = patient.name.split(' ');
                if (nameParts.length > 1) {
                  setPatientName(nameParts[0]);
                  setPatientLastName(nameParts.slice(1).join(' '));
                } else {
                  setPatientName(patient.name);
                  setPatientLastName('');
                }

                setPatientPhone(patient.phone);
                setPatientEmail(patient.email || '');
              } else {
                console.warn('Cita page - Could not find patient for current user in loaded patients');
              }
            }
          } catch (error) {
            console.error("Error parsing patients from localStorage:", error);
          }
        }
      }
    };

    // Load patients initially
    reloadPatientsFromStorage();

    // Listen for patient updates
    const handlePatientsUpdated = () => {
      console.log('Cita page - Received patientsUpdated event');
      reloadPatientsFromStorage();
    };

    // Add event listener
    window.addEventListener('patientsUpdated', handlePatientsUpdated);

    // Clean up event listener
    return () => {
      window.removeEventListener('patientsUpdated', handlePatientsUpdated);
    };
  }, [isPatientUser, currentUser]);

  // Use a ref to track previous authentication state to prevent multiple resets
  const prevAuthRef = useRef<boolean | null>(null);

  // Use a ref to track if we've processed URL parameters to avoid redundant processing
  const hasProcessedUrlParams = useRef<boolean>(false);

  // Use a ref to track previous selected date to prevent infinite loops
  const prevSelectedDateRef = useRef<Date | null>(null);

  // Reset form state when authentication state changes (specifically on logout)
  useEffect(() => {
    // Only reset if we're transitioning from authenticated to not authenticated
    // This prevents the effect from running on initial render and multiple times
    if (prevAuthRef.current === true && !isAuthenticated) {
      console.log('Cita page - User logged out, resetting form state');

      // Reset to step 1 (Fecha section)
      setCurrentStep(1);

      // Reset date and time selection
      setSelectedDate(null);
      setSelectedTime(null);
      setConfirmedTime(null);
      setSelectedConsultationType(null);

      // Reset patient search state
      setPatientDniSearch("");
      setSearchPerformed(false);
      setPatientNotFound(false);
      setPatientFound(null);
      setPatientError("");

      // Reset patient form fields
      setPatientName("");
      setPatientLastName("");
      setPatientDni("");
      setPatientPhone("");
      setPatientEmail("");
      setPatientCoverage("");
      setPatientPlan("");
      setNoCoverage(false);
      setUseDefaultCoverage(true);
      setHasDefaultCoverage(false);

      // Reset collapsible sections
      setCompletedSections({
        dni: { completed: false, value: "" },
        datos: { completed: false, value: "" },
        cobertura: { completed: false, value: "" }
      });
      setActiveSection("dni");

      // Reset verification state
      setShowVerificationSection(false);
      setPatientToVerify(null);
      setVerificationCode("");
      setActualVerificationCode("");

      // Reset selected associated patient
      setSelectedAssociatedPatient(null);

      // Reset edit dialog state
      setShowEditDialog(false);
      setPatientToEdit(null);
    }

    // Update the ref with current authentication state
    prevAuthRef.current = isAuthenticated;
  }, [isAuthenticated]);

  // Handle preselected date/time from URL parameters
  useEffect(() => {
    // Only run this once when doctor & medicalCenter are loaded
    if (doctor && medicalCenter && !hasProcessedUrlParams.current) {
      // Mark that we've processed URL parameters to avoid running this logic again
      hasProcessedUrlParams.current = true;

      // Only continue if there are actually URL parameters to process
      if (preselectedDate || preselectedTime) {
        // First get all available dates based on the current consultation type
        const availableDatesForConsultation = getAvailableDates(
          doctor,
          medicalCenter,
          appointments,
          blockedSlots,
          selectedConsultationType
        );

        // Then check if the preselected date is valid (is among available dates)
        if (preselectedDate) {
          const parsedDate = parseISO(preselectedDate);

          // Only set the date if it's available for the current consultation type
          if (availableDatesForConsultation.some(date => isSameDay(date, parsedDate))) {
            setSelectedDate(parsedDate);

            // If there's a preselected time, verify it's available for this date
            if (preselectedTime) {
              const availableTimesForDate = getAvailableTimeSlots(
                doctor,
                medicalCenter,
                parsedDate,
                Object.values(appointments)
                  .flat()
                  .filter(apt => apt.doctorId === doctorId && apt.medicalCenterId === medicalCenterId),
                blockedSlots,
                doctorId || '',
                medicalCenterId || '',
                selectedConsultationType
              );

              // Only set the time if it's available
              if (availableTimesForDate.includes(preselectedTime)) {
                setSelectedTime(preselectedTime);
              }
            }
          } else {
            // If the date is not available, log for debugging but don't select anything
            console.log('Preselected date is not available for the current consultation type');
          }
        }
      }
    }
  }, [doctor, medicalCenter, preselectedDate, preselectedTime, appointments, doctorId, medicalCenterId, blockedSlots, selectedConsultationType]);

  // Handle coverage from URL parameters
  useEffect(() => {
    // Format the coverage string based on URL parameters
    if (urlNoCoverage) {
      setSelectedUrlCoverage("Sin Cobertura");
    } else if (urlCoverage) {
      if (urlPlan) {
        setSelectedUrlCoverage(`${urlCoverage} ${urlPlan}`);
      } else {
        setSelectedUrlCoverage(urlCoverage);
      }
    }
  }, [urlCoverage, urlPlan, urlNoCoverage]);

  // Handle consultation type from URL parameters
  useEffect(() => {
    if (urlConsultationType && !selectedConsultationType) {
      setSelectedConsultationType(urlConsultationType);
    }
  }, [urlConsultationType, selectedConsultationType]);

  // Get the CoverageContext to check if doctor accepts coverage
  const { isDoctorCoverageExcluded } = useContext(CoverageContext)

  // Effect to check coverage acceptance when relevant state changes
  useEffect(() => {
    if (!doctor) {
      setIsCoverageAccepted(null); // No doctor, no check needed
      return;
    }

    let coverageToCheck = "";
    let baseCoverageName = "";

    // Apply the same priority logic as in other functions
    if (useDefaultCoverage && hasDefaultCoverage) {
      // For the default patient, use their coverage
      if (isPatientUser && currentUser?.defaultPatientId && !searchPerformed) {
        const defaultPatient = getPatientById(currentUser.defaultPatientId);
        coverageToCheck = defaultPatient?.coverage || "Sin Cobertura";
      } else if (selectedAssociatedPatient) {
        coverageToCheck = selectedAssociatedPatient.coverage || "Sin Cobertura";
      } else if (patientFound) {
        coverageToCheck = patientFound.coverage || "Sin Cobertura";
      }
    } else if (overrideNoCoverage) {
      coverageToCheck = "Sin Cobertura";
    } else if (overrideCoverage) {
      coverageToCheck = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
    } else if (noCoverage) {
      coverageToCheck = "Sin Cobertura";
    } else if (patientCoverage) {
      coverageToCheck = patientPlan ? `${patientCoverage} ${patientPlan}` : patientCoverage;
    }

    if (coverageToCheck === "Sin Cobertura") {
      baseCoverageName = "Sin Cobertura";
    } else if (coverageToCheck) {
      // Extract base name (e.g., "OSDE" from "OSDE 310")
      const foundCoverage = DEFAULT_COVERAGES.find(cov => coverageToCheck.startsWith(cov.name));
      baseCoverageName = foundCoverage ? foundCoverage.name : "";
    }

    // Check if the doctor accepts this coverage using the CoverageContext
    if (baseCoverageName && doctor.id) {
      // Find the coverage in DEFAULT_COVERAGES
      const foundCoverage = DEFAULT_COVERAGES.find(cov => cov.name === baseCoverageName);
      if (foundCoverage) {
        // Check if this specific plan is excluded
        let planId = null;
        if (coverageToCheck !== "Sin Cobertura" && coverageToCheck !== baseCoverageName) {
          // Extract plan part (e.g., "310" from "OSDE 310")
          const planPart = coverageToCheck.substring(baseCoverageName.length).trim();
          if (planPart) {
            const coveragePlans = foundCoverage.plans || [];
            if (coveragePlans.includes(planPart)) {
              planId = planPart;
            }
          }
        }

        // Use the isDoctorCoverageExcluded function from CoverageContext for all coverages, including Sin Cobertura
        const isExcluded = isDoctorCoverageExcluded(doctor.id, foundCoverage.id, planId);
        setIsCoverageAccepted(!isExcluded);
      } else {
        setIsCoverageAccepted(null); // Coverage not found in system
      }
    } else {
      setIsCoverageAccepted(null); // Cannot determine or no coverage selected
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    doctor,
    useDefaultCoverage,
    hasDefaultCoverage,
    patientCoverage,
    patientPlan,
    noCoverage,
    isPatientUser,
    currentUser,
    getPatientById,
    patientFound,
    overrideCoverage,
    overridePlan,
    overrideNoCoverage,
    isDoctorCoverageExcluded
  ]);

  useEffect(() => {
    if (doctorId && medicalCenterId) {
      setLoading(true)
      // Get the doctor specific to this medical center
      let foundDoctor = storage.getDoctorForMedicalCenter(doctorId, medicalCenterId)
      const allMedicalCenters = storage.getMedicalCenters()
      const foundMedicalCenter = allMedicalCenters.find(mc => mc.id === medicalCenterId)

      // If doctor not found in this medical center, try to get from all doctors as fallback
      if (!foundDoctor && foundMedicalCenter) {
        const allDoctors = storage.getAllDoctorsInSystem()
        const doctorFromAll = allDoctors.find(d => d.id === doctorId)
        foundDoctor = doctorFromAll || null
      }

      if (foundDoctor && foundMedicalCenter) {
        setDoctor(foundDoctor)
        setMedicalCenter(foundMedicalCenter)

        // If consultation type is not already set from URL, don't set a default
        // Otherwise, keep the one from the URL
        if (!urlConsultationType) {
          setSelectedConsultationType(null);
        }

        // We'll calculate available dates after setting the consultation type
        // This will happen in the next effect that depends on selectedConsultationType
      }
      setLoading(false)
    }
  }, [doctorId, medicalCenterId, urlConsultationType])

  useEffect(() => {
    if (doctor && medicalCenter && selectedDate) {
      const doctorAppointments = Object.values(appointments)
        .flat()
        .filter(apt => apt.doctorId === doctorId && apt.medicalCenterId === medicalCenterId)
      const slots = getAvailableTimeSlots(
        doctor,
        medicalCenter,
        selectedDate,
        doctorAppointments,
        blockedSlots,
        doctorId || '',
        medicalCenterId || '',
        selectedConsultationType
      )
      setAvailableTimeSlots(slots)

      if (selectedTime && !slots.includes(selectedTime)) {
        setSelectedTime(null)
      }
    }
  }, [doctor, medicalCenter, selectedDate, appointments, doctorId, medicalCenterId, selectedTime, blockedSlots, selectedConsultationType])

  // Recalculate available dates when appointments change or consultation type changes
  useEffect(() => {
    if (doctor && medicalCenter) {
      const allAvailableDates = getAvailableDates(
        doctor,
        medicalCenter,
        appointments,
        blockedSlots,
        selectedConsultationType
      )
      setAvailableDates(allAvailableDates)

      // Skip auto-selection in two cases:
      // 1. If we're still processing URL parameters
      // 2. If the selectedDate changed in the previous render and it wasn't to null
      //    (to prevent cascading updates)
      const shouldSkipAutoSelection =
        !hasProcessedUrlParams.current ||
        (prevSelectedDateRef.current !== selectedDate && selectedDate !== null);

      // Update the ref for next render
      prevSelectedDateRef.current = selectedDate;

      if (!shouldSkipAutoSelection) {
        // Handle case where selected date is no longer valid
        if (selectedDate && !allAvailableDates.some(date => isSameDay(date, selectedDate))) {
          if (allAvailableDates.length > 0) {
            setSelectedDate(allAvailableDates[0]);
            setSelectedTime(null);
          } else {
            setSelectedDate(null);
            setSelectedTime(null);
          }
        }
        // If no date is selected and we have available dates, select the first one
        else if (!selectedDate && allAvailableDates.length > 0) {
          setSelectedDate(allAvailableDates[0]);
        }
      }
    }
  }, [doctor, medicalCenter, appointments, blockedSlots, selectedConsultationType, selectedDate])

  const displayedDates = selectedDate
    ? availableDates.filter(date => isSameMonth(date, selectedDate))
    : availableDates

  // Track if search has been performed
  const [searchPerformed, setSearchPerformed] = useState(false)
  const [patientNotFound, setPatientNotFound] = useState(false)
  const [patientError, setPatientError] = useState<string>("") // For error messages
  const [coverageError, setCoverageError] = useState<string>("") // For coverage validation errors
  const [selectedAssociatedPatient, setSelectedAssociatedPatient] = useState<Patient | null>(null)

  // State for collapsible sections
  const [activeSection, setActiveSection] = useState<"dni" | "datos" | "cobertura" | "">("dni")
  const [completedSections, setCompletedSections] = useState({
    dni: { completed: false, value: "" },
    datos: { completed: false, value: "" },
    cobertura: { completed: false, value: "" }
  })
  const [showPasswordDialog, setShowPasswordDialog] = useState<boolean>(false)
  const [passwordError, setPasswordError] = useState<string>("")

  // State for patient edit dialog
  const [showEditDialog, setShowEditDialog] = useState<boolean>(false)
  const [patientToEdit, setPatientToEdit] = useState<Patient | null>(null)

  // State for patient verification
  const [showVerificationSection, setShowVerificationSection] = useState<boolean>(false)
  const [patientToVerify, setPatientToVerify] = useState<Patient | null>(null)
  const [verificationCode, setVerificationCode] = useState<string>("")
  const [actualVerificationCode, setActualVerificationCode] = useState<string>("")

  const searchPatient = () => {
    if (patientDniSearch) {
      setSearchingPatient(true)
      setSearchPerformed(true)
      setPatientNotFound(false)
      setPatientError("") // Clear any previous errors

      // Clear all fields first
      setPatientName('')
      setPatientLastName('') // Also clear last name
      setPatientPhone('')
      setPatientEmail('')
      setPatientCoverage('')
      setPatientPlan('')
      setNoCoverage(false)
      setUseDefaultCoverage(true)
      setHasDefaultCoverage(false)

      try {
        const foundPatient = patients.find(p => p.dni === patientDniSearch)

        // Check if the patient is associated with the current user
        let isAssociatedWithUser = false
        if (isPatientUser && currentUser && foundPatient) {
          // Check if it's the default patient
          if (currentUser.defaultPatientId === foundPatient.id) {
            isAssociatedWithUser = true
          } else {
            // Check if it's in the associated patients list
            const associatedPatients = getAssociatedPatients()
            isAssociatedWithUser = associatedPatients.some(p => p.id === foundPatient.id)
          }
        }

        if (foundPatient) {
          // Check if we're in the "Crear nuevo paciente" flow
          if (!selectedAssociatedPatient && completedSections.dni.completed === false) {
            // We're trying to create a new patient, but this DNI already exists
            setPatientNotFound(false)

            // Check if the patient has no userId and the current user is authenticated
            if (!foundPatient.userId && isPatientUser && currentUser) {
              // This patient exists but has no user - allow claiming it
              setPatientToVerify(foundPatient)

              // Set the patient data in the form
              setPatientDni(foundPatient.dni)

              // Split the name into first and last name
              const nameParts = foundPatient.name.split(' ');
              if (nameParts.length > 1) {
                setPatientName(nameParts[0]);
                setPatientLastName(nameParts.slice(1).join(' '));
              } else {
                setPatientName(foundPatient.name);
                setPatientLastName('');
              }

              setPatientPhone(foundPatient.phone)
              setPatientEmail(foundPatient.email || '')

              // Handle coverage information
              if (foundPatient.coverage) {
                const defaultCoverage = foundPatient.defaultCoverage || foundPatient.coverage
                setHasDefaultCoverage(true)

                const coverageParts = defaultCoverage.split(' ')
                if (coverageParts.length > 1) {
                  setPatientCoverage(coverageParts[0])
                  setPatientPlan(coverageParts.slice(1).join(' '))
                  setNoCoverage(false)
                } else if (defaultCoverage === "Sin Cobertura") {
                  setPatientCoverage('')
                  setPatientPlan('')
                  setNoCoverage(true)
                } else {
                  setPatientCoverage(defaultCoverage)
                  setPatientPlan('')
                  setNoCoverage(false)
                }
              } else {
                setPatientCoverage('')
                setPatientPlan('')
                setNoCoverage(true)
                setHasDefaultCoverage(false)
              }

              // Update collapsible sections - mark DNI as completed
              setCompletedSections({
                dni: { completed: true, value: `DNI: ${foundPatient.dni}` },
                datos: { completed: false, value: "" },
                cobertura: { completed: false, value: "" }
              })

              // Generate a verification code
              const newVerificationCode = Math.floor(100000 + Math.random() * 900000).toString() // 6-digit code
              setActualVerificationCode(newVerificationCode)

              // For testing purposes, log the code to console
              console.log("Verification code:", newVerificationCode)

              // Send the verification SMS
              if (foundPatient.id) {
                sendPatientVerificationCode(foundPatient.id)
              }

              // Show the verification section
              setShowVerificationSection(true)
              setVerificationCode("") // Clear any previous verification code
              setSearchingPatient(false)
              return
            }

            // If the patient is associated with the current user, show a specific message
            if (isAssociatedWithUser) {
              setPatientError(`Este DNI ya está asociado a su cuenta. Por favor, seleccione el paciente desde la lista de pacientes asociados.`)
            } else {
              // If the patient is not associated with the current user, don't reveal that the DNI exists
              // Just treat it as a new patient creation attempt
              setPatientNotFound(true)
              setPatientDni(patientDniSearch) // Set DNI from search

              // Update collapsible sections for new patient
              setCompletedSections(prev => ({
                ...prev,
                dni: { completed: true, value: `DNI: ${patientDniSearch}` }
              }))
              setActiveSection("datos") // Move to datos section
            }

            setSearchingPatient(false)
            return
          }

          // If the patient is associated with the current user or we're not in the "Crear nuevo paciente" flow
          if (isAssociatedWithUser || !isPatientUser) {
            setPatientFound(foundPatient)
            setPatientDni(foundPatient.dni) // Set DNI from found patient

            // Split the name into first and last name
            const nameParts = foundPatient.name.split(' ');
            if (nameParts.length > 1) {
              setPatientName(nameParts[0]);
              setPatientLastName(nameParts.slice(1).join(' '));
            } else {
              setPatientName(foundPatient.name);
              setPatientLastName('');
            }

            setPatientPhone(foundPatient.phone)
            setPatientEmail(foundPatient.email || '') // Handle case where email might be undefined

            // Handle coverage information
            if (foundPatient.coverage) {
              // Store the default coverage
              const defaultCoverage = foundPatient.defaultCoverage || foundPatient.coverage
              setHasDefaultCoverage(true)

              // Check if coverage has a plan format (e.g., "OSDE 310")
              const coverageParts = defaultCoverage.split(' ');
              if (coverageParts.length > 1) {
                // If format is "OSDE 310", separate into coverage and plan
                setPatientCoverage(coverageParts[0])
                setPatientPlan(coverageParts.slice(1).join(' '))
                setNoCoverage(false)
              } else if (defaultCoverage === "Sin Cobertura") {
                // If it's "Sin Cobertura"
                setPatientCoverage('')
                setPatientPlan('')
                setNoCoverage(true)
              } else {
                // If it's just a coverage name
                setPatientCoverage(defaultCoverage)
                setPatientPlan('')
                setNoCoverage(false)
              }
            } else {
              // No coverage
              setPatientCoverage('')
              setPatientPlan('')
              setNoCoverage(true)
              setHasDefaultCoverage(false)
            }

            // Update collapsible sections for found patient
            setCompletedSections(prev => ({
              ...prev,
              dni: { completed: true, value: `DNI: ${foundPatient.dni}` }
            }))
            setActiveSection("datos") // Move to datos section
          } else {
            // If the patient is not associated with the current user, don't reveal that the DNI exists
            // Just treat it as a new patient creation attempt
            setPatientFound(null)
            setPatientNotFound(true)
            setPatientDni(patientDniSearch) // Set DNI from search

            // Update collapsible sections for new patient
            setCompletedSections(prev => ({
              ...prev,
              dni: { completed: true, value: `DNI: ${patientDniSearch}` }
            }))
            setActiveSection("datos") // Move to datos section
          }
        } else {
          // No patient found
          setPatientFound(null)
          setPatientNotFound(true)
          setPatientDni(patientDniSearch) // Set DNI from search

          // Update collapsible sections for new patient
          setCompletedSections(prev => ({
            ...prev,
            dni: { completed: true, value: `DNI: ${patientDniSearch}` }
          }))
          setActiveSection("datos") // Move to datos section
        }
      } catch (error) {
        // Handle any errors that occur during patient search
        if (error instanceof Error) {
          setPatientError(error.message);
        } else {
          setPatientError("Error al buscar el paciente. Intente nuevamente.");
        }
      } finally {
        setSearchingPatient(false)
      }
    }
  }

  const handleDateChange = (monthsToAdd: number) => {
    if (selectedDate) {
      const newMonth = addMonths(selectedDate, monthsToAdd)
      const datesInNewMonth = availableDates.filter(date => isSameMonth(date, newMonth))

      // Only proceed if there are available dates in the new month
      if (datesInNewMonth.length > 0) {
        const firstAvailableDate = datesInNewMonth.reduce((earliest, current) =>
          earliest < current ? earliest : current
        )
        setSelectedDate(firstAvailableDate)
        setSelectedTime(null)
      } else {
        // If no dates available in the target month, look for the next month with available dates
        let foundMonth = false
        let searchMonths = 1
        const maxSearchMonths = 12 // Limit search to 12 months to avoid infinite loop

        // Search forward if we were moving forward, backward if we were moving backward
        const searchDirection = monthsToAdd > 0 ? 1 : -1

        while (!foundMonth && searchMonths <= maxSearchMonths) {
          const nextMonth = addMonths(newMonth, searchDirection * searchMonths)
          const datesInNextMonth = availableDates.filter(date => isSameMonth(date, nextMonth))

          if (datesInNextMonth.length > 0) {
            const firstAvailableDate = datesInNextMonth.reduce((earliest, current) =>
              earliest < current ? earliest : current
            )
            setSelectedDate(firstAvailableDate)
            setSelectedTime(null)
            foundMonth = true
          }

          searchMonths++
        }

        // If no available dates found in any month, don't change the selected date
      }
    }
  }

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    setSelectedTime(null)
  }

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time)
  }

  // Function to get coverage summary text
  const getCoverageSummary = (): string => {
    let summaryText = "";

    // Prioritize based on what's selected
    if (useDefaultCoverage && hasDefaultCoverage) {
      summaryText = selectedPatientCoverage;
    } else if (overrideNoCoverage) {
      summaryText = "Sin Cobertura";
    } else if (overrideCoverage) {
      summaryText = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
    } else if (selectedAssociatedPatient) {
      // If an associated patient is selected, use their coverage
      summaryText = selectedAssociatedPatient.coverage || "Sin Cobertura";
    } else if (isPatientUser && currentUser?.defaultPatientId && !searchPerformed) {
      // If using the default patient
      const defaultPatient = getPatientById(currentUser.defaultPatientId);
      summaryText = defaultPatient?.coverage || "Sin Cobertura";
    }

    if (!summaryText) summaryText = "No seleccionado"; // Fallback

    // Add warning prefix if not accepted
    if (isCoverageAccepted === false) {
      return `${summaryText} (No aceptada)`;
    }

    return summaryText;
  };

  // Update selectedPatientCoverage when patient changes
  useEffect(() => {
    if (selectedAssociatedPatient) {
      setSelectedPatientCoverage(selectedAssociatedPatient.coverage || "Sin Cobertura");
      setHasDefaultCoverage(true);
    } else if (isPatientUser && currentUser?.defaultPatientId && !searchPerformed) {
      const defaultPatient = getPatientById(currentUser.defaultPatientId);
      if (defaultPatient) {
        setSelectedPatientCoverage(defaultPatient.coverage || "Sin Cobertura");
        setHasDefaultCoverage(true);
      }
    } else if (patientFound) {
      setSelectedPatientCoverage(patientFound.coverage || "Sin Cobertura");
      setHasDefaultCoverage(true);
    }
  }, [selectedAssociatedPatient, isPatientUser, currentUser, searchPerformed, getPatientById, patientFound]);

  // Function to check if coverage is valid (not "No seleccionado" and plan is selected when needed)
  const isCoverageValid = (): boolean => {
    // Get the coverage summary text based on priority
    let coverageText = "";

    if (useDefaultCoverage && hasDefaultCoverage) {
      coverageText = selectedPatientCoverage;
    } else if (overrideNoCoverage) {
      coverageText = "Sin Cobertura";
    } else if (overrideCoverage) {
      coverageText = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
    }

    // Check if coverage is valid (not empty and not "No seleccionado")
    if (!coverageText || coverageText === "No seleccionado") return false;

    // If a coverage is selected but plan is not selected, return false
    if (!useDefaultCoverage && overrideCoverage && !overrideNoCoverage && !overridePlan) return false;

    return true;
  };

  const handleNextStep = () => {
    // Clear any previous errors
    setPatientError("");

    if (currentStep === 1 && selectedTime) {
      // Always proceed to step 2, regardless of authentication status
      // Authentication will be handled within step 2
      setCurrentStep(2);
      // Scroll to top when moving to next step - use setTimeout to ensure DOM has updated
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100);
    } else if (currentStep === 2) {
      // If the user is a patient and has a default patient, and either:
      // 1. Hasn't searched for another patient (searchPerformed is false), OR
      // 2. Has clicked "Reservar para mí" after searching (searchPerformed is false AND selectedAssociatedPatient is null)
      // Then use the default patient
      if (isPatientUser && currentUser?.defaultPatientId && (!searchPerformed || (searchPerformed && !selectedAssociatedPatient))) {
        console.log('Using default patient for appointment in handleNextStep:', currentUser.defaultPatientId);

        // If using default patient, make sure we're using the correct name and last name
        const defaultPatient = getPatientById(currentUser.defaultPatientId);
        if (defaultPatient) {
          const nameParts = defaultPatient.name.split(' ');
          if (nameParts.length > 1) {
            setPatientName(nameParts[0]);
            setPatientLastName(nameParts.slice(1).join(' '));
          } else {
            setPatientName(defaultPatient.name);
            setPatientLastName('');
          }
        }

        createAppointment(currentUser.defaultPatientId);
        return;
      } else if (selectedAssociatedPatient && selectedAssociatedPatient.id) {
        console.log('Using associated patient for appointment in handleNextStep:', selectedAssociatedPatient.id);

        // If using associated patient, make sure we're using the correct name and last name
        const nameParts = selectedAssociatedPatient.name.split(' ');
        if (nameParts.length > 1) {
          setPatientName(nameParts[0]);
          setPatientLastName(nameParts.slice(1).join(' '));
        } else {
          setPatientName(selectedAssociatedPatient.name);
          setPatientLastName('');
        }

        createAppointment(selectedAssociatedPatient.id);
        return;
      }

      const validationResult = validatePatientForm();
      if (!validationResult.valid) {
        if (validationResult.error) {
          setPatientError(validationResult.error);
        }
        return;
      }

      // Format coverage string based on selected options
      let coverageString = "";
      if (noCoverage) {
        coverageString = "Sin Cobertura"; // Ensure proper capitalization
      } else if (patientCoverage) {
        coverageString = patientCoverage;
        if (patientPlan) {
          coverageString += " " + patientPlan;
        }
      }

      // If no coverage is selected, default to "Sin Cobertura"
      if (!coverageString) {
        coverageString = "Sin Cobertura";
      }

      try {
        // Format the full name with proper capitalization
        const fullName = capitalizeName(`${patientName.trim()} ${patientLastName.trim()}`.trim());

        // Create patient object
        const newPatient: Patient = {
          // If patient was found, use their ID, otherwise it will be generated in addPatient
          ...(patientFound?.id ? { id: patientFound.id } : {}),
          name: fullName,
          dni: patientDni,
          phone: patientPhone,
          email: patientEmail, // This will include any newly added email
          coverage: coverageString,
          // If this is a new patient or we're updating an existing one, set the defaultCoverage
          // Only set defaultCoverage if it's a new patient or if the patient doesn't have one yet
          ...(!patientFound || !patientFound.defaultCoverage ? { defaultCoverage: coverageString } : {})
        }

        console.log('Cita page - about to add patient to context:', newPatient)

        // Add patient to context - this is now our single source of truth
        const patientId = addPatient(newPatient)
        console.log('Cita page - patient added/updated with ID:', patientId)

        // Verify patient was added by checking if it exists in the patients array
        const patientExists = getPatientById(patientId) !== undefined
        console.log('Cita page - patient exists in context:', patientExists)

        // Double check all patients in context
        console.log('Cita page - all patients in context:', patients.length)
        console.log('Cita page - can find patient by ID:', getPatientById(patientId))
        console.log('Cita page - can find patient by name:', getPatientByName(patientName))

        // If this is a new patient (not found by search) and we're not using the "Crear paciente" button flow
        // (which already creates the patient), show password dialog to create a user
        if (!patientFound && !selectedAssociatedPatient) {
          setShowPasswordDialog(true);
          return; // Wait for password dialog to complete
        }

        // If we're using an associated patient, use that patient's ID
        if (selectedAssociatedPatient && selectedAssociatedPatient.id) {
          createAppointment(selectedAssociatedPatient.id);
          return;
        }

        // If patient was found, proceed with appointment creation
        createAppointment(patientId);
      } catch (error) {
        // Handle errors from patient creation
        if (error instanceof Error) {
          setPatientError(error.message);
        } else {
          setPatientError("Error al crear el paciente. Intente nuevamente.");
        }
      }
    }
  }

  // Function to handle verification code submission
  const handleVerifyPatient = async () => {
    if (!patientToVerify || !currentUser || !patientToVerify.id) return;

    setPatientError("");

    try {
      // For development purposes, directly compare with the actualVerificationCode
      // This is a workaround for the demo to make verification work
      if (verificationCode === actualVerificationCode) {
        // Do NOT associate the patient with the user yet - this will happen when the user clicks "Asociar paciente"
        // We just verify the code and show the patient data

        // Call the success handler
        handleVerificationSuccess(patientToVerify.id);

        // Reset verification state
        setShowVerificationSection(false);
        setVerificationCode("");
      } else {
        setPatientError("El código de verificación es incorrecto. Intente nuevamente.");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Error al verificar el código";
      setPatientError(errorMessage);
    }
  };

  // Function to handle verification success
  const handleVerificationSuccess = (patientId: string) => {
    // Get the verified patient
    const verifiedPatient = getPatientById(patientId);
    if (verifiedPatient) {
      // Update the form with the verified patient's data
      setPatientFound(verifiedPatient);

      // The patient data should already be set in the form from the searchPatient function
      // But we'll set it again to be sure
      setPatientDni(verifiedPatient.dni);

      // Split the name into first and last name
      const nameParts = verifiedPatient.name.split(' ');
      if (nameParts.length > 1) {
        setPatientName(nameParts[0]);
        setPatientLastName(nameParts.slice(1).join(' '));
      } else {
        setPatientName(verifiedPatient.name);
        setPatientLastName('');
      }

      setPatientPhone(verifiedPatient.phone);
      setPatientEmail(verifiedPatient.email || '');

      // Handle coverage information
      if (verifiedPatient.coverage) {
        const defaultCoverage = verifiedPatient.defaultCoverage || verifiedPatient.coverage;
        setHasDefaultCoverage(true);

        const coverageParts = defaultCoverage.split(' ');
        if (coverageParts.length > 1) {
          setPatientCoverage(coverageParts[0]);
          setPatientPlan(coverageParts.slice(1).join(' '));
          setNoCoverage(false);
        } else if (defaultCoverage === "Sin Cobertura") {
          setPatientCoverage('');
          setPatientPlan('');
          setNoCoverage(true);
        } else {
          setPatientCoverage(defaultCoverage);
          setPatientPlan('');
          setNoCoverage(false);
        }
      } else {
        setPatientCoverage('');
        setPatientPlan('');
        setNoCoverage(true);
        setHasDefaultCoverage(false);
      }

      // Update collapsible sections - mark DNI as completed and datos with patient name
      setCompletedSections({
        dni: { completed: true, value: `DNI: ${verifiedPatient.dni}` },
        datos: { completed: false, value: verifiedPatient.name },
        cobertura: { completed: false, value: "" }
      });

      // Clear verification state
      setPatientToVerify(null);
      setShowVerificationSection(false);

      // Open the datos section
      setActiveSection("datos");

      // Show success message in green
      setPatientError("Paciente verificado exitosamente. Complete los datos para asociarlo a su cuenta.");
    }
  };

  // Function to handle password confirmation and user creation
  const handlePasswordConfirm = async (password: string) => {
    try {
      // Get the patient that was just created
      const patient = getPatientByDni(patientDni);
      if (!patient) {
        setPasswordError("No se pudo encontrar el paciente recién creado.");
        return;
      }

      // Create a user for the patient
      const { userId, patientId } = await createPatientUser(patient, password);
      console.log('Created user with ID:', userId, 'for patient:', patientId);

      // Close the password dialog
      setShowPasswordDialog(false);

      // Create the appointment
      createAppointment(patientId);
    } catch (error) {
      if (error instanceof Error) {
        setPasswordError(error.message);
      } else {
        setPasswordError("Error al crear la cuenta de usuario. Intente nuevamente.");
      }
    }
  }

  // Helper function to validate if a consultation type exists for the doctor
  const isValidConsultationType = (typeName: string | null): boolean => {
    if (!typeName || !doctor || !doctor.consultationTypes) {
      return false;
    }

    return doctor.consultationTypes.some(ct => ct.name === typeName && ct.availableOnline !== false);
  };

  // Helper function to get the duration for a consultation type
  const getDuration = (typeName: string | null): number => {
    if (!typeName || !doctor) {
      return doctor?.appointmentSlotDuration || 15;
    }

    const consultationType = doctor.consultationTypes.find(ct => ct.name === typeName);
    if (!consultationType) {
      return doctor.appointmentSlotDuration || 15;
    }

    const slotDuration = doctor.appointmentSlotDuration || 15;
    const multiplier = consultationType.duration === "default" ? 1 :
                      (isNaN(parseInt(consultationType.duration)) ? 1 : parseInt(consultationType.duration));

    return slotDuration * multiplier;
  };

  // Helper function to generate pricing information components
  const getPricingInfo = () => {
    if (!selectedConsultationType || !doctor) return null;

    const selectedType = doctor.consultationTypes.find(t => t.name === selectedConsultationType);
    if (!selectedType) return null;

    const elements = [];

    // If "Sin Cobertura" is selected, show the base price
    if (urlNoCoverage && selectedType.acceptsPrivatePay) {
      elements.push(
        <div key="price" className="p-3 bg-blue-50 border border-blue-100 rounded-md">
          <div className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2 text-blue-600" />
            <div>
              <span className="font-semibold text-blue-800 text-base">Precio particular:</span>
              <span className="ml-2 text-blue-700 text-base font-medium">${formatPrice(selectedType.basePrice)}</span>
            </div>
          </div>
        </div>
      );
    }

    // If a coverage is selected, check if it has a copay
    if (urlCoverage && !urlNoCoverage && coverageId) {
      // Find the copay for this coverage and plan
      const copay = selectedType.copays?.find(
        c => c.coverageId === coverageId &&
            (c.planId === null || (urlPlan && c.planId === urlPlan))
      );

      if (copay) {
        elements.push(
          <div key="copay" className="p-3 bg-amber-50 border border-amber-100 rounded-md">
            <div className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2 text-amber-600" />
              <div>
                <span className="font-semibold text-amber-800 text-base">Copago:</span>
                <span className="ml-2 text-amber-700 text-base font-medium">${formatPrice(copay.amount)}</span>
              </div>
            </div>
          </div>
        );
      }
    }

    return elements.length > 0 ? <div className="space-y-3 mt-3">{elements}</div> : null;
  };

  // Function to create an appointment for a patient
  const createAppointment = async (patientId: string) => {
    if (selectedDate && selectedTime) {
      console.log('Creating appointment with time:', selectedTime)

      // Validate that the consultation type exists for this doctor
      if (selectedConsultationType && !isValidConsultationType(selectedConsultationType)) {
        console.error(`Invalid consultation type: ${selectedConsultationType} for doctor: ${doctorId}`);
        // Fall back to default consultation type or show an error
        setPatientError("El tipo de consulta seleccionado no está disponible para este médico. Por favor, seleccione otro tipo de consulta.");
        return;
      }

      // Store the confirmed time so it's preserved even if selectedTime changes
      setConfirmedTime(selectedTime)

      // Determine which patient ID to use for the appointment
      let finalPatientId = patientId;

      // If the user is a patient and has a default patient, and either:
      // 1. Hasn't searched for another patient (searchPerformed is false), OR
      // 2. Has clicked "Reservar para mí" after searching (searchPerformed is false AND selectedAssociatedPatient is null)
      // Then use the default patient
      if (isPatientUser && currentUser?.defaultPatientId && (!searchPerformed || (searchPerformed && !selectedAssociatedPatient))) {
        console.log('Using default patient for appointment:', currentUser.defaultPatientId);
        finalPatientId = currentUser.defaultPatientId;

        // If using default patient, make sure we're using the correct name and last name
        const defaultPatient = getPatientById(currentUser.defaultPatientId);
        if (defaultPatient) {
          const nameParts = defaultPatient.name.split(' ');
          if (nameParts.length > 1) {
            setPatientName(nameParts[0]);
            setPatientLastName(nameParts.slice(1).join(' '));
          } else {
            setPatientName(defaultPatient.name);
            setPatientLastName('');
          }
        }
      } else if (selectedAssociatedPatient && selectedAssociatedPatient.id) {
        console.log('Using associated patient for appointment:', selectedAssociatedPatient.id);
        finalPatientId = selectedAssociatedPatient.id;

        // If using associated patient, make sure we're using the correct name and last name
        const nameParts = selectedAssociatedPatient.name.split(' ');
        if (nameParts.length > 1) {
          setPatientName(nameParts[0]);
          setPatientLastName(nameParts.slice(1).join(' '));
        } else {
          setPatientName(selectedAssociatedPatient.name);
          setPatientLastName('');
        }
      } else {
        console.log('Using provided patient ID for appointment:', patientId);
      }

      const dateStr = format(selectedDate, 'yyyy-MM-dd')
      const appointmentId = generateCustomId('apt')
      // Determine the coverage to use based on user selection with clear priority
      let coverageToUse = "";
      if (useDefaultCoverage && hasDefaultCoverage) {
        // Use the patient's default coverage
        coverageToUse = selectedPatientCoverage;
      } else if (overrideNoCoverage) {
        // Use "Sin Cobertura" if no coverage is selected
        coverageToUse = "Sin Cobertura";
      } else if (overrideCoverage) {
        // Use the override coverage and plan
        coverageToUse = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
      } else if (noCoverage) {
        // Use "Sin Cobertura" if noCoverage is true
        coverageToUse = "Sin Cobertura";
      } else if (patientFound && patientFound.coverage) {
        // Use the patient's coverage if found
        coverageToUse = patientFound.coverage;
      } else {
        // Fallback to the patient's coverage from the database
        coverageToUse = getPatientById(finalPatientId)?.coverage || "Sin Cobertura";
      }

      // Calculate the appointment duration based on the consultation type
      const appointmentDuration = getDuration(selectedConsultationType);

      // If we're modifying an existing appointment, use updateAppointment instead of addAppointment
      if (modifyingAppointmentId) {
        // Prepare the updated appointment data
        const updatedAppointment: Partial<Appointment> = {
          date: dateStr,
          time: selectedTime,
          patient: finalPatientId,
          type: selectedConsultationType || "Consulta",
          coverage: coverageToUse,
          contact: patientPhone || getPatientById(finalPatientId)?.phone || '',
          doctorId: doctorId!,
          medicalCenterId: medicalCenterId!,
          duration: appointmentDuration
        }

        // Update the existing appointment
        updateAppointment(modifyingAppointmentId, updatedAppointment)
        console.log(`Modified appointment: ${modifyingAppointmentId}`)
      } else {
        // Create a new appointment
        const newAppointment: Appointment = {
          id: appointmentId,
          date: dateStr,
          time: selectedTime, // Ensure this is never null
          patient: finalPatientId, // This is the patient ID from our single source of truth
          status: "Agendado",
          source: "Turnera",
          type: selectedConsultationType || "Consulta",
          coverage: coverageToUse, // Use the determined coverage
          contact: patientPhone || getPatientById(finalPatientId)?.phone || '',
          doctorId: doctorId!,
          medicalCenterId: medicalCenterId!,
          duration: appointmentDuration
        }
        await addAppointment(newAppointment)
        console.log(`Created new appointment: ${appointmentId}`)
      }

      setCurrentStep(3)
      // Scroll to top when moving to confirmation step - use setTimeout to ensure DOM has updated
      setTimeout(() => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }, 100)
    }
  }

  const validatePatientForm = useCallback(() => {
    // If the user is a patient and has a default patient, we can proceed
    if (isPatientUser && currentUser?.defaultPatientId && !searchPerformed) {
      return { valid: true };
    }

    // Check if required fields are filled
    if (!patientName || !patientLastName || !patientDni) {
      return { valid: false, error: "Nombre, Apellido y DNI son campos obligatorios" };
    }

    // Validate DNI length (7-8 characters)
    if (patientDni.length < 7 || patientDni.length > 8) {
      return { valid: false, error: "El DNI debe tener entre 7 y 8 dígitos" };
    }

    // Check if the DNI belongs to the user's default patient or any associated patient
    if (isPatientUser && currentUser) {
      // Check default patient
      const defaultPatient = currentUser.defaultPatientId ? getPatientById(currentUser.defaultPatientId) : null;
      if (defaultPatient && defaultPatient.dni === patientDni) {
        return { valid: false, error: "Este DNI ya está asociado a su cuenta. Por favor, seleccione 'Reservar para mí' o elija otro paciente asociado." };
      }

      // Check associated patients
      const associatedPatients = getAssociatedPatients();
      const isDniAlreadyAssociated = associatedPatients.some(p => p.dni === patientDni);
      if (isDniAlreadyAssociated) {
        return { valid: false, error: "Este DNI ya está asociado a su cuenta. Por favor, seleccione el paciente desde la lista de pacientes asociados." };
      }
    }

    // Validate phone if provided and not just the area code
    if (patientPhone && patientPhone.trim() !== "+" && patientPhone.trim() !== "" && patientPhone.length > 5) {
      try {
        const phoneNumber = phoneUtil.parseAndKeepRawInput(patientPhone);
        const isValid = phoneUtil.isValidNumber(phoneNumber);

        if (!isValid) {
          return { valid: false, error: "El número de teléfono no es válido" };
        }
      } catch {
        return { valid: false, error: "El número de teléfono no es válido" };
      }
    }

    if ((!patientPhone || patientPhone.trim() === "+" || patientPhone.trim() === "") &&
        searchPerformed && !patientFound && isPatientUser && currentUser) {
      const defaultPatient = getPatientById(currentUser.defaultPatientId || "");
      if (!defaultPatient?.phone) {
        return { valid: false, error: "Debe ingresar un número de teléfono o usar un usuario con teléfono registrado" };
      }
    }

    // Validate email if provided
    if (patientEmail && patientEmail.trim() !== "" && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(patientEmail)) {
      return { valid: false, error: "El correo electrónico no es válido" };
    }

    if (!patientEmail && searchPerformed && !patientFound && isPatientUser && currentUser) {
      const defaultPatient = getPatientById(currentUser.defaultPatientId || "");
      if (!defaultPatient?.email && !currentUser.email) {
        return { valid: false, error: "Debe ingresar un correo electrónico o usar un usuario con email registrado" };
      }
    }

    return { valid: true };
  }, [patientName, patientLastName, patientDni, patientPhone, patientEmail, patientFound, isPatientUser, currentUser, searchPerformed, phoneUtil, getPatientById, getAssociatedPatients]);

  // Function to handle going back to previous step or navigating to source page
  const handlePreviousStep = () => {
    // Clear any errors when navigating
    setPatientError("");

    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    } else {
      if (source === 'modifying') {
        // If source is 'modifying', always go back to the patient dashboard
        router.push('/plataforma/paciente')
      } else if (doctorId && medicalCenterId) {
        // Check the source parameter to determine where to go back to
        if (source === 'medicalCenter') {
          router.push(`/plataforma/reservar/cm/${medicalCenterId}`)
        } else if (source === 'search') {
          // Get the search type and query from URL if available
          const searchType = searchParams.get('searchType') || 'especialidad' // Default to specialty
          const query = searchParams.get('q') || ''
          const location = searchParams.get('loc') || ''

          // Build coverage parameters for the URL
          let coverageParams = ''
          if (urlNoCoverage) {
            coverageParams = '&noCoverage=true'
          } else if (urlCoverage) {
            coverageParams = `&coverage=${encodeURIComponent(urlCoverage)}`
            if (urlPlan) {
              coverageParams += `&plan=${encodeURIComponent(urlPlan)}`
            }
          }

          // Redirect back to the search results page
          router.push(`/plataforma/buscar/${searchType}?q=${encodeURIComponent(query)}&loc=${encodeURIComponent(location)}${coverageParams}`)
        } else {
          // Default to doctor page if source is not specified or is 'doctor'
          router.push(`/plataforma/reservar/d/${doctorId}`)
        }
      } else {
        router.push("/")
      }
    }
  }

  // Handle successful authentication
  const handleAuthSuccess = () => {
    console.log('Cita page - Authentication success, checking for patient user');

    // Reload patients from localStorage to ensure we have the latest data
    const storedPatients = localStorage.getItem("patients");
    if (storedPatients) {
      try {
        const parsedPatients = JSON.parse(storedPatients);
        console.log('Cita page - Reloaded patients after auth success:', parsedPatients.length);
      } catch (error) {
        console.error("Error parsing patients from localStorage:", error);
      }
    }

    // Only handle authentication success for patient users
    if (isPatientUser && currentUser?.defaultPatientId) {
      console.log('Cita page - Patient user authenticated with defaultPatientId:', currentUser.defaultPatientId);

      // Try to get the patient directly from localStorage
      let patient = null;
      if (storedPatients) {
        try {
          const parsedPatients = JSON.parse(storedPatients);
          patient = parsedPatients.find((p: Patient) => p.id === currentUser.defaultPatientId);
          if (patient) {
            console.log('Cita page - Found patient in localStorage:', patient.name);
          } else {
            console.warn('Cita page - Patient not found in localStorage, trying context');
            // Fallback to context
            patient = getPatientById(currentUser.defaultPatientId);
          }
        } catch (error) {
          console.error("Error finding patient in localStorage:", error);
          // Fallback to context
          patient = getPatientById(currentUser.defaultPatientId);
        }
      } else {
        // Fallback to context
        patient = getPatientById(currentUser.defaultPatientId);
      }

      if (patient) {
        console.log('Cita page - Setting patient data for:', patient.name);
        setPatientFound(patient);
        setPatientDni(patient.dni);
        setPatientName(patient.name);
        setPatientPhone(patient.phone);
        setPatientEmail(patient.email || '');

        // Handle coverage information
        if (patient.coverage) {
          // Store the default coverage
          const defaultCoverage = patient.defaultCoverage || patient.coverage;
          setHasDefaultCoverage(true);

          // Check if coverage has a plan format (e.g., "OSDE 310")
          const coverageParts = defaultCoverage.split(' ');
          if (coverageParts.length > 1) {
            // If format is "OSDE 310", separate into coverage and plan
            setPatientCoverage(coverageParts[0]);
            setPatientPlan(coverageParts.slice(1).join(' '));
            setNoCoverage(false);
          } else if (defaultCoverage === "Sin Cobertura") {
            // If it's "Sin Cobertura"
            setPatientCoverage('');
            setPatientPlan('');
            setNoCoverage(true);
          } else {
            // If it's just a coverage name
            setPatientCoverage(defaultCoverage);
            setPatientPlan('');
            setNoCoverage(false);
          }
        }

        setSearchPerformed(true);

        // Update collapsible sections for authenticated patient
        setCompletedSections({
          dni: { completed: true, value: `DNI: ${patient.dni}` },
          datos: { completed: true, value: patient.name },
          cobertura: { completed: true, value: patient.coverage || "Sin Cobertura" }
        });
        setActiveSection("cobertura"); // Show all sections, with cobertura active
      } else {
        console.error('Cita page - Could not find patient for current user after authentication');
      }
    } else {
      console.log('Cita page - Not a patient user or no defaultPatientId');
    }
    // Non-patient users are treated as if they're not logged in
  }

  // We no longer need handleAuthCancel since we removed the onCancel prop from PatientAuthForm

  const handleFinish = () => {
    // Reset all state before navigating away
    setCurrentStep(1)
    setSelectedDate(null)
    setSelectedTime(null)
    setConfirmedTime(null)
    setSelectedConsultationType(null)
    setPatientDniSearch("")
    setPatientFound(null)
    setPatientName("")
    setPatientDni("")
    setPatientPhone("")
    setPatientEmail("")
    setPatientCoverage("")
    setPatientPlan("")
    setNoCoverage(false)
    setUseDefaultCoverage(true)
    setHasDefaultCoverage(false)

    // If we were modifying an appointment, or the user is a patient, navigate to the patient dashboard
    if (modifyingAppointmentId || isPatientUser) {
      router.push('/plataforma/paciente')
    } else {
      // Otherwise, navigate back to the source page (doctor, medical center, or search)
      if (source === 'doctor' && doctorId) {
        router.push(`/plataforma/reservar/d/${doctorId}`)
      } else if (source === 'medicalCenter' && medicalCenterId) {
        router.push(`/plataforma/reservar/cm/${medicalCenterId}`)
      } else if (source === 'search') {
        // Get the search type and query from URL if available
        const searchType = searchParams.get('searchType') || 'especialidad' // Default to specialty
        const query = searchParams.get('q') || ''
        const location = searchParams.get('loc') || ''

        // Build coverage parameters for the URL
        let coverageParams = ''
        if (urlNoCoverage) {
          coverageParams = '&noCoverage=true'
        } else if (urlCoverage) {
          coverageParams = `&coverage=${encodeURIComponent(urlCoverage)}`
          if (urlPlan) {
            coverageParams += `&plan=${encodeURIComponent(urlPlan)}`
          }
        }

        // Redirect back to the search results page
        router.push(`/plataforma/buscar/${searchType}?q=${encodeURIComponent(query)}&loc=${encodeURIComponent(location)}${coverageParams}`)
      } else if (doctorId) {
        // Fallback to doctor page if source is not specified
        router.push(`/plataforma/reservar/d/${doctorId}`)
      } else if (medicalCenterId) {
        // Fallback to medical center page if source is not specified
        router.push(`/plataforma/reservar/cm/${medicalCenterId}`)
      } else {
        // Fallback to home page if neither doctor nor medical center is specified
        router.push("/")
      }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-blue-50">
        <Header />
        <div className="flex items-center justify-center min-h-[calc(100vh-66px)]">
          <div className="text-center bg-white p-8 rounded-xl shadow-lg border border-blue-100">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
              <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600 animate-pulse" />
              </div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">Cargando información</h3>
            <p className="text-gray-500">Preparando su reserva...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!doctor || !medicalCenter) {
    return (
      <div className="min-h-screen bg-blue-50">
        <Header />
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md p-8 text-center border border-red-100">
            <div className="bg-red-50 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6">
              <Calendar className="h-10 w-10 text-red-500" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">Información no encontrada</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              No pudimos encontrar la información necesaria para su reserva. Por favor, intente nuevamente.
            </p>
            <Button
              onClick={() => router.push("/")}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5"
              size="lg"
            >
              Volver al inicio
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-blue-50">
      <Header />

      {/* Password Dialog */}
      <PasswordDialog
        open={showPasswordDialog}
        onOpenChange={setShowPasswordDialog}
        onConfirm={handlePasswordConfirm}
        error={passwordError}
      />

      {/* Patient verification is now inline in the DNI section */}

      {/* Patient Edit Dialog */}
      <PatientEditDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        patient={patientToEdit}
        onSave={(updatedPatient) => {
          // Update the patient in the context
          addPatient(updatedPatient);

          // Update the selected patient
          setSelectedAssociatedPatient(updatedPatient);
          setPatientFound(updatedPatient);

          // Update form fields with the updated patient data
          setPatientDni(updatedPatient.dni);

          // Split the name into first and last name
          const nameParts = updatedPatient.name.split(' ');
          if (nameParts.length > 1) {
            setPatientName(nameParts[0]);
            setPatientLastName(nameParts.slice(1).join(' '));
          } else {
            setPatientName(updatedPatient.name);
            setPatientLastName('');
          }

          setPatientPhone(updatedPatient.phone);
          setPatientEmail(updatedPatient.email || '');

          // Handle coverage information
          if (updatedPatient.coverage) {
            const defaultCoverage = updatedPatient.defaultCoverage || updatedPatient.coverage;
            setHasDefaultCoverage(true);

            const coverageParts = defaultCoverage.split(' ');
            if (coverageParts.length > 1) {
              setPatientCoverage(coverageParts[0]);
              setPatientPlan(coverageParts.slice(1).join(' '));
              setNoCoverage(false);
            } else if (defaultCoverage === "Sin Cobertura") {
              setPatientCoverage('');
              setPatientPlan('');
              setNoCoverage(true);
            } else {
              setPatientCoverage(defaultCoverage);
              setPatientPlan('');
              setNoCoverage(false);
            }
          } else {
            setPatientCoverage('');
            setPatientPlan('');
            setNoCoverage(true);
            setHasDefaultCoverage(false);
          }

          // Mark all sections as completed
          setCompletedSections({
            dni: { completed: true, value: `DNI: ${updatedPatient.dni}` },
            datos: { completed: true, value: updatedPatient.name },
            cobertura: { completed: true, value: updatedPatient.coverage || "Sin Cobertura" }
          });

          // Close the dialog
          setShowEditDialog(false);
          setPatientToEdit(null);
          setPatientError(""); // Clear any error messages
        }}
        onCancel={() => {
          setShowEditDialog(false);
          setPatientToEdit(null);
        }}
        plansByCoverage={plansByCoverage}
      />

      <main className="container mx-auto px-4 py-6 sm:py-8">
        <div className="max-w-3xl mx-auto">
          {/* Steps Counter in a stylish container */}
          <div className="mb-8 bg-white p-5 rounded-xl shadow-sm border border-[#0070F3]/20">
            <div className="flex items-center justify-between max-w-[360px] mx-auto">
              <div className="flex flex-col items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= 1 ? 'bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 text-[#0a7c82]' : 'bg-gray-100 text-gray-400'}`}>
                  <span className={`${currentStep >= 1 ? 'text-[#0a7c82]' : 'text-gray-400'} font-bold text-lg`}>1</span>
                </div>
                <span className="mt-1 text-xs font-medium text-[#1c2533]">Fecha</span>
              </div>
              <div className={`w-20 sm:w-24 h-0.5 ${currentStep >= 2 ? 'bg-[#1cd8e1]' : 'bg-gray-200'}`}></div>
              <div className="flex flex-col items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= 2 ? 'bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 text-[#0a7c82]' : 'bg-gray-100 text-gray-400'}`}>
                  <span className={`${currentStep >= 2 ? 'text-[#0a7c82]' : 'text-gray-400'} font-bold text-lg`}>2</span>
                </div>
                <span className="mt-1 text-xs font-medium text-[#1c2533]">Datos</span>
              </div>
              <div className={`w-20 sm:w-24 h-0.5 ${currentStep >= 3 ? 'bg-[#1cd8e1]' : 'bg-gray-200'}`}></div>
              <div className="flex flex-col items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= 3 ? 'bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 text-[#0a7c82]' : 'bg-gray-100 text-gray-400'}`}>
                  <span className={`${currentStep >= 3 ? 'text-[#0a7c82]' : 'text-gray-400'} font-bold text-lg`}>3</span>
                </div>
                <span className="mt-1 text-xs font-medium text-[#1c2533]">Confirmación</span>
              </div>
            </div>
          </div>

          <Card className="w-full overflow-hidden rounded-xl shadow-sm border border-[#0070F3]/20">
            <CardHeader className="bg-gradient-to-r from-[#0070F3]/20 to-[#0070F3]/10 py-3 px-5 border-b border-[#0070F3]/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 mr-3 shrink-0">
                    <span className="text-[#0a7c82] font-bold text-lg">{currentStep}</span>
                  </div>
                  <CardTitle className="text-lg font-semibold text-[#1c2533]">
                    {currentStep === 1 && "Seleccionar Fecha y Hora"}
                    {currentStep === 2 && "Ingresar Datos del Paciente"}
                    {currentStep === 3 && "Reserva Confirmada"}
                  </CardTitle>
                </div>
                {modifyingAppointmentId && (
                  <Badge className="bg-amber-100 text-amber-800 border border-amber-200 hover:bg-amber-100 hover:text-amber-800">
                    Modificando turno
                  </Badge>
                )}
              </div>
            </CardHeader>

            <CardContent className="p-3 sm:p-6">
              {currentStep === 1 && (
                <DateTimeSelectionStep
                  doctor={doctor}
                  medicalCenter={medicalCenter}
                  selectedUrlCoverage={selectedUrlCoverage}
                  selectedConsultationType={selectedConsultationType}
                  setSelectedConsultationType={setSelectedConsultationType}
                  selectedDate={selectedDate}
                  displayedDates={displayedDates}
                  availableTimeSlots={availableTimeSlots}
                  selectedTime={selectedTime}
                  availableDates={availableDates}
                  handleDateChange={handleDateChange}
                  handleDateSelect={handleDateSelect}
                  handleTimeSelect={handleTimeSelect}
                  urlNoCoverage={urlNoCoverage}
                  urlCoverage={urlCoverage}
                  urlPlan={urlPlan}
                  coverageId={coverageId}
                />
              )}

              {currentStep === 2 && (
                <div className="space-y-4">
                  {!isPatientUser ? (
                    // Show authentication form for unauthenticated users
                    <div className="mb-6">
                      <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
                        <p className="text-blue-800 font-medium">Para continuar con la reserva, necesita iniciar sesión o registrarse.</p>
                      </div>
                      <PatientAuthForm
                        onSuccess={handleAuthSuccess}
                      />
                    </div>
                  ) : (
                    <PatientSelectionStep
                      currentUser={currentUser}
                      isPatientUser={isPatientUser}
                      searchPerformed={searchPerformed}
                      selectedAssociatedPatient={selectedAssociatedPatient}
                      patientDniSearch={patientDniSearch}
                      patientFound={patientFound}
                      patientNotFound={patientNotFound}
                      patientError={patientError}
                      patientToVerify={patientToVerify}
                      showVerificationSection={showVerificationSection}
                      verificationCode={verificationCode}
                      actualVerificationCode={actualVerificationCode}
                      patientDni={patientDni}
                      patientName={patientName}
                      patientLastName={patientLastName}
                      patientPhone={patientPhone}
                      patientEmail={patientEmail}
                      phoneError={phoneError}
                      phoneUtil={phoneUtil}
                      searchingPatient={searchingPatient}
                      activeSection={activeSection}
                      completedSections={completedSections}
                      getPatientById={getPatientById}
                      getAssociatedPatients={getAssociatedPatients}
                      setPatientDniSearch={setPatientDniSearch}
                      setPatientFound={setPatientFound}
                      setPatientNotFound={setPatientNotFound}
                      setSearchPerformed={setSearchPerformed}
                      setSelectedAssociatedPatient={setSelectedAssociatedPatient}
                      setPatientError={setPatientError}
                      setCompletedSections={setCompletedSections}
                      setActiveSection={setActiveSection}
                      setPatientDni={setPatientDni}
                      setPatientName={setPatientName}
                      setPatientLastName={setPatientLastName}
                      setPatientPhone={setPatientPhone}
                      setPatientEmail={setPatientEmail}
                      setPatientCoverage={setPatientCoverage}
                      setPatientPlan={setPatientPlan}
                      setNoCoverage={setNoCoverage}
                      setHasDefaultCoverage={setHasDefaultCoverage}
                      setPatientToEdit={setPatientToEdit}
                      setShowEditDialog={setShowEditDialog}
                      setVerificationCode={setVerificationCode}
                      searchPatient={searchPatient}
                      handleVerifyPatient={handleVerifyPatient}
                      capitalizeName={capitalizeName}
                      getSpanishCountries={getSpanishCountries}
                      isModifying={!!modifyingAppointmentId}
                    />
                  )}

                  {searchPerformed && !modifyingAppointmentId && (
                    <PatientDataAndCoverageSection
                      searchPerformed={searchPerformed}
                      selectedAssociatedPatient={selectedAssociatedPatient}
                      patientFound={patientFound}
                      patientToVerify={patientToVerify}
                      patientDni={patientDni}
                      patientName={patientName}
                      patientLastName={patientLastName}
                      patientPhone={patientPhone}
                      patientEmail={patientEmail}
                      phoneError={phoneError}
                      phoneUtil={phoneUtil}
                      isPatientUser={isPatientUser}
                      currentUser={currentUser}
                      activeSection={activeSection}
                      completedSections={completedSections}
                      patientCoverage={patientCoverage}
                      patientPlan={patientPlan}
                      noCoverage={noCoverage}
                      hasDefaultCoverage={hasDefaultCoverage}
                      useDefaultCoverage={useDefaultCoverage}
                      plansByCoverage={plansByCoverage}
                      getPatientById={getPatientById}
                      setPatientName={setPatientName}
                      setPatientLastName={setPatientLastName}
                      setPatientPhone={setPatientPhone}
                      setPatientEmail={setPatientEmail}
                      setPhoneError={setPhoneError}
                      setPatientError={setPatientError}
                      setCompletedSections={setCompletedSections}
                      setActiveSection={setActiveSection}
                      setPatientCoverage={setPatientCoverage}
                      setPatientPlan={setPatientPlan}
                      setNoCoverage={setNoCoverage}
                      setUseDefaultCoverage={setUseDefaultCoverage}
                      setPatientFound={setPatientFound}
                      setSelectedAssociatedPatient={setSelectedAssociatedPatient}
                      addPatient={addPatient}
                      associatePatientWithUser={associatePatientWithUser}
                      capitalizeName={capitalizeName}
                      validatePatientForm={validatePatientForm}
                      getSpanishCountries={getSpanishCountries}
                    />
                  )}

                  {/* Separation between Patient Selection and Coverage */}
                  {(selectedAssociatedPatient || (isPatientUser && currentUser?.defaultPatientId && !searchPerformed)) && !modifyingAppointmentId && (
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-gray-200" />
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="bg-white px-4 text-gray-500 font-medium">Cobertura médica</span>
                      </div>
                    </div>
                  )}

                  {/* Coverage Pill - Only show when a patient is selected and not modifying */}
                  {(selectedAssociatedPatient || (isPatientUser && currentUser?.defaultPatientId && !searchPerformed)) && !modifyingAppointmentId && (
                    <div className="space-y-4">
                      <div className="text-center">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">Cobertura médica</h3>
                        <p className="text-sm text-gray-600">Confirmá la cobertura a utilizar para esta atención</p>
                      </div>

                      <div 
                        className={`border-2 rounded-xl p-4 cursor-pointer transition-all hover:shadow-md ${
                          isCoverageAccepted === false
                            ? 'border-yellow-500 bg-yellow-50'
                            : isCoverageValid() && isCoverageAccepted === true
                              ? 'border-green-500 bg-green-50'
                              : 'border-gray-200 bg-white hover:border-gray-300'
                        }`}
                        onClick={() => {
                          setOpenSections(prev => {
                            const newCoverageOpen = !prev.coverage;
                            
                            // If closing the pill manually and no coverage is selected, revert to default
                            if (prev.coverage && !newCoverageOpen && !useDefaultCoverage && !overrideCoverage && !overrideNoCoverage) {
                              setUseDefaultCoverage(true);
                              setOverrideCoverage("");
                              setOverridePlan("");
                              setOverrideNoCoverage(false);
                              setCoverageError("");
                            }
                            
                            return {
                              ...prev,
                              coverage: newCoverageOpen
                            };
                          });
                        }}
                      >
                        <div className="flex items-center space-x-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                            isCoverageAccepted === false
                              ? 'bg-yellow-100 text-yellow-600'
                              : isCoverageValid() && isCoverageAccepted === true
                                ? 'bg-green-100 text-green-600'
                                : 'bg-gray-100 text-gray-600'
                          }`}>
                            <CreditCard className="h-6 w-6" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900 flex items-center">
                              {getCoverageSummary()}
                              {isCoverageAccepted === false && (
                                <AlertCircle className="h-4 w-4 ml-2 text-yellow-500" />
                              )}
                            </h3>
                            <p className="text-sm text-gray-600">
                              {isCoverageAccepted === false 
                                ? "Esta cobertura no es aceptada por el profesional"
                                : "La cobertura es aceptada por el profesional"
                              }
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {isCoverageValid() && isCoverageAccepted === true && (
                              <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                                <Check className="h-4 w-4 text-white" />
                              </div>
                            )}
                                                          {isCoverageAccepted === false && (
                                <div className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center">
                                  <AlertCircle className="h-4 w-4 text-white" />
                                </div>
                              )}
                          </div>
                        </div>

                        {/* Expandable Content */}
                        {openSections.coverage && (
                          <div className="mt-6 pt-4 border-t border-gray-200 space-y-4">
                            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                              <div className="flex flex-col space-y-3">
                                <div className="flex flex-col">
                                  <p className="text-sm text-blue-800">Cobertura principal del paciente:</p>
                                  <p className="text-sm font-medium text-blue-700 mt-1">{selectedPatientCoverage}</p>
                                </div>
                                <div className="flex items-center justify-between pt-2 border-t border-blue-200" onClick={(e) => e.stopPropagation()}>
                                  <Label htmlFor="use-default-coverage" className="text-sm font-medium text-blue-800">
                                    {useDefaultCoverage ? "Usar esta cobertura" : "Usar otra cobertura"}
                                  </Label>
                                  <Switch
                                    id="use-default-coverage"
                                    checked={useDefaultCoverage}
                                    onCheckedChange={(checked) => {
                                      setUseDefaultCoverage(checked);
                                      setCoverageError(""); // Clear any previous errors
                                      if (checked) {
                                        // Reset override values when switching back to default coverage
                                        setOverrideCoverage("");
                                        setOverridePlan("");
                                        setOverrideNoCoverage(false);
                                      }
                                    }}
                                  />
                                </div>
                              </div>
                            </div>

                            {!useDefaultCoverage && (
                              <div className="space-y-3 border-t border-gray-200 pt-3" onClick={(e) => e.stopPropagation()}>
                                {!overrideCoverage && !overrideNoCoverage && (
                                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                                    <p className="text-sm text-amber-800 font-medium">
                                      Por favor seleccioná una cobertura o marcá &quot;Sin Cobertura&quot; para continuar.
                                    </p>
                                  </div>
                                )}
                                <div className="space-y-3">
                                  <Select
                                    open={isCoverageOpen}
                                    onOpenChange={setIsCoverageOpen}
                                    value={overrideCoverage}
                                    onValueChange={(value) => {
                                      setOverrideCoverage(value);
                                      setOverridePlan("");
                                      setCoverageError(""); // Clear any previous errors
                                    }}
                                    disabled={overrideNoCoverage}
                                  >
                                    <SelectTrigger id="override-coverage" className="flex justify-between z-[51]">
                                      <div className="text-left"><SelectValue placeholder="Seleccionar Cobertura" /></div>
                                    </SelectTrigger>
                                    <SelectContent className="z-[1001]">
                                      {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                                        <SelectItem key={cov.id} value={cov.name}>{cov.name}</SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <Select
                                    open={isPlanOpen}
                                    onOpenChange={setIsPlanOpen}
                                    value={overridePlan}
                                    onValueChange={(value) => {
                                      setOverridePlan(value);
                                      setCoverageError(""); // Clear any previous errors
                                    }}
                                    disabled={!overrideCoverage || overrideNoCoverage}
                                  >
                                    <SelectTrigger id="override-plan" className="flex justify-between z-[51]">
                                      <div className="text-left"><SelectValue placeholder="Seleccionar Plan" /></div>
                                    </SelectTrigger>
                                    <SelectContent className="z-[51]">
                                      {overrideCoverage &&
                                        plansByCoverage[overrideCoverage]?.map((plan) => (
                                          <SelectItem key={plan} value={plan}>
                                            {plan}
                                          </SelectItem>
                                        ))}
                                    </SelectContent>
                                  </Select>
                                  {selectedPatientCoverage !== "Sin Cobertura" && (
                                    <div className="flex items-center space-x-2">
                                      <Switch
                                        id="override-no-coverage"
                                        checked={overrideNoCoverage}
                                        onCheckedChange={(checked) => {
                                          setOverrideNoCoverage(checked);
                                          setCoverageError(""); // Clear any previous errors
                                          if (checked) {
                                            setOverrideCoverage("");
                                            setOverridePlan("");
                                          }
                                        }}
                                      />
                                      <Label htmlFor="override-no-coverage">Sin Cobertura</Label>
                                    </div>
                                  )}

                                  {/* Error message */}
                                  {coverageError && (
                                    <div className="text-red-500 text-sm mt-1">{coverageError}</div>
                                  )}

                                  {/* Aceptar button */}
                                  <Button
                                    className="w-full mt-2 bg-blue-600 hover:bg-blue-700 text-white"
                                    onClick={() => {
                                      // If no coverage selected, default back to patient's coverage
                                      if (!useDefaultCoverage && !overrideCoverage && !overrideNoCoverage) {
                                        setUseDefaultCoverage(true);
                                        setOverrideCoverage("");
                                        setOverridePlan("");
                                        setOverrideNoCoverage(false);
                                        setCoverageError("");
                                      }

                                      // Close the coverage section
                                      setOpenSections(prev => ({
                                        ...prev,
                                        coverage: false
                                      }));
                                    }}
                                  >
                                    Aceptar
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <ReservationSummary
                    doctor={doctor}
                    medicalCenter={medicalCenter}
                    selectedDate={selectedDate}
                    selectedTime={selectedTime}
                    selectedConsultationType={selectedConsultationType}
                    coverage={(() => {
                      // Use the same logic as getCoverageSummary to ensure consistency
                      let coverageText = "";

                      // Prioritize based on what's selected (same logic as getCoverageSummary)
                      if (useDefaultCoverage && hasDefaultCoverage) {
                        coverageText = selectedPatientCoverage;
                      } else if (overrideNoCoverage) {
                        coverageText = "Sin Cobertura";
                      } else if (overrideCoverage) {
                        coverageText = overridePlan ? `${overrideCoverage} ${overridePlan}` : overrideCoverage;
                      } else if (selectedAssociatedPatient) {
                        // If an associated patient is selected, use their coverage
                        coverageText = selectedAssociatedPatient.coverage || "Sin Cobertura";
                      } else if (isPatientUser && currentUser?.defaultPatientId && !searchPerformed) {
                        // If using the default patient
                        const defaultPatient = getPatientById(currentUser.defaultPatientId);
                        coverageText = defaultPatient?.coverage || "Sin Cobertura";
                      } else if (patientFound) {
                        coverageText = patientFound.coverage || "Sin Cobertura";
                      } else if (selectedUrlCoverage) {
                        coverageText = selectedUrlCoverage;
                      } else if (noCoverage) {
                        coverageText = "Sin Cobertura";
                      } else if (patientCoverage) {
                        coverageText = patientPlan ? `${patientCoverage} ${patientPlan}` : patientCoverage;
                      }

                      return coverageText || "Sin Cobertura";
                    })()}
                    getPricingInfo={getPricingInfo}
                    variant="step"
                    className="mt-6"
                  />

                  {patientError && (
                    <div className={`mt-4 p-3 rounded-md text-sm ${patientError.includes("exitosamente") ? 'bg-green-50 border border-green-100 text-green-600' : 'bg-red-50 border border-red-100 text-red-600'}`}>
                      {patientError}
                    </div>
                  )}
                </div>
              )}

              {currentStep === 3 && (
                <ConfirmationStep
                  doctor={doctor}
                  medicalCenter={medicalCenter}
                  patientName={patientName}
                  patientLastName={patientLastName}
                  patientEmail={patientEmail}
                  selectedDate={selectedDate}
                  confirmedTime={confirmedTime}
                  selectedTime={selectedTime}
                  selectedUrlCoverage={selectedUrlCoverage}
                  selectedConsultationType={selectedConsultationType}
                  getPricingInfo={getPricingInfo}
                  isModifying={!!modifyingAppointmentId}
                />
              )}
            </CardContent>

            <CardFooter className="bg-[#F4F7F9] border-t border-[#0070F3]/10 p-4 sm:p-5">
              {currentStep < 3 ? (
                <div className="w-full flex gap-2">
                  <Button
                    variant="outline"
                    onClick={handlePreviousStep}
                    className="flex-1 border-[#0070F3]/30 text-[#0070F3] hover:bg-[#0070F3]/5"
                  >
                    <ChevronLeft className="h-4 w-4 sm:mr-1" />
                    <span className="hidden sm:inline">Volver</span>
                  </Button>
                  <div className="relative flex-1">
                    <Button
                      className={`bg-[#0070F3] hover:bg-[#0070F3]/90 w-full`}
                      onClick={handleNextStep}
                      disabled={currentStep === 1 ? (
                        !selectedTime ||
                        !selectedConsultationType ||
                        (selectedConsultationType !== null && !isValidConsultationType(selectedConsultationType)) ||
                        (selectedConsultationType !== null && availableDates.length === 0)
                      ) : (currentStep === 2 && (
                        (!isPatientUser && (!patientName || !patientDni)) ||
                        (searchPerformed && !selectedAssociatedPatient && !patientFound && !patientNotFound) || // Disable if "Add new patient" is selected but no patient is found or created yet
                        (searchPerformed && !patientFound && !selectedAssociatedPatient && !currentUser?.defaultPatientId) || // Disable until new patient is created, but allow if user has a default patient
                        (!selectedAssociatedPatient && !currentUser?.defaultPatientId && !completedSections.cobertura.completed) || // Only check sections if not using an associated patient and not using default patient
                        isCoverageAccepted === false // Disable if coverage is not accepted by the doctor
                      ))}
                    >
                      {currentStep === 1 ? "Continuar" : "Confirmar Reserva"}
                    </Button>
                    {currentStep === 1 && selectedConsultationType !== null && !isValidConsultationType(selectedConsultationType) && (
                      <div className="absolute -bottom-8 left-0 w-full text-center text-sm text-red-500">
                        Este tipo de consulta no está disponible
                      </div>
                    )}
                    {currentStep === 1 && selectedConsultationType !== null && isValidConsultationType(selectedConsultationType) && availableDates.length === 0 && (
                      <div className="absolute -bottom-8 left-0 w-full text-center text-sm text-red-500">
                        No hay fechas disponibles para este tipo de consulta
                      </div>
                    )}
                    {currentStep === 2 && isCoverageAccepted === false}
                  </div>
                </div>
              ) : (
                <Button
                  className="w-full bg-green-600 hover:bg-green-700"
                  onClick={handleFinish}
                >
                  Finalizar
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </main>

      <CitaFooter />

      {/* Add global styles for animations */}
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(15px); }
          to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
          from { opacity: 0; transform: translateX(20px); }
          to { opacity: 1; transform: translateX(0); }
        }

        @keyframes scaleIn {
          from { opacity: 0; transform: scale(0.95); }
          to { opacity: 1; transform: scale(1); }
        }

        .card-grid-item {
          animation: scaleIn 0.5s ease-out forwards;
          opacity: 0;
        }

        .card-grid-item:nth-child(1) { animation-delay: 0.1s; }
        .card-grid-item:nth-child(2) { animation-delay: 0.15s; }
        .card-grid-item:nth-child(3) { animation-delay: 0.2s; }
        .card-grid-item:nth-child(4) { animation-delay: 0.25s; }
        .card-grid-item:nth-child(5) { animation-delay: 0.3s; }
        .card-grid-item:nth-child(6) { animation-delay: 0.35s; }
        .card-grid-item:nth-child(7) { animation-delay: 0.4s; }
        .card-grid-item:nth-child(8) { animation-delay: 0.45s; }
        .card-grid-item:nth-child(9) { animation-delay: 0.5s; }

        /* Hover effects */
        .card-grid-item:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
        }
      `}</style>
    </div>
  );
}