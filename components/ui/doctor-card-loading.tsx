"use client"

import { Loader2 } from "lucide-react"

interface DoctorCardLoadingProps {
  doctorId: string
  isLoading: boolean
}

export function DoctorCardLoading({ doctorId, isLoading }: DoctorCardLoadingProps) {
  if (!isLoading) return null

  return (
    <div 
      id={`doctor-loading-${doctorId}`}
      className="absolute inset-0 z-40 bg-blue-50 bg-opacity-50 flex items-center justify-center rounded-lg"
    >
      <div className="flex items-center justify-center">
        <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
      </div>
    </div>
  )
}
