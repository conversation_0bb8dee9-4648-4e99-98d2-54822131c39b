"use client"

import { useEffect, useState } from "react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MapPin } from "lucide-react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import {
  getCABALocationsFromMedicalCenters,
  getGBALocationsFromMedicalCenters,
  getSearchLocationOptionsFromMedicalCenters,
} from "@/data/locations"

interface Props {
  noInsurance: boolean
  setNoInsurance: (value: boolean) => void
  onApplyFilters?: (coverageData: { noInsurance: boolean; coverageId: string; coverageName: string; plan: string }) => void
  onTimeFilterChange?: (value: string) => void
  onSortChange?: (value: "date" | "distance") => void
  sortBy?: "date" | "distance"
  searchType?: string
  searchQuery?: string
  locationQuery?: string
}

export default function SearchFiltersDesktop({ onTimeFilterChange, onSortChange, sortBy = "date" }: Props) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const [selectedRegion, setSelectedRegion] = useState<"all" | "caba" | "gba">("all")
  const [selectedBarrio, setSelectedBarrio] = useState<string>("")
  const locationOptions = getSearchLocationOptionsFromMedicalCenters()

  // Controlled time based on URL
  const timeValue = searchParams.get("time") || "all"

  // Initialize location from URL
  useEffect(() => {
    const zoneParam = searchParams.get("zone")
    const locParam = searchParams.get("loc")
    const locationParam = locParam || zoneParam
    if (locationParam) {
      if (locationParam === "all") {
        setSelectedRegion("all")
        setSelectedBarrio("")
      } else if (locationParam === "caba" || locationParam === "Capital Federal") {
        setSelectedRegion("caba")
        setSelectedBarrio("all")
      } else if (locationParam === "gba" || locationParam === "Gran Buenos Aires") {
        setSelectedRegion("gba")
        setSelectedBarrio("all")
      } else {
        const caba = getCABALocationsFromMedicalCenters().find((l) => l.id === locationParam || l.name === locationParam)
        if (caba) {
          setSelectedRegion("caba")
          setSelectedBarrio(caba.id)
        } else {
          const gba = getGBALocationsFromMedicalCenters().find((l) => l.id === locationParam || l.name === locationParam)
          if (gba) {
            setSelectedRegion("gba")
            setSelectedBarrio(gba.id)
          }
        }
      }
    }
  }, [searchParams])

  const updateParam = (name: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set(name, value)
    router.replace(`${pathname}?${params.toString()}`)
    if (name === "time" && onTimeFilterChange) onTimeFilterChange(value)
    if (name === "sort" && onSortChange) onSortChange(value as "date" | "distance")
  }

  return (
    <div className="p-4 md:p-5">
      <div className="hidden md:grid grid-cols-2 gap-6">
        {/* Col 1: Orden + Ubicación */}
        <div className="space-y-5">
          <div>
            <Label htmlFor="sort" className="text-sm font-medium text-gray-700 mb-1 block">
              Ordenar por
            </Label>
            <Select value={sortBy} onValueChange={(v: "date" | "distance") => updateParam("sort", v)}>
              <SelectTrigger id="sort" className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                <SelectValue placeholder="Seleccionar orden" />
              </SelectTrigger>
              <SelectContent className="z-[90]">
                <SelectItem value="date">Primer turno disponible</SelectItem>
                <SelectItem value="distance">Menor distancia</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            <div className="flex items-center mb-1">
              <MapPin className="h-4 w-4 mr-1.5 text-[#0070F3]" />
              <Label htmlFor="region" className="text-sm font-medium text-gray-700">
                Ubicación
              </Label>
            </div>
            <div>
              <Select
                value={selectedRegion}
                onValueChange={(value: "all" | "caba" | "gba") => {
                  setSelectedRegion(value)
                  if (value !== "all") setSelectedBarrio("all")
                  else setSelectedBarrio("")
                  const params = new URLSearchParams(searchParams.toString())
                  params.delete("zone")
                  if (value === "all") params.set("loc", "all")
                  else if (value === "caba") params.set("loc", "Capital Federal")
                  else if (value === "gba") params.set("loc", "Gran Buenos Aires")
                  router.replace(`${pathname}?${params.toString()}`)
                }}
              >
                <SelectTrigger id="region" className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                  <SelectValue placeholder="Seleccionar ubicación" />
                </SelectTrigger>
                <SelectContent className="z-[90]">
                  <SelectItem value="all">Todas las ubicaciones</SelectItem>
                  {locationOptions.some((l) => l.region === "caba" && l.type === "region") && (
                    <SelectItem value="caba">Capital Federal</SelectItem>
                  )}
                  {locationOptions.some((l) => l.region === "gba" && l.type === "region") && (
                    <SelectItem value="gba">Gran Buenos Aires</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            {(selectedRegion === "caba" || selectedRegion === "gba") && (
              <div>
                <Select
                  value={selectedBarrio}
                  onValueChange={(value) => {
                    setSelectedBarrio(value)
                    const params = new URLSearchParams(searchParams.toString())
                    params.delete("zone")
                    if (value === "all") {
                      if (selectedRegion === "caba") params.set("loc", "Capital Federal")
                      else if (selectedRegion === "gba") params.set("loc", "Gran Buenos Aires")
                    } else {
                      const locations =
                        selectedRegion === "caba" ? getCABALocationsFromMedicalCenters() : getGBALocationsFromMedicalCenters()
                      const found = locations.find((l) => l.id === value)
                      params.set("loc", found ? found.name : value)
                    }
                    router.replace(`${pathname}?${params.toString()}`)
                  }}
                >
                  <SelectTrigger className="w-full h-10 text-sm bg-white border-gray-200 focus:ring-[#0070F3] focus:border-[#0070F3]">
                    <SelectValue placeholder={selectedRegion === "caba" ? "Barrio" : "Municipio"} />
                  </SelectTrigger>
                  <SelectContent className="z-[90] max-h-60 overflow-auto">
                    <SelectItem value="all">
                      {selectedRegion === "caba" ? "Todos los barrios" : "Todos los municipios"}
                    </SelectItem>
                    {(selectedRegion === "caba"
                      ? getCABALocationsFromMedicalCenters()
                      : getGBALocationsFromMedicalCenters()
                    ).map((l) => (
                      <SelectItem key={l.id} value={l.id}>
                        {l.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        {/* Col 2: Horario */}
        <div className="border-l md:pl-6">
          <Label className="text-sm font-medium text-gray-700 mb-3 block">Horario</Label>
          <RadioGroup value={timeValue} onValueChange={(v) => updateParam("time", v)} className="space-y-3">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="all" id="all_pop" className="text-[#0070F3]" />
              <Label htmlFor="all_pop" className="text-sm text-gray-600 cursor-pointer">
                Todos
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="morning" id="morning_pop" className="text-[#0070F3]" />
              <Label htmlFor="morning_pop" className="text-sm text-gray-600 cursor-pointer">
                Mañana (hasta 12:00)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="afternoon" id="afternoon_pop" className="text-[#0070F3]" />
              <Label htmlFor="afternoon_pop" className="text-sm text-gray-600 cursor-pointer">
                Tarde (12:00 a 17:00)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="evening" id="evening_pop" className="text-[#0070F3]" />
              <Label htmlFor="evening_pop" className="text-sm text-gray-600 cursor-pointer">
                Noche (desde 17:00)
              </Label>
            </div>
          </RadioGroup>
        </div>
      </div>
    </div>
  )
}


