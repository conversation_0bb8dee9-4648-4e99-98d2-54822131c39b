import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Star } from "lucide-react"

const testimonials = [
  {
    quote: "Turnera me facilitó encontrar un especialista. ¡Conseguí una cita la misma semana!",
    author: "<PERSON>",
    role: "<PERSON><PERSON><PERSON>",
    avatar: "/placeholder.svg?height=80&width=80",
    rating: 5,
  },
  {
    quote: "Como médico, Turnera me ha ayudado a llegar a más pacientes y gestionar mi agenda de manera eficiente.",
    author: "Dr<PERSON> <PERSON>",
    role: "Cardiólogo",
    avatar: "/placeholder.svg?height=80&width=80",
    rating: 5,
  },
  {
    quote: "Me encanta lo simple que es reservar citas. ¡Me ahorra mucho tiempo!",
    author: "<PERSON>",
    role: "Paciente",
    avatar: "/placeholder.svg?height=80&width=80",
    rating: 5,
  },
]

export default function Testimonials() {
  return (
    <section id="testimonios" className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-8 max-w-7xl">
        <h2 className="text-3xl font-bold text-center mb-12">Lo Que Dicen Nuestros Usuarios</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4">{testimonial.quote}</p>
                <div className="flex items-center">
                  <Image
                    src={testimonial.avatar || "/placeholder.svg"}
                    alt={testimonial.author}
                    width={40}
                    height={40}
                    className="rounded-full mr-4"
                  />
                  <div>
                    <p className="font-semibold">{testimonial.author}</p>
                    <p className="text-sm text-gray-500">{testimonial.role}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

