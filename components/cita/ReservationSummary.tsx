"use client"

import React from "react"
import {format} from "date-fns"
import {es} from "date-fns/locale"
import {Calendar, MapPin, User, Shield, Clock, CreditCard, Info, AlertCircle} from "lucide-react"
import {Doctor} from "@/types/doctor"
import {MedicalCenter} from "@/types/medical-center"

// Utility function to format prices with dots for thousands and commas for decimals
export const formatPrice = (price: number | string): string => {
    const num = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(num)) return String(price);

    // Format with dots for thousands and commas for decimals (Spanish/Argentine format)
    return num.toLocaleString('es-AR', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
};

interface ReservationSummaryProps {
    doctor: Doctor
    medicalCenter: MedicalCenter
    selectedDate: Date | null
    selectedTime: string | null
    confirmedTime?: string | null
    selectedConsultationType: string | null
    coverage?: string | null
    patientName?: string
    patientLastName?: string
    patientEmail?: string
    showPatientInfo?: boolean
    showEmailNotification?: boolean
    getPricingInfo?: () => React.ReactNode
    variant?: 'step' | 'confirmation'
    className?: string
}

export const ReservationSummary: React.FC<ReservationSummaryProps> = ({
                                                                          doctor,
                                                                          medicalCenter,
                                                                          selectedDate,
                                                                          selectedTime,
                                                                          confirmedTime,
                                                                          selectedConsultationType,
                                                                          coverage,
                                                                          patientName,
                                                                          patientLastName,
                                                                          patientEmail,
                                                                          showPatientInfo = false,
                                                                          showEmailNotification = false,
                                                                          getPricingInfo,
                                                                          variant = 'step',
                                                                          className = "",
                                                                      }) => {
    const timeToDisplay = confirmedTime || selectedTime
    const selectedType = selectedConsultationType ? doctor.consultationTypes.find(t => t.name === selectedConsultationType) : null

    return (
        <div className={`bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100 ${className}`}>
            {/* Header */}
            <div className="bg-gradient-to-r from-[#0070F3]/10 to-[#0070F3]/5 px-5 py-3 border-b border-[#0070F3]/20">
                <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-[#0070F3]/20 rounded-lg flex items-center justify-center">
                        <Calendar className="h-4 w-4 text-[#0070F3]"/>
                    </div>
                    <div>
                        <h3 className="text-lg font-bold text-[#1c2533]">
                            {variant === 'confirmation' ? 'Detalles de la reserva' : 'Resumen de tu reserva'}
                        </h3>
                    </div>
                </div>
            </div>

            <div className="p-5 space-y-4">
                {/* Patient Info (if shown) */}
                {showPatientInfo && patientName && (
                    <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div className="flex items-center space-x-3">
                            <div className="w-7 h-7 bg-[#1cd8e1]/20 rounded-lg flex items-center justify-center">
                                <User className="h-4 w-4 text-[#0a7c82]"/>
                            </div>
                            <div>
                                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">Paciente
                                </div>
                                <div className="text-gray-900 font-semibold">{patientName} {patientLastName}</div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Main Appointment Details - Combined Card */}
                <div className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Left Column - Date & Time */}
                        <div className="flex items-start space-x-3">
                            <div className="w-7 h-7 bg-[#0070F3]/10 rounded-lg flex items-center justify-center mt-0.5">
                                <Clock className="h-4 w-4 text-[#0070F3]"/>
                            </div>
                            <div className="flex-1">
                                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Fecha
                                    y Hora
                                </div>
                                {selectedDate && (
                                    <div className="text-gray-900 font-semibold mb-2">
                                        {format(selectedDate, "EEEE d 'de' MMMM", {locale: es})}
                                    </div>
                                )}
                                <div
                                    className="inline-flex items-center px-3 py-1 rounded-full bg-[#0070F3]/10 text-[#0070F3] font-medium text-sm">
                                    <Clock className="h-3 w-3 mr-1"/>
                                    {timeToDisplay || 'No seleccionada'}
                                </div>
                            </div>
                        </div>

                        {/* Right Column - Doctor Info */}
                        <div className="flex items-start space-x-3">
                            <div className="w-7 h-7 bg-[#1cd8e1]/20 rounded-lg flex items-center justify-center mt-0.5">
                                <User className="h-4 w-4 text-[#0a7c82]"/>
                            </div>
                            <div className="flex-1">
                                <div
                                    className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Profesional
                                </div>
                                <div className="text-gray-900 font-semibold mb-2">Dr. {doctor.name}</div>
                                {doctor.specialties && doctor.specialties.length > 0 && (
                                    <div className="flex flex-wrap gap-1">
                                        {doctor.specialties.map((specialty, index) => (
                                            <span key={index}
                                                  className="inline-flex items-center px-2 py-0.5 rounded-full bg-[#1cd8e1]/20 text-[#0a7c82] text-xs font-medium">
                        {specialty}
                      </span>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Location Row */}
                    <div className="mt-4 pt-4 border-t border-gray-100">
                        <div className="flex items-start space-x-3">
                            <div className="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                <MapPin className="h-4 w-4 text-gray-600"/>
                            </div>
                            <div className="flex-1">
                                <div
                                    className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Establecimiento
                                </div>
                                <div className="text-gray-700 text-sm">
                                    {formatMedicalCenterLocation(medicalCenter, LocationFormatType.MEDICAL_CENTER_SIMPLE)}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Consultation Details Row */}
                    {(coverage || selectedConsultationType) && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                            <div className="flex items-start space-x-3">
                                <div className="w-7 h-7 bg-[#0070F3]/10 rounded-lg flex items-center justify-center">
                                    <Shield className="h-4 w-4 text-[#0070F3]"/>
                                </div>
                                <div className="flex-1">
                                    <div
                                        className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Detalles
                                        de la Consulta
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                        {selectedConsultationType && (
                                            <div
                                                className="inline-flex items-center px-3 py-1 rounded-lg bg-[#0070F3]/10 text-[#0070F3] font-medium text-sm">
                                                {selectedConsultationType}
                                            </div>
                                        )}
                                        {coverage && (
                                            <div
                                                className="inline-flex items-center px-3 py-1 rounded-lg bg-[#1cd8e1]/20 text-[#0a7c82] font-medium text-sm">
                                                <Shield className="h-3 w-3 mr-1"/>
                                                {coverage}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Pricing Information - If needed */}
                {getPricingInfo && getPricingInfo() && (
                    <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div className="flex items-start space-x-3">
                            <div className="w-7 h-7 bg-[#1cd8e1]/20 rounded-lg flex items-center justify-center">
                                <CreditCard className="h-4 w-4 text-[#0a7c82]"/>
                            </div>
                            <div className="flex-1">
                                <div
                                    className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Información
                                    de Pago
                                </div>
                                <div className="space-y-2">
                                    {getPricingInfo()}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Instructions and Requirements - If needed */}
                {selectedType && (selectedType.hasInstructions || selectedType.requiresMedicalOrder) && (
                    <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <div className="flex items-start space-x-3">
                            <div className="w-7 h-7 bg-gray-100 rounded-lg flex items-center justify-center">
                                <Info className="h-4 w-4 text-gray-600"/>
                            </div>
                            <div className="flex-1">
                                <div
                                    className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Información
                                    Importante
                                </div>
                                <div className="space-y-3">
                                    {selectedType.hasInstructions && selectedType.instructions && (
                                        <div
                                            className="bg-gradient-to-r from-blue-50 to-blue-50 border border-blue-200 rounded-lg p-3">
                                            <div className="flex items-start space-x-2">
                                                <div
                                                    className="w-5 h-5 bg-blue-500 rounded flex items-center justify-center flex-shrink-0 mt-0.5">
                                                    <Info className="h-3 w-3 text-white"/>
                                                </div>
                                                <div className="flex-1">
                                                    <h5 className="font-semibold text-blue-900 text-sm mb-1">Instrucciones
                                                        importantes</h5>
                                                    <p className="text-blue-800 text-sm leading-relaxed whitespace-pre-line">
                                                        {selectedType.instructions}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    {selectedType.requiresMedicalOrder && (
                                        <div
                                            className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg p-3">
                                            <div className="flex items-center space-x-2">
                                                <div
                                                    className="w-5 h-5 bg-amber-500 rounded flex items-center justify-center flex-shrink-0">
                                                    <AlertCircle className="h-3 w-3 text-white"/>
                                                </div>
                                                <div className="flex-1">
                                                    <p className="text-amber-900 font-semibold text-sm">Orden médica
                                                        requerida</p>
                                                    <p className="text-amber-700 text-sm">Debe presentar orden médica
                                                        para esta atención</p>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Email Notification - If shown */}
                {showEmailNotification && patientEmail && (
                    <div className="bg-[#1cd8e1]/10 border border-[#1cd8e1]/30 rounded-xl p-4">
                        <div className="flex items-start space-x-3">
                            <div
                                className="w-7 h-7 bg-[#1cd8e1] rounded-lg flex items-center justify-center flex-shrink-0">
                                <Info className="h-4 w-4 text-white"/>
                            </div>
                            <div className="flex-1">
                                <h4 className="font-semibold text-[#0a7c82] text-sm mb-1">Confirmación enviada</h4>
                                <p className="text-[#0a7c82] text-sm">
                                    Detalles enviados a <span
                                    className="font-semibold bg-[#1cd8e1]/20 px-2 py-0.5 rounded">{patientEmail}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
} 