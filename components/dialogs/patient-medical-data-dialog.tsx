"use client"

import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { Badge } from "@/components/ui/badge"
import { User, Heart, FileText, Calendar, Lock, Edit3, Save, X, Clock, Phone, Mail, IdCard, Building } from "lucide-react"
import { format } from "date-fns"
import { es } from "date-fns/locale"
import { Appointment } from "@/types/scheduler"
import { Patient } from "@/types/patient"
import { DoctorPatientMedicalData, ConsultationNote } from "@/types/patient"
import { useDoctorPatientMedicalData } from "@/hooks/useDoctorPatientMedicalData"
import { toast } from "sonner"

interface PatientMedicalDataDialogProps {
  isOpen: boolean
  onClose: () => void
  appointment: Appointment | null
  patient: Patient | null
  doctorId: string
  onAppointmentStatusChange?: (appointmentId: string, newStatus: Appointment['status']) => void
}

export const PatientMedicalDataDialog: React.FC<PatientMedicalDataDialogProps> = ({
  isOpen,
  onClose,
  appointment,
  patient,
  doctorId,
  onAppointmentStatusChange
}) => {
  const {
    getOrCreateMedicalData,
    updatePersonalData,
    addConsultationNote,
    updateConsultationNote,
    lockConsultationNote,
    getConsultationNoteByAppointment,
    isConsultationNoteLocked
  } = useDoctorPatientMedicalData()

  const [medicalData, setMedicalData] = useState<DoctorPatientMedicalData | null>(null)
  const [isEditingPersonalData, setIsEditingPersonalData] = useState(false)
  const [isWritingNote, setIsWritingNote] = useState(false)
  const [isEditingNote, setIsEditingNote] = useState(false)
  const [currentConsultationNote, setCurrentConsultationNote] = useState("")
  const [existingNote, setExistingNote] = useState<ConsultationNote | null>(null)
  const [remainingEditTime, setRemainingEditTime] = useState<number>(0)
  const [showSaveConfirmation, setShowSaveConfirmation] = useState(false)
  
  // Ref for scrolling to save button area
  const saveButtonAreaRef = useRef<HTMLDivElement>(null)
  
  // Helper function to get medical center name by ID
  const getMedicalCenterName = (medicalCenterId?: string): string => {
    if (!medicalCenterId) return 'Establecimiento no especificado'
    const medicalCenters = storage.getMedicalCenters()
    const center = medicalCenters.find(c => c.id === medicalCenterId)
    return center?.name || 'Establecimiento desconocido'
  }
  
  // Personal data form state
  const [personalDataForm, setPersonalDataForm] = useState({
    height: '',
    weight: '',
    dateOfBirth: '',
    bloodType: '',
    allergies: '',
    chronicConditions: '',
    medications: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: ''
  })

  // Auto scroll to save button area when editing mode is enabled
  useEffect(() => {
    if (isEditingPersonalData && saveButtonAreaRef.current) {
      // Small delay to ensure the DOM has updated
      setTimeout(() => {
        saveButtonAreaRef.current?.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'end',
          inline: 'nearest'
        })
      }, 100)
    }
  }, [isEditingPersonalData])

  // Reset editing states and unsaved changes when dialog is closed
  useEffect(() => {
    if (!isOpen) {
      setIsEditingPersonalData(false)
      setIsWritingNote(false)
      setIsEditingNote(false)
      setCurrentConsultationNote("")
      setShowSaveConfirmation(false)
      
      // Reset personal data form to original values if there was unsaved data
      if (medicalData) {
        setPersonalDataForm({
          height: medicalData.personalData.height?.toString() || '',
          weight: medicalData.personalData.weight?.toString() || '',
          dateOfBirth: medicalData.personalData.dateOfBirth || '',
          bloodType: medicalData.personalData.bloodType || '',
          allergies: medicalData.personalData.allergies || '',
          chronicConditions: medicalData.personalData.chronicConditions || '',
          medications: medicalData.personalData.medications || '',
          emergencyContactName: medicalData.personalData.emergencyContact?.name || '',
          emergencyContactPhone: medicalData.personalData.emergencyContact?.phone || '',
          emergencyContactRelationship: medicalData.personalData.emergencyContact?.relationship || ''
        })
      }
    }
  }, [isOpen, medicalData])

  // Timer effect for note editing window
  useEffect(() => {
    if (existingNote && !existingNote.isLocked) {
      const noteCreatedAt = new Date(existingNote.createdAt).getTime()
      const now = Date.now()
      const elapsed = now - noteCreatedAt
      const fifteenMinutes = 15 * 60 * 1000 // 15 minutes in milliseconds
      const remaining = Math.max(0, fifteenMinutes - elapsed)
      
      setRemainingEditTime(remaining)
      
      if (remaining > 0) {
        const interval = setInterval(() => {
          const currentTime = Date.now()
          const newRemaining = Math.max(0, fifteenMinutes - (currentTime - noteCreatedAt))
          setRemainingEditTime(newRemaining)
          
          if (newRemaining === 0) {
            // Auto-lock the note when time expires
            lockConsultationNote(doctorId, patient?.id || '', appointment?.id || '')
            setExistingNote(prev => prev ? { ...prev, isLocked: true } : null)
            setIsEditingNote(false)
            clearInterval(interval)
            toast.info("El tiempo de edición ha expirado. Las notas han sido finalizadas.")
          }
        }, 1000)
        
        return () => clearInterval(interval)
      }
    }
  }, [existingNote, doctorId, patient, appointment, lockConsultationNote])

  // Load medical data when dialog opens
  useEffect(() => {
    if (isOpen && patient && doctorId) {
      const data = getOrCreateMedicalData(doctorId, patient.id!)
      setMedicalData(data)
      
      // Load existing consultation note for this appointment if it exists
      if (appointment) {
        const note = getConsultationNoteByAppointment(doctorId, patient.id!, appointment.id)
        setExistingNote(note)
      }
      
      // Populate personal data form
      setPersonalDataForm({
        height: data.personalData.height?.toString() || '',
        weight: data.personalData.weight?.toString() || '',
        dateOfBirth: data.personalData.dateOfBirth || '',
        bloodType: data.personalData.bloodType || '',
        allergies: data.personalData.allergies || '',
        chronicConditions: data.personalData.chronicConditions || '',
        medications: data.personalData.medications || '',
        emergencyContactName: data.personalData.emergencyContact?.name || '',
        emergencyContactPhone: data.personalData.emergencyContact?.phone || '',
        emergencyContactRelationship: data.personalData.emergencyContact?.relationship || ''
      })
    }
  }, [isOpen, patient, doctorId, appointment, getOrCreateMedicalData, getConsultationNoteByAppointment])

  const handleSavePersonalData = () => {
    if (!patient) return

    const updatedPersonalData = {
      height: personalDataForm.height ? parseInt(personalDataForm.height) : undefined,
      weight: personalDataForm.weight ? parseInt(personalDataForm.weight) : undefined,
      dateOfBirth: personalDataForm.dateOfBirth || undefined,
      bloodType: personalDataForm.bloodType || undefined,
      allergies: personalDataForm.allergies || undefined,
      chronicConditions: personalDataForm.chronicConditions || undefined,
      medications: personalDataForm.medications || undefined,
      emergencyContact: personalDataForm.emergencyContactName ? {
        name: personalDataForm.emergencyContactName,
        phone: personalDataForm.emergencyContactPhone,
        relationship: personalDataForm.emergencyContactRelationship
      } : undefined
    }

    const result = updatePersonalData(doctorId, patient.id!, updatedPersonalData)
    if (result) {
      setMedicalData(result)
      setIsEditingPersonalData(false)
      toast.success("Datos personales actualizados correctamente")
    } else {
      toast.error("Error al actualizar los datos personales")
    }
  }

  const handleStartConsultation = () => {
    if (appointment && onAppointmentStatusChange) {
      onAppointmentStatusChange(appointment.id, "En Atención")
      setIsWritingNote(true)
    }
  }

  const handleConfirmSaveConsultationNote = () => {
    if (!appointment || !patient || !currentConsultationNote.trim()) return

    const result = addConsultationNote(doctorId, patient.id!, appointment.id, currentConsultationNote.trim(), appointment.type, appointment.medicalCenterId)
    if (result) {
      setExistingNote(result)
      setCurrentConsultationNote("")
      setIsWritingNote(false)
      setShowSaveConfirmation(false)
      toast.success("Nota de atención guardada")
      
      // Update appointment status to "Atendido"
      if (onAppointmentStatusChange) {
        onAppointmentStatusChange(appointment.id, "Atendido")
      }
    } else {
      toast.error("Error al guardar la nota de atención")
    }
  }

  const handleEditNote = () => {
    if (existingNote && remainingEditTime > 0) {
      setCurrentConsultationNote(existingNote.notes)
      setIsEditingNote(true)
    }
  }

  const handleSaveEditedNote = () => {
    if (!appointment || !patient || !currentConsultationNote.trim() || !existingNote) return

    // Update the existing note
    const result = updateConsultationNote(doctorId, patient.id!, appointment.id, currentConsultationNote.trim())
    if (result) {
      setExistingNote(result)
      setCurrentConsultationNote("")
      setIsEditingNote(false)
      toast.success("Nota de atención actualizada")
    } else {
      toast.error("Error al actualizar la nota de atención")
    }
  }

  const formatTimeRemaining = (milliseconds: number) => {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = Math.floor((milliseconds % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const canWriteNote = appointment && !existingNote && !isConsultationNoteLocked(doctorId, patient?.id || '', appointment.id)
  const canStartConsultation = appointment && (appointment.status === "Agendado" || appointment.status === "Recepcionado" || appointment.status === "En Atención") && !existingNote
  const canEditNote = existingNote && !existingNote.isLocked && remainingEditTime > 0

  if (!patient || !medicalData) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-5xl h-[95vh] max-h-[95vh] overflow-hidden bg-white p-4 sm:p-6 flex flex-col">
        <DialogHeader className="border-b border-gray-100 pb-4 flex-shrink-0">
          <DialogTitle className="text-lg sm:text-xl font-semibold text-[#1c2533] flex items-center gap-2 sm:gap-3">
            <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-lg bg-[#0070F3]/10 flex items-center justify-center">
              <User className="h-3 w-3 sm:h-4 sm:w-4 text-[#0070F3]" />
            </div>
            <span className="truncate">Datos Médicos - {patient.name}</span>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="personal" className="flex-1 overflow-hidden flex flex-col min-h-0">
          <TabsList className="grid w-full grid-cols-3 bg-gray-50 h-10 sm:h-11 flex-shrink-0">
            <TabsTrigger 
              value="personal" 
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm data-[state=active]:bg-white data-[state=active]:text-[#0070F3] data-[state=active]:border-b-2 data-[state=active]:border-[#0070F3] px-2"
            >
              <Heart className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Datos personales</span>
              <span className="sm:hidden">Personal</span>
            </TabsTrigger>
            <TabsTrigger 
              value="consultation" 
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm data-[state=active]:bg-white data-[state=active]:text-[#0070F3] data-[state=active]:border-b-2 data-[state=active]:border-[#0070F3] px-2"
            >
              <FileText className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Atención actual</span>
              <span className="sm:hidden">Atención</span>
            </TabsTrigger>
            <TabsTrigger 
              value="history" 
              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm data-[state=active]:bg-white data-[state=active]:text-[#0070F3] data-[state=active]:border-b-2 data-[state=active]:border-[#0070F3] px-2"
            >
              <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Historial de atenciones</span>
              <span className="sm:hidden">Historial</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="personal" className="mt-4 sm:mt-6 flex-1 overflow-hidden">
            <div className="h-full overflow-auto space-y-4 sm:space-y-6 pr-2 sm:pr-4">
              {/* Basic Patient Information */}
              <Card className="border border-gray-200">
                <CardHeader className="pb-3 sm:pb-4">
                  <CardTitle className="text-base sm:text-lg font-medium text-[#1c2533] flex items-center gap-2">
                    <div className="w-5 h-5 sm:w-6 sm:h-6 rounded bg-[#0070F3]/10 flex items-center justify-center">
                      <User className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-[#0070F3]" />
                    </div>
                    Información del Paciente
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                    <div className="space-y-1">
                      <Label className="text-xs sm:text-sm font-medium text-gray-600 flex items-center gap-1">
                        <User className="h-3 w-3" />
                        Nombre
                      </Label>
                      <p className="text-sm font-medium text-[#1c2533] break-words">{patient.name}</p>
                    </div>
                    
                    <div className="space-y-1">
                      <Label className="text-xs sm:text-sm font-medium text-gray-600 flex items-center gap-1">
                        <IdCard className="h-3 w-3" />
                        DNI
                      </Label>
                      <p className="text-sm font-medium text-[#1c2533]">{patient.dni}</p>
                    </div>
                    
                    <div className="space-y-1">
                      <Label className="text-xs sm:text-sm font-medium text-gray-600 flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        Teléfono
                      </Label>
                      <p className="text-sm font-medium text-[#1c2533]">{patient.phone}</p>
                    </div>
                    
                    <div className="space-y-1">
                      <Label className="text-xs sm:text-sm font-medium text-gray-600 flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        Email
                      </Label>
                      <p className="text-sm font-medium text-[#1c2533] break-words">{patient.email || 'No registrado'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Medical Data */}
              <Card className="border border-gray-200">
                <CardHeader className="flex flex-col sm:flex-row sm:items-center justify-between pb-3 sm:pb-4 gap-3 sm:gap-0">
                  <CardTitle className="text-base sm:text-lg font-medium text-[#1c2533]">Datos Médicos</CardTitle>
                  <Button
                    variant={isEditingPersonalData ? "outline" : "default"}
                    size="sm"
                    onClick={() => setIsEditingPersonalData(!isEditingPersonalData)}
                    className={`w-full sm:w-auto ${isEditingPersonalData ? "border-gray-300" : "bg-[#0070F3] hover:bg-[#0070F3]/90 text-white"}`}
                  >
                    {isEditingPersonalData ? (
                      <>
                        <X className="h-4 w-4 mr-1" />
                        Cancelar
                      </>
                    ) : (
                      <>
                        <Edit3 className="h-4 w-4 mr-1" />
                        Editar
                      </>
                    )}
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="height" className="text-xs sm:text-sm font-medium text-gray-700">Altura (cm)</Label>
                          <Input
                            id="height"
                            type="number"
                            value={personalDataForm.height}
                            onChange={(e) => setPersonalDataForm(prev => ({ ...prev, height: e.target.value }))}
                            disabled={!isEditingPersonalData}
                            placeholder="170"
                            className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] text-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="weight" className="text-xs sm:text-sm font-medium text-gray-700">Peso (kg)</Label>
                          <Input
                            id="weight"
                            type="number"
                            value={personalDataForm.weight}
                            onChange={(e) => setPersonalDataForm(prev => ({ ...prev, weight: e.target.value }))}
                            disabled={!isEditingPersonalData}
                            placeholder="70"
                            className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] text-sm"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="dateOfBirth" className="text-xs sm:text-sm font-medium text-gray-700">Fecha de Nacimiento</Label>
                          <Input
                            id="dateOfBirth"
                            type="date"
                            value={personalDataForm.dateOfBirth}
                            onChange={(e) => setPersonalDataForm(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                            disabled={!isEditingPersonalData}
                            className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] text-sm"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="bloodType" className="text-xs sm:text-sm font-medium text-gray-700">Grupo Sanguíneo</Label>
                          <Input
                            id="bloodType"
                            value={personalDataForm.bloodType}
                            onChange={(e) => setPersonalDataForm(prev => ({ ...prev, bloodType: e.target.value }))}
                            disabled={!isEditingPersonalData}
                            placeholder="A+"
                            className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] text-sm"
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="allergies" className="text-xs sm:text-sm font-medium text-gray-700">Alergias</Label>
                        <Textarea
                          id="allergies"
                          value={personalDataForm.allergies}
                          onChange={(e) => setPersonalDataForm(prev => ({ ...prev, allergies: e.target.value }))}
                          disabled={!isEditingPersonalData}
                          placeholder="Penicilina, mariscos..."
                          rows={2}
                          className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] resize-none text-sm"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="chronicConditions" className="text-xs sm:text-sm font-medium text-gray-700">Enfermedades Crónicas</Label>
                        <Textarea
                          id="chronicConditions"
                          value={personalDataForm.chronicConditions}
                          onChange={(e) => setPersonalDataForm(prev => ({ ...prev, chronicConditions: e.target.value }))}
                          disabled={!isEditingPersonalData}
                          placeholder="Hipertensión, diabetes..."
                          rows={2}
                          className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] resize-none text-sm"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="medications" className="text-xs sm:text-sm font-medium text-gray-700">Medicaciones Actuales</Label>
                        <Textarea
                          id="medications"
                          value={personalDataForm.medications}
                          onChange={(e) => setPersonalDataForm(prev => ({ ...prev, medications: e.target.value }))}
                          disabled={!isEditingPersonalData}
                          placeholder="Enalapril 10mg, Metformina 500mg..."
                          rows={2}
                          className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] resize-none text-sm"
                        />
                      </div>
                    </div>

                    {/* Emergency Contact Section */}
                    <div className="space-y-4 mt-6 lg:mt-0">
                      <div className="border-l-4 border-[#0070F3] pl-4">
                        <h4 className="text-sm sm:text-base font-medium text-[#1c2533] mb-4">Contacto de Emergencia</h4>
                        
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="emergencyContactName" className="text-xs sm:text-sm font-medium text-gray-700">Nombre</Label>
                            <Input
                              id="emergencyContactName"
                              value={personalDataForm.emergencyContactName}
                              onChange={(e) => setPersonalDataForm(prev => ({ ...prev, emergencyContactName: e.target.value }))}
                              disabled={!isEditingPersonalData}
                              placeholder="María González"
                              className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] text-sm"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="emergencyContactPhone" className="text-xs sm:text-sm font-medium text-gray-700">Teléfono</Label>
                            <Input
                              id="emergencyContactPhone"
                              value={personalDataForm.emergencyContactPhone}
                              onChange={(e) => setPersonalDataForm(prev => ({ ...prev, emergencyContactPhone: e.target.value }))}
                              disabled={!isEditingPersonalData}
                              placeholder="+54 11 1234-5678"
                              className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] text-sm"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="emergencyContactRelationship" className="text-xs sm:text-sm font-medium text-gray-700">Relación</Label>
                            <Input
                              id="emergencyContactRelationship"
                              value={personalDataForm.emergencyContactRelationship}
                              onChange={(e) => setPersonalDataForm(prev => ({ ...prev, emergencyContactRelationship: e.target.value }))}
                              disabled={!isEditingPersonalData}
                              placeholder="Esposa, hijo, madre..."
                              className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] text-sm"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {isEditingPersonalData && (
                    <div ref={saveButtonAreaRef} className="flex flex-col sm:flex-row justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                      <Button variant="outline" onClick={() => setIsEditingPersonalData(false)} className="border-gray-300 w-full sm:w-auto">
                        Cancelar
                      </Button>
                      <Button onClick={handleSavePersonalData} className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white w-full sm:w-auto">
                        <Save className="h-4 w-4 mr-1" />
                        Guardar
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="consultation" className="mt-4 sm:mt-6 flex-1 overflow-hidden">
            <Card className="border border-gray-200 h-full flex flex-col">
              <CardHeader className="pb-3 sm:pb-4 flex-shrink-0">
                <CardTitle className="text-base sm:text-lg font-medium text-[#1c2533]">Atención actual</CardTitle>
                <CardDescription className="text-xs sm:text-sm text-gray-600">
                  {appointment ? (
                    <div className="space-y-1">
                      <div>{`Turno del ${format(new Date(appointment.date + 'T12:00:00'), "dd 'de' MMMM 'de' yyyy", { locale: es })} a las ${appointment.time}`}</div>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="outline" className="text-xs">
                          <FileText className="h-3 w-3 mr-1" />
                          {appointment.type}
                        </Badge>
                        {appointment.medicalCenterId && (
                          <Badge variant="outline" className="text-xs">
                            <Building className="h-3 w-3 mr-1" />
                            {getMedicalCenterName(appointment.medicalCenterId)}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ) : 'No hay turno seleccionado'}
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                <div className="h-full overflow-auto pr-2 sm:pr-4">
                  {!appointment ? (
                    <div className="text-center text-gray-500 py-8 sm:py-12">
                      <FileText className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-sm">No hay turno seleccionado</p>
                    </div>
                  ) : existingNote ? (
                    <div className="space-y-4 sm:space-y-6">
                      <div className="flex items-center gap-2 sm:gap-3 flex-wrap">
                        <Badge variant={existingNote.isLocked ? "secondary" : "default"} className={`text-xs ${existingNote.isLocked ? "bg-gray-100 text-gray-700" : "bg-green-100 text-green-700"}`}>
                          {existingNote.isLocked ? (
                            <>
                              <Lock className="h-3 w-3 mr-1" />
                              Finalizada
                            </>
                          ) : (
                            "Atendido"
                          )}
                        </Badge>
                        {canEditNote && (
                          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatTimeRemaining(remainingEditTime)} restantes
                          </Badge>
                        )}
                        <span className="text-xs text-gray-500">
                          {format(new Date(existingNote.createdAt), "dd/MM/yyyy 'a las' HH:mm", { locale: es })}
                        </span>
                                            </div>
                      
                      {isEditingNote ? (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="editConsultationNotes" className="text-xs sm:text-sm font-medium text-gray-700">Notas de la atención</Label>
                            <Textarea
                              id="editConsultationNotes"
                              value={currentConsultationNote}
                              onChange={(e) => setCurrentConsultationNote(e.target.value)}
                              placeholder="Escriba las observaciones de la atención..."
                              rows={4}
                              className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] resize-none min-h-[100px] max-h-[300px] sm:max-h-[400px] text-sm"
                              style={{ height: 'auto', minHeight: '100px' }}
                              onInput={(e) => {
                                const target = e.target as HTMLTextAreaElement;
                                target.style.height = 'auto';
                                target.style.height = Math.min(target.scrollHeight, window.innerWidth < 640 ? 300 : 400) + 'px';
                              }}
                            />
                          </div>
                          <div className="flex flex-col sm:flex-row justify-end gap-3">
                            <Button variant="outline" onClick={() => {
                              setIsEditingNote(false)
                              setCurrentConsultationNote("")
                            }} className="border-gray-300 w-full sm:w-auto">
                              Cancelar
                            </Button>
                            <Button 
                              onClick={handleSaveEditedNote}
                              disabled={!currentConsultationNote.trim()}
                              className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white w-full sm:w-auto"
                            >
                              <Save className="h-4 w-4 mr-1" />
                              Guardar
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-gray-50 rounded-lg p-4 sm:p-6 border border-gray-200">
                          <div className="flex items-center justify-between mb-4">
                            <Label className="text-xs sm:text-sm font-medium text-gray-700">Notas de la atención</Label>
                            {canEditNote && (
                              <Button variant="ghost" size="sm" onClick={handleEditNote} className="h-8 w-8 p-0">
                                <Edit3 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                          <p className="text-xs sm:text-sm text-[#1c2533] whitespace-pre-wrap leading-relaxed">{existingNote.notes}</p>
                        </div>
                      )}
                    </div>
                  ) : canWriteNote && isWritingNote ? (
                    <div className="space-y-4 sm:space-y-6">
                      <div className="flex items-center gap-2">
                        <Badge className="bg-[#0070F3]/10 text-[#0070F3] hover:bg-[#0070F3]/20 hover:text-[#0070F3] text-xs">Atención en curso</Badge>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="consultationNotes" className="text-xs sm:text-sm font-medium text-gray-700">Notas de la atención</Label>
                        <Textarea
                          id="consultationNotes"
                          value={currentConsultationNote}
                          onChange={(e) => setCurrentConsultationNote(e.target.value)}
                          placeholder="Escriba las observaciones de la atención..."
                          rows={4}
                          className="border-gray-200 focus:border-[#0070F3] focus:ring-[#0070F3] resize-none min-h-[100px] max-h-[300px] sm:max-h-[400px] text-sm"
                          style={{ height: 'auto', minHeight: '100px' }}
                          onInput={(e) => {
                            const target = e.target as HTMLTextAreaElement;
                            target.style.height = 'auto';
                            target.style.height = Math.min(target.scrollHeight, window.innerWidth < 640 ? 300 : 400) + 'px';
                          }}
                        />
                      </div>
                      <div className="flex flex-col sm:flex-row justify-end gap-3">
                        <Button variant="outline" onClick={() => setIsWritingNote(false)} className="border-gray-300 w-full sm:w-auto">
                          Cancelar
                        </Button>
                        <AlertDialog open={showSaveConfirmation} onOpenChange={setShowSaveConfirmation}>
                          <AlertDialogTrigger asChild>
                            <Button 
                              onClick={() => setShowSaveConfirmation(true)}
                              disabled={!currentConsultationNote.trim()}
                              className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white w-full sm:w-auto"
                            >
                              <Save className="h-4 w-4 mr-1" />
                              Finalizar Atención
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="w-[90vw] max-w-md">
                            <AlertDialogHeader>
                              <AlertDialogTitle className="text-base">¿Confirmar finalización de atención?</AlertDialogTitle>
                              <AlertDialogDescription className="text-sm">
                                Las notas se guardarán permanentemente. Tendrá 15 minutos para realizar modificaciones.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter className="flex-col sm:flex-row gap-2">
                              <AlertDialogCancel className="w-full sm:w-auto">Cancelar</AlertDialogCancel>
                              <AlertDialogAction onClick={handleConfirmSaveConsultationNote} className="bg-[#0070F3] hover:bg-[#0070F3]/90 w-full sm:w-auto">
                                Confirmar
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  ) : canStartConsultation ? (
                    <div className="text-center py-8 sm:py-12">
                      <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-lg bg-[#0070F3]/10 flex items-center justify-center mx-auto mb-4 sm:mb-6">
                        <FileText className="h-6 w-6 sm:h-8 sm:w-8 text-[#0070F3]" />
                      </div>
                      <p className="text-xs sm:text-sm text-gray-600 mb-4 sm:mb-6 px-4">
                        {appointment.status === "Recepcionado" 
                          ? "El paciente está listo para la atención" 
                          : appointment.status === "En Atención"
                          ? "Aún no hay notas sobre esta atención"
                          : "El paciente aún no fue recepcionado"}
                      </p>
                      <Button onClick={handleStartConsultation} size="lg" className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white w-full sm:w-auto">
                        {appointment.status === "En Atención" ? "Escribir notas" : "Iniciar atención"}
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 py-8 sm:py-12">
                      <FileText className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-sm px-4">
                        {appointment.status === "Atendido" ? 
                          "La atención ya fue finalizada" : 
                          "La atención aún no ha comenzado"}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="mt-4 sm:mt-6 flex-1 overflow-hidden">
            <Card className="border border-gray-200 h-full flex flex-col">
              <CardHeader className="pb-3 sm:pb-4 flex-shrink-0">
                <CardTitle className="text-base sm:text-lg font-medium text-[#1c2533]">Historial de atenciones</CardTitle>
                <CardDescription className="text-xs sm:text-sm text-gray-600">
                  Registro de atenciones anteriores
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                <div className="h-full overflow-auto pr-2 sm:pr-4">
                  {medicalData.consultationNotes.length === 0 ? (
                    <div className="text-center text-gray-500 py-8 sm:py-12">
                      <Calendar className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-4 text-gray-300" />
                      <p className="text-sm">No hay atenciones anteriores registradas</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {medicalData.consultationNotes
                        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                        .map((note) => (
                          <div key={note.id} className="border border-gray-200 rounded-lg p-4 sm:p-6">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-2">
                              <div className="flex items-center gap-2 sm:gap-3 flex-wrap">
                                <Badge variant={note.isLocked ? "secondary" : "default"} className={`text-xs ${note.isLocked ? "bg-gray-100 text-gray-700" : "bg-green-100 text-green-700"}`}>
                                  {note.isLocked ? (
                                    <>
                                      <Lock className="h-3 w-3 mr-1" />
                                      Finalizada
                                    </>
                                  ) : (
                                    "Atendido"
                                  )}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {format(new Date(note.createdAt), "dd/MM/yyyy 'a las' HH:mm", { locale: es })}
                                </span>
                              </div>
                            </div>
                            
                            {/* Consultation type and establishment */}
                            <div className="flex flex-wrap gap-2 mb-3">
                              <Badge variant="outline" className="text-xs">
                                <FileText className="h-3 w-3 mr-1" />
                                {note.consultationType}
                              </Badge>
                                                             {note.medicalCenterId && (
                                 <Badge variant="outline" className="text-xs">
                                   <Building className="h-3 w-3 mr-1" />
                                   {getMedicalCenterName(note.medicalCenterId)}
                                 </Badge>
                               )}
                            </div>
                            
                            <p className="text-xs sm:text-sm text-[#1c2533] whitespace-pre-wrap leading-relaxed">{note.notes}</p>
                          </div>
                        ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 