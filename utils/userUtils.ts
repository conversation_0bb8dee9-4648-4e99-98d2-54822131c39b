import {MedicalCenterPermission} from "@/types/MedicalCenter/medicalCenterPermission";
import {User, UserRole} from "@/types/users";

export const getRoleName = (role: MedicalCenterPermission): string => {
    switch (role) {
        case MedicalCenterPermission.SUPER_ADMIN:
            return "Superusuario";
        case MedicalCenterPermission.ADMIN:
            return "Administrador";
        case MedicalCenterPermission.RECEPTIONIST:
            return "Recepcionista";
        default:
            return "Desconocido";
    }
};

export function getTurneraUserId(user: User | undefined | null): number | undefined {
    return user ? user.idFromRole.get(UserRole.TURNERA_USER) : undefined
}

export function getProfessionalUserId(user: User | undefined | null): number | undefined {
    return user ? user.idFromRole.get(UserRole.PROFESSIONAL_USER) : undefined
}

export function getEmployeeUserId(user: User | undefined | null): number | undefined {
    return user ? user.idFromRole.get(UserRole.EMPLOYEE_USER) : undefined
}

export function getEmployeeUserIdOrFail(user: User | undefined | null): number {
    const id = getEmployeeUserId(user)
    if (!id) {
        throw new Error("User is not an employee")
    }
    return id
}
