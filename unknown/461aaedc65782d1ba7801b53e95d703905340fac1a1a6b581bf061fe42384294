"use client"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Stethoscope, ArrowLeft, ShieldCheck } from "lucide-react"
import { useRouter } from "next/navigation"
import { Auth0RegisterButton } from "@/components/auth/Auth0RegisterButton"

export default function RegistrationTypePage() {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-blue-50 flex flex-col">
      <header className="py-3">
          <div className="container mx-auto flex justify-center items-center">
            <Link href="/">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-8 w-auto"
                priority
              />
            </Link>
          </div>
        </header>

      <main className="flex-grow container mx-auto px-4 py-2 sm:py-4 flex items-start justify-center">
        <div className="w-full max-w-3xl mt-4">
          <Button
            variant="outline"
            className="mb-4 flex items-center gap-[0.375rem] transition-all duration-300 bg-white border-blue-300/50 hover:bg-blue-100/70 hover:border-blue-400 px-[0.75rem] py-[0.375rem] rounded-lg shadow-sm hover:shadow-md"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-[1rem] h-[1rem] text-blue-600 group-hover:text-blue-800 transition-colors" />
            <span className="text-blue-700 group-hover:text-blue-900 font-medium text-[0.875rem]">Volver</span>
          </Button>

          <div className="bg-white p-8 sm:p-10 rounded-2xl shadow-xl border border-gray-100">
            <div className="text-center mb-8">
              <h1 className="text-3xl sm:text-4xl font-bold text-[#1c2533] mb-3">
                Crear una cuenta
              </h1>
              <p className="text-gray-600 text-lg">
                Seleccioná el tipo de cuenta que deseas crear.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Auth0RegisterButton
                className="h-auto py-8 px-6 flex flex-col items-center justify-center text-center rounded-xl border-2 border-gray-200 hover:border-[#0070F3] hover:bg-[#0070F3]/5 transition-all duration-300 group"
              >
                <div className="mb-4 p-4 bg-[#0070F3]/10 rounded-full group-hover:bg-[#0070F3]/20 transition-colors">
                  <User className="h-10 w-10 text-[#0070F3]" />
                </div>
                <h2 className="text-xl font-semibold text-[#1c2533] group-hover:text-[#0070F3] transition-colors">
                  Paciente
                </h2>
                <p className="text-gray-500 mt-1 text-sm whitespace-normal overflow-wrap-anywhere w-full">
                  Registrate para buscar profesionales y reservar turnos médicos.
                </p>
              </Auth0RegisterButton>

              <Link href="/plataforma/profesional/registro" passHref>
                <Button
                  variant="outline"
                  className="w-full h-auto py-8 px-6 flex flex-col items-center justify-center text-center rounded-xl border-2 border-gray-200 hover:border-[#1cd8e1] hover:bg-[#1cd8e1]/5 transition-all duration-300 group"
                >
                  <div className="mb-4 p-4 bg-[#1cd8e1]/10 rounded-full group-hover:bg-[#1cd8e1]/20 transition-colors">
                    <Stethoscope className="h-10 w-10 text-[#0a7c82]" />
                  </div>
                  <h2 className="text-xl font-semibold text-[#1c2533] group-hover:text-[#0a7c82] transition-colors">
                    Profesional o Establecimiento
                  </h2>
                  <p className="text-gray-500 mt-1 text-sm whitespace-normal overflow-wrap-anywhere w-full">
                    Registrá tu consultorio o centro médico para ofrecer tus servicios.
                  </p>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-white border-t border-gray-200 py-8 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-7 w-auto mb-3"
              />
              <p className="text-gray-500 text-sm">
                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
              </p>
            </div>

            <div className="flex flex-col items-center md:items-end">
              <div className="flex items-center mb-3">
                <ShieldCheck className="h-4 w-4 text-[#1cd8e1] mr-1.5" />
                <span className="text-sm text-[#1c2533] font-medium">Reservá turnos médicos 24/7</span>
              </div>
              <div className="flex space-x-4">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                  Términos y Condiciones
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                  Privacidad
                </Button>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}