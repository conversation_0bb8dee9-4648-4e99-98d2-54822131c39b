"use client"

import { useState, useEffect, use<PERSON>ontext, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import Image from "next/image"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Building2, Calendar, MapPin, User, Clock, Search, X,
  ArrowLeft, CheckCircle2, ShieldCheck,
  Stethoscope, Menu
} from "lucide-react"
import { PatientUserPill } from "@/components/ui/PatientUserPill"
import { useAuth } from "@/contexts/AuthContext"
import { MedicalCenter } from "@/types/medical-center"
import { Doctor } from "@/types/doctor"
import { useAppointments } from "@/contexts/AppointmentContext"
import { CoverageContext } from "@/contexts/CoverageContext"
import { usePatients } from "@/contexts/PatientContext"
import { format, addDays, startOfDay } from "date-fns"
import { es } from "date-fns/locale"
import { Input } from "@/components/ui/input"
import { Appointment } from "@/types/scheduler"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { DoctorCardLoading } from "@/components/ui/doctor-card-loading"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { getAvailableTimeSlots } from "@/utils/appointmentUtils"

// Helper function to get next available appointment for a doctor
const getNextAvailableAppointment = (
  doctor: Doctor,
  medicalCenter: MedicalCenter,
  existingAppointments: Record<string, Appointment[]>,
  maxDaysToCheck = 30
) => {
  const today = startOfDay(new Date());

  // Check each day for the next 30 days
  for (let i = 0; i < maxDaysToCheck; i++) {
    const checkDate = addDays(today, i);
    const dayOfWeek = checkDate.getDay();

    // Check if the doctor works on this day
    const workingDay = doctor.workingDays[dayOfWeek.toString()];
    if (!workingDay || !workingDay.enabled || !workingDay.hours.length) {
      continue;
    }

    // Check if there are any date exceptions for this day
    const dateStr = format(checkDate, 'yyyy-MM-dd');
    const dateException = doctor.dateExceptions?.[dateStr];

    // If the date is explicitly disabled, skip this day
    if (dateException && !dateException.enabled) {
      continue;
    }

    // Get existing appointments for this date
    const dateAppointments = existingAppointments[dateStr] || [];

    // Convert to array of appointments for this date
    const appointmentsForDate = dateAppointments.filter(apt =>
      apt.doctorId === doctor.id && apt.medicalCenterId === medicalCenter.id
    );

    // Use our utility function to get available time slots for this date
    const availableSlots = getAvailableTimeSlots(
      doctor,
      medicalCenter,
      checkDate,
      appointmentsForDate,
      {}, // No blocked slots data for this simple case
      doctor.id,
      medicalCenter.id
    );

    // If there are available slots, return the first one
    if (availableSlots.length > 0) {
      return { date: checkDate, time: availableSlots[0] };
    }
  }

  return null;
};

export default function MedicalCenterBookingPage() {
  const params = useParams()
  const router = useRouter()
  const medicalCenterId = params.medicalCenterId as string
  const { currentUser, logout } = useAuth()
  const { getPatientById } = usePatients()

  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [medicalCenter, setMedicalCenter] = useState<MedicalCenter | null>(null)
  const [allDoctors, setAllDoctors] = useState<Doctor[]>([])
  const [filteredDoctors, setFilteredDoctors] = useState<Doctor[]>([])
  const [loading, setLoading] = useState(true)
  const [doctorNextAppointments, setDoctorNextAppointments] = useState<Record<string, { date: Date; time: string } | null>>({})
  const [specialties, setSpecialties] = useState<string[]>([])
  const [selectedSpecialty, setSelectedSpecialty] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [loadingDoctorId, setLoadingDoctorId] = useState<string | null>(null)

  // Coverage filter state
  const [selectedCoverage, setSelectedCoverage] = useState<string>("")
  const [selectedPlan, setSelectedPlan] = useState<string>("")
  const [noCoverage, setNoCoverage] = useState<boolean>(false)
  const [selectedConsultationType, setSelectedConsultationType] = useState<string>("")
  const [hasInitializedCoverage, setHasInitializedCoverage] = useState<boolean>(false)

  // We'll use the CoverageContext in the existing context section below

  // Get appointments from context
  const { appointments } = useAppointments()

  // Load medical center and its doctors
  useEffect(() => {
    if (medicalCenterId) {
      setLoading(true)

      // Get the medical center
      const allMedicalCenters = storage.getMedicalCenters()
      const foundMedicalCenter = allMedicalCenters.find(center => center.id === medicalCenterId)

      if (foundMedicalCenter) {
        setMedicalCenter(foundMedicalCenter)

        // Get all doctors in the system
        const doctorsInSystem = storage.getAllDoctorsInSystem()

        // Filter to only include doctors that work at this medical center
        const centerDoctors = doctorsInSystem.filter(doctor =>
          foundMedicalCenter.doctors.includes(doctor.id)
        )

        // Sort doctors alphabetically by name
        const sortedDoctors = [...centerDoctors].sort((a, b) =>
          a.name.localeCompare(b.name)
        )

        setAllDoctors(sortedDoctors)
        setFilteredDoctors(sortedDoctors)

        // Extract all unique specialties
        const allSpecialties = new Set<string>()
        sortedDoctors.forEach(doctor => {
          if (doctor.specialties && doctor.specialties.length > 0) {
            doctor.specialties.forEach(specialty => {
              allSpecialties.add(specialty)
            })
          }
        })

        // Convert to array and sort alphabetically
        const specialtiesArray = Array.from(allSpecialties).sort()
        setSpecialties(specialtiesArray)

        // Get next available appointment for each doctor
        const nextAppointments: Record<string, { date: Date; time: string } | null> = {};

        for (const doctor of sortedDoctors) {
          nextAppointments[doctor.id] = getNextAvailableAppointment(
            doctor,
            foundMedicalCenter,
            appointments
          );
        }

        setDoctorNextAppointments(nextAppointments);
      }

      setLoading(false)
    }
  }, [medicalCenterId, appointments])

  // Get coverage context for filtering
  const {
    medicalCoverages,
    isDoctorCoverageExcluded,
    getDoctorsAcceptingCoverage
  } = useContext(CoverageContext)

  // Get available coverages for this medical center's doctors
  const availableCoveragesForCenter = useMemo(() => {
    if (!allDoctors.length || !medicalCoverages.length) return [];

    // Get all coverages accepted by at least one doctor in this medical center
    const availableCoverages = medicalCoverages.filter(coverage => {
      // Skip "Sin Cobertura" as it's handled separately
      if (coverage.name === "Sin Cobertura") return false;

      // Check if any doctor in this medical center accepts this coverage
      // The isDoctorCoverageExcluded function has been updated to handle consultation type exclusions
      return allDoctors.some(doctor => !isDoctorCoverageExcluded(doctor.id, coverage.id));
    });

    return availableCoverages;
  }, [allDoctors, medicalCoverages, isDoctorCoverageExcluded]);

  // Dynamically create plansByCoverage from available coverages
  const plansByCoverage = useMemo(() => {
    return availableCoveragesForCenter.reduce((acc, coverage) => {
      // For each coverage, only include plans that are accepted by at least one doctor
      const acceptedPlans = coverage.plans?.filter(plan => {
        return allDoctors.some(doctor => !isDoctorCoverageExcluded(doctor.id, coverage.id, plan));
      }) || [];

      acc[coverage.name] = acceptedPlans;
      return acc;
    }, {} as Record<string, string[]>);
  }, [availableCoveragesForCenter, allDoctors, isDoctorCoverageExcluded])

  // Initialize coverage with user's default coverage if logged in
  useEffect(() => {
    // Only run this once and only after medical center and doctors are loaded
    if (!hasInitializedCoverage && !loading && medicalCenter && allDoctors.length > 0) {
      // First check URL parameters for coverage information
      const urlParams = new URLSearchParams(window.location.search);
      const coverageFromUrl = urlParams.get('coverage');
      const planFromUrl = urlParams.get('plan');
      const noCoverageFromUrl = urlParams.get('noCoverage');
      const consultationTypeFromUrl = urlParams.get('consultationType');

      if (noCoverageFromUrl === 'true') {
        // If noCoverage is specified in URL, set that first
        setNoCoverage(true);
        setHasInitializedCoverage(true);
      } else if (coverageFromUrl) {
        // If coverage is in URL, try to set that
        const coverage = availableCoveragesForCenter.find(c => c.name === coverageFromUrl);

        if (coverage) {
          // Check if any doctor accepts this coverage
          const doctorsAcceptingCoverage = allDoctors.filter(doctor =>
            !isDoctorCoverageExcluded(doctor.id, coverage.id)
          );

          if (doctorsAcceptingCoverage.length > 0) {
            setSelectedCoverage(coverageFromUrl);

            // Set plan if available
            if (planFromUrl) {
              const availablePlans = plansByCoverage[coverageFromUrl] || [];
              if (availablePlans.includes(planFromUrl)) {
                setSelectedPlan(planFromUrl);
              }
            }
            setHasInitializedCoverage(true);
          }
        }
      } else if (currentUser) {
        // If no coverage from URL but user is logged in, use their default coverage
        const defaultPatient = currentUser.defaultPatientId ? getPatientById(currentUser.defaultPatientId) : null;

        if (defaultPatient) {
          // Get the patient's default coverage
          const defaultCoverage = defaultPatient.defaultCoverage || defaultPatient.coverage;

          if (defaultCoverage) {
            // Check if coverage has a plan format (e.g., "OSDE 310")
            const coverageParts = defaultCoverage.split(' ');

            if (defaultCoverage === "Sin Cobertura") {
              // If it's "Sin Cobertura"
              setNoCoverage(true);
              setHasInitializedCoverage(true);
            } else if (coverageParts.length > 1) {
              // If format is "OSDE 310", separate into coverage and plan
              const coverageName = coverageParts[0];
              const planName = coverageParts.slice(1).join(' ');

              // Find the coverage in the available coverages
              const coverage = availableCoveragesForCenter.find(c => c.name === coverageName);

              if (coverage) {
                // Check if any doctor accepts this coverage
                const doctorsAcceptingCoverage = allDoctors.filter(doctor =>
                  !isDoctorCoverageExcluded(doctor.id, coverage.id)
                );

                if (doctorsAcceptingCoverage.length > 0) {
                  setSelectedCoverage(coverageName);

                  // Check if the plan is valid for this coverage
                  const availablePlans = plansByCoverage[coverageName] || [];
                  if (availablePlans.includes(planName)) {
                    setSelectedPlan(planName);
                  }
                  setHasInitializedCoverage(true);
                }
              }
            } else {
              // If it's just a coverage name
              // Find the coverage in the available coverages
              const coverage = availableCoveragesForCenter.find(c => c.name === defaultCoverage);

              if (coverage) {
                // Check if any doctor accepts this coverage
                const doctorsAcceptingCoverage = allDoctors.filter(doctor =>
                  !isDoctorCoverageExcluded(doctor.id, coverage.id)
                );

                if (doctorsAcceptingCoverage.length > 0) {
                  setSelectedCoverage(defaultCoverage);
                  setHasInitializedCoverage(true);
                }
              }
            }
          }
        }
      }

      // If we got here and haven't initialized coverage yet,
      // mark as initialized but don't set any default coverage
      if (!hasInitializedCoverage) {
        setHasInitializedCoverage(true);
      }

      // Check if consultation type is specified in URL and set it
      if (consultationTypeFromUrl) {
        // We need to check if this consultation type is valid for the selected coverage
        // First, filter doctors that accept the selected coverage
        let filteredDoctors = [...allDoctors];

        if (noCoverageFromUrl === 'true') {
          // Filter doctors that accept patients without coverage
          const sinCoberturaCoverage = medicalCoverages.find(c => c.name === "Sin Cobertura");
          if (sinCoberturaCoverage) {
            filteredDoctors = filteredDoctors.filter(doctor =>
              !isDoctorCoverageExcluded(doctor.id, sinCoberturaCoverage.id)
            );
          }
        } else if (coverageFromUrl) {
          // Filter doctors that accept the selected coverage and plan
          const coverage = medicalCoverages.find(c => c.name === coverageFromUrl);
          if (coverage) {
            filteredDoctors = filteredDoctors.filter(doctor => {
              const isCoverageExcluded = isDoctorCoverageExcluded(doctor.id, coverage.id);

              // Check if this is a doctor-specific coverage
              const doctorsAcceptingCoverage = getDoctorsAcceptingCoverage(coverage.id);
              const isDoctorSpecific = doctorsAcceptingCoverage.length === 1 &&
                                      doctorsAcceptingCoverage[0] === doctor.id;

              if (isCoverageExcluded && !isDoctorSpecific) return false;

              if (planFromUrl) {
                return !isDoctorCoverageExcluded(doctor.id, coverage.id, planFromUrl);
              }

              return true;
            });
          }
        }

        // Get all available consultation types from filtered doctors
        const allAvailableConsultationTypes = new Set<string>();
        filteredDoctors.forEach(doctor => {
          doctor.consultationTypes?.forEach(ct => {
            if (ct.availableOnline !== false) {
              allAvailableConsultationTypes.add(ct.name);
            }
          });
        });

        // Now check if the consultation type exists among the filtered doctors
        const consultationTypeExists = filteredDoctors.some(doctor =>
          doctor.consultationTypes?.some(ct =>
            ct.name === consultationTypeFromUrl &&
            ct.availableOnline !== false &&
            // If "Sin Cobertura" is selected, only show types that accept private patients
            (noCoverageFromUrl === 'true' ? ct.acceptsPrivatePay !== false : true) &&
            // If a coverage is selected, check if it's not excluded for this consultation type
            (coverageFromUrl && !noCoverageFromUrl ?
              !ct.excludedCoverages?.some(exclusion => {
                const coverage = medicalCoverages.find(c => c.name === coverageFromUrl);
                if (!coverage) return false;
                return exclusion.coverageId === coverage.id &&
                  (exclusion.planId === null || (planFromUrl && exclusion.planId === planFromUrl));
              }) :
              true
            )
          )
        );

        if (consultationTypeExists) {
          setSelectedConsultationType(consultationTypeFromUrl);
        }
      }
    }
  }, [
    currentUser,
    getPatientById,
    hasInitializedCoverage,
    loading,
    medicalCenter,
    allDoctors,
    availableCoveragesForCenter,
    plansByCoverage,
    isDoctorCoverageExcluded,
    medicalCoverages,
    getDoctorsAcceptingCoverage
  ])

  // Handle consultation type from URL in a separate effect
  useEffect(() => {
    if (hasInitializedCoverage && allDoctors.length > 0 && medicalCenter) {
      // Get URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const consultationTypeFromUrl = urlParams.get('consultationType');

      if (consultationTypeFromUrl && !selectedConsultationType) {
        // Get all unique consultation types from doctors that match the current coverage filter
        let filteredDoctors = [...allDoctors];

        // Apply coverage filters
        if (noCoverage) {
          // Filter doctors that accept patients without coverage
          const sinCoberturaCoverage = medicalCoverages.find(c => c.name === "Sin Cobertura");
          if (sinCoberturaCoverage) {
            filteredDoctors = filteredDoctors.filter(doctor =>
              !isDoctorCoverageExcluded(doctor.id, sinCoberturaCoverage.id)
            );
          }
        } else if (selectedCoverage) {
          // Filter doctors that accept the selected coverage and plan
          const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
          if (coverage) {
            filteredDoctors = filteredDoctors.filter(doctor => {
              const isCoverageExcluded = isDoctorCoverageExcluded(doctor.id, coverage.id);

              // Check if this is a doctor-specific coverage
              const doctorsAcceptingCoverage = getDoctorsAcceptingCoverage(coverage.id);
              const isDoctorSpecific = doctorsAcceptingCoverage.length === 1 &&
                                      doctorsAcceptingCoverage[0] === doctor.id;

              if (isCoverageExcluded && !isDoctorSpecific) return false;

              if (selectedPlan) {
                return !isDoctorCoverageExcluded(doctor.id, coverage.id, selectedPlan);
              }

              return true;
            });
          }
        }

        // Get all available consultation types
        const availableConsultationTypes = new Set<string>();
        filteredDoctors.forEach(doctor => {
          doctor.consultationTypes?.forEach(ct => {
            if (ct.availableOnline !== false) {
              // Check if it's compatible with the current coverage
              if (noCoverage) {
                // For "Sin Cobertura", check if it accepts private patients
                if (ct.acceptsPrivatePay !== false) {
                  availableConsultationTypes.add(ct.name);
                }
              } else if (selectedCoverage) {
                // For selected coverage, check if it's not excluded
                const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
                if (coverage) {
                  const isExcluded = ct.excludedCoverages?.some(
                    exclusion =>
                      exclusion.coverageId === coverage.id &&
                      (exclusion.planId === null || (selectedPlan && exclusion.planId === selectedPlan))
                  );

                  if (!isExcluded) {
                    availableConsultationTypes.add(ct.name);
                  }
                }
              } else {
                // If no coverage filter, add all consultation types
                availableConsultationTypes.add(ct.name);
              }
            }
          });
        });

        // Check if the URL consultation type is in the available types
        if (availableConsultationTypes.has(consultationTypeFromUrl)) {
          setSelectedConsultationType(consultationTypeFromUrl);

          // Schedule scrolling to the consultation type section after a short delay
          // to ensure the UI has updated and the section is visible
          setTimeout(() => {
            const consultationSection = document.getElementById('consultation-type');
            if (consultationSection) {
              consultationSection.scrollIntoView({ behavior: 'smooth' });
            }
          }, 500);
        }
      }
    }
  }, [
    hasInitializedCoverage,
    allDoctors,
    medicalCenter,
    noCoverage,
    selectedCoverage,
    selectedPlan,
    selectedConsultationType,
    medicalCoverages,
    isDoctorCoverageExcluded,
    getDoctorsAcceptingCoverage
  ]);

  // Filter doctors based on specialty, search query, coverage, and consultation type
  useEffect(() => {
    if (allDoctors.length > 0) {
      // If no coverage is selected, reset filters and show all doctors
      if (!selectedCoverage && !noCoverage) {
        setSelectedSpecialty(null);
        setSearchQuery("");
        setSelectedConsultationType("");
        setFilteredDoctors([...allDoctors]);
        return;
      }

      let filtered = [...allDoctors]

      // Filter by specialty if one is selected
      if (selectedSpecialty) {
        filtered = filtered.filter(doctor =>
          doctor.specialties && doctor.specialties.some(s => s === selectedSpecialty)
        )
      }

      // Filter by search query if one exists
      if (searchQuery.trim() !== "") {
        const query = searchQuery.toLowerCase()
        filtered = filtered.filter(doctor =>
          doctor.name.toLowerCase().includes(query)
        )
      }

      // Filter by coverage
      if (noCoverage) {
        // Show doctors that accept patients without coverage
        // Find the "Sin Cobertura" coverage in the medicalCoverages array
        const sinCoberturaCoverage = medicalCoverages.find(c => c.name === "Sin Cobertura");
        if (sinCoberturaCoverage) {
          // Filter doctors that accept "Sin Cobertura"
          filtered = filtered.filter(doctor => !isDoctorCoverageExcluded(doctor.id, sinCoberturaCoverage.id));
        }
      } else if (selectedCoverage) {
        // Filter by selected coverage
        filtered = filtered.filter(doctor => {
          // Find the coverage in the medicalCoverages array
          const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
          if (!coverage) return false;

          // Check if doctor accepts this coverage
          // A doctor accepts a coverage if it's not excluded for them
          const isCoverageExcluded = isDoctorCoverageExcluded(doctor.id, coverage.id);

          // Check if this is a doctor-specific coverage (only this doctor accepts it)
          const doctorsAcceptingCoverage = getDoctorsAcceptingCoverage(coverage.id);
          const isDoctorSpecific = doctorsAcceptingCoverage.length === 1 && doctorsAcceptingCoverage[0] === doctor.id;

          // If the coverage is excluded and not doctor-specific, filter out this doctor
          if (isCoverageExcluded && !isDoctorSpecific) return false;

          // If plan is selected, check if doctor accepts this specific plan
          if (selectedPlan) {
            // Check if the plan is excluded for this doctor
            return !isDoctorCoverageExcluded(doctor.id, coverage.id, selectedPlan);
          }

          return true;
        })
      }

      // Filter by consultation type if one is selected
      if (selectedConsultationType) {
        filtered = filtered.filter(doctor => {
          // Check if the doctor has this consultation type
          const hasConsultationType = doctor.consultationTypes?.some(ct =>
            ct.name === selectedConsultationType &&
            // Only include types that are available online
            ct.availableOnline !== false
          );

          if (!hasConsultationType) return false;

          // If "Sin Cobertura" is selected, check if the consultation type accepts private patients
          if (noCoverage) {
            const consultationType = doctor.consultationTypes?.find(ct => ct.name === selectedConsultationType);
            return consultationType?.acceptsPrivatePay !== false;
          }

          // If a coverage is selected, check if it's not excluded for this consultation type
          if (selectedCoverage) {
            const consultationType = doctor.consultationTypes?.find(ct => ct.name === selectedConsultationType);
            if (!consultationType) return false;

            const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
            if (!coverage) return false;

            // Check if this coverage is excluded for this consultation type
            const isExcluded = consultationType.excludedCoverages?.some(
              exclusion =>
                exclusion.coverageId === coverage.id &&
                (exclusion.planId === null || (selectedPlan && exclusion.planId === selectedPlan))
            );

            return !isExcluded;
          }

          return true;
        });
      }

      setFilteredDoctors(filtered)
    }
  }, [allDoctors, selectedSpecialty, searchQuery, selectedCoverage, selectedPlan, noCoverage, selectedConsultationType, isDoctorCoverageExcluded, getDoctorsAcceptingCoverage, medicalCoverages])

  // Handle specialty selection
  const handleSpecialtyClick = (specialty: string) => {
    if (selectedSpecialty === specialty) {
      // If clicking the already selected specialty, deselect it
      setSelectedSpecialty(null)
    } else {
      // Otherwise, select the clicked specialty
      setSelectedSpecialty(specialty)
    }
  }

  // Handle search query change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }

  // Handle coverage selection
  const handleCoverageChange = (coverage: string) => {
    // If coverage is being deselected (empty string), reset everything
    if (coverage === "") {
      setSelectedCoverage("")
      setSelectedPlan("")
      setSelectedConsultationType("")
      setNoCoverage(false)
      return
    }

    setSelectedCoverage(coverage)
    setSelectedPlan("") // Reset plan when coverage changes
    setSelectedConsultationType("") // Reset consultation type when coverage changes
    setNoCoverage(false) // Uncheck no coverage option
  }

  // Handle plan selection
  const handlePlanChange = (plan: string) => {
    setSelectedPlan(plan)
    setSelectedConsultationType("") // Reset consultation type when plan changes
  }

  // Handle no coverage toggle
  const handleNoCoverageChange = (checked: boolean) => {
    setNoCoverage(checked)
    if (checked) {
      // Reset coverage and plan selections when no coverage is checked
      setSelectedCoverage("")
      setSelectedPlan("")
      setSelectedConsultationType("")
    }
  }

  // Get available plans for selected coverage
  const getAvailablePlans = (): string[] => {
    if (!selectedCoverage) return [];
    return plansByCoverage[selectedCoverage] || [];
  }

  const handleSelectDoctor = (doctorId: string) => {
    // Show loading indicator for this doctor
    setLoadingDoctorId(doctorId)

    // Build coverage parameters
    let coverageParams = '';
    if (noCoverage) {
      coverageParams = '&noCoverage=true';
    } else if (selectedCoverage) {
      coverageParams = `&coverage=${encodeURIComponent(selectedCoverage)}`;
      if (selectedPlan) {
        coverageParams += `&plan=${encodeURIComponent(selectedPlan)}`;
      }
    }

    // Add consultation type parameter if selected
    let consultationTypeParam = '';
    if (selectedConsultationType) {
      consultationTypeParam = `&consultationType=${encodeURIComponent(selectedConsultationType)}`;
    }

    // Navigate to the booking form for this doctor at this medical center
    router.push(`/plataforma/reservar/cita?doctorId=${doctorId}&medicalCenterId=${medicalCenterId}&source=medicalCenter${coverageParams}${consultationTypeParam}`)
  }

  const handleSelectAppointment = (doctorId: string, date: Date, time: string) => {
    // Build coverage parameters
    let coverageParams = '';
    if (noCoverage) {
      coverageParams = '&noCoverage=true';
    } else if (selectedCoverage) {
      coverageParams = `&coverage=${encodeURIComponent(selectedCoverage)}`;
      if (selectedPlan) {
        coverageParams += `&plan=${encodeURIComponent(selectedPlan)}`;
      }
    }

    // Add consultation type parameter if selected
    let consultationTypeParam = '';
    if (selectedConsultationType) {
      consultationTypeParam = `&consultationType=${encodeURIComponent(selectedConsultationType)}`;
    }

    // Navigate to the booking form with pre-selected date and time
    const dateStr = format(date, 'yyyy-MM-dd')
    router.push(`/plataforma/reservar/cita?doctorId=${doctorId}&medicalCenterId=${medicalCenterId}&date=${dateStr}&time=${time}&source=medicalCenter${coverageParams}${consultationTypeParam}`)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#F4F7F9]">
        <div className="text-center bg-white p-8 rounded-xl shadow-md border border-blue-100 max-w-md w-full mx-4">
          <div className="relative mb-6">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-100 border-t-blue-600 mx-auto"></div>
            <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
              <Building2 className="h-6 w-6 text-blue-600 animate-pulse" />
            </div>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Cargando profesionales</h3>
          <p className="text-gray-600">Estamos buscando los mejores profesionales disponibles para ti...</p>

          <div className="mt-6 space-y-2">
            <div className="h-2.5 bg-gray-200 rounded-full w-3/4 mx-auto animate-pulse"></div>
            <div className="h-2.5 bg-gray-200 rounded-full w-1/2 mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!medicalCenter) {
    return (
      <div className="min-h-screen bg-[#F4F7F9] py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md p-8 text-center border border-red-100">
            <div className="bg-red-50 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6">
              <Building2 className="h-10 w-10 text-red-500" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">Establecimiento no encontrado</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              No pudimos encontrar información sobre el establecimiento solicitado. Por favor, intente nuevamente.
            </p>
            <Button
              onClick={() => router.push("/")}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5"
              size="lg"
            >
              Volver al inicio
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-blue-50">
      {/* Fixed height placeholder to prevent content jumping */}
      <div className="h-[60px] w-full" />

      {/* Fixed frosted header */}
      <header className="fixed top-0 left-0 right-0 z-50">
        {/* Frosty backdrop */}
        <div className="fixed top-0 left-0 right-0 w-full z-40 backdrop-blur-sm bg-blue-50/70 h-[60px]" />

        {/* Header content */}
        <div className="max-w-6xl mx-auto px-7 flex justify-between items-center h-[60px] relative z-50">
          {/* Logo and mobile menu button */}
          <div className="flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 md:hidden"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </button>
            <Link href="/">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera"
                width={100}
                height={30}
                priority
                className="h-7 w-auto"
              />
            </Link>
          </div>

          {/* Auth buttons/user pill */}
          <div className="flex items-center gap-2">
            {currentUser ? (
              <>
                <Button variant="ghost" asChild className="hidden sm:inline-flex text-sm hover:bg-blue-50 hover:text-blue-600">
                  <Link href="/plataforma/paciente">Mis turnos</Link>
                </Button>
                <PatientUserPill
                  currentUser={currentUser}
                  currentPatient={currentUser?.defaultPatientId ? getPatientById(currentUser.defaultPatientId) || null : null}
                  logout={logout}
                />
              </>
            ) : (
              <>
                <Button variant="outline" className="rounded-[12px]" size="sm" asChild>
                  <Link href="/plataforma/paciente/registro">Registrarme</Link>
                </Button>
                <Button className="rounded-[12px] bg-blue-600 hover:bg-blue-700" size="sm" asChild>
                  <Link href="/plataforma/paciente/login">Iniciar sesión</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu dropdown */}
          {isMenuOpen && (
            <div className="absolute top-full left-0 right-0 mx-4 bg-white shadow-lg rounded-xl md:hidden z-50 mt-2">
              <div className="flex flex-col p-4 space-y-4">
                <Link href="/plataforma/paciente" className="mx-3 text-slate-800 hover:text-blue-600">
                  Mis turnos
                </Link>
              </div>
            </div>
          )}
        </div>
      </header>

      <main className="container mx-auto px-4 py-6 sm:py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 bg-white p-4 rounded-xl shadow-sm border border-gray-100">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="mr-3 text-[#0070F3] hover:bg-[#0070F3]/5 hover:text-[#0070F3] border-0"
                onClick={() => {
                  // Check if we came from the map
                  const urlParams = new URLSearchParams(window.location.search);
                  const source = urlParams.get('source');

                  if (source === 'map') {
                    // Go back to the search page
                    router.back();
                  } else {
                    // Default behavior - go to home
                    router.push("/");
                  }
                }}
              >
                <ArrowLeft className="h-4 w-4 mr-1.5" />
                Volver
              </Button>

              <div className="border-l border-gray-200 pl-3">
                <h1 className="text-xl font-bold text-[#1c2533]">Reservar turno</h1>
                <p className="text-gray-600 text-sm">Establecimiento médico</p>
              </div>
            </div>
          </div>

          {/* Medical Center Card - Redesigned */}
          <div className="w-full mb-8 overflow-hidden rounded-2xl shadow-sm border border-gray-100">
            {/* Integrated Card with Header and Coverage Section */}
            <div className="bg-white p-6 relative">
              {/* Blue accent dot/gradient on the right */}
              <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-br from-[#0070F3]/5 to-transparent rounded-tl-[120px] pointer-events-none"></div>

              <div className="flex flex-row items-start gap-4 relative z-0">
                {/* Medical Center Avatar - Modern Style */}
                <div className="flex-shrink-0 relative">
                  <div className="w-20 h-20 rounded-2xl bg-white shadow-lg p-0.5 relative">
                    <div className="w-full h-full rounded-xl overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-[#0070F3] to-[#0070F3]/80"></div>
                      <div className="absolute inset-0 bg-[url('/images/pattern-dots.png')] bg-repeat opacity-5"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-white text-2xl font-bold">
                          {medicalCenter.name.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-1 shadow-md">
                      <div className="bg-[#0070F3] rounded-full p-1">
                        <Building2 className="h-3 w-3 text-white" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Medical Center Info - Clean Layout - Mobile optimized to be next to avatar */}
                <div className="flex-1">
                  <h2 className="text-xl md:text-2xl font-bold text-[#1c2533]">{medicalCenter.name}</h2>

                  {medicalCenter.address && (
                    <div className="flex items-center mt-1 text-gray-600">
                      <MapPin className="h-4 w-4 mr-1.5 text-[#0070F3] flex-shrink-0" />
                      <span className="text-sm">{medicalCenter.address}</span>
                    </div>
                  )}

                  {/* Stats Pills */}
                  <div className="flex flex-wrap gap-2 mt-3">
                    <div className="flex items-center bg-[#0070F3]/5 px-3 py-1.5 rounded-full">
                      <Stethoscope className="h-4 w-4 mr-1.5 text-[#0070F3]" />
                      <span className="text-sm font-medium text-[#1c2533]">{allDoctors.length} {allDoctors.length === 1 ? 'profesional' : 'profesionales'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Integrated Coverages Section - No visual separation */}
              <div className="mt-6 pt-5 border-t border-gray-100">
                <div className="flex items-center mb-3">
                  <ShieldCheck className="h-4 w-4 mr-1.5 text-[#0a7c82]" />
                  <h3 className="text-sm font-semibold text-[#1c2533]">Coberturas aceptadas</h3>
                </div>

                <div className="flex flex-wrap gap-2">
                  {availableCoveragesForCenter.length > 0 ? (
                    availableCoveragesForCenter.slice(0, 5).map((coverage, index) => (
                      <Badge
                        key={index}
                        className="bg-[#1cd8e1]/20 shadow-sm border border-[#1cd8e1]/30 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium hover:bg-[#1cd8e1]/25 transition-colors"
                      >
                        {coverage.name}
                      </Badge>
                    ))
                  ) : (
                    <Badge
                      className="bg-[#1cd8e1]/20 shadow-sm border border-[#1cd8e1]/30 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium hover:bg-[#1cd8e1]/25 transition-colors"
                    >
                      Sin Cobertura
                    </Badge>
                  )}

                  {availableCoveragesForCenter.length > 5 && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge
                            className="bg-[#1cd8e1]/20 border border-[#1cd8e1]/30 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium cursor-pointer hover:bg-[#1cd8e1]/25 transition-colors"
                          >
                            +{availableCoveragesForCenter.length - 5} más
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="p-2 space-y-1">
                            {availableCoveragesForCenter.slice(5).map((coverage, index) => (
                              <div key={index} className="text-xs py-0.5">{coverage.name}</div>
                            ))}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Coverage Selection Section - Primary Filter */}
          <div className="w-full mb-6 rounded-xl overflow-hidden shadow-sm border border-[#0070F3]/30">
            <div className="bg-gradient-to-r from-[#0070F3]/10 to-[#0070F3]/5 py-3 px-5 border-b border-[#0070F3]/20">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 mr-3 shrink-0">
                  <span className="text-[#0a7c82] font-bold text-2xl">1</span>
                </div>
                <h3 className="text-lg font-semibold text-[#1c2533]">Seleccioná tu cobertura médica</h3>
              </div>
            </div>

            <div className="p-5 bg-white">
              {/* Desktop Layout */}
              <div className="hidden md:flex md:flex-row gap-4 items-end">
                {/* Coverage and Plan Selection */}
                <div className="flex-1 grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="coverage" className="block mb-2 text-sm font-medium text-gray-700">Cobertura</Label>
                    <Select
                      value={selectedCoverage}
                      onValueChange={handleCoverageChange}
                      disabled={noCoverage}
                    >
                      <SelectTrigger id="coverage" className="w-full bg-white border-gray-200 rounded-lg">
                        <SelectValue placeholder="Seleccionar Cobertura" />
                      </SelectTrigger>
                      <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                        {availableCoveragesForCenter.map(coverage => (
                          <SelectItem key={coverage.id} value={coverage.name}>
                            {coverage.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="plan" className="block mb-2 text-sm font-medium text-gray-700">Plan</Label>
                    <Select
                      value={selectedPlan}
                      onValueChange={handlePlanChange}
                      disabled={!selectedCoverage || noCoverage}
                    >
                      <SelectTrigger id="plan" className="w-full bg-white border-gray-200 rounded-lg">
                        <SelectValue placeholder="Seleccionar Plan" />
                      </SelectTrigger>
                      <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                        {selectedCoverage &&
                          getAvailablePlans().map((plan) => (
                            <SelectItem key={plan} value={plan}>
                              {plan}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Sin Cobertura toggle - Desktop */}
                <div className="flex items-center py-2 px-3 bg-[#F4F7F9] rounded-lg border border-gray-100 min-w-[200px] h-[38px]">
                  <Switch
                    id="no-coverage-desktop"
                    checked={noCoverage}
                    onCheckedChange={handleNoCoverageChange}
                    className="data-[state=checked]:bg-[#1cd8e1]"
                  />
                  <Label htmlFor="no-coverage-desktop" className="ml-2 font-medium text-[#1c2533]">Sin Cobertura</Label>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="flex md:hidden flex-col gap-4">
                <div>
                  <Label htmlFor="coverage-mobile" className="block mb-2 text-sm font-medium text-gray-700">Cobertura</Label>
                  <Select
                    value={selectedCoverage}
                    onValueChange={handleCoverageChange}
                    disabled={noCoverage}
                  >
                    <SelectTrigger id="coverage-mobile" className="w-full bg-white border-gray-200 rounded-lg">
                      <SelectValue placeholder="Seleccionar Cobertura" />
                    </SelectTrigger>
                    <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                      {availableCoveragesForCenter.map(coverage => (
                        <SelectItem key={coverage.id} value={coverage.name}>
                          {coverage.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="plan-mobile" className="block mb-2 text-sm font-medium text-gray-700">Plan</Label>
                  <Select
                    value={selectedPlan}
                    onValueChange={handlePlanChange}
                    disabled={!selectedCoverage || noCoverage}
                  >
                    <SelectTrigger id="plan-mobile" className="w-full bg-white border-gray-200 rounded-lg">
                      <SelectValue placeholder="Seleccionar Plan" />
                    </SelectTrigger>
                    <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                      {selectedCoverage &&
                        getAvailablePlans().map((plan) => (
                          <SelectItem key={plan} value={plan}>
                            {plan}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Sin Cobertura toggle - Mobile */}
                <div className="flex items-center py-2 px-3 bg-[#F4F7F9] rounded-lg border border-gray-100">
                  <Switch
                    id="no-coverage-mobile"
                    checked={noCoverage}
                    onCheckedChange={handleNoCoverageChange}
                    className="data-[state=checked]:bg-[#1cd8e1]"
                  />
                  <Label htmlFor="no-coverage-mobile" className="ml-2 font-medium text-[#1c2533]">Sin Cobertura</Label>
                </div>

                {/* Ver profesionales button - Mobile only */}
                {(selectedCoverage || noCoverage) && (
                  <Button
                    className="mt-2 bg-[#0070F3] hover:bg-[#0070F3]/90 text-white"
                    onClick={() => {
                      const resultsSection = document.getElementById('doctors-results');
                      if (resultsSection) resultsSection.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    <User className="h-4 w-4 mr-2" />
                    Ver profesionales
                  </Button>
                )}
              </div>

              {/* Active coverage filters */}
              {(selectedCoverage || noCoverage) && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center mb-2">
                    <CheckCircle2 className="h-4 w-4 text-[#0a7c82] mr-2" />
                    <h3 className="text-sm font-medium text-[#1c2533]">Cobertura seleccionada</h3>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {noCoverage ? (
                      <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                        Sin Cobertura
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                          onClick={() => setNoCoverage(false)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <>
                        {selectedCoverage && (
                          <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                            Cobertura: {selectedCoverage}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                              onClick={() => {
                                setSelectedCoverage("");
                                setSelectedPlan("");
                              }}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                        {selectedPlan && (
                          <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                            Plan: {selectedPlan}
                            <Button
                              variant="ghost"
                              size="sm"
                              className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                              onClick={() => setSelectedPlan("")}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Consultation Type Selection - Step 2 */}
          {(noCoverage || (selectedCoverage && (selectedPlan || getAvailablePlans().length === 0))) && (
            <div className="w-full mb-6 rounded-xl overflow-hidden shadow-sm border border-[#0070F3]/30">
              <div className="bg-gradient-to-r from-[#0070F3]/10 to-[#0070F3]/5 py-3 px-5 border-b border-[#0070F3]/20">
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 mr-3 shrink-0">
                    <span className="text-[#0a7c82] font-bold text-2xl">2</span>
                  </div>
                  <h3 className="text-lg font-semibold text-[#1c2533]">Seleccioná la atención que necesitás</h3>
                </div>
              </div>

              <div className="p-5 bg-white">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="col-span-2">
                    <Label htmlFor="consultation-type" className="block mb-2 text-sm font-medium text-gray-700">Tipo de atención</Label>
                    <Select
                      value={selectedConsultationType}
                      onValueChange={setSelectedConsultationType}
                    >
                      <SelectTrigger id="consultation-type" className="w-full bg-white border-gray-200 rounded-lg">
                        <SelectValue placeholder="Seleccionar tipo de atención" />
                      </SelectTrigger>
                      <SelectContent position="popper" align="start" sideOffset={4} className="z-[100]">
                        {/* Get unique consultation types from all doctors that accept the selected coverage */}
                        {(() => {
                          // First, filter doctors that accept the selected coverage
                          let filteredDoctors = [...allDoctors];

                          if (noCoverage) {
                            // Filter doctors that accept patients without coverage
                            const sinCoberturaCoverage = medicalCoverages.find(c => c.name === "Sin Cobertura");
                            if (sinCoberturaCoverage) {
                              filteredDoctors = filteredDoctors.filter(doctor =>
                                !isDoctorCoverageExcluded(doctor.id, sinCoberturaCoverage.id)
                              );
                            }
                          } else if (selectedCoverage) {
                            // Filter doctors that accept the selected coverage and plan
                            const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
                            if (coverage) {
                              filteredDoctors = filteredDoctors.filter(doctor => {
                                const isCoverageExcluded = isDoctorCoverageExcluded(doctor.id, coverage.id);

                                // Check if this is a doctor-specific coverage
                                const doctorsAcceptingCoverage = getDoctorsAcceptingCoverage(coverage.id);
                                const isDoctorSpecific = doctorsAcceptingCoverage.length === 1 &&
                                                        doctorsAcceptingCoverage[0] === doctor.id;

                                if (isCoverageExcluded && !isDoctorSpecific) return false;

                                if (selectedPlan) {
                                  return !isDoctorCoverageExcluded(doctor.id, coverage.id, selectedPlan);
                                }

                                return true;
                              });
                            }
                          }

                          // Now get consultation types from the filtered doctors
                          const allConsultationTypes = filteredDoctors.flatMap(doctor =>
                            doctor.consultationTypes?.filter(ct =>
                              // Only include types that are available online
                              ct.availableOnline !== false &&
                              // If "Sin Cobertura" is selected, only show types that accept private patients
                              (noCoverage ? ct.acceptsPrivatePay !== false : true) &&
                              // If a coverage is selected, check if it's not excluded for this consultation type
                              (!selectedCoverage || !noCoverage ?
                                !ct.excludedCoverages?.some(exclusion => {
                                  const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
                                  if (!coverage) return false;
                                  return exclusion.coverageId === coverage.id &&
                                    (exclusion.planId === null || (selectedPlan && exclusion.planId === selectedPlan));
                                }) :
                                true
                              )
                            ) || []
                          );

                          // Get unique consultation type names
                          const uniqueTypes = Array.from(new Set(
                            allConsultationTypes.map(ct => ct.name)
                          )).sort();

                          return uniqueTypes.map(typeName => (
                            <SelectItem key={typeName} value={typeName}>
                              {typeName}
                            </SelectItem>
                          ));
                        })()}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Active consultation type */}
                {selectedConsultationType && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="h-4 w-4 text-[#0a7c82] mr-2" />
                      <h3 className="text-sm font-medium text-[#1c2533]">Tipo de atención seleccionada</h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                        {selectedConsultationType}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                          onClick={() => setSelectedConsultationType("")}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Professional Filters Section - Secondary Filters */}
          {selectedConsultationType && (
            <div className="w-full mb-8 rounded-xl overflow-hidden shadow-sm border border-[#0070F3]/30">
              <div className="bg-gradient-to-r from-[#0070F3]/10 to-[#0070F3]/5 py-3 px-5 border-b border-[#0070F3]/20">
                <div className="flex items-center">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-[#1cd8e1]/20 border-2 border-[#1cd8e1]/40 mr-3 shrink-0">
                    <span className="text-[#0a7c82] font-bold text-2xl">3</span>
                  </div>
                  <div className="flex items-center">
                    <h3 className="text-lg font-semibold text-[#1c2533]">Filtrá por profesional o especialidad</h3>
                    <span className="ml-2 text-xs font-medium px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full">Opcional</span>
                  </div>
                </div>
              </div>

              <div className="p-5 bg-white">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Search box */}
                  <div>
                    <Label htmlFor="search" className="block mb-2 text-sm font-medium text-gray-700">Buscar por nombre</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        id="search"
                        type="text"
                        placeholder="Ingrese nombre del profesional..."
                        className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        value={searchQuery}
                        onChange={handleSearchChange}
                      />
                    </div>
                  </div>

                  {/* Specialty filter */}
                  {specialties.length > 0 && (
                    <div>
                      <Label className="block mb-2 text-sm font-medium text-gray-700">Filtrar por especialidad</Label>
                      <div className="flex flex-wrap gap-2">
                        {specialties.map((specialty) => (
                          <Button
                            key={specialty}
                            variant="outline"
                            size="sm"
                            className={`rounded-full text-xs font-medium ${
                              selectedSpecialty === specialty
                                ? 'bg-[#1cd8e1]/15 text-[#0a7c82] border-[#1cd8e1]/30 hover:bg-[#1cd8e1]/20'
                                : 'bg-[#F4F7F9] text-[#1c2533] border-gray-100 hover:bg-[#1cd8e1]/10 hover:text-[#0a7c82]'
                            }`}
                            onClick={() => handleSpecialtyClick(specialty)}
                          >
                            {specialty}
                            {selectedSpecialty === specialty && (
                              <X className="ml-1 h-3 w-3" />
                            )}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Active professional filters */}
                {(selectedSpecialty || searchQuery) && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="h-4 w-4 text-[#0a7c82] mr-2" />
                      <h3 className="text-sm font-medium text-[#1c2533]">Filtros activos</h3>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {selectedSpecialty && (
                        <div className="bg-[#1cd8e1]/20 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                          Especialidad: {selectedSpecialty}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                            onClick={() => setSelectedSpecialty(null)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}

                      {searchQuery && (
                        <div className="bg-[#1cd8e1]/15 text-[#0a7c82] px-3 py-1.5 rounded-full text-xs font-medium flex items-center border border-[#1cd8e1]/30">
                          Búsqueda: {searchQuery}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="ml-1 h-5 w-5 p-0 text-[#1c2533] hover:bg-[#1cd8e1]/20"
                            onClick={() => setSearchQuery("")}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}

                      {(selectedSpecialty || searchQuery) && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs border-gray-200 hover:bg-gray-50"
                          onClick={() => {
                            setSelectedSpecialty(null);
                            setSearchQuery("");
                          }}
                        >
                          Limpiar filtros
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Show message when no coverage is selected, when coverage is selected but no plan, or when no consultation type is selected */}
          {(!selectedCoverage && !noCoverage) || (selectedCoverage && !selectedPlan && getAvailablePlans().length > 0) || !selectedConsultationType ? (
            <div className="w-full overflow-hidden border border-blue-200 shadow-md rounded-xl bg-white">
              <div className="p-8 text-center">
                <div className="bg-[#1cd8e1]/10 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6 border-4 border-[#1cd8e1]/20">
                  <ShieldCheck className="h-10 w-10 text-[#0a7c82]" />
                </div>
                <h3 className="text-xl font-semibold text-[#1c2533] mb-3">
                  {!selectedCoverage && !noCoverage ?
                    "Selecciona tu cobertura médica" :
                    selectedCoverage && !selectedPlan && getAvailablePlans().length > 0 ?
                    "Selecciona el plan de tu cobertura" :
                    "Seleccioná la atención que necesitás"
                  }
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {!selectedCoverage ?
                    "Por favor, selecciona una cobertura o marca la opción \"Sin Cobertura\" para continuar." :
                    !selectedPlan && getAvailablePlans().length > 0 ?
                    "Por favor, selecciona un plan para la cobertura elegida para continuar." :
                    !selectedConsultationType ?
                    "Por favor, seleccioná el tipo de atención médica que necesitás para ver los profesionales disponibles." :
                    ""
                  }
                </p>
                <Button
                  onClick={() => {
                    if (!selectedCoverage && !noCoverage) {
                      const filterSection = document.getElementById('coverage');
                      if (filterSection) filterSection.scrollIntoView({ behavior: 'smooth' });
                    } else if (selectedCoverage && !selectedPlan && getAvailablePlans().length > 0) {
                      const planSection = document.getElementById('plan');
                      if (planSection) planSection.scrollIntoView({ behavior: 'smooth' });
                    } else if (!selectedConsultationType) {
                      const consultationSection = document.getElementById('consultation-type');
                      if (consultationSection) consultationSection.scrollIntoView({ behavior: 'smooth' });
                    }
                  }}
                  className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white px-6"
                >
                  {!selectedCoverage ?
                    "Seleccionar cobertura" :
                    !selectedPlan && getAvailablePlans().length > 0 ?
                    "Seleccionar plan" :
                    "Seleccionar tipo de atención"
                  }
                </Button>
              </div>
            </div>
          ) : filteredDoctors.length === 0 ? (
            <div className="w-full overflow-hidden border border-red-200 shadow-md rounded-xl bg-white">
              <div className="p-8 text-center">
                <div className="bg-red-50 rounded-full h-20 w-20 flex items-center justify-center mx-auto mb-6 border-4 border-red-100">
                  <User className="h-10 w-10 text-red-500" />
                </div>
                <h3 className="text-xl font-semibold text-[#1c2533] mb-3">No hay profesionales disponibles</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {searchQuery || selectedSpecialty ?
                    "No se encontraron profesionales con los filtros seleccionados. Intenta con otros criterios de búsqueda." :
                    "Este establecimiento no tiene profesionales configurados para reserva online. Por favor, contacta directamente con el establecimiento."}
                </p>
                {(searchQuery || selectedSpecialty || selectedCoverage || noCoverage || selectedConsultationType) ? (
                  <Button
                    onClick={() => {
                      setSearchQuery("");
                      setSelectedSpecialty(null);
                      setSelectedCoverage("");
                      setSelectedPlan("");
                      setNoCoverage(false);
                      setSelectedConsultationType("");
                    }}
                    className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white px-6"
                  >
                    Limpiar filtros
                  </Button>
                ) : (
                  <Button
                    onClick={() => router.push("/")}
                    className="bg-[#0070F3] hover:bg-[#0070F3]/90 text-white px-6"
                  >
                    Volver al inicio
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <>
              {/* Results header */}
              <div id="doctors-results" className="flex flex-col sm:flex-row justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-[#1c2533] mb-3 sm:mb-0">
                  {filteredDoctors.length} {filteredDoctors.length === 1 ? 'profesional disponible' : 'profesionales disponibles'}
                </h2>
                <p className="text-sm text-gray-600">
                  Seleccioná un profesional para ver sus horarios disponibles
                </p>
              </div>

              {/* Doctor cards grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                {filteredDoctors.map((doctor: Doctor) => (
                  <div
                    key={doctor.id}
                    className="relative overflow-hidden rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 group bg-white"
                  >
                    {/* Doctor card loading overlay */}
                    <DoctorCardLoading doctorId={doctor.id} isLoading={loadingDoctorId === doctor.id} />

                    <div className="flex flex-col h-full">
                      {/* Doctor Header */}
                      <div className="p-5 relative cursor-pointer" onClick={() => handleSelectDoctor(doctor.id)}>
                        <div className="flex items-start gap-4">
                          {/* Doctor Avatar */}
                          <div className="relative">
                            <div className="w-16 h-16 rounded-2xl bg-white shadow-md p-0.5 relative">
                              <div className="w-full h-full rounded-xl overflow-hidden">
                                <div className="absolute inset-0 bg-gradient-to-br from-[#0070F3] to-[#0070F3]/80"></div>
                                <div className="absolute inset-0 bg-[url('/images/pattern-dots.png')] bg-repeat opacity-5"></div>
                                <div className="absolute inset-0 flex items-center justify-center">
                                  <User className="h-6 w-6 text-white" />
                                </div>
                              </div>

                              {/* No status indicator */}
                            </div>
                          </div>

                          {/* Doctor Info */}
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h3 className="text-lg font-bold text-[#1c2533] group-hover:text-[#0070F3] transition-colors">
                                Dr. {doctor.name}
                              </h3>

                              {/* Price/Copay Information */}
                              {selectedConsultationType && (
                                (() => {
                                  // Find the consultation type in the doctor's configuration
                                  const consultationType = doctor.consultationTypes?.find(
                                    ct => ct.name === selectedConsultationType
                                  );

                                  if (!consultationType) return null;

                                  // If "Sin Cobertura" is selected, show the base price
                                  if (noCoverage) {
                                    if (consultationType.acceptsPrivatePay !== false) {
                                      return (
                                        <div className="bg-[#0070F3]/10 text-[#0070F3] px-2 py-1 rounded-lg text-xs font-semibold flex items-center">
                                          <span className="whitespace-nowrap">
                                            ${consultationType.basePrice.toLocaleString('es-AR')}
                                          </span>
                                        </div>
                                      );
                                    }
                                    return null;
                                  }

                                  // If a coverage is selected, check if it has a copay
                                  if (selectedCoverage) {
                                    // Find the coverage in the medicalCoverages array
                                    const coverage = medicalCoverages.find(c => c.name === selectedCoverage);
                                    if (!coverage) return null;

                                    // Find the copay for this coverage and plan
                                    const copay = consultationType.copays?.find(
                                      c => c.coverageId === coverage.id &&
                                          (c.planId === null || (selectedPlan && c.planId === selectedPlan))
                                    );

                                    if (copay) {
                                      return (
                                        <div className="bg-amber-50 text-amber-600 px-2 py-1 rounded-lg text-xs font-semibold flex items-center">
                                          <span className="whitespace-nowrap">
                                            Copago: ${copay.amount.toLocaleString('es-AR')}
                                          </span>
                                        </div>
                                      );
                                    }
                                  }

                                  return null;
                                })()
                              )}
                            </div>

                            {doctor.specialties && doctor.specialties.length > 0 && (
                              <p className="text-sm text-gray-600 mt-0.5 line-clamp-1">
                                {doctor.specialties.join(", ")}
                              </p>
                            )}

                            {/* No location info */}
                          </div>
                        </div>

                        {/* No advanced booking indicator */}
                      </div>

                      {/* Divider */}
                      <div className="h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>

                      {/* Next Appointment Section */}
                      <div className="p-5 flex-1">
                        <h4 className="text-sm font-medium text-[#1c2533] mb-3 flex items-center">
                          <Clock className="h-4 w-4 mr-1.5 text-[#0070F3]" />
                          Próximo turno disponible:
                        </h4>

                        {doctorNextAppointments[doctor.id] ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full text-sm justify-between h-auto py-2.5 border-[#0070F3]/20 hover:bg-[#0070F3]/5 hover:text-[#0070F3] rounded-lg"
                            onClick={() => handleSelectAppointment(
                              doctor.id,
                              doctorNextAppointments[doctor.id]!.date,
                              doctorNextAppointments[doctor.id]!.time
                            )}
                          >
                            <div className="flex items-center">
                              <Calendar className="h-3.5 w-3.5 mr-1.5 text-[#0070F3]" />
                              <span className="font-medium">
                                {format(doctorNextAppointments[doctor.id]!.date, "EEE d MMM", { locale: es })}
                              </span>
                            </div>
                            <span className="font-semibold text-[#0070F3]">{doctorNextAppointments[doctor.id]!.time} hs</span>
                          </Button>
                        ) : (
                          <div className="bg-gray-50 rounded-lg p-3 text-center">
                            <p className="text-sm text-gray-500">No hay turnos disponibles</p>
                          </div>
                        )}
                      </div>

                      {/* Action Footer */}
                      <div className="p-4 bg-[#F4F7F9] border-t border-gray-100">
                        <Button
                          className="w-full bg-[#0070F3] hover:bg-[#0070F3]/90 text-white py-2.5 rounded-lg shadow-sm"
                          onClick={() => handleSelectDoctor(doctor.id)}
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          Ver todos los turnos
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </main>

      <footer className="bg-white border-t border-gray-200 py-8 mt-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-7 w-auto mb-3"
              />
              <p className="text-gray-500 text-sm">
                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
              </p>
            </div>

            <div className="flex flex-col items-center md:items-end">
              <div className="flex items-center mb-3">
                <ShieldCheck className="h-4 w-4 text-[#1cd8e1] mr-1.5" />
                <span className="text-sm text-[#1c2533] font-medium">Reservá turnos médicos 24/7</span>
              </div>
              <div className="flex space-x-4">
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-full">
                  Términos y Condiciones
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-full">
                  Privacidad
                </Button>
                <Button variant="ghost" size="sm" className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-full">
                  Ayuda
                </Button>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Add global styles for animations */}
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(15px); }
          to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
          from { opacity: 0; transform: translateX(20px); }
          to { opacity: 1; transform: translateX(0); }
        }

        @keyframes scaleIn {
          from { opacity: 0; transform: scale(0.95); }
          to { opacity: 1; transform: scale(1); }
        }

        .card-grid-item {
          animation: scaleIn 0.5s ease-out forwards;
          opacity: 0;
        }

        .card-grid-item:nth-child(1) { animation-delay: 0.1s; }
        .card-grid-item:nth-child(2) { animation-delay: 0.15s; }
        .card-grid-item:nth-child(3) { animation-delay: 0.2s; }
        .card-grid-item:nth-child(4) { animation-delay: 0.25s; }
        .card-grid-item:nth-child(5) { animation-delay: 0.3s; }
        .card-grid-item:nth-child(6) { animation-delay: 0.35s; }
        .card-grid-item:nth-child(7) { animation-delay: 0.4s; }
        .card-grid-item:nth-child(8) { animation-delay: 0.45s; }
        .card-grid-item:nth-child(9) { animation-delay: 0.5s; }

        /* Hover effects */
        .card-grid-item:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
        }

        /* Ensure share button appears on hover */
        .card-grid-item:hover .absolute.opacity-0 {
          opacity: 1 !important;
        }
      `}</style>
    </div>
  );
}
