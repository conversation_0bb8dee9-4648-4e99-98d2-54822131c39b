import {NextRequest, NextResponse} from 'next/server';
import {AppointmentModificationRequest} from '@/app/api/requestBodies/AppointmentModificationRequest';

export async function PUT(request: NextRequest) {
    try {
        const body: AppointmentModificationRequest = await request.json();

        // Validate required fields
        const requiredFields: (keyof AppointmentModificationRequest)[] = [
            'appointmentId',
            'employeeUserId'
        ];

        for (const field of requiredFields) {
            if (body[field] === undefined || body[field] === null) {
                return NextResponse.json(
                    {error: `Missing required field: ${field}`},
                    {status: 400}
                );
            }
        }

        // Validate date format if provided (dd-MM-yyyy)
        if (body.newDate) {
            const dateRegex = /^\d{2}-\d{2}-\d{4}$/;
            if (!dateRegex.test(body.newDate)) {
                return NextResponse.json(
                    {error: 'Invalid date format. Expected dd-MM-yyyy'},
                    {status: 400}
                );
            }
        }

        // Validate time format if provided (HH:mm:ss)
        if (body.newStartTime) {
            const timeRegex = /^\d{2}:\d{2}:\d{2}$/;
            if (!timeRegex.test(body.newStartTime)) {
                return NextResponse.json(
                    {error: 'Invalid time format. Expected HH:mm:ss'},
                    {status: 400}
                );
            }
        }

        // Make request to external API
        const response = await fetch(
            `${process.env.BACKEND_URL}/appointment/modify`,
            {
                method: 'PUT',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to modify appointment',
                    details: errorText,
                    status: response.status
                },
                {status: response.status}
            );
        }

        const result = await response.json();
        return NextResponse.json(result, {status: 200});

    } catch (error) {
        console.error('Error modifying appointment:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            {status: 500}
        );
    }
}
