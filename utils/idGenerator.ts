import ShortUniqueId from 'short-unique-id';

// Create instances of ShortUniqueId for different entity types
const doctorIdGenerator = new ShortUniqueId({ length: 6 });
const medicalCenterIdGenerator = new ShortUniqueId({ length: 6 });
const patientIdGenerator = new ShortUniqueId({ length: 6 });
const userIdGenerator = new ShortUniqueId({ length: 6 });

/**
 * Generates a unique ID for a doctor with 'd-' prefix
 * @returns A unique doctor ID (e.g., 'd-a1b2c3')
 */
export const generateDoctorId = (): string => {
  return `d-${doctorIdGenerator.randomUUID()}`;
};

/**
 * Generates a unique ID for a medical center with 'mc-' prefix
 * @returns A unique medical center ID (e.g., 'mc-a1b2c3')
 */
export const generateMedicalCenterId = (): string => {
  return `mc-${medicalCenterIdGenerator.randomUUID()}`;
};

/**
 * Generates a unique ID for a patient with 'p-' prefix
 * @returns A unique patient ID (e.g., 'p-a1b2c3')
 */
export const generatePatientId = (): string => {
  return `p-${patientIdGenerator.randomUUID()}`;
};

/**
 * Generates a generic unique ID with a custom prefix
 * @param prefix The prefix to use for the ID
 * @returns A unique ID with the specified prefix
 */
export const generateCustomId = (prefix: string): string => {
  const customIdGenerator = new ShortUniqueId({ length: 6 });
  return `${prefix}-${customIdGenerator.randomUUID()}`;
};

/**
 * Generates a unique ID for a user with 'u-' prefix
 * @returns A unique user ID (e.g., 'u-a1b2c3')
 */
export const generateUserId = (): string => {
  return `u-${userIdGenerator.randomUUID()}`;
};
