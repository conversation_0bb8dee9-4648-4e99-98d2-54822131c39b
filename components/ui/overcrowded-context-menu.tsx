"use client"

import {useEffect, useRef} from "react"

import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

interface OvercrowdedContextMenuProps {
    x: number
    y: number
    appointments: ProfessionalAppointment[]
    onSelect: (appointment: ProfessionalAppointment) => void
    onClose: () => void
}

export const OvercrowdedContextMenu = ({
                                           x,
                                           y,
                                           appointments,
                                           onSelect,
                                           onClose
                                       }: OvercrowdedContextMenuProps) => {
    const menuRef = useRef<HTMLDivElement>(null)

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
                onClose()
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [onClose])

    return (
        <div
            ref={menuRef}
            className="fixed bg-white border rounded-md shadow-lg z-50 py-1 text-[0.75rem]"
            style={{left: `${x}px`, top: `${y}px`}}
        >
            {appointments.map((appointment) => (
                <div
                    key={appointment.id}
                    className="px-3 py-1.5 hover:bg-gray-100 cursor-pointer"
                    onClick={(e) => {
                        e.stopPropagation(); // Prevent bubbling to handleOutsideClick
                        onSelect(appointment);
                    }}
                >
                    {appointment.patientName} - {appointment.identificationNumber || "DNI no disponible"}
                </div>
            ))}
        </div>
    )
}