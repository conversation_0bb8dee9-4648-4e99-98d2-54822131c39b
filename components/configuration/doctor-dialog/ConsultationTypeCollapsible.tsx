import React from "react"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown } from "lucide-react"

interface ConsultationTypeCollapsibleProps {
  title: string
  children: React.ReactNode
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  summary?: string
  isComplete?: boolean
  hasWarning?: boolean
}

export function ConsultationTypeCollapsible({
  title,
  children,
  isOpen,
  onOpenChange,
  summary,
  isComplete = false,
  hasWarning = false
}: ConsultationTypeCollapsibleProps) {
  return (
    <Collapsible
      open={isOpen}
      onOpenChange={onOpenChange}
      className={`w-full mb-3 sm:mb-4 ${isOpen ? 'border rounded-lg' : ''} ${
        isOpen ? 'border-gray-200' : ''
      }`}
    >
      <CollapsibleTrigger className={`flex items-center justify-between w-full p-2 sm:p-3 transition-colors duration-200 cursor-pointer ${
        !isOpen ? 'border rounded-lg' : ''
      } ${
        !isOpen && hasWarning ? 'border-yellow-400 bg-yellow-50 text-yellow-800 hover:bg-yellow-100' :
        !isOpen && isComplete ? 'border-green-400 bg-green-50 text-green-800 hover:bg-green-100' :
        !isOpen ? 'border-gray-200 hover:bg-gray-50' : ''
      } ${
        isOpen ? 'rounded-b-none bg-green-50 border-green-200 text-green-800' : ''
      }`}>
        <div className="flex flex-col items-start text-left">
          <h3 className={`text-xs sm:text-sm font-medium ${
            !isOpen && hasWarning ? 'text-yellow-900' :
            !isOpen && isComplete ? 'text-green-900' : 
            isOpen ? 'text-green-900' : ''
          }`}>{title}</h3>
          {!isOpen && summary && (
            <span className={`text-xs sm:text-sm break-words max-w-full mt-1 pl-0 ${
              hasWarning ? 'text-yellow-700' :
              isComplete ? 'text-green-700' : 'text-gray-500'
            }`}>{summary}</span>
          )}
        </div>
        <ChevronDown className={`h-4 w-4 transition-transform ml-1 flex-shrink-0 ${
          isOpen ? 'transform rotate-180 text-green-700' : ''
        } ${
          hasWarning && !isOpen ? 'text-yellow-600' :
          isComplete && !isOpen ? 'text-green-600' : ''
        }`} />
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-3 sm:space-y-4 transition-all pt-3 sm:pt-4 px-2 sm:px-3 pb-2 sm:pb-3">
        {children}
      </CollapsibleContent>
    </Collapsible>
  )
}
