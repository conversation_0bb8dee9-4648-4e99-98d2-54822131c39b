"use client"

import {useContext, useState} from "react"
import {<PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Switch} from "@/components/ui/switch"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {PatientContext} from "@/contexts/PatientContext"
import {User, UserRole} from "@/types/users"

import {toast} from "sonner"
import {CheckCircle, CreditCard, User as UserIcon} from "lucide-react"

interface FirstLoginOverlayProps {
    isOpen: boolean
    onClose: () => void
    user: User
}

// Progress indicator component
interface StepIndicatorProps {
    currentStep: number
    totalSteps: number
}

function StepIndicator({currentStep, totalSteps}: StepIndicatorProps) {
    return (
        <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
        <span className="text-sm font-medium text-gray-700">
          Paso {currentStep} de {totalSteps}
        </span>
                <span className="text-xs text-gray-500">
          {currentStep === 1 ? "Confirmar DNI" : "Configurar Cobertura"}
        </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
                    style={{width: `${(currentStep / totalSteps) * 100}%`}}
                />
            </div>
            <div className="flex justify-between mt-3">
                <div className="flex flex-col items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                        currentStep >= 1
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-500'
                    }`}>
                        {currentStep > 1 ? <CheckCircle className="h-3 w-3"/> : '1'}
                    </div>
                    <span className="text-xs text-gray-600 mt-1">DNI</span>
                </div>
                <div className="flex flex-col items-center">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                        currentStep >= 2
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-500'
                    }`}>
                        {currentStep > 2 ? <CheckCircle className="h-3 w-3"/> : '2'}
                    </div>
                    <span className="text-xs text-gray-600 mt-1">Cobertura</span>
                </div>
            </div>
        </div>
    )
}

export default function FirstLoginOverlay({isOpen, onClose, user}: FirstLoginOverlayProps) {
    const [identificationNumber, setIdentificationNumber] = useState("")
    const [coverage, setCoverage] = useState("")
    const [plan, setPlan] = useState("")
    const [noCoverage, setNoCoverage] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [step, setStep] = useState<"dni" | "coverage">("dni")

    const patientContext = useContext(PatientContext)


    const plansByCoverage = DEFAULT_COVERAGES.reduce((acc, cov) => {
        acc[cov.name] = cov.plans
        return acc
    }, {} as Record<string, string[]>)

    const handleDniSubmit = () => {
        if (!identificationNumber || identificationNumber.length < 7) {
            toast.error("Por favor, ingrese un DNI válido")
            return
        }
        setStep("coverage")
    }

    const handleCoverageSubmit = async () => {
        if (!noCoverage && !coverage) {
            toast.error("Por favor, seleccione una cobertura o marque 'No tengo cobertura'")
            return
        }

        setIsSubmitting(true)

        try {
            // Update patient profile if exists
            if (user.idFromRole.get(UserRole.TURNERA_USER) && patientContext) {
                const patient = patientContext.patients.find(p => p.id === user.idFromRole.get(UserRole.TURNERA_USER))
                if (patient) {
                    // Determine final coverage
                    let finalCoverage
                    if (noCoverage) {
                        finalCoverage = "Sin Cobertura"
                    } else if (coverage) {
                        finalCoverage = plan ? `${coverage} ${plan}`.trim() : coverage.trim()
                    } else {
                        finalCoverage = "Sin Cobertura"
                    }

                    const updatedPatient = {
                        ...patient,
                        dni: identificationNumber,
                        coverage: finalCoverage,
                        defaultCoverage: finalCoverage
                    }

                    if (patient.id) {
                        patientContext.updatePatient(patient.id, updatedPatient)
                    }

                    toast.success(
                        <div>
                            <p>Perfil actualizado exitosamente</p>
                            <p className="text-xs mt-1">DNI y cobertura configurados</p>
                        </div>
                    )
                }
            }

            onClose()
        } catch (error) {
            console.error("Error updating profile:", error)
            toast.error("Error al actualizar el perfil")
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={() => {
        }} modal>
            <DialogContent className="sm:max-w-lg shadow-xl border-0">
                <div
                    className="bg-gradient-to-br from-blue-50 to-indigo-50 -m-6 p-6 rounded-t-lg border-b border-blue-100">
                    <DialogHeader className="space-y-0">
                        <div className="flex items-center justify-center mb-4">
                            <div className="bg-white rounded-full p-3 shadow-sm">
                                {step === "dni" ? (
                                    <UserIcon className="h-6 w-6 text-blue-600"/>
                                ) : (
                                    <CreditCard className="h-6 w-6 text-teal-600"/>
                                )}
                            </div>
                        </div>
                        <DialogTitle className="text-center text-xl font-semibold text-gray-900">
                            Completar Perfil de Paciente
                        </DialogTitle>
                        <DialogDescription className="text-center text-gray-600 mt-2">
                            {step === "dni"
                                ? "Para usar tu cuenta como paciente, necesitamos confirmar tu DNI"
                                : "Configura tu cobertura médica para reservar turnos"
                            }
                        </DialogDescription>
                    </DialogHeader>
                </div>

                <div className="pt-4">
                    <StepIndicator currentStep={step === "dni" ? 1 : 2} totalSteps={2}/>
                </div>

                <div className="space-y-6 pb-2">
                    {step === "dni" ? (
                        <div className="space-y-6">
                            <div className="bg-white border border-gray-200 rounded-lg p-5">
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="dni" className="text-sm font-medium text-gray-700">
                                            Documento Nacional de Identidad *
                                        </Label>
                                        <Input
                                            id="dni"
                                            type="text"
                                            placeholder="Ej: 12345678"
                                            value={identificationNumber}
                                            onChange={(e) => setIdentificationNumber(e.target.value)}
                                            maxLength={8}
                                            className="text-base"
                                        />
                                        <p className="text-xs text-gray-500">
                                            Ingrese su DNI sin puntos ni espacios
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-end gap-3">
                                <Button
                                    onClick={handleDniSubmit}
                                    disabled={!identificationNumber || identificationNumber.length < 7}
                                    className="bg-blue-600 hover:bg-blue-700 px-6"
                                >
                                    Continuar
                                </Button>
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-6">
                            <div className="bg-white border border-gray-200 rounded-lg p-5">
                                <div className="space-y-4">
                                    <div className="space-y-3">
                                        <Label className="text-sm font-medium text-gray-700">
                                            Cobertura Médica
                                        </Label>
                                        <Select
                                            value={coverage}
                                            onValueChange={(value) => {
                                                setCoverage(value)
                                                setPlan("")
                                            }}
                                            disabled={noCoverage}
                                        >
                                            <SelectTrigger className="text-base">
                                                <SelectValue placeholder="Seleccionar Cobertura"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {DEFAULT_COVERAGES.filter(cov => cov.name !== "Sin Cobertura").map(cov => (
                                                    <SelectItem key={cov.id} value={cov.name}>{cov.name}</SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>

                                        {coverage && (
                                            <Select
                                                value={plan}
                                                onValueChange={(value) => setPlan(value)}
                                                disabled={noCoverage}
                                            >
                                                <SelectTrigger className="text-base">
                                                    <SelectValue placeholder="Seleccionar Plan"/>
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {plansByCoverage[coverage]?.map((planOption) => (
                                                        <SelectItem key={planOption}
                                                                    value={planOption}>{planOption}</SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}

                                        <div className="flex items-center space-x-3 pt-2">
                                            <Switch
                                                id="no-coverage"
                                                checked={noCoverage}
                                                onCheckedChange={(checked) => {
                                                    setNoCoverage(checked)
                                                    if (checked) {
                                                        setCoverage("")
                                                        setPlan("")
                                                    }
                                                }}
                                            />
                                            <Label htmlFor="no-coverage" className="text-sm text-gray-700">
                                                No tengo cobertura médica
                                            </Label>
                                        </div>

                                        <p className="text-xs text-gray-500 mt-2">
                                            Esta información se usará para reservar turnos como paciente
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div className="flex justify-between gap-3">
                                <Button
                                    variant="outline"
                                    onClick={() => setStep("dni")}
                                    className="px-6"
                                >
                                    Volver
                                </Button>
                                <Button
                                    onClick={handleCoverageSubmit}
                                    disabled={isSubmitting || (!noCoverage && !coverage)}
                                    className="bg-blue-600 hover:bg-blue-700 px-6"
                                >
                                    {isSubmitting ? "Guardando..." : "Completar"}
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    )
}
