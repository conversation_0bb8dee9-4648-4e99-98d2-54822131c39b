"use client"

import Re<PERSON>, {create<PERSON>ontext, ReactNode, useContext, useEffect, useState} from "react"
import {ConsultationType, CoverageException, Doctor, MedicalCoverage} from "@/types/doctor"
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import {DoctorContext} from "@/contexts/DoctorContext"
import {DEFAULT_COVERAGES} from "@/data/coverages"

interface CoverageContextProps {
    // Coverage data
    medicalCoverages: MedicalCoverage[]
    setMedicalCoverages: (coverages: MedicalCoverage[]) => void
    availableCoverages: MedicalCoverage[]

    // Dialog state
    isAddCoverageDialogOpen: boolean
    setIsAddCoverageDialogOpen: (open: boolean) => void
    editingCoverage: MedicalCoverage | null
    setEditingCoverage: (coverage: MedicalCoverage | null) => void

    // Form state
    newCoverageName: string
    setNewCoverageName: (name: string) => void
    newCoveragePlan: string
    setNewCoveragePlan: (plan: string) => void
    selectedCoverages: string[]
    setSelectedCoverages: React.Dispatch<React.SetStateAction<string[]>>
    selectedPlans: { [coverageId: string]: string[] }
    setSelectedPlans: React.Dispatch<React.SetStateAction<{ [coverageId: string]: string[] }>>

    // Doctor coverage exceptions
    doctorCoverageExceptions: { [key: string]: CoverageException[] }
    setDoctorCoverageExceptions: (exceptions: { [key: string]: CoverageException[] }) => void
    toggleDoctorCoverageException: (doctorId: string, coverageId: string, planId?: string | null) => void
    isDoctorCoverageExcluded: (doctorId: string, coverageId: string, planId?: string | null) => boolean

    // Consultation type coverage exclusions
    isConsultationTypeExcluded: (consultationType: ConsultationType, coverageId: string, planId?: string | null) => boolean

    // Medical center coverages
    toggleMedicalCenterCoverage: (coverageId: string) => void
    removeCoverageFromMedicalCenter: (coverageId: string) => void
    isCoverageAcceptedByMedicalCenter: (coverageId: string) => boolean
    getAcceptedCoveragesForCenter: () => string[]

    // Coverage operations
    addCoverage: () => void
    getDoctorsAcceptingCoverage: (coverageId: string) => string[]
}

export const CoverageContext = createContext<CoverageContextProps>({} as CoverageContextProps)

export function CoverageProvider({children}: { children: ReactNode }) {
    const {activeMedicalCenterId} = useContext(MedicalCenterContext)
    const {doctors} = useContext(DoctorContext)

    // Track if we're on the client side
    const [isClient, setIsClient] = useState(false)

    // Initialize with empty array, we'll load all coverages but only show the ones explicitly added
    const [medicalCoverages, setMedicalCoverages] = useState<MedicalCoverage[]>([])
    const [newCoverageName, setNewCoverageName] = useState("")
    const [newCoveragePlan, setNewCoveragePlan] = useState("")
    const [isAddCoverageDialogOpen, setIsAddCoverageDialogOpen] = useState(false)
    const [editingCoverage, setEditingCoverage] = useState<MedicalCoverage | null>(null)
    const [doctorCoverageExceptions, setDoctorCoverageExceptions] = useState<{ [key: string]: CoverageException[] }>({})

    // Load all available coverages from the data file
    const availableCoverages = DEFAULT_COVERAGES

    // Initialize selected coverages and plans for the dialog
    const [selectedCoverages, setSelectedCoverages] = useState<string[]>([])
    const [selectedPlans, setSelectedPlans] = useState<{ [coverageId: string]: string[] }>({})

    // Initialize on client side
    useEffect(() => {
        setIsClient(true)

    }, [])

    // Load coverages when active medical center changes
    useEffect(() => {
        if (!isClient || !activeMedicalCenterId) return


        // TODO FACU :load coverage from api in future PR
        setMedicalCoverages([])
        setDoctorCoverageExceptions({})
    }, [activeMedicalCenterId, isClient])


    // Add coverage function now handles multiple coverages and plans
    const addCoverage = () => {
        if (selectedCoverages.length > 0) {
            // Get the selected coverages with their plans
            const newCoverages = selectedCoverages.map(coverageId => {
                const coverage = availableCoverages.find(c => c.id === coverageId)
                if (!coverage) return null

                // If we have selected plans for this coverage, use them
                // Otherwise use all plans from the default coverage
                const plans = selectedPlans[coverageId] || [...coverage.plans]

                return {
                    ...coverage,
                    plans
                }
            }).filter(Boolean) as MedicalCoverage[]

            // Add the new coverages to the existing ones
            const updatedCoverages = [...medicalCoverages]

            // For each new coverage, check if it already exists
            newCoverages.forEach(newCoverage => {
                const existingIndex = updatedCoverages.findIndex(c => c.id === newCoverage.id)
                if (existingIndex >= 0) {
                    // If it exists, update the plans
                    const existingCoverage = updatedCoverages[existingIndex]
                    const existingPlans = existingCoverage.plans || []
                    const newPlans = newCoverage.plans || []

                    // Combine plans without duplicates
                    const combinedPlans = [...new Set([...existingPlans, ...newPlans])]

                    updatedCoverages[existingIndex] = {
                        ...existingCoverage,
                        plans: combinedPlans
                    }
                } else {
                    // If it doesn't exist, add it
                    updatedCoverages.push(newCoverage)
                }
            })

            setMedicalCoverages(updatedCoverages)

            console.log(`Added ${newCoverages.length} coverages to medical center ${activeMedicalCenterId}`)

            // Reset the form
            setSelectedCoverages([])
            setSelectedPlans({})
            setNewCoverageName("")
            setNewCoveragePlan("")
            setIsAddCoverageDialogOpen(false)
        }
    }

    // Toggle a coverage for the medical center (add/remove from medical center's coverage list)
    const toggleMedicalCenterCoverage = (coverageId: string) => {
        const isCurrentlyAccepted = isCoverageAcceptedByMedicalCenter(coverageId);

        if (isCurrentlyAccepted) {
            // Remove the coverage from the medical center
            setMedicalCoverages(prev => prev.filter(c => c.id !== coverageId));
            console.log(`Removed coverage ${coverageId} from medical center ${activeMedicalCenterId}`);
        } else {
            // Add the coverage to the medical center (find it in available coverages)
            const coverageToAdd = availableCoverages.find(c => c.id === coverageId);
            if (coverageToAdd) {
                setMedicalCoverages(prev => [...prev, coverageToAdd]);
                console.log(`Added coverage ${coverageId} to medical center ${activeMedicalCenterId}`);
            }
        }
    }

    // Remove a coverage from the medical center completely
    const removeCoverageFromMedicalCenter = (coverageId: string) => {
        // Remove it from the medicalCoverages array for this medical center
        setMedicalCoverages(prev => prev.filter(c => c.id !== coverageId));

        console.log(`Completely removed coverage ${coverageId} from medical center ${activeMedicalCenterId}`);
    }

    // Check if a coverage is accepted by the medical center
    const isCoverageAcceptedByMedicalCenter = (coverageId: string) => {
        // A coverage is accepted if it exists in the medical center's coverage list
        return medicalCoverages.some(coverage => coverage.id === coverageId);
    }

    // Get all coverages accepted by the medical center
    const getAcceptedCoveragesForCenter = () => {
        // Return the IDs of all coverages in the medical center's coverage list
        return medicalCoverages.map(coverage => coverage.id);
    }

    // Toggle a doctor's exception for a coverage
    const toggleDoctorCoverageException = (doctorId: string, coverageId: string, planId: string | null = null) => {
        // Special case for Sin Cobertura
        const sinCobertura = medicalCoverages.find(c => c.name === "Sin Cobertura");
        const isSinCobertura = sinCobertura && sinCobertura.id === coverageId;

        // If this is Sin Cobertura, or a center-accepted coverage, allow it to be toggled
        const isValidCoverage = isCoverageAcceptedByMedicalCenter(coverageId) || isSinCobertura;
        if (!isValidCoverage) return;

        const newExceptions = {...doctorCoverageExceptions}
        if (!newExceptions[doctorId]) newExceptions[doctorId] = []

        const coveragePlans = medicalCoverages.find((c) => c.id === coverageId)?.plans || []
        const isCoverageExcluded = isDoctorCoverageExcluded(doctorId, coverageId)

        if (planId === null) {
            const isCurrentlyExcluded = isCoverageExcluded
            const coverageExceptionIndex = newExceptions[doctorId].findIndex(
                (e) => e.coverageId === coverageId && e.planId === null
            )

            if (isCurrentlyExcluded) {
                newExceptions[doctorId] = newExceptions[doctorId].filter((e) => e.coverageId !== coverageId)
            } else {
                if (coverageExceptionIndex === -1) {
                    newExceptions[doctorId].push({coverageId, planId: null, excluded: true})
                }
                coveragePlans.forEach((plan) => {
                    const planExceptionIndex = newExceptions[doctorId].findIndex(
                        (e) => e.coverageId === coverageId && e.planId === plan
                    )
                    if (planExceptionIndex === -1) {
                        newExceptions[doctorId].push({coverageId, planId: plan, excluded: true})
                    }
                })
            }
        } else {
            if (!isCoverageExcluded) {
                const exceptionIndex = newExceptions[doctorId].findIndex(
                    (e) => e.coverageId === coverageId && e.planId === planId
                )
                if (exceptionIndex >= 0) {
                    newExceptions[doctorId].splice(exceptionIndex, 1)
                } else {
                    newExceptions[doctorId].push({coverageId, planId, excluded: true})
                }
            }
        }

        if (newExceptions[doctorId].length === 0) delete newExceptions[doctorId]
        setDoctorCoverageExceptions(newExceptions)
    }

    // Check if a doctor is excluded from a coverage
    const isDoctorCoverageExcluded = (doctorId: string, coverageId: string, planId: string | null = null) => {
        // Special case for Sin Cobertura
        const sinCobertura = medicalCoverages.find(c => c.name === "Sin Cobertura");
        const isSinCobertura = sinCobertura && sinCobertura.id === coverageId;

        // Sin Cobertura is accepted by default for all doctors unless there's an explicit exception
        if (isSinCobertura) {
            const exceptions = doctorCoverageExceptions[doctorId] || [];
            const coverageException = exceptions.find((e) => e.coverageId === coverageId && e.planId === null);
            return coverageException?.excluded || false;
        }

        // If coverage is not accepted by the medical center, it's automatically excluded for all doctors
        if (!isCoverageAcceptedByMedicalCenter(coverageId)) return true;

        // Check doctor coverage exceptions
        const exceptions = doctorCoverageExceptions[doctorId] || []
        if (planId !== null) {
            return exceptions.some((e) => e.coverageId === coverageId && e.planId === planId && e.excluded)
        }
        const coverageException = exceptions.find((e) => e.coverageId === coverageId && e.planId === null)
        const coveragePlans = medicalCoverages.find((c) => c.id === coverageId)?.plans || []
        const allPlansExcluded = coveragePlans.every((plan) =>
            exceptions.some((e) => e.coverageId === coverageId && e.planId === plan && e.excluded)
        )

        // IMPORTANT: We no longer check consultation type exclusions here
        // This is because consultation type exclusions should only affect which consultation types
        // are available for a coverage, not whether the doctor accepts the coverage at all

        return (
            (coverageException?.excluded &&
                !exceptions.some((e) => e.coverageId === coverageId && e.planId !== null && !e.excluded)) ||
            allPlansExcluded
        )
    }

    // Function to check if a specific consultation type excludes a coverage
    const isConsultationTypeExcluded = (consultationType: ConsultationType, coverageId: string, planId: string | null = null) => {
        // For Sin Cobertura, check if the consultation type accepts private pay
        const sinCobertura = medicalCoverages.find(c => c.name === "Sin Cobertura");
        const isSinCobertura = sinCobertura && sinCobertura.id === coverageId;

        if (isSinCobertura) {
            return consultationType.acceptsPrivatePay === false;
        }

        // For other coverages, check if the coverage is in the excludedCoverages list
        return consultationType.excludedCoverages?.some(
            exclusion =>
                exclusion.coverageId === coverageId &&
                (exclusion.planId === null || exclusion.planId === planId)
        ) || false;
    };

    // Function to get all doctors who accept a specific coverage
    const getDoctorsAcceptingCoverage = (coverageId: string) => {
        // If the coverage is not accepted by the medical center, no doctors accept it
        if (!isCoverageAcceptedByMedicalCenter(coverageId)) {
            return [];
        }

        // Return all doctors who don't have an exception for this coverage
        return doctors
            .filter((doctor: Doctor) => !isDoctorCoverageExcluded(doctor.id, coverageId))
            .map((doctor: Doctor) => doctor.id);
    }

    return (
        <CoverageContext.Provider
            value={{
                // Coverage data
                medicalCoverages,
                setMedicalCoverages,
                availableCoverages,

                // Dialog state
                isAddCoverageDialogOpen,
                setIsAddCoverageDialogOpen,
                editingCoverage,
                setEditingCoverage,

                // Form state
                newCoverageName,
                setNewCoverageName,
                newCoveragePlan,
                setNewCoveragePlan,
                selectedCoverages,
                setSelectedCoverages,
                selectedPlans,
                setSelectedPlans,

                // Doctor coverage exceptions
                doctorCoverageExceptions,
                setDoctorCoverageExceptions,
                toggleDoctorCoverageException,
                isDoctorCoverageExcluded,

                // Consultation type coverage exclusions
                isConsultationTypeExcluded,

                // Medical center coverages
                toggleMedicalCenterCoverage,
                removeCoverageFromMedicalCenter,
                isCoverageAcceptedByMedicalCenter,
                getAcceptedCoveragesForCenter,

                // Coverage operations
                addCoverage,
                getDoctorsAcceptingCoverage
            }}
        >
            {children}
        </CoverageContext.Provider>
    )
}
