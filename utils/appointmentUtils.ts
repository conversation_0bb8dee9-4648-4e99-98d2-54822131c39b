// Appointment utility functions for calculating available time slots and dates
import {Doctor} from "@/types/doctor"
import {MedicalCenter} from "@/types/medical-center"
import {Appointment} from "@/types/scheduler"
import {addDays, format, isSameDay, parseISO, startOfDay} from "date-fns"
import {es} from "date-fns/locale"
import {ConsecutiveAppointmentScheduler} from "@/utils/consecutiveAppointments"
import type {BlockedSlot} from "@/types/professional-schedules";
import {convertTimeStringToMinutes} from "@/utils/dateUtils";


export function isSlotBlocked(blockedSlotsByDate: Record<string, BlockedSlot[]>, date: string, time: string, appointmentDuration: number): boolean {
    const blockedSlots = blockedSlotsByDate[date] || [];
    return blockedSlots.some(slot => {
        const slotStartTime = convertTimeStringToMinutes(slot.startTime)
        const slotEndTime = slotStartTime + appointmentDuration * slot.appointmentIntervalAmount
        const timeInMinutes = convertTimeStringToMinutes(time);
        return timeInMinutes >= slotStartTime && timeInMinutes < slotEndTime;
    });
}


/**
 * Helper function to calculate slots needed based on appointment duration
 */
export const getSlotsNeeded = (appointment: Appointment, slotDuration: number): number => {
    return Math.ceil((appointment.duration || slotDuration) / slotDuration);
};

/**
 * Helper function to check if a slot is covered by a multi-slot appointment
 */
export const isSlotCoveredByMultiSlot = (
    timeSlot: string,
    timeSlots: string[],
    existingAppointments: Appointment[],
    slotDuration: number
): boolean => {
    // Get appointments that start before this slot
    const earlierAppointments = existingAppointments.filter(apt => {
        const aptIndex = timeSlots.indexOf(apt.time);
        const slotIndex = timeSlots.indexOf(timeSlot);
        return aptIndex >= 0 && aptIndex < slotIndex;
    });

    // Check if any of these appointments cover this slot
    return earlierAppointments.some(apt => {
        const aptIndex = timeSlots.indexOf(apt.time);
        const slotIndex = timeSlots.indexOf(timeSlot);
        const slotsNeeded = getSlotsNeeded(apt, slotDuration);
        return aptIndex + slotsNeeded > slotIndex;
    });
};

/**
 * Helper function to get time slots from hours
 * Handles both regular and consecutive booking restrictions
 * Takes into account appointment durations
 */
export const getTimeSlotsFromHours = (
    doctor: Doctor,
    _medicalCenter: MedicalCenter, // Using underscore to indicate it's intentionally unused
    selectedDate: Date,
    existingAppointments: Appointment[],
    hours: Array<{ start: string; end: string }>,
    blockedSlotsData: Record<string, Record<string, Record<string, string[]>>>,
    doctorId: string,
    medicalCenterId: string
): string[] => {
    // Get blocked slots for this doctor and date
    const dateStr = format(selectedDate, 'yyyy-MM-dd');
    const blockedTimesForDoctor = blockedSlotsData[medicalCenterId]?.[doctorId]?.[dateStr] || [];
    const now = new Date(); // Current time for min hours in advance check
    const slotDuration = doctor.appointmentSlotDuration || 15;
    const dayId = selectedDate.getDay();

    // Check if consecutive booking restrictions are enabled
    // Only use consecutive restrictions if onlyConsecutiveBookings is true
    const maxConsecutiveSlots = doctor.maxConsecutiveBookingsVisible || Number.MAX_SAFE_INTEGER;
    const useConsecutiveRestriction = doctor.onlyConsecutiveBookings === true;

    // Apply min hours in advance restriction
    const minAdvanceHours = doctor.onlineBookingMinHours || 0; // Default to 0 if not set

    // Filter appointments for this date and doctor, excluding cancelled appointments
    const dateAppointments = existingAppointments
        .filter(apt => isSameDay(parseISO(apt.date), selectedDate) &&
            apt.doctorId === doctorId &&
            apt.medicalCenterId === medicalCenterId &&
            apt.status !== "Cancelado");

    if (!useConsecutiveRestriction) {
        // Use the original algorithm if no consecutive booking restrictions
        const allSlots: string[] = [];

        hours.forEach(hourRange => {
            const startTime = hourRange.start.split(':').map(Number);
            const endTime = hourRange.end.split(':').map(Number);
            const startMinutes = startTime[0] * 60 + startTime[1];
            const endMinutes = endTime[0] * 60 + endTime[1];

            for (let mins = startMinutes; mins < endMinutes; mins += slotDuration) {
                const hours = Math.floor(mins / 60);
                const minutes = mins % 60;
                const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                allSlots.push(timeStr);
            }
        });

        // Get directly booked slots
        const bookedSlots = dateAppointments.map(apt => apt.time);

        // Build a map of slots covered by multi-slot appointments
        const coveredSlots = new Set<string>();
        allSlots.forEach(slot => {
            if (isSlotCoveredByMultiSlot(slot, allSlots, dateAppointments, slotDuration)) {
                coveredSlots.add(slot);
            }
        });

        // Filter out slots that are too soon (min hours in advance), blocked, or covered by multi-slot appointments
        const filteredSlots = allSlots.filter(slot => {
            // Skip min hours check if we're not on today's date
            if (!isSameDay(selectedDate, now)) {
                // Still check if the slot is blocked or covered
                const isBlocked = blockedTimesForDoctor.includes(slot);
                const isCovered = coveredSlots.has(slot);
                return !isBlocked && !isCovered;
            }

            const [hours, minutes] = slot.split(':').map(Number);
            const slotTime = new Date(selectedDate);
            slotTime.setHours(hours, minutes, 0, 0);

            // Calculate hours difference
            const diffMs = slotTime.getTime() - now.getTime();
            const diffHours = diffMs / (1000 * 60 * 60);

            // Check if the slot is blocked or covered
            const isBlocked = blockedTimesForDoctor.includes(slot);
            const isCovered = coveredSlots.has(slot);

            // Return true if the slot is not too soon, not blocked, and not covered
            return diffHours >= minAdvanceHours && !isBlocked && !isCovered;
        });

        // Sort slots from earliest to latest
        return filteredSlots.filter(slot => !bookedSlots.includes(slot)).sort();
    } else {
        // Use the ConsecutiveAppointmentScheduler for restricted consecutive bookings
        const bookedSlots = dateAppointments.filter(apt => apt.status !== "Cancelado").map(apt => apt.time);

        // Process each working hour range
        let availableSlots: string[] = [];

        hours.forEach(hourRange => {
            const [startHour, startMinute] = hourRange.start.split(':').map(Number);
            const [endHour, endMinute] = hourRange.end.split(':').map(Number);

            // Create a list of all possible time slots for this hour range
            const allPossibleSlots: string[] = [];
            const startMinutes = startHour * 60 + startMinute;
            const endMinutes = endHour * 60 + endMinute;

            for (let mins = startMinutes; mins < endMinutes; mins += slotDuration) {
                const h = Math.floor(mins / 60);
                const m = mins % 60;
                allPossibleSlots.push(`${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`);
            }

            // For same-day appointments, filter out slots that are too soon
            let validSlots = [...allPossibleSlots];
            if (isSameDay(selectedDate, now)) {
                const currentHour = now.getHours();
                const currentMinute = now.getMinutes();
                const currentTimeInMinutes = currentHour * 60 + currentMinute;

                validSlots = allPossibleSlots.filter(slot => {
                    const [slotHour, slotMinute] = slot.split(':').map(Number);
                    const slotTimeInMinutes = slotHour * 60 + slotMinute;
                    const diffMinutes = slotTimeInMinutes - currentTimeInMinutes;
                    const diffHours = diffMinutes / 60;

                    return diffHours >= minAdvanceHours;
                });

                // If no valid slots remain after filtering, skip this hour range
                if (validSlots.length === 0) {
                    return; // Skip this hour range
                }
            }

            // Get the earliest valid slot time
            const firstValidSlot = validSlots[0];
            const [effectiveStartHour, effectiveStartMinute] = firstValidSlot ? firstValidSlot.split(':').map(Number) : [startHour, startMinute];

            // Create start and end DateTimes
            const startDateTime = new Date(selectedDate);
            startDateTime.setHours(effectiveStartHour, effectiveStartMinute, 0, 0);

            const endDateTime = new Date(selectedDate);
            endDateTime.setHours(endHour, endMinute, 0, 0);

            // Check if this specific time range has last-to-first enabled
            let useLastToFirst = false;
            if (doctor.lastToFirstRangesByDay && doctor.lastToFirstRangesByDay[dayId.toString()]) {
                const matchingRange = doctor.lastToFirstRangesByDay[dayId.toString()].find(
                    range => range.start === hourRange.start && range.end === hourRange.end && range.enabled
                );
                useLastToFirst = !!matchingRange;
            }

            // Create scheduler for this time range with the adjusted start time
            const scheduler = new ConsecutiveAppointmentScheduler(
                startDateTime,
                endDateTime,
                slotDuration,
                maxConsecutiveSlots,
                useLastToFirst // Pass the last-to-first flag
            );

            // Mark existing appointments as reserved
            scheduler.markSlotsAsReserved(bookedSlots);

            // Mark blocked slots
            scheduler.markSlotsAsBlocked(blockedTimesForDoctor);

            // Mark slots covered by multi-slot appointments as blocked
            allPossibleSlots.forEach(slot => {
                if (isSlotCoveredByMultiSlot(slot, allPossibleSlots, dateAppointments, slotDuration)) {
                    scheduler.blockSlot(slot);
                }
            });

            // Mark slots that are too soon as blocked
            if (isSameDay(selectedDate, now)) {
                allPossibleSlots.forEach(slot => {
                    if (!validSlots.includes(slot)) {
                        scheduler.blockSlot(slot);
                    }
                });
            }

            // Get available slots for this range - the scheduler will handle the last-to-first ordering internally
            const rangeSlots = scheduler.getAvailableTimesAsStrings();
            availableSlots = [...availableSlots, ...rangeSlots];
        });

        // Double-check that all slots are valid (not blocked and not too soon)
        const filteredSlots = availableSlots.filter(slot => {
            // Skip min hours check if we're not on today's date
            if (!isSameDay(selectedDate, now)) {
                // Still check if the slot is blocked (should already be filtered out by the scheduler)
                const isBlocked = blockedTimesForDoctor.includes(slot);
                return !isBlocked;
            }

            // For same-day appointments, double-check the time
            const [hours, minutes] = slot.split(':').map(Number);
            const slotTime = new Date(selectedDate);
            slotTime.setHours(hours, minutes, 0, 0);

            // Calculate hours difference
            const diffMs = slotTime.getTime() - now.getTime();
            const diffHours = diffMs / (1000 * 60 * 60);

            // Check if the slot is blocked (should already be filtered out by the scheduler)
            const isBlocked = blockedTimesForDoctor.includes(slot);

            // Return true if the slot is not too soon and not blocked
            return diffHours >= minAdvanceHours && !isBlocked;
        });

        // Sort slots from earliest to latest for display purposes
        // This ensures a consistent UI regardless of last-to-first settings
        return filteredSlots.sort();
    }
};

/**
 * Get available time slots for a doctor on a specific date
 * Handles both regular working days and extraordinary dates
 * Applies min hours in advance restriction
 * Takes into account consultation type's online booking restrictions
 */
export const getAvailableTimeSlots = (
    doctor: Doctor,
    medicalCenter: MedicalCenter,
    selectedDate: Date,
    existingAppointments: Appointment[],
    blockedSlotsData: Record<string, Record<string, Record<string, string[]>>>,
    doctorId: string,
    medicalCenterId: string,
    selectedConsultationTypeName: string | null = null
): string[] => {
    const dateStr = format(selectedDate, 'yyyy-MM-dd');

    // Find the selected consultation type
    const selectedConsultationType = selectedConsultationTypeName
        ? doctor.consultationTypes.find(ct => ct.name === selectedConsultationTypeName)
        : null;

    // If a consultation type is selected and it's not available online, return empty array
    if (selectedConsultationType && selectedConsultationType.availableOnline === false) {
        return [];
    }

    // Check if the consultation type has a daily limit
    if (selectedConsultationType && selectedConsultationType.dailyLimit !== "unlimited") {
        // Count how many appointments of this type are already scheduled for this date (excluding cancelled)
        const dailyAppointmentsOfType = existingAppointments
            .filter(apt =>
                apt.doctorId === doctorId &&
                apt.medicalCenterId === medicalCenterId &&
                apt.type === selectedConsultationTypeName &&
                apt.date === dateStr && // Only count appointments for this specific date
                apt.status !== "Cancelado"
            ).length;

        // If the daily limit has been reached, return empty array
        if (dailyAppointmentsOfType >= selectedConsultationType.dailyLimit) {
            return [];
        }
    }

    // Get the day ID for checking online booking hours
    const dayOfWeek = format(selectedDate, 'EEEE', {locale: es}).toLowerCase();
    const dayId = ['domingo', 'lunes', 'martes', 'miércoles', 'jueves', 'viernes', 'sábado']
        .indexOf(dayOfWeek);
    const dayIdStr = dayId.toString();

    // First check if this date has an exception
    const dateException = doctor.dateExceptions?.[dateStr];

    // If this is a disabled day, return empty array
    if (dateException && !dateException.enabled) {
        return [];
    }

    // If this is an extraordinary day with agenda, use those hours
    if (dateException && dateException.enabled && dateException.hours && dateException.hours.length > 0) {
        // Check if this is a consultation type that should be available on extraordinary dates
        const isAvailableOnExtraordinaryDates =
            !selectedConsultationType ||
            selectedConsultationType.availableOnExtraordinaryDates !== false;

        // If we have a selected consultation type with online booking restrictions
        // and it's not available on extraordinary dates
        if (selectedConsultationType && selectedConsultationType.availableOnline && !isAvailableOnExtraordinaryDates) {
            const onlineHours = selectedConsultationType.onlineBookingHoursByDay?.[dayIdStr] || [];

            // If there are no enabled online hours for this day, return empty array
            if (!onlineHours.some(range => range.enabled)) {
                return [];
            }

            // Filter the doctor's hours to only include those that overlap with the consultation type's online hours
            const filteredHours: Array<{ start: string; end: string }> = [];

            for (const doctorHour of dateException.hours) {
                for (const onlineHour of onlineHours) {
                    if (!onlineHour.enabled) continue;

                    // Check if the ranges overlap
                    if (doctorHour.end <= onlineHour.start || doctorHour.start >= onlineHour.end) {
                        continue;
                    }

                    // Use the overlapping range
                    const effectiveStart = doctorHour.start > onlineHour.start ? doctorHour.start : onlineHour.start;
                    const effectiveEnd = doctorHour.end < onlineHour.end ? doctorHour.end : onlineHour.end;

                    filteredHours.push({start: effectiveStart, end: effectiveEnd});
                }
            }

            // Use the filtered hours for time slot generation
            return getTimeSlotsFromHours(doctor, medicalCenter, selectedDate, existingAppointments, filteredHours, blockedSlotsData, doctorId, medicalCenterId);
        }

        // If no consultation type is selected, it doesn't have online restrictions,
        // or it's available on extraordinary dates, use the doctor's extraordinary hours directly
        return getTimeSlotsFromHours(doctor, medicalCenter, selectedDate, existingAppointments, dateException.hours, blockedSlotsData, doctorId, medicalCenterId);
    }

    // If no exception or exception without hours, check regular working day
    const workingDay = doctor.workingDays[dayIdStr];
    if (!workingDay || !workingDay.enabled || !workingDay.hours.length) {
        return [];
    }

    // If we have a selected consultation type with online booking restrictions
    if (selectedConsultationType && selectedConsultationType.availableOnline) {
        const onlineHours = selectedConsultationType.onlineBookingHoursByDay?.[dayIdStr] || [];

        // If there are no enabled online hours for this day, return empty array
        if (!onlineHours.some(range => range.enabled)) {
            return [];
        }

        // Filter the doctor's hours to only include those that overlap with the consultation type's online hours
        const filteredHours: Array<{ start: string; end: string }> = [];

        for (const doctorHour of workingDay.hours) {
            for (const onlineHour of onlineHours) {
                if (!onlineHour.enabled) continue;

                // Check if the ranges overlap
                if (doctorHour.end <= onlineHour.start || doctorHour.start >= onlineHour.end) {
                    continue;
                }

                // Use the overlapping range
                const effectiveStart = doctorHour.start > onlineHour.start ? doctorHour.start : onlineHour.start;
                const effectiveEnd = doctorHour.end < onlineHour.end ? doctorHour.end : onlineHour.end;

                filteredHours.push({start: effectiveStart, end: effectiveEnd});
            }
        }

        // Use the filtered hours for time slot generation
        return getTimeSlotsFromHours(doctor, medicalCenter, selectedDate, existingAppointments, filteredHours, blockedSlotsData, doctorId, medicalCenterId);
    }

    // Use the regular working day hours if no consultation type is selected or it doesn't have online restrictions
    return getTimeSlotsFromHours(doctor, medicalCenter, selectedDate, existingAppointments, workingDay.hours, blockedSlotsData, doctorId, medicalCenterId);
};

/**
 * Get available dates for a doctor
 * Handles both regular working days and extraordinary dates
 * Takes into account consultation type's online booking restrictions
 */
export const getAvailableDates = (
    doctor: Doctor,
    medicalCenter: MedicalCenter,
    existingAppointments: Record<string, Appointment[]>,
    blockedSlotsData: Record<string, Record<string, Record<string, string[]>>>,
    selectedConsultationTypeName: string | null = null,
    maxDaysToCheck = 60
): Date[] => {
    const now = new Date();
    const today = startOfDay(now);
    const availableDates: Date[] = [];
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;
    const minAdvanceHours = doctor.onlineBookingMinHours || 0; // Default to 0 if not set

    // Find the selected consultation type
    const selectedConsultationType = selectedConsultationTypeName
        ? doctor.consultationTypes.find(ct => ct.name === selectedConsultationTypeName)
        : null;

    // If a consultation type is selected and it's not available online, return empty array
    if (selectedConsultationType && selectedConsultationType.availableOnline === false) {
        return [];
    }

    // Check if the consultation type has a daily limit
    const hasReachedDailyLimit = (date: Date): boolean => {
        if (!selectedConsultationType || selectedConsultationType.dailyLimit === "unlimited") {
            return false;
        }

        const dateStr = format(date, 'yyyy-MM-dd');
        const dateAppointments = existingAppointments[dateStr] || [];

        // Count how many appointments of this type are already scheduled for this date (excluding cancelled)
        const dailyAppointmentsOfType = dateAppointments
            .filter(apt =>
                apt.doctorId === doctor.id &&
                apt.medicalCenterId === medicalCenter.id &&
                apt.type === selectedConsultationTypeName &&
                apt.status !== "Cancelado"
            ).length;

        // Return true if the daily limit has been reached
        return dailyAppointmentsOfType >= selectedConsultationType.dailyLimit;
    };

    // Apply max days in advance restriction
    const maxAdvanceDays = doctor.onlineBookingAdvanceDays || 30; // Default to 30 days if not set
    const effectiveMaxDays = Math.min(maxDaysToCheck, maxAdvanceDays);

    for (let i = 0; i < effectiveMaxDays; i++) {
        const checkDate = addDays(today, i);
        const dateStr = format(checkDate, 'yyyy-MM-dd');
        const dayOfWeek = checkDate.getDay();
        const isToday = i === 0; // Check if this is today
        const dayId = dayOfWeek.toString();

        // First check if this date has an exception
        const dateException = doctor.dateExceptions?.[dateStr];

        // If this is a disabled day, skip it
        if (dateException && !dateException.enabled) {
            continue;
        }

        // Skip this date if the daily limit for this consultation type has been reached
        if (hasReachedDailyLimit(checkDate)) {
            continue;
        }

        // If this is an extraordinary day with agenda, use those hours
        if (dateException && dateException.enabled && dateException.hours && dateException.hours.length > 0) {
            const hoursToUse = dateException.hours;

            // Process this extraordinary day
            const dateAppointments = existingAppointments[dateStr] || [];
            const bookedTimes = new Set(dateAppointments
                .filter(apt => apt.doctorId === doctor.id && apt.medicalCenterId === medicalCenter.id)
                .map(apt => apt.time));

            let hasAvailableSlot = false;

            // Check if this is a consultation type that should be available on extraordinary dates
            const isAvailableOnExtraordinaryDates =
                !selectedConsultationType ||
                selectedConsultationType.availableOnExtraordinaryDates !== false;

            // If the consultation type is available online and available on extraordinary dates or doesn't have online booking restrictions
            if (!selectedConsultationType ||
                !selectedConsultationType.availableOnline ||
                isAvailableOnExtraordinaryDates) {
                // If no consultation type is selected, it's not available online, or it is available on extraordinary dates,
                // use the doctor's extraordinary day hours directly
                for (const hourRange of hoursToUse) {
                    const [startHour, startMinute] = hourRange.start.split(':').map(Number);
                    const [endHour, endMinute] = hourRange.end.split(':').map(Number);
                    const startMinutes = startHour * 60 + startMinute;
                    const endMinutes = endHour * 60 + endMinute;
                    const slotDuration = doctor.appointmentSlotDuration || 15;

                    for (let mins = startMinutes; mins < endMinutes; mins += slotDuration) {
                        const hours = Math.floor(mins / 60);
                        const minutes = mins % 60;
                        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                        const isBlocked = blockedSlotsData[medicalCenter.id]?.[doctor.id]?.[dateStr]?.includes(timeStr) || false;

                        // For today, check if the slot is in the future and respects min hours in advance
                        if (isToday) {
                            const slotTimeInMinutes = hours * 60 + minutes;
                            const diffMinutes = slotTimeInMinutes - currentTimeInMinutes;
                            const diffHours = diffMinutes / 60;

                            // Skip slots that are in the past or too soon based on minAdvanceHours
                            if (diffHours < minAdvanceHours) {
                                continue;
                            }
                        }

                        if (!bookedTimes.has(timeStr) && !isBlocked) {
                            hasAvailableSlot = true;
                            break;
                        }
                    }
                    if (hasAvailableSlot) break;
                }
            } else if (selectedConsultationType && selectedConsultationType.availableOnline) {
                // For consultation types that require checking online booking hours (availableOnline=true but not availableOnExtraordinaryDates)
                const onlineHours = selectedConsultationType.onlineBookingHoursByDay?.[dayId] || [];

                // If there are no enabled online hours for this day, skip it
                if (!onlineHours.some(range => range.enabled)) {
                    continue;
                }

                // Check if any of the online booking hours have available slots
                for (const onlineRange of onlineHours) {
                    if (!onlineRange.enabled) continue;

                    // Find overlapping hours between doctor's working hours and consultation type's online hours
                    for (const hourRange of hoursToUse) {
                        // Check if the ranges overlap
                        const doctorStart = hourRange.start;
                        const doctorEnd = hourRange.end;
                        const onlineStart = onlineRange.start;
                        const onlineEnd = onlineRange.end;

                        // Skip if no overlap
                        if (doctorEnd <= onlineStart || doctorStart >= onlineEnd) {
                            continue;
                        }

                        // Use the overlapping range
                        const effectiveStart = doctorStart > onlineStart ? doctorStart : onlineStart;
                        const effectiveEnd = doctorEnd < onlineEnd ? doctorEnd : onlineEnd;

                        // Convert to minutes for slot calculation
                        const [startHour, startMinute] = effectiveStart.split(':').map(Number);
                        const [endHour, endMinute] = effectiveEnd.split(':').map(Number);
                        const startMinutes = startHour * 60 + startMinute;
                        const endMinutes = endHour * 60 + endMinute;
                        const slotDuration = doctor.appointmentSlotDuration || 15;

                        // Check each slot in the overlapping range
                        for (let mins = startMinutes; mins < endMinutes; mins += slotDuration) {
                            const hours = Math.floor(mins / 60);
                            const minutes = mins % 60;
                            const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                            const isBlocked = blockedSlotsData[medicalCenter.id]?.[doctor.id]?.[dateStr]?.includes(timeStr) || false;

                            // For today, check if the slot is in the future and respects min hours in advance
                            if (isToday) {
                                const slotTimeInMinutes = hours * 60 + minutes;
                                const diffMinutes = slotTimeInMinutes - currentTimeInMinutes;
                                const diffHours = diffMinutes / 60;

                                // Skip slots that are in the past or too soon based on minAdvanceHours
                                if (diffHours < minAdvanceHours) {
                                    continue;
                                }
                            }

                            if (!bookedTimes.has(timeStr) && !isBlocked) {
                                hasAvailableSlot = true;
                                break;
                            }
                        }
                        if (hasAvailableSlot) break;
                    }
                    if (hasAvailableSlot) break;
                }
            }

            if (hasAvailableSlot) {
                availableDates.push(checkDate);
            }

            // Skip the regular working day check for this date
            continue;
        }

        // If no exception or exception without hours, check regular working day
        const workingDay = doctor.workingDays[dayOfWeek.toString()];
        if (!workingDay || !workingDay.enabled || !workingDay.hours.length) {
            continue;
        }

        const hoursToUse = workingDay.hours;
        const dateAppointments = existingAppointments[dateStr] || [];
        const bookedTimes = new Set(dateAppointments
            .filter(apt => apt.doctorId === doctor.id && apt.medicalCenterId === medicalCenter.id && apt.status !== "Cancelado")
            .map(apt => apt.time));

        let hasAvailableSlot = false;

        // Check if the selected consultation type has online booking hours for this day
        if (selectedConsultationType && selectedConsultationType.availableOnline) {
            const onlineHours = selectedConsultationType.onlineBookingHoursByDay?.[dayId] || [];

            // If there are no enabled online hours for this day, skip it
            if (!onlineHours.some(range => range.enabled)) {
                continue;
            }

            // Check if any of the online booking hours have available slots
            for (const onlineRange of onlineHours) {
                if (!onlineRange.enabled) continue;

                // Find overlapping hours between doctor's working hours and consultation type's online hours
                for (const hourRange of hoursToUse) {
                    // Check if the ranges overlap
                    const doctorStart = hourRange.start;
                    const doctorEnd = hourRange.end;
                    const onlineStart = onlineRange.start;
                    const onlineEnd = onlineRange.end;

                    // Skip if no overlap
                    if (doctorEnd <= onlineStart || doctorStart >= onlineEnd) {
                        continue;
                    }

                    // Use the overlapping range
                    const effectiveStart = doctorStart > onlineStart ? doctorStart : onlineStart;
                    const effectiveEnd = doctorEnd < onlineEnd ? doctorEnd : onlineEnd;

                    // Convert to minutes for slot calculation
                    const [startHour, startMinute] = effectiveStart.split(':').map(Number);
                    const [endHour, endMinute] = effectiveEnd.split(':').map(Number);
                    const startMinutes = startHour * 60 + startMinute;
                    const endMinutes = endHour * 60 + endMinute;
                    const slotDuration = doctor.appointmentSlotDuration || 15;

                    // Check each slot in the overlapping range
                    for (let mins = startMinutes; mins < endMinutes; mins += slotDuration) {
                        const hours = Math.floor(mins / 60);
                        const minutes = mins % 60;
                        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                        const isBlocked = blockedSlotsData[medicalCenter.id]?.[doctor.id]?.[dateStr]?.includes(timeStr) || false;

                        // For today, check if the slot is in the future and respects min hours in advance
                        if (isToday) {
                            const slotTimeInMinutes = hours * 60 + minutes;
                            const diffMinutes = slotTimeInMinutes - currentTimeInMinutes;
                            const diffHours = diffMinutes / 60;

                            // Skip slots that are in the past or too soon based on minAdvanceHours
                            if (diffHours < minAdvanceHours) {
                                continue;
                            }
                        }

                        if (!bookedTimes.has(timeStr) && !isBlocked) {
                            hasAvailableSlot = true;
                            break;
                        }
                    }
                    if (hasAvailableSlot) break;
                }
                if (hasAvailableSlot) break;
            }
        } else {
            // If no consultation type is selected or it doesn't have online restrictions,
            // use the doctor's regular hours
            for (const hourRange of hoursToUse) {
                const [startHour, startMinute] = hourRange.start.split(':').map(Number);
                const [endHour, endMinute] = hourRange.end.split(':').map(Number);
                const startMinutes = startHour * 60 + startMinute;
                const endMinutes = endHour * 60 + endMinute;
                const slotDuration = doctor.appointmentSlotDuration || 15;

                // Check if this specific time range has last-to-first enabled
                let useLastToFirst = false;
                if (doctor.lastToFirstRangesByDay && doctor.lastToFirstRangesByDay[dayOfWeek.toString()]) {
                    const matchingRange = doctor.lastToFirstRangesByDay[dayOfWeek.toString()].find(
                        range => range.start === hourRange.start && range.end === hourRange.end && range.enabled
                    );
                    useLastToFirst = !!matchingRange;
                }

                // If last-to-first is enabled, iterate through slots in reverse order
                const minsArray = [];
                for (let mins = startMinutes; mins < endMinutes; mins += slotDuration) {
                    minsArray.push(mins);
                }

                // If last-to-first is enabled, reverse the order of slots to check
                if (useLastToFirst) {
                    minsArray.reverse();
                }

                for (const mins of minsArray) {
                    const hours = Math.floor(mins / 60);
                    const minutes = mins % 60;
                    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                    const isBlocked = blockedSlotsData[medicalCenter.id]?.[doctor.id]?.[dateStr]?.includes(timeStr) || false;

                    // For today, check if the slot is in the future and respects min hours in advance
                    if (isToday) {
                        const slotTimeInMinutes = hours * 60 + minutes;
                        const diffMinutes = slotTimeInMinutes - currentTimeInMinutes;
                        const diffHours = diffMinutes / 60;

                        // Skip slots that are in the past or too soon based on minAdvanceHours
                        if (diffHours < minAdvanceHours) {
                            continue;
                        }
                    }

                    if (!bookedTimes.has(timeStr) && !isBlocked) {
                        hasAvailableSlot = true;
                        break;
                    }
                }
                if (hasAvailableSlot) break;
            }
        }

        if (hasAvailableSlot) {
            availableDates.push(checkDate);
        }
    }

    return availableDates;
};
