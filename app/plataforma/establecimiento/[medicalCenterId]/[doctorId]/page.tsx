"use client";

import React, {Suspense, useEffect, useMemo, useState} from "react";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Building2, Printer} from "lucide-react";
import {Button} from "@/components/ui/button";
import {generateTimeSlots, getMonthData} from "@/utils/dateUtils";
import {generateDoctorAgendaPDF} from "@/utils/pdfUtils";
import {AppointmentActionBar} from "@/components/schedulecomponents/appointment-action-bar";
import {AppointmentDetailsDialog} from "@/components/schedulecomponents/appointment-details-dialog";
import {WeekView} from "@/components/doctorscheduleviews/week-view";
import {MonthView} from "@/components/doctorscheduleviews/month-view";
import {DayView} from "@/components/doctorscheduleviews/day-view";
import {AgendaOptionsDialog} from "@/components/schedulecomponents/agenda-options-dialog";
import {PatientDetailsDialog} from "@/components/schedulecomponents/patient-details-dialog";
import {SchedulerSidebar} from "@/components/schedulecomponents/scheduler-sidebar";
import {OvercrowdedContextMenu} from "@/components/ui/overcrowded-context-menu";
import type {AppointmentData} from "@/components/schedulecomponents/new-appointment-form";
import {useParams, useRouter, useSearchParams} from "next/navigation";
import {AppointmentState, BlockedSlot, ProfessionalSchedulesResponse} from "@/types/professional-schedules";
import {MedicalCenterUserPill} from "@/components/ui/MedicalCenterUserPill";
import {useAuth} from "@/hooks/useAuth";
import Link from "next/link";
import Image from "next/image";
import {getEmployeeUserId, getEmployeeUserIdOrFail} from "@/utils/userUtils";
import {MedicalCenterRoleForEmployeeUser} from "@/types/MedicalCenter/medicalCenterRoleForEmployeeUser";
import {useMedicalCenterEventsContext} from "@/contexts/MedicalCenterEventsContext";
import {getSchedules} from "@/app/api/utils/professionalSchedules/ProfessionalSchedulesUtils";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {PatientResponse} from "@/types/patient/patientResponse";
import {AppointmentConsultationType} from "@/types/consultationTypes/ConsultationType";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";
import {PatientAppointment} from "@/types/patient/patientAppointment";


// Wrapper component that doesn't use useSearchParams directly
export default function SchedulerPageWrapper() {
    return (
        <Suspense fallback={
            <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white">
                <div className="text-center bg-white p-8 rounded-xl shadow-lg border border-blue-100">
                    <div className="relative">
                        <div
                            className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
                        <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
                            <Building2 className="h-6 w-6 text-blue-600 animate-pulse"/>
                        </div>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">Cargando información</h3>
                    <p className="text-gray-500">Preparando la agenda del profesional...</p>
                </div>
            </div>
        }>
            <SchedulerPage/>
        </Suspense>
    )
}

// Main component that uses useSearchParams
function SchedulerPage() {
    const params = useParams();
    const router = useRouter();
    const searchParams = useSearchParams();
    const {currentUser, logout} = useAuth();
    const medicalCenterId = Number.parseInt(params.medicalCenterId as string);
    const doctorId = Number.parseInt(params.doctorId as string);
    // Use MedicalCenterEventsContext
    const {
        isConnected: sseConnected,
        error: sseError,
        dataStore,
        eventManager,
        initialize,
        isInitialized
    } = useMedicalCenterEventsContext();

    // Retrieve stored doctor information from localStorage or dataStore
    const [doctorInfo, setDoctorInfo] = useState<DoctorsForMedicalCenter>();
    const [medicalCenter, setMedicalCenter] = useState<MedicalCenterRoleForEmployeeUser>();

    // Initialize the MedicalCenterEventsContext
    useEffect(() => {
        if (!isInitialized && currentUser && medicalCenterId) {
            const employeeUserId = getEmployeeUserId(currentUser);
            if (employeeUserId) {
                initialize(medicalCenterId, employeeUserId);
            }
        }
    }, [currentUser, medicalCenterId, isInitialized]);

    useEffect(() => {
        if (dataStore?.doctors) {
            const doctor = dataStore.doctors.find(d => d.id === doctorId);
            if (doctor) {
                setDoctorInfo(doctor);
            }
        }
    }, [doctorId, medicalCenterId, router, dataStore?.doctors]);

    // Fetch schedules on mount and when dependencies change
    useEffect(() => {
        const fetchSchedules = async () => {
            if (!doctorInfo) return;

            setSchedulesLoading(true);
            setSchedulesError(null);
            setProfessionalSchedules(doctorInfo.agenda);
            setSchedulesLoading(false);
        };

        fetchSchedules();
    }, [doctorInfo]);

    useEffect(() => {
        if (currentUser) {
            const userMedicalCenters = currentUser.medicalCenters;
            const center = userMedicalCenters?.find(center => center.id === medicalCenterId);
            if (!center) {
                console.error(`Medical center ${medicalCenterId} not found for user ${currentUser.name}`);
                router.push(`/plataforma/establecimiento`);
            }
            setMedicalCenter(center);
        }
    }, [currentUser, medicalCenterId, router])


    const [filteredStoredPatients, setFilteredStoredPatients] = useState<PatientResponse[]>([]);
    const [storedPatientsLoading, setStoredPatientsLoading] = useState(false);

    // Professional schedules state
    const [professionalSchedules, setProfessionalSchedules] = useState<ProfessionalSchedulesResponse | null>(null);
    const [schedulesLoading, setSchedulesLoading] = useState(false);
    const [schedulesError, setSchedulesError] = useState<string | null>(null);

    const [view, setView] = useState<"day" | "week" | "month">("day");
    const [currentDate, setCurrentDate] = useState<Date>(new Date());
    const [selectedDate, setSelectedDate] = useState<Date>(new Date());
    const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
    const [isNewAppointment, setIsNewAppointment] = useState(false);
    const [selectedAppointment, setSelectedAppointment] = useState<ProfessionalAppointment | null>(null);
    const [showObservations, setShowObservations] = useState(false);
    const [weekStart, setWeekStart] = useState<Date>(() => {
        const start = new Date();
        start.setDate(start.getDate() - start.getDay() + 1);
        return start;
    });
    const [showAgendaOptions, setShowAgendaOptions] = useState(false);
    const [lastSelectedDate, setLastSelectedDate] = useState<Date>(new Date());
    const [selectedPatient, setSelectedPatient] = useState<PatientResponse | null>(null);
    const [showPatientDetails, setShowPatientDetails] = useState(false);
    const [patientSearchTerm, setPatientSearchTerm] = useState("");
    const [isPatientSearchFocused, setIsPatientSearchFocused] = useState(false);
    const [contextMenu, setContextMenu] = useState<{ x: number; y: number } | null>(null);
    const [overcrowdedMenu, setOvercrowdedMenu] = useState<{
        x: number;
        y: number;
        appointments: ProfessionalAppointment[]
    } | null>(
        null
    );
    const [isDoctorDropdownOpen, setIsDoctorDropdownOpen] = useState(false);

    const initialView =
        (searchParams.get("view") as "day" | "week" | "month") || "day";
    const queryDate = searchParams.get("date");

    let initialDate: Date;
    if (queryDate) {
        const [year, month, day] = queryDate.split("-").map(Number);
        initialDate = new Date(year, month - 1, day);
    } else {
        initialDate = new Date();
    }

    // Use current date as initial date since we don't have doctor context
    const initialAdjustedDate = queryDate && !isNaN(initialDate.getTime()) ? initialDate : new Date();

    useEffect(() => {
        setCurrentDate(initialAdjustedDate);
        setSelectedDate(initialAdjustedDate);
        setLastSelectedDate(initialAdjustedDate);
        const startOfWeek = new Date(initialAdjustedDate);
        startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay() + 1);
        setWeekStart(startOfWeek);
    }, [initialAdjustedDate]);


    // Patient search functionality using context data
    useEffect(() => {
        const searchPatients = () => {
            if (!patientSearchTerm.trim()) {
                setFilteredStoredPatients([]);
                return;
            }

            setStoredPatientsLoading(true);

            try {
                if (!dataStore?.patients) {
                    setFilteredStoredPatients([]);
                    return;
                }

                const normalizedQuery = patientSearchTerm.toLowerCase().trim();
                const results = dataStore.patients.filter(patient =>
                    patient.name.toLowerCase().includes(normalizedQuery) ||
                    patient.identificationNumber.includes(normalizedQuery)
                );

                setFilteredStoredPatients(results);
                console.log('Patient search results:', results.length);
            } catch (error) {
                console.error('Error searching stored patients:', error);
                setFilteredStoredPatients([]);
            } finally {
                setStoredPatientsLoading(false);
            }
        };

        const debounceTimer = setTimeout(searchPatients, 300);
        return () => clearTimeout(debounceTimer);
    }, [patientSearchTerm, dataStore?.patients]);


    // Function to manually refetch schedules for a specific month
    const refetchSchedulesForMonth = async (date: Date) => {
        if (!medicalCenterId || !doctorId) return;

        try {
            setSchedulesLoading(true);
            setSchedulesError(null);
            const response = await getSchedules(medicalCenterId, doctorId, getEmployeeUserIdOrFail(currentUser), date);
            setProfessionalSchedules(response);
        } catch (error) {
            console.error('Error refetching professional schedules:', error);
            setSchedulesError('Failed to load professional schedules');
        } finally {
            setSchedulesLoading(false);
        }
    };


    const appointmentsByDate: Record<string, ProfessionalAppointment[]> = useMemo(() => {
        if (!professionalSchedules) return {};

        const appointmentsByDate: Record<string, ProfessionalAppointment[]> = {};

        professionalSchedules.appointments.forEach((apiAppointment) => {
            if (!appointmentsByDate[apiAppointment.date]) {
                appointmentsByDate[apiAppointment.date] = [];
            }
            appointmentsByDate[apiAppointment.date].push(apiAppointment);
        });

        return appointmentsByDate;
    }, [professionalSchedules]);


    const blockedSlotsByDate: Record<string, BlockedSlot[]> = useMemo(() => {
        if (!professionalSchedules) return {};

        const blockedSlotsByDate: Record<string, BlockedSlot[]> = {};

        professionalSchedules.blockedSlots.forEach((apiBlockedSlot) => {
            if (!blockedSlotsByDate[apiBlockedSlot.date]) {
                blockedSlotsByDate[apiBlockedSlot.date] = [];
            }
            blockedSlotsByDate[apiBlockedSlot.date].push(apiBlockedSlot);
        })

        return blockedSlotsByDate;
    }, [professionalSchedules]);


    // Generate time slots based on professional schedules
    const timeSlots = useMemo(() => {
        //  TODO :FIX THIS
        if (!professionalSchedules) return [];

        const dateStr = selectedDate.toISOString().split("T")[0];
        const dayOfWeek = selectedDate.getDay();

        // Map day numbers to day names for API
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        const dayName = dayNames[dayOfWeek];

        // Check for special schedules first (specific dates)
        const specialSchedule = professionalSchedules.specialSchedules.find(
            schedule => schedule.date === dateStr
        );

        if (specialSchedule) {
            return generateTimeSlots(
                specialSchedule.startTime,
                specialSchedule.endTime,
                doctorInfo?.appointmentDuration || 15
            );
        }

        // Check regular appointment schedules
        const regularSchedule = professionalSchedules.appointmentSchedules.find(
            schedule => schedule.dayOfWeek.toLowerCase() === dayName
        );

        if (regularSchedule) {
            return generateTimeSlots(
                regularSchedule.startTime,
                regularSchedule.endTime,
                doctorInfo?.appointmentDuration || 15
            );
        }

        return [];
    }, [professionalSchedules, selectedDate]);


    const {days, startingDay} = useMemo(() => {
        const data = getMonthData(currentDate.getFullYear(), currentDate.getMonth());
        const adjustedStartingDay = (data.startingDay - 1 + 7) % 7;
        return {days: data.days, startingDay: adjustedStartingDay};
    }, [currentDate]);


    const availableSlots = useMemo(() => {
        const bookedSlots = new Set<string>();
        const slotDuration = 15; // Default slot duration

        const year = selectedDate.getFullYear();
        const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
        const day = String(selectedDate.getDate()).padStart(2, "0");
        const dateStr = `${year}-${month}-${day}`;
        const dayAppointments = (appointmentsByDate[dateStr] || []);

        dayAppointments.forEach((apt) => {
            const startIndex = timeSlots.indexOf(apt.startTime);
            if (startIndex === -1) return;
            const duration = apt.appointmentIntervalAmount || slotDuration;
            const slotsNeeded = Math.ceil(duration / slotDuration);
            for (let i = 0; i < slotsNeeded && startIndex + i < timeSlots.length; i++) {
                bookedSlots.add(timeSlots[startIndex + i]);
            }
        });

        return timeSlots.filter((slot) => !bookedSlots.has(slot));
    }, [timeSlots, appointmentsByDate, selectedDate]);

    // Placeholder for working days update - would need API integration
    const handleUpdateWorkingDays = (daysOff: string[], extraordinaryDay?: {
        date: string;
        hours: { start: string; end: string }[]
    }, daysToRemove?: string[]) => {
        console.log('Working days update requested:', {daysOff, extraordinaryDay, daysToRemove});
        // TODO: Implement API call to update professional schedules
    };

    // Navigation state management removed since we're not using context

    const handleToday = () => {
        const today = new Date();
        setCurrentDate(today);
        setSelectedDate(today);
        setLastSelectedDate(today);

        const newWeekStart = new Date(today);
        newWeekStart.setDate(newWeekStart.getDate() - newWeekStart.getDay() + 1);
        setWeekStart(newWeekStart);

        // Set view to "day" when clicking on "Hoy" button
        setView("day");
    };

    // Handle printing the doctor's agenda
    const handlePrintAgenda = () => {
        // Get appointments for the selected date
        const year = selectedDate.getFullYear();
        const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
        const day = String(selectedDate.getDate()).padStart(2, "0");
        const dateStr = `${year}-${month}-${day}`;

        const dateAppointments = (appointmentsByDate[dateStr] || [])
            .filter(apt =>
                apt.state !== AppointmentState.CANCELLED
            );


        const getPatientName = (appointment: ProfessionalAppointment) => appointment.patientName;
        if (!doctorInfo) {
            return;
        }

        generateDoctorAgendaPDF(
            doctorInfo,
            selectedDate,
            dateAppointments,
            getPatientName
        );
    };

    const handlePrevMonth = () => {
        if (view === "week") {
            const newWeekStart = new Date(weekStart);
            newWeekStart.setDate(newWeekStart.getDate() - 7);
            setWeekStart(newWeekStart);
            setCurrentDate(newWeekStart);
            setSelectedDate(newWeekStart);
            setLastSelectedDate(newWeekStart);

            // Refetch schedules if month changed
            if (newWeekStart.getMonth() !== currentDate.getMonth()) {
                refetchSchedulesForMonth(newWeekStart);
            }
        } else {
            const firstDayOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
            setCurrentDate(firstDayOfPrevMonth);
            if (firstDayOfPrevMonth.getMonth() !== selectedDate.getMonth() || firstDayOfPrevMonth.getFullYear() !== selectedDate.getFullYear()) {
                setSelectedDate(firstDayOfPrevMonth);
                setLastSelectedDate(firstDayOfPrevMonth);
            }

            // Refetch schedules for the new month
            refetchSchedulesForMonth(firstDayOfPrevMonth);
        }
    };

    const handleNextMonth = () => {
        if (view === "week") {
            const newWeekStart = new Date(weekStart);
            newWeekStart.setDate(newWeekStart.getDate() + 7);
            setWeekStart(newWeekStart);
            setCurrentDate(newWeekStart);
            setSelectedDate(newWeekStart);
            setLastSelectedDate(newWeekStart);

            // Refetch schedules if month changed
            if (newWeekStart.getMonth() !== currentDate.getMonth()) {
                refetchSchedulesForMonth(newWeekStart);
            }
        } else {
            const firstDayOfNextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
            setCurrentDate(firstDayOfNextMonth);
            if (firstDayOfNextMonth.getMonth() !== selectedDate.getMonth() || firstDayOfNextMonth.getFullYear() !== selectedDate.getFullYear()) {
                setSelectedDate(firstDayOfNextMonth);
                setLastSelectedDate(firstDayOfNextMonth);
            }

            // Refetch schedules for the new month
            refetchSchedulesForMonth(firstDayOfNextMonth);
        }
    };

    const handleWeekChange = (newWeekStart: Date) => {
        if (newWeekStart.getMonth() !== currentDate.getMonth()) {
            setCurrentDate(newWeekStart);
            // Refetch schedules for the new month
            refetchSchedulesForMonth(newWeekStart);
        }
        setWeekStart(newWeekStart);
        setSelectedDate(newWeekStart);
        setLastSelectedDate(newWeekStart);
    };
    // TODO THIS FUNCTION IS WRONG!!!
    const isDayAvailable = (day: Date) => {
        if (!professionalSchedules) return true; // Show all days if no schedule data

        const dateStr = day.toISOString().split("T")[0];
        const dayOfWeek = day.getDay();

        // Map day numbers to day names for API
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        const dayName = dayNames[dayOfWeek];

        // Check for special schedules first (specific dates)
        const specialSchedule = professionalSchedules.specialSchedules.find(
            schedule => schedule.date === dateStr
        );

        if (specialSchedule) {
            return true; // Special schedule exists for this date
        }

        // Check regular appointment schedules
        const regularSchedule = professionalSchedules.appointmentSchedules.find(
            schedule => schedule.dayOfWeek.toLowerCase() === dayName
        );

        return !!regularSchedule; // Day is available if there's a regular schedule
    };

    const handleDateSelect = (date: Date) => {
        if (date.toDateString() !== selectedDate.toDateString()) {
            setSelectedDate(date);
            setCurrentDate(date);
            setLastSelectedDate(date);
            setSelectedAppointment(null);
            setView("day");
        } else if (view === "week") {
            setView("day");
        }
        if (view === "month") {
            setView("day");
        } else if (view === "week") {
            const newWeekStart = new Date(date);
            newWeekStart.setDate(newWeekStart.getDate() - newWeekStart.getDay() + 1);
            setWeekStart(newWeekStart);
        }
    };

    const handleNewAppointment = async (data: AppointmentData) => {
        if (!data.patient) {
            console.error("Patient data is missing");
            return;
        }

        try {
            // Format the date for the API (DD-MM-YYYY)
            const day = String(selectedDate.getDate()).padStart(2, "0");
            const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
            const year = selectedDate.getFullYear();
            const formattedDate = `${day}-${month}-${year}`;

            // Format the time for the API (HH:MM:SS)
            const formattedTime = `${data.time}:00`;

            const employeeUserId = getEmployeeUserId(currentUser);

            // Prepare the request body for the API
            const appointmentRequest = {
                medicalCenterId: medicalCenterId,
                professionalId: doctorId,
                healthInsuranceId: data.selected_coverage,
                patientId: parseInt(data.patient.id),
                consultationTypeId: 1, // TODO: This should come from consultation type mapping
                patientNotes: "",
                consultationTypeDescription: data.type,
                price: 0, // TODO: This should be calculated based on consultation type
                appointmentSlotDuration: data.duration,
                employeeUserId: employeeUserId,
                date: formattedDate,
                startTime: formattedTime
            };

            console.log('Creating appointment with data:', appointmentRequest);

            // Call the API endpoint
            const response = await fetch('/api/medical-center-appointment-creation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(appointmentRequest),
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error('Failed to create appointment:', errorData);
                // TODO: Show error message to user
                return;
            }

            const result = await response.json();
            console.log('Appointment created successfully:', result);

            // Refetch schedules to update the UI with the new appointment
            await refetchSchedulesForMonth(selectedDate);

            // Clear the form state
            setIsNewAppointment(false);
            setSelectedSlot(null);

        } catch (error) {
            console.error('Error creating appointment:', error);
            // TODO: Show error message to user
        }
    };

    const handleSlotClick = (time: string, appointment?: ProfessionalAppointment) => {
        if (appointment) {
            if (selectedAppointment?.id === appointment.id) {
                setSelectedAppointment(null);
            } else {
                setSelectedAppointment(appointment);
                setSelectedSlot(null);
                setIsNewAppointment(false);
            }
        } else {
            setSelectedAppointment(null);
            const dateStr = `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, "0")}-${String(
                selectedDate.getDate()
            ).padStart(2, "0")}`;
            const newSelectedSlot = `${dateStr}:${time}`;
            if (selectedSlot === newSelectedSlot) {
                setSelectedSlot(null);
                setIsNewAppointment(false);
            } else {
                console.log("handleSlotClick: Setting selectedSlot to:", newSelectedSlot);
                setSelectedSlot(newSelectedSlot);
                setIsNewAppointment(true);
            }
        }
    };

    const handleFreeSlotSelect = (date: string, time: string) => {
        const [year, month, day] = date.split("-").map(Number);
        setSelectedDate(new Date(year, month - 1, day));
        const newSelectedSlot = `${date}:${time}`;
        console.log("handleFreeSlotSelect: Setting selectedSlot to:", newSelectedSlot);
        setSelectedSlot(newSelectedSlot);
        setIsNewAppointment(true);
    };

    const handleStatusChange = (newStatus: AppointmentState) => {
        if (selectedAppointment) {
            console.log('Status change requested:', {appointmentId: selectedAppointment.id, newStatus});
            // TODO: Implement API call to update appointment status
            setSelectedAppointment(null);
        }
    };

    const handleModifyAppointment = (date: Date, time: string, consultationTypes: AppointmentConsultationType[], doctorIdParam: number, duration?: number) => {
        if (selectedAppointment) {
            const modifiedDate = new Date(date);
            if (!isNaN(modifiedDate.getTime())) {
                const updatedFields = {
                    date: modifiedDate.toISOString().split("T")[0],
                    time,
                    types: consultationTypes,
                    doctorId: doctorIdParam
                };

                console.log('Appointment modification requested:', {
                    appointmentId: selectedAppointment.id,
                    updatedFields
                });
                // TODO: Implement API call to update appointment

                if (doctorIdParam !== doctorId) {
                    router.push(`/plataforma/establecimiento/${medicalCenterId}/${doctorIdParam}?view=day&date=${updatedFields.date}`);
                } else {
                    setCurrentDate(modifiedDate);
                    setSelectedDate(modifiedDate);
                    setLastSelectedDate(modifiedDate);
                }
            } else {
                console.error("Invalid date received in handleModifyAppointment");
            }
        }
    };

    const handleCancelAppointment = () => {
        if (selectedAppointment) {
            console.log('Appointment cancellation requested:', {appointmentId: selectedAppointment.id});
            // TODO: Implement API call to cancel appointment
            setSelectedAppointment(null);
        }
    };

    const handleContextMenu = (e: React.MouseEvent, appointment: ProfessionalAppointment) => {
        e.preventDefault();
        setSelectedAppointment(appointment);
        setContextMenu({x: e.pageX, y: e.pageY});
        setOvercrowdedMenu(null);
    };

    const handleOvercrowdedSelect = (e: React.MouseEvent, appointments: ProfessionalAppointment[]) => {
        e.preventDefault();
        console.log("handleOvercrowdedSelect triggered", {x: e.pageX, y: e.pageY, appointments});
        setSelectedAppointment(null);
        setOvercrowdedMenu({x: e.pageX, y: e.pageY, appointments});
    };

    const handleOvercrowdedAppointmentSelect = (appointment: ProfessionalAppointment) => {
        console.log("handleOvercrowdedAppointmentSelect triggered", {appointment, overcrowdedMenu});
        if (overcrowdedMenu) {
            setSelectedAppointment(appointment);
            setOvercrowdedMenu(null);
            setContextMenu({x: overcrowdedMenu.x, y: overcrowdedMenu.y});
            console.log("Selected appointment:", appointment);
            console.log("Setting contextMenu to:", {x: overcrowdedMenu.x, y: overcrowdedMenu.y});
        } else {
            console.log("overcrowdedMenu is null, cannot proceed");
        }
    };

    const handlePatientSelect = (patient: PatientResponse) => {
        setSelectedPatient(patient);
        setShowPatientDetails(true);
    };

    const handleAppointmentSelect = (
        (appointment: ProfessionalAppointment | PatientAppointment | null) => {
            if (appointment !== null && appointment instanceof PatientAppointment) {
                const appointmentId = appointment.id || -1
                appointment = professionalSchedules?.appointments?.find(apt => apt.id === appointmentId) || null;
            }
            console.log("SchedulerPage: Setting selectedAppointment to:", appointment);
            setSelectedAppointment(appointment);
            if (!appointment) {
                setIsNewAppointment(false);
                setSelectedSlot(null);
            } else if (appointment && isNewAppointment) {
                setIsNewAppointment(false);
                setSelectedSlot(null);
            }
        });

    // Patient management now handled by PatientContext

    // Patient search now handled by PatientContext

    useEffect(() => {
        setSelectedAppointment(null);
        setContextMenu(null);
        setOvercrowdedMenu(null);
    }, [view]);

    useEffect(() => {
        console.log("SchedulerPage state update - overcrowdedMenu:", overcrowdedMenu);
        console.log("SchedulerPage state update - contextMenu:", contextMenu);
    }, [overcrowdedMenu, contextMenu]);

    // Clear selected appointment if it becomes cancelled
    useEffect(() => {
        if (selectedAppointment) {
            // Find the current appointment in the appointments state
            let currentAppointment: ProfessionalAppointment | null = null;
            for (const date in appointmentsByDate) {
                const appointment = appointmentsByDate[date].find(apt => apt.id === selectedAppointment.id);
                if (appointment) {
                    currentAppointment = appointment;
                    break;
                }
            }

            // If the appointment is now cancelled, clear the selection
            if (currentAppointment && currentAppointment.state === AppointmentState.CANCELLED) {
                console.log("Selected appointment was cancelled, clearing selection");
                setSelectedAppointment(null);
                setContextMenu(null);
            }
        }
    }, [appointmentsByDate, selectedAppointment]);

    // PatientContext now handles loading patients from localStorage

    // Context-dependent logic removed - using API data instead

    if (schedulesLoading && !doctorInfo) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white">
                <div className="text-center bg-white p-8 rounded-xl shadow-lg border border-blue-100">
                    <div className="relative">
                        <div
                            className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-6"></div>
                        <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
                            <Building2 className="h-6 w-6 text-blue-600 animate-pulse"/>
                        </div>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">Cargando información</h3>
                    <p className="text-gray-500">Preparando la agenda del profesional...</p>
                </div>
            </div>
        );
    }

    if (schedulesError) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white">
                <div className="text-center bg-white p-8 rounded-xl shadow-lg border border-red-100">
                    <div className="relative">
                        <div className="rounded-full h-16 w-16 bg-red-50 mx-auto mb-6 flex items-center justify-center">
                            <AlertTriangle className="h-8 w-8 text-red-500"/>
                        </div>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-1">Error al cargar la agenda</h3>
                    <p className="text-gray-500 mb-4">{schedulesError}</p>
                    <Button
                        variant="outline"
                        className="border-blue-200 text-blue-600 hover:bg-blue-50"
                        onClick={() => router.push('/plataforma/establecimiento')}
                    >
                        Volver a establecimientos
                    </Button>
                </div>
            </div>
        );
    }

    return (doctorInfo &&
        <>
            <style jsx global>{`
                html {
                    font-size: 13px;
                }

                @media (min-width: 1500px) {
                    html {
                        font-size: 15px;
                    }
                }

                @media (min-width: 2560px) {
                    html {
                        font-size: 17px;
                    }
                }

                .container {
                    max-width: 100%;
                }

                @media (min-width: 1400px) {
                    .container {
                        max-width: 1400px;
                    }
                }
            `}</style>
            <div className="min-h-screen bg-blue-50 flex flex-col">
                <header className="border-b bg-white shadow-sm">
                    <div
                        className="container mx-auto px-[0.5rem] sm:px-[0.75rem] md:px-[1.5rem] py-[0.75rem] flex items-center justify-between">
                        <Link href={`/plataforma/establecimiento/${medicalCenterId}`} className="flex items-center">
                            <Image
                                src="/images/turnera-logo.svg"
                                alt="Turnera Logo"
                                width={120}
                                height={40}
                                className="h-[1.8rem] w-auto"
                                priority
                            />
                        </Link>
                        if(medicalCenter)
                        <MedicalCenterUserPill
                            medicalCenter={medicalCenter}
                            currentUser={currentUser}
                            medicalCenterId={medicalCenterId}
                            logout={logout}
                        />
                    </div>
                </header>

                <nav className="bg-blue-50">
                    <div className="container mx-auto px-[0.5rem] sm:px-[0.75rem] md:px-[1rem] py-[0.75rem]">
                        <div className="flex items-center justify-between gap-[0.75rem] sm:gap-[1rem] md:gap-[1.25rem]">
                            <Link href={`/plataforma/establecimiento/${medicalCenterId}`} className="group">
                                <Button
                                    variant="outline"
                                    className="flex items-center gap-[0.375rem] transition-all duration-300 bg-white border-blue-300/50 hover:bg-blue-100/70 hover:border-blue-400 px-[0.75rem] py-[0.375rem] rounded-lg shadow-sm hover:shadow-md"
                                >
                                    <ArrowLeft
                                        className="w-[1rem] h-[1rem] text-blue-600 group-hover:text-blue-800 transition-colors"/>
                                    <span
                                        className="text-blue-700 group-hover:text-blue-900 font-medium text-[0.875rem]">Atrás</span>
                                </Button>
                            </Link>

                            <div className="flex-1 flex items-center justify-center">
                                <div
                                    className="bg-white border border-blue-300/50 rounded-lg px-[1rem] py-[0.375rem] shadow-sm relative h-[2.25rem] flex items-center">
                                    <Button
                                        variant="ghost"
                                        className="flex items-center gap-[0.5rem] p-0 hover:bg-transparent h-full w-full justify-start"
                                        onClick={() => setIsDoctorDropdownOpen(!isDoctorDropdownOpen)}
                                    >
                                        <span className="text-blue-800 text-[1rem] font-semibold">Agenda de</span>
                                        <span className="-ml-[0.2rem] text-blue-800 font-semibold text-[1rem]">
                                            {`Dr. ${doctorInfo.fullName}`}
                                        </span>
                                    </Button>
                                </div>
                            </div>

                            <div className="flex items-center gap-[1rem]">
                                <Button
                                    variant="outline"
                                    className="text-[0.875rem] h-[3rem] px-[1rem] py-[0.375rem] text-blue-700 border-blue-300/50 shadow-sm hover:bg-blue-50 hover:text-blue-600 flex items-center gap-2"
                                    onClick={handlePrintAgenda}
                                >
                                    <Printer className="h-4 w-4"/>
                                    Imprimir agenda
                                </Button>
                                <Button
                                    variant="outline"
                                    className="text-[0.875rem] h-[3rem] px-[1rem] py-[0.375rem] text-blue-700 border-blue-300/50 shadow-sm hover:bg-blue-50 hover:text-blue-600"
                                    onClick={handleToday}
                                >
                                    Hoy
                                </Button>
                                <div
                                    className="flex rounded-lg bg-white p-[0.25rem] border border-blue-300/50 shadow-sm backdrop-blur-sm">
                                    <Button
                                        variant="ghost"
                                        className={`px-[1rem] py-[0.375rem] rounded-md transition-all duration-300 text-[0.875rem] ${
                                            view === "day"
                                                ? "bg-white text-blue-700 font-semibold shadow-[0_2px_8px_-1px_rgba(37,99,235,0.15)] ring-1 ring-blue-200 hover:ring-blue-300 hover:scale-[1.02]"
                                                : "text-blue-600 hover:bg-blue-100/50 hover:text-blue-800 hover:shadow-sm"
                                        }`}
                                        onClick={() => setView("day")}
                                    >
                                        Día
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        className={`px-[1rem] py-[0.375rem] rounded-md transition-all duration-300 text-[0.875rem] ${
                                            view === "week"
                                                ? "bg-white text-blue-700 font-semibold shadow-[0_2px_8px_-1px_rgba(37,99,235,0.15)] ring-1 ring-blue-200 hover:ring-blue-300 hover:scale-[1.02]"
                                                : "text-blue-600 hover:bg-blue-100/50 hover:text-blue-800 hover:shadow-sm"
                                        }`}
                                        onClick={() => {
                                            setView("week");
                                            const newWeekStart = new Date(lastSelectedDate);
                                            newWeekStart.setDate(newWeekStart.getDate() - newWeekStart.getDay() + 1);
                                            setWeekStart(newWeekStart);
                                        }}
                                    >
                                        Semana
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        className={`px-[1rem] py-[0.375rem] rounded-md transition-all duration-300 text-[0.875rem] ${
                                            view === "month"
                                                ? "bg-white text-blue-700 font-semibold shadow-[0_2px_8px_-1px_rgba(37,99,235,0.15)] ring-1 ring-blue-200 hover:ring-blue-300 hover:scale-[1.02]"
                                                : "text-blue-600 hover:bg-blue-100/50 hover:text-blue-800 hover:shadow-sm"
                                        }`}
                                        onClick={() => setView("month")}
                                    >
                                        Mes
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>

                <div
                    className="container mx-auto px-[0.5rem] sm:px-[0.75rem] md:px-[1rem] flex gap-[0.75rem] sm:gap-[1rem] md:gap-[1.5rem] flex-1 overflow-visible">
                    <SchedulerSidebar
                        medicalCenterId={medicalCenterId}
                        employeeUserId={getEmployeeUserIdOrFail(currentUser)}
                        patientSearchTerm={patientSearchTerm}
                        setPatientSearchTerm={setPatientSearchTerm}
                        isPatientSearchFocused={isPatientSearchFocused}
                        setIsPatientSearchFocused={setIsPatientSearchFocused}
                        professionalSchedules={professionalSchedules}
                        appointmentDuration={doctorInfo.appointmentDuration}
                        filteredStoredPatients={filteredStoredPatients}
                        patientsLoading={storedPatientsLoading}
                        handlePatientSelect={handlePatientSelect}
                        currentDate={currentDate}
                        handlePrevMonth={handlePrevMonth}
                        handleNextMonth={handleNextMonth}
                        days={days}
                        startingDay={startingDay}
                        isDayAvailable={isDayAvailable}
                        handleDateSelect={handleDateSelect}
                        setIsNewAppointment={setIsNewAppointment}
                        setShowAgendaOptions={setShowAgendaOptions}
                        isNewAppointment={isNewAppointment}
                        selectedSlot={selectedSlot}
                        selectedDate={selectedDate}
                        appointments={appointmentsByDate}
                        doctorId={doctorId}
                        acceptedCoverageNames={[]}
                        availableSlots={availableSlots}
                        // Patient management now handled by PatientContext
                        timeSlots={timeSlots}
                        handleNewAppointment={handleNewAppointment}
                        setSelectedSlot={setSelectedSlot}
                    />

                    {view === "week" ? (
                        <div className="flex-1 rounded-lg border border-black mb-[5rem]">
                            <WeekView
                                appointmentsByDate={appointmentsByDate}
                                blockedSlots={blockedSlotsByDate}
                                currentDate={selectedDate}
                                onAppointmentSelect={handleAppointmentSelect}
                                onDateSelect={handleDateSelect}
                                weekStart={weekStart}
                                setWeekStart={setWeekStart}
                                onWeekChange={handleWeekChange}
                                selectedAppointment={selectedAppointment}
                                appointmentDuration={doctorInfo.appointmentDuration}
                                professionalSchedules={professionalSchedules}
                                onContextMenu={handleContextMenu}
                                onOvercrowdedSelect={handleOvercrowdedSelect}
                                doctorId={doctorId}
                                onFreeSlotSelect={handleFreeSlotSelect}
                                selectedSlot={selectedSlot}
                                setSelectedSlot={setSelectedSlot}
                                medicalCenterId={medicalCenterId}
                            />
                            {overcrowdedMenu && (
                                <OvercrowdedContextMenu
                                    x={overcrowdedMenu.x}
                                    y={overcrowdedMenu.y}
                                    appointments={overcrowdedMenu.appointments}
                                    onSelect={handleOvercrowdedAppointmentSelect}
                                    onClose={() => setOvercrowdedMenu(null)}
                                />
                            )}
                        </div>
                    ) : view === "month" ? (
                        <div className="flex-1 rounded-lg border border-black mb-[5rem]">
                            <MonthView
                                appointments={appointmentsByDate}
                                currentDate={currentDate}
                                onDateClick={handleDateSelect}
                                availableDays={[]}
                                onPrevMonth={handlePrevMonth}
                                onNextMonth={handleNextMonth}
                                setView={setView}
                                appointmentDuration={doctorInfo.appointmentDuration}
                                professionalSchedules={professionalSchedules}
                                doctorId={doctorId}
                                medicalCenterId={medicalCenterId}
                            />
                        </div>
                    ) : (
                        <DayView
                            selectedDate={selectedDate}
                            timeSlots={timeSlots}
                            appointmentsByDate={appointmentsByDate}
                            blockedSlotsByDate={blockedSlotsByDate}
                            selectedAppointment={selectedAppointment}
                            doctorInfo={doctorInfo}
                            employeeUserId={getEmployeeUserIdOrFail(currentUser)}
                            selectedSlot={selectedSlot}
                            onSlotClick={handleSlotClick}
                            professionalId={doctorId}
                            medicalCenterId={medicalCenterId}
                        />
                    )}
                </div>

                <AppointmentDetailsDialog
                    appointment={selectedAppointment}
                    isOpen={showObservations}
                    onClose={() => setShowObservations(false)}
                    doctorInfo={doctorInfo}
                />
                {selectedAppointment && (
                    <>
                        {showObservations && <div className="fixed inset-0 z-40"/>}
                        <AppointmentActionBar
                            onObservationsClick={() => setShowObservations(true)}
                            currentStatus={selectedAppointment.state}
                            onStatusChange={handleStatusChange}
                            onModifyAppointment={handleModifyAppointment}
                            onCancelAppointment={handleCancelAppointment}
                            appointment={selectedAppointment}
                            availableDates={[]}
                            availableTimeSlots={availableSlots}
                            contextMenu={contextMenu}
                            setContextMenu={setContextMenu}
                            onAction={(action: string) => {
                                if (action === "cancel") handleCancelAppointment();
                                else if (action === "modify") {
                                    handleModifyAppointment(
                                        selectedDate,
                                        selectedAppointment?.startTime || "",
                                        selectedAppointment?.consultationTypes || "",
                                        doctorId
                                    );
                                } else if (action === "observations") setShowObservations(true);
                                else if (action === "status") handleStatusChange(selectedAppointment?.state || "PENDING");
                            }}
                            view={view}
                            appointments={appointmentsByDate}
                            doctors={[]}
                            doctorInfo={doctorInfo} employeeUserId={0}/>
                    </>
                )}
                <AgendaOptionsDialog
                    isOpen={showAgendaOptions}
                    onClose={() => setShowAgendaOptions(false)}
                    doctorId={doctorId}
                    medicalCenterId={medicalCenterId}
                    appointments={appointmentsByDate}
                    doctorWorkingDays={{}}
                    dateExceptions={{}}
                    onUpdateWorkingDays={handleUpdateWorkingDays}
                />
                <PatientDetailsDialog
                    patient={selectedPatient}
                    isOpen={showPatientDetails}
                    onClose={() => setShowPatientDetails(false)}
                    onAppointmentSelect={handleAppointmentSelect}
                />
            </div>
        </>
    );
}
