"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import { Mail, AlertCircle, ArrowLeft, User, Building2, Stethoscope } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"
import Image from "next/image"
import { PhoneInput } from "react-international-phone"
import { PhoneNumberUtil } from 'google-libphonenumber'
import { getSpanishCountries } from "@/data/phoneCountries"
import { sendPasswordResetEmail } from "@/services/email"
import { sendPasswordResetSMS } from "@/services/phone"
import { Patient } from "@/types/patient"

export default function RecuperarContrasenaPage() {
  const router = useRouter()
  const phoneUtil = PhoneNumberUtil.getInstance()

  const [selectedRole, setSelectedRole] = useState<"paciente" | "profesional" | "establecimiento">("paciente")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("+54") // Default to Argentina
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [phoneError, setPhoneError] = useState("")
  const [success, setSuccess] = useState(false)

  // Get background color based on selected role
  const getBgColor = () => {
    switch (selectedRole) {
      case "paciente":
        return "bg-blue-50"
      case "profesional":
        return "bg-cyan-50" // Using cyan for professional
      case "establecimiento":
        return "bg-[#0fb5bd]/5" // Using teal for establishment
      default:
        return "bg-blue-50"
    }
  }

  // Get accent color based on selected role
  const getAccentColor = () => {
    switch (selectedRole) {
      case "paciente":
        return "text-[#0070F3] border-[#0070F3] hover:bg-[#0070F3]/5"
      case "profesional":
        return "text-[#0fb5bd] border-[#0fb5bd] hover:bg-[#0fb5bd]/5"
      case "establecimiento":
        return "text-[#0a7c82] border-[#0a7c82] hover:bg-[#0a7c82]/5"
      default:
        return "text-[#0070F3] border-[#0070F3] hover:bg-[#0070F3]/5"
    }
  }

  // Get button color based on selected role
  const getButtonColor = () => {
    switch (selectedRole) {
      case "paciente":
        return "bg-[#0070F3] hover:bg-[#0070F3]/90"
      case "profesional":
        return "bg-[#0fb5bd] hover:bg-[#0fb5bd]/90"
      case "establecimiento":
        return "bg-[#0a7c82] hover:bg-[#0a7c82]/90"
      default:
        return "bg-[#0070F3] hover:bg-[#0070F3]/90"
    }
  }

  const generateVerificationCode = (): string => {
    return Math.floor(100000 + Math.random() * 900000).toString() // 6-digit code
  }

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setSuccess(false)

    if (!email) {
      setError("Por favor, ingrese su correo electrónico")
      return
    }

    setIsLoading(true)

    try {
      // Find user by email
      const user = storage.getUserByEmail(email)

      if (!user) {
        setError("No se encontró ningún usuario con este correo electrónico")
        setIsLoading(false)
        return
      }

      // Generate verification code
      const verificationCode = generateVerificationCode()
      const verificationCodeExpiry = new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes from now

      // Update user with verification code
      const updatedUser = {
        ...user,
        verificationCode,
        verificationCodeExpiry
      }

      // Save updated user
      storage.saveUser(updatedUser)

      // Send verification email
      await sendPasswordResetEmail(email, user.name, verificationCode)

      setSuccess(true)
      toast.success("Se ha enviado un código de verificación a su correo electrónico")

      // Redirect to reset page
      setTimeout(() => {
        router.push(`/plataforma/recuperar-contrasena/reset?email=${encodeURIComponent(email)}`)
      }, 2000)
    } catch (error) {
      console.error("Error sending verification email:", error)
      setError("Error al enviar el código de verificación. Intente nuevamente.")
    } finally {
      setIsLoading(false)
    }
  }

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setPhoneError("")
    setSuccess(false)

    // Validate phone
    if (!phone || phone === "+" || phone.length <= 5) {
      setError("Por favor, ingrese su número de teléfono")
      return
    }

    // Validate phone format
    try {
      const phoneNumber = phoneUtil.parseAndKeepRawInput(phone)
      const isValid = phoneUtil.isValidNumber(phoneNumber)

      if (!isValid) {
        setPhoneError("El número de teléfono no es válido")
        return
      }
    } catch {
      setPhoneError("El número de teléfono no es válido")
      return
    }

    setIsLoading(true)

    try {
      // Find patient by phone
      const patients = JSON.parse(localStorage.getItem("patients") || "[]") as Patient[]
      const patient = patients.find((p) => p.phone === phone)

      if (!patient) {
        setError("No se encontró ningún paciente con este número de teléfono")
        setIsLoading(false)
        return
      }

      if (!patient.userId) {
        setError("Este paciente no tiene una cuenta de usuario asociada")
        setIsLoading(false)
        return
      }

      // Find user by patient's userId
      const users = storage.getUsers()
      const user = users.find(u => u.id === patient.userId)

      if (!user) {
        setError("No se encontró ningún usuario asociado a este paciente")
        setIsLoading(false)
        return
      }

      // Generate verification code
      const verificationCode = generateVerificationCode()
      const verificationCodeExpiry = new Date(Date.now() + 10 * 60 * 1000).toISOString() // 10 minutes from now

      // Update user with verification code
      const updatedUser = {
        ...user,
        verificationCode,
        verificationCodeExpiry
      }

      // Save updated user
      storage.saveUser(updatedUser)

      // Send verification SMS
      await sendPasswordResetSMS(phone, verificationCode)

      setSuccess(true)
      toast.success("Se ha enviado un código de verificación a su teléfono")

      // Redirect to reset page
      setTimeout(() => {
        router.push(`/plataforma/recuperar-contrasena/reset?phone=${encodeURIComponent(phone)}`)
      }, 2000)
    } catch (error) {
      console.error("Error sending verification SMS:", error)
      setError("Error al enviar el código de verificación. Intente nuevamente.")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={`min-h-screen ${getBgColor()} flex flex-col transition-colors duration-300`}>
      <header className="py-3">
        <div className="container mx-auto flex justify-center items-center">
          <Link href="/">
            <Image
              src="/images/turnera-logo.svg"
              alt="Turnera Logo"
              width={120}
              height={36}
              className="h-8 w-auto"
              priority
            />
          </Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-2 sm:py-4 flex items-center justify-center">
        <div className="w-full max-w-md">
          <Card className="bg-white p-8 rounded-2xl shadow-xl border border-gray-100">
            <div className="text-center mb-6">
              <div className="flex items-center justify-center mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="mr-2 p-0 h-8 w-8 absolute left-8"
                  onClick={() => router.push("/plataforma/login")}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-2xl font-bold text-[#1c2533]">
                  Recuperar contraseña
                </h1>
              </div>
              <p className="text-gray-600">
                Seleccione el tipo de cuenta para recuperar su contraseña.
              </p>
            </div>

            {/* Role selector */}
            <div className="grid grid-cols-3 gap-3 mb-6">
              <button
                type="button"
                onClick={() => setSelectedRole("paciente")}
                className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 transition-all duration-300 ${
                  selectedRole === "paciente"
                    ? "border-[#0070F3] bg-[#0070F3]/5"
                    : "border-gray-200 hover:border-[#0070F3] hover:bg-[#0070F3]/5"
                }`}
              >
                <div className={`p-2 rounded-full ${selectedRole === "paciente" ? "bg-[#0070F3]/10" : "bg-gray-100"}`}>
                  <User className={`h-5 w-5 ${selectedRole === "paciente" ? "text-[#0070F3]" : "text-gray-500"}`} />
                </div>
                <span className={`text-sm font-medium mt-1 ${selectedRole === "paciente" ? "text-[#0070F3]" : "text-gray-700"}`}>
                  Paciente
                </span>
              </button>

              <button
                type="button"
                onClick={() => setSelectedRole("establecimiento")}
                className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 transition-all duration-300 ${
                  selectedRole === "establecimiento"
                    ? "border-[#0a7c82] bg-[#0a7c82]/5"
                    : "border-gray-200 hover:border-[#0a7c82] hover:bg-[#0a7c82]/5"
                }`}
              >
                <div className={`p-2 rounded-full ${selectedRole === "establecimiento" ? "bg-[#0a7c82]/10" : "bg-gray-100"}`}>
                  <Building2 className={`h-5 w-5 ${selectedRole === "establecimiento" ? "text-[#0a7c82]" : "text-gray-500"}`} />
                </div>
                <span className={`text-sm font-medium mt-1 ${selectedRole === "establecimiento" ? "text-[#0a7c82]" : "text-gray-700"}`}>
                  Establecimiento
                </span>
              </button>

              <button
                type="button"
                onClick={() => setSelectedRole("profesional")}
                className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 transition-all duration-300 ${
                  selectedRole === "profesional"
                    ? "border-[#0fb5bd] bg-[#0fb5bd]/5"
                    : "border-gray-200 hover:border-[#0fb5bd] hover:bg-[#0fb5bd]/5"
                }`}
              >
                <div className={`p-2 rounded-full ${selectedRole === "profesional" ? "bg-[#0fb5bd]/10" : "bg-gray-100"}`}>
                  <Stethoscope className={`h-5 w-5 ${selectedRole === "profesional" ? "text-[#0fb5bd]" : "text-gray-500"}`} />
                </div>
                <span className={`text-sm font-medium mt-1 ${selectedRole === "profesional" ? "text-[#0fb5bd]" : "text-gray-700"}`}>
                  Profesional
                </span>
              </button>
            </div>

            {/* Role-specific description */}
            <div className="text-center mb-6">
              {selectedRole === "paciente" && (
                <p className="text-sm text-gray-600">
                  Recupere el acceso a su cuenta de paciente.
                </p>
              )}
              {selectedRole === "profesional" && (
                <p className="text-sm text-gray-600">
                  Recupere el acceso a su cuenta profesional.
                </p>
              )}
              {selectedRole === "establecimiento" && (
                <p className="text-sm text-gray-600">
                  Recupere el acceso a su establecimiento médico.
                </p>
              )}
            </div>

            {error && (
              <div className="bg-red-50 p-3 rounded-md flex items-start text-red-600 text-sm mb-4">
                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                <span>{error}</span>
              </div>
            )}

            {selectedRole === "paciente" ? (
              <form onSubmit={handlePhoneSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-gray-700">Teléfono</Label>
                  <div className="custom-phone-input">
                    <PhoneInput
                      defaultCountry="ar"
                      value={phone}
                      onChange={(phoneValue) => {
                        setPhone(phoneValue);
                        setPhoneError("");
                      }}
                      inputStyle={{
                        width: '100%',
                        height: '2.5rem'
                      }}
                      className="w-full custom-phone-input with-dial-code-preview"
                      placeholder="Teléfono"
                      countrySelectorStyleProps={{
                        buttonStyle: {
                          paddingLeft: '10px',
                          paddingRight: '5px'
                        }
                      }}
                      hideDropdown={false}
                      disableDialCodeAndPrefix={true}
                      showDisabledDialCodeAndPrefix={true}
                      disableFormatting={false}
                      preferredCountries={['ar', 'cl', 'uy', 'br', 'py', 'bo', 'pe', 'ec', 'co', 've', 'mx', 'es']}
                      countries={getSpanishCountries()}
                    />
                    {phoneError && (
                      <div className="text-xs text-red-600 mt-1">
                        {phoneError}
                      </div>
                    )}
                  </div>
                </div>

                <Button
                  type="submit"
                  className={`w-full ${getButtonColor()} transition-colors h-11 text-base font-medium text-white`}
                  disabled={isLoading || success}
                >
                  {isLoading ? "Enviando..." : success ? "Código enviado" : "Enviar código de verificación"}
                </Button>
              </form>
            ) : (
              <form onSubmit={handleEmailSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-700">Correo electrónico</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      className="pl-10 border-input"
                      required
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className={`w-full ${getButtonColor()} transition-colors h-11 text-base font-medium text-white`}
                  disabled={isLoading || success}
                >
                  {isLoading ? "Enviando..." : success ? "Código enviado" : "Enviar código de verificación"}
                </Button>
              </form>
            )}

            <div className="mt-6 pt-6 border-t border-gray-100 text-center">
              <Button
                variant="link"
                onClick={() => router.push("/plataforma/login")}
                className={`text-sm font-medium ${getAccentColor()}`}
              >
                Volver al inicio de sesión
              </Button>
            </div>
          </Card>
        </div>
      </main>

      <footer className="bg-white border-t border-gray-200 py-8 mt-auto">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <Image
                src="/images/turnera-logo.svg"
                alt="Turnera Logo"
                width={120}
                height={36}
                className="h-7 w-auto mb-3"
              />
              <p className="text-gray-500 text-sm">
                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
