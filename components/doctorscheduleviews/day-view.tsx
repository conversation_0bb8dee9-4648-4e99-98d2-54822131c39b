"use client";

import React, {useCallback, useEffect, useMemo, useRef, useState} from "react";
import {Info, Lock, Unlock} from "lucide-react";
import {ConsultationInfoTable, type ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable";
import {AppointmentSource, AppointmentState, BlockedSlot} from "@/types/professional-schedules";
import {isSlotBlocked} from "@/utils/appointmentUtils";
import {toggleBlockedSlot, ToggleBlockedSlotRequest} from "@/app/api/utils/appointment/SlotApiUtils";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

// Truncated text with tooltip component
interface TruncatedTextProps {
    text: string;
    className?: string;
    isSelected?: boolean;
}

function TruncatedText({text, className = "", isSelected = false}: TruncatedTextProps) {
    const textRef = useRef<HTMLDivElement>(null);
    const [isTruncated, setIsTruncated] = useState(false);
    const [showTooltip, setShowTooltip] = useState(false);

    useEffect(() => {
        const checkTruncation = () => {
            const element = textRef.current;
            if (element) {
                const isTrunc = element.scrollWidth > element.clientWidth;
                setIsTruncated(isTrunc);
            }
        };

        // Run the check after a small delay to ensure the DOM has updated
        const timeoutId = setTimeout(checkTruncation, 100);

        // Also check on resize
        window.addEventListener('resize', checkTruncation);

        return () => {
            clearTimeout(timeoutId);
            window.removeEventListener('resize', checkTruncation);
        };
    }, [text]);

    return (
        <div
            className="relative flex-1 min-w-0 w-full overflow-hidden"
            onMouseEnter={() => isTruncated && setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
        >
            <div
                ref={textRef}
                className={`truncate w-full ${isSelected ? 'font-medium text-blue-600' : ''} ${className}`}
            >
                {text}
            </div>
            {isTruncated && showTooltip && (
                <div
                    className="fixed transform -translate-x-1/2 bg-gray-800 text-white text-[0.75rem] rounded px-[0.5rem] py-[0.25rem] whitespace-nowrap z-50 shadow-md"
                    style={{
                        left: textRef.current ? textRef.current.getBoundingClientRect().left + textRef.current.getBoundingClientRect().width / 2 : 0,
                        top: textRef.current ? textRef.current.getBoundingClientRect().top - 30 : 0,
                    }}
                >
                    {text}
                </div>
            )}
        </div>
    );
}

interface DayViewProps {
    selectedDate: Date;
    timeSlots: string[];
    appointmentsByDate: Record<string, ProfessionalAppointment[]>
    blockedSlotsByDate: Record<string, BlockedSlot[]>;
    selectedAppointment: ProfessionalAppointment | null;
    selectedSlot: string | null;
    onSlotClick: (time: string, appointment?: ProfessionalAppointment) => void;
    doctorInfo: DoctorsForMedicalCenter
    professionalId: number;
    medicalCenterId: number;
    employeeUserId: number;
}

export function DayView({
                            selectedDate,
                            timeSlots,
                            appointmentsByDate,
                            blockedSlotsByDate,
                            selectedAppointment,
                            selectedSlot,
                            onSlotClick,
                            doctorInfo,
                            professionalId,
                            medicalCenterId,
                            employeeUserId
                        }: DayViewProps) {

    const [currentTime, setCurrentTime] = useState(new Date());
    const appointmentDuration: number = doctorInfo.appointmentDuration;
    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false);
    const [selectedCoverage, setSelectedCoverage] = useState("");

    const [consultationTypesInfo, setConsultationTypesInfo] = useState<ConsultationTypeInfo[]>([]);

    const todayAppointmentsByStartTimeMap: Map<string, ProfessionalAppointment[]> = useMemo(() => {
        const map = new Map<string, ProfessionalAppointment[]>();
        const year = selectedDate.getFullYear();
        const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
        const day = String(selectedDate.getDate()).padStart(2, "0");
        const dateStr = `${year}-${month}-${day}`;

        const sortedAppointments = [...(appointmentsByDate[dateStr] || [])]
            .sort((a, b) => a.startTime.localeCompare(b.startTime));
        sortedAppointments.forEach((apt) => {
            const current = map.get(apt.startTime) || [];
            current.push(apt);
            map.set(apt.startTime, current);
        });
        return map;
    }, [appointmentsByDate, selectedDate]);

    const consultationTypes = useMemo(() => doctorInfo.consultationTypes || [], [doctorInfo.consultationTypes]);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date());
        }, 60000);

        return () => clearInterval(interval);
    }, []);

    // Helper to check if a time slot is in the past (only allow previous appointment slot)
    const isPastTimeSlot = (time: string): boolean => {
        const now = new Date();
        const [hours, minutes] = time.split(':').map(Number);
        const slotDate = new Date(selectedDate);
        slotDate.setHours(hours, minutes, 0, 0);

        // Only show current time indicator for today
        if (selectedDate.toDateString() !== now.toDateString()) {
            // For dates other than today, all past slots are disabled
            return slotDate.getTime() < now.getTime();
        }

        // For today, find the current or next upcoming slot
        const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();
        const slotTimeMinutes = hours * 60 + minutes;

        // Find the index of the current slot in the timeSlots array
        const currentSlotIndex = timeSlots.findIndex(slot => {
            const [slotHour, slotMinute] = slot.split(':').map(Number);
            const slotTime = slotHour * 60 + slotMinute;
            return slotTime >= currentTimeMinutes;
        });

        // If we found a current/upcoming slot, allow only the previous slot to be available
        if (currentSlotIndex > 0) {
            const timeSlotIndex = timeSlots.indexOf(time);
            // Allow only the immediately previous slot (currentSlotIndex - 1) and future slots
            return timeSlotIndex < currentSlotIndex - 1;
        } else if (currentSlotIndex === 0) {
            // If current time is before the first slot, no past slots to allow
            return slotTimeMinutes < currentTimeMinutes;
        } else {
            // If current time is after all slots, allow only the last slot
            const timeSlotIndex = timeSlots.indexOf(time);
            return timeSlotIndex < timeSlots.length - 1;
        }
    };

    // Helper to count sobreturnos before a specific time
    const countSobreturnosBeforeTime = (currentTimeMinutes: number): number => {
        let sobreturnoCount = 0;

        // Iterate through all time slots up to the current time
        for (let i = 0; i < timeSlots.length; i++) {
            const [slotHour, slotMinute] = timeSlots[i].split(':').map(Number);
            const slotTime = slotHour * 60 + slotMinute;

            // Only count sobreturnos for slots before the current time
            if (slotTime < currentTimeMinutes) {
                const appointmentsForTime = todayAppointmentsByStartTimeMap.get(timeSlots[i]) || [];
                // Count sobreturnos (all appointments after the first one)
                const sobreturnosInSlot = Math.max(0, appointmentsForTime.length - 1);
                sobreturnoCount += sobreturnosInSlot;
            }
        }

        return sobreturnoCount;
    };

    // Helper to identify time ranges in the time slots
    const identifyTimeRanges = (): { start: number; end: number; startTime: string; endTime: string }[] => {
        if (timeSlots.length === 0) return [];

        const ranges: { start: number; end: number; startTime: string; endTime: string }[] = [];
        let currentRangeStart = 0;

        for (let i = 1; i < timeSlots.length; i++) {
            const prevSlot = timeSlots[i - 1];
            const currentSlot = timeSlots[i];

            const [prevHour, prevMinute] = prevSlot.split(':').map(Number);
            const [currentHour, currentMinute] = currentSlot.split(':').map(Number);

            const prevTimeMinutes = prevHour * 60 + prevMinute;
            const currentTimeMinutes = currentHour * 60 + currentMinute;

            // Check if there's a gap larger than the appointment duration
            if (currentTimeMinutes - prevTimeMinutes > appointmentDuration) {
                // End the current range
                ranges.push({
                    start: currentRangeStart,
                    end: i - 1,
                    startTime: timeSlots[currentRangeStart],
                    endTime: timeSlots[i - 1]
                });
                // Start a new range
                currentRangeStart = i;
            }
        }

        // Add the last range
        if (currentRangeStart < timeSlots.length) {
            ranges.push({
                start: currentRangeStart,
                end: timeSlots.length - 1,
                startTime: timeSlots[currentRangeStart],
                endTime: timeSlots[timeSlots.length - 1]
            });
        }

        return ranges;
    };

    // Helper to get the current time position
    const getCurrentTimePosition = (): {
        time: string;
        ranges: Array<{
            position: number;
            inRange: boolean;
            rangeStartPosition: number;
            rangeEndPosition: number;
        }>
    } | null => {
        // Only show time indicator for today
        if (selectedDate.toDateString() !== new Date().toDateString()) {
            return null;
        }

        // Get current hours and minutes
        const hours = currentTime.getHours();
        const minutes = currentTime.getMinutes();
        const currentTimeMinutes = hours * 60 + minutes;

        // Check if current time is within the time slots range
        if (timeSlots.length === 0) return null;

        // Format time in 24-hour format without AM/PM
        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

        // Identify time ranges
        const timeRanges = identifyTimeRanges();
        if (timeRanges.length === 0) {
            // If no distinct ranges, fall back to the original calculation
            // Get first and last time slot to check if current time is in range
            const firstSlot = timeSlots[0];
            const lastSlot = timeSlots[timeSlots.length - 1];

            const [firstHour, firstMinute] = firstSlot.split(':').map(Number);
            const [lastHour, lastMinute] = lastSlot.split(':').map(Number);

            const firstTime = firstHour * 60 + firstMinute;
            const lastTime = lastHour * 60 + lastMinute + appointmentDuration; // Add duration for the last slot

            // Check if current time is within the range of time slots
            if (currentTimeMinutes < firstTime || currentTimeMinutes > lastTime) {
                return null;
            }

            // Calculate position percentage based on current time
            // Find the closest time slot before the current time
            let closestSlotIndex = 0;
            let closestSlotTime = 0;

            for (let i = 0; i < timeSlots.length; i++) {
                const [slotHour, slotMinute] = timeSlots[i].split(':').map(Number);
                const slotTime = slotHour * 60 + slotMinute;

                if (slotTime <= currentTimeMinutes && slotTime > closestSlotTime) {
                    closestSlotIndex = i;
                    closestSlotTime = slotTime;
                }
            }

            // Calculate position based on the closest slot and the percentage through to the next slot
            const slotHeight = 3; // Height of each slot in rem (from the CSS)
            const [closestHour, closestMinute] = timeSlots[closestSlotIndex].split(':').map(Number);
            const closestTime = closestHour * 60 + closestMinute;

            // Calculate how far we are between this slot and the next
            const nextSlotTime = closestSlotIndex < timeSlots.length - 1 ?
                (() => {
                    const [nextHour, nextMinute] = timeSlots[closestSlotIndex + 1].split(':').map(Number);
                    return nextHour * 60 + nextMinute;
                })() :
                closestTime + appointmentDuration;

            const timeProgress = (currentTimeMinutes - closestTime) / (nextSlotTime - closestTime);
            const basePosition = (closestSlotIndex * slotHeight) + (timeProgress * slotHeight);

            // Count sobreturnos before the current time and adjust position
            const sobreturnosBeforeCurrentTime = countSobreturnosBeforeTime(currentTimeMinutes);

            // Each sobreturno adds one slot height (3rem)
            const sobreturnoOffset = sobreturnosBeforeCurrentTime * slotHeight;

            // Add the sobreturno offset to the base position
            const adjustedPosition = basePosition + sobreturnoOffset;

            return {
                time: formattedTime,
                ranges: [{
                    position: adjustedPosition,
                    inRange: true,
                    rangeStartPosition: 0,
                    rangeEndPosition: (timeSlots.length - 1) * slotHeight
                }]
            };
        }

        // Calculate positions for each range
        const rangePositions = timeRanges.map(range => {
            const [startHour, startMinute] = timeSlots[range.start].split(':').map(Number);
            const [endHour, endMinute] = timeSlots[range.end].split(':').map(Number);

            const rangeStartTime = startHour * 60 + startMinute;
            const rangeEndTime = endHour * 60 + endMinute + appointmentDuration; // Add duration for the last slot

            const slotHeight = 3; // Height of each slot in rem (from the CSS)

            // Calculate range start and end positions
            const rangeStartPosition = range.start * slotHeight;
            const rangeEndPosition = (range.end + 1) * slotHeight; // +1 to include the full height of the last slot

            // Check if current time is within this range
            const inRange = currentTimeMinutes >= rangeStartTime && currentTimeMinutes <= rangeEndTime;

            if (inRange) {
                // Find the closest time slot before the current time within this range
                let closestSlotIndex = range.start;
                let closestSlotTime = rangeStartTime;

                for (let i = range.start; i <= range.end; i++) {
                    const [slotHour, slotMinute] = timeSlots[i].split(':').map(Number);
                    const slotTime = slotHour * 60 + slotMinute;

                    if (slotTime <= currentTimeMinutes && slotTime > closestSlotTime) {
                        closestSlotIndex = i;
                        closestSlotTime = slotTime;
                    }
                }

                // Calculate position based on the closest slot and the percentage through to the next slot
                const [closestHour, closestMinute] = timeSlots[closestSlotIndex].split(':').map(Number);
                const closestTime = closestHour * 60 + closestMinute;

                // Calculate how far we are between this slot and the next
                const nextSlotTime = closestSlotIndex < range.end ?
                    (() => {
                        const [nextHour, nextMinute] = timeSlots[closestSlotIndex + 1].split(':').map(Number);
                        return nextHour * 60 + nextMinute;
                    })() :
                    closestTime + appointmentDuration;

                const timeProgress = (currentTimeMinutes - closestTime) / (nextSlotTime - closestTime);
                const basePosition = (closestSlotIndex * slotHeight) + (timeProgress * slotHeight);

                // Count sobreturnos before the current time within this range
                const sobreturnosBeforeCurrentTime = countSobreturnosBeforeTime(currentTimeMinutes);

                // Each sobreturno adds one slot height (3rem)
                const sobreturnoOffset = sobreturnosBeforeCurrentTime * slotHeight;

                // Add the sobreturno offset to the base position
                const adjustedPosition = basePosition + sobreturnoOffset;

                return {
                    position: adjustedPosition,
                    inRange,
                    rangeStartPosition,
                    rangeEndPosition
                };
            } else {
                // If the current time is before this range, position at the start of the range
                if (currentTimeMinutes < rangeStartTime) {
                    return {
                        position: rangeStartPosition, // Position exactly at the start of the range
                        inRange,
                        rangeStartPosition,
                        rangeEndPosition
                    };
                }
                // If the current time is after this range, position at the end of the range
                else {
                    return {
                        position: rangeEndPosition,
                        inRange,
                        rangeStartPosition,
                        rangeEndPosition
                    };
                }
            }
        });

        return {
            time: formattedTime,
            ranges: rangePositions
        };
    };

    // Build a map of slots covered by multi-slot appointments, including parent appointment info
    const coveredSlots = new Map<string, ProfessionalAppointment>();
    timeSlots.forEach((time) => {
        // Filter out cancelled appointments
        const appointments = (todayAppointmentsByStartTimeMap.get(time) || []).filter(apt => apt.state !== AppointmentState.CANCELLED);
        appointments.forEach((appointment) => {
            if (appointment.state === AppointmentState.PENDING) {
                const slotsNeeded = appointment.appointmentIntervalAmount;
                const startIndex = timeSlots.indexOf(time);
                for (let i = 0; i < slotsNeeded && startIndex + i < timeSlots.length; i++) {
                    const slotTime = timeSlots[startIndex + i];
                    // Check if there are any non-cancelled appointments at this slot
                    if (!todayAppointmentsByStartTimeMap.get(slotTime)?.some((apt) => apt.startTime === slotTime && apt.state !== AppointmentState.CANCELLED)) {
                        coveredSlots.set(slotTime, appointment); // Store parent appointment for reserved slots
                    }
                }
            }
        });
    });

    const hasSubsequentAppointments = (appointment: ProfessionalAppointment): boolean => {
        const startIndex = timeSlots.indexOf(appointment.startTime);
        for (let i = 1; i < appointment.appointmentIntervalAmount && startIndex + i < timeSlots.length; i++) {
            const slotTime = timeSlots[startIndex + i];
            const slotAppointments = todayAppointmentsByStartTimeMap.get(slotTime) || [];
            if (slotAppointments.some(apt => apt.startTime === slotTime && apt.state !== AppointmentState.CANCELLED)) {
                return true;
            }
        }
        return false;
    };

    // Function to show consultation type instructions and requirements
    const showConsultationInfo = useCallback((appointment: ProfessionalAppointment) => {
        if (appointment.hasConsultationTypeInfo(consultationTypes)) {
            const typesInfo = appointment.getConsultationTypeInfo(consultationTypes);
            setSelectedCoverage(appointment.healthInsuranceInformation);
            // Store all types info for the table display
            setConsultationTypesInfo(typesInfo);
            setShowInstructionsDialog(true);
        }
    }, [consultationTypes]);

    return (
        <div
            className="flex-1 overflow-auto pb-[5rem] mb-[2rem] w-full"
            onClick={(e) => {
                // Check if the click was on a padlock icon or its container
                const target = e.target as HTMLElement;
                const isPadlockClick = target.closest('.padlock-icon-container');

                if (isPadlockClick) {
                    // Don't do anything for padlock clicks
                    e.stopPropagation();
                    return;
                }

                if (target.closest("button")) {
                    e.stopPropagation();
                }
            }}
        >
            <div className="bg-white rounded-lg border border-black shadow w-full">
                <div
                    className="p-[0.75rem] sm:p-[1rem] md:p-[1.25rem] border-b bg-white sticky top-0 z-10 rounded-t-lg">
                    <h2 className="text-[1.125rem] font-semibold text-center">
                        {selectedDate.toLocaleDateString("es-ES", {
                            weekday: "long",
                            day: "numeric",
                            month: "long",
                            year: "numeric",
                        })}
                    </h2>
                </div>

                <div
                    className="grid grid-cols-7 gap-[0.5rem] sm:gap-[0.75rem] md:gap-[1rem] p-[0.5rem] sm:p-[0.75rem] md:p-[1rem] border-b text-[0.75rem] sm:text-[0.8rem] md:text-[0.875rem] font-medium sticky top-0 bg-white">
                    <div className="text-center">HORARIO</div>
                    <div className="text-center">PACIENTE</div>
                    <div className="text-center">ESTADO</div>
                    <div className="text-center">FUENTE</div>
                    <div className="text-center">ATENCIÓN</div>
                    <div className="text-center">COBERTURA</div>
                    <div className="text-center">CONTACTO</div>
                </div>

                {timeSlots.length === 0 ? (
                    <div className="text-center text-gray-500 py-[2rem] text-[1rem]">No hay agenda hoy.</div>
                ) : (
                    <div className="divide-y relative">
                        {/* Current time indicator */}
                        {(() => {
                            const currentTimeInfo = getCurrentTimePosition();
                            if (!currentTimeInfo) return null;

                            return (
                                <>
                                    {/* First render all the lines */}
                                    {currentTimeInfo.ranges.map((range, index) => (
                                        <div
                                            key={`time-indicator-line-${index}`}
                                            className="absolute left-0 pointer-events-none"
                                            style={{
                                                top: `calc(${range.position}rem - 1px)`, // Subtract 1px to position on top of the dividing line
                                                width: '100%',
                                                zIndex: 10, // Higher z-index to ensure it's above the dividing line
                                            }}
                                        >
                                            <div
                                                className={`absolute top-0 left-0 right-0 h-[1.5px] ${
                                                    range.inRange ? 'bg-blue-500/70' : 'bg-gray-400/50'
                                                }`}
                                                style={{
                                                    boxShadow: range.inRange
                                                        ? '0 0 3px rgba(59, 130, 246, 0.5)'
                                                        : '0 0 2px rgba(156, 163, 175, 0.4)'
                                                }}
                                            />
                                        </div>
                                    ))}

                                    {/* Then render the time badges separately with higher z-index */}
                                    {(() => {
                                        // Check if the time is in any range
                                        const isTimeInAnyRange = currentTimeInfo.ranges.some(range => range.inRange);

                                        return currentTimeInfo.ranges.map((range, index) => {
                                            // Show blue badge when time is in this range
                                            if (range.inRange) {
                                                return (
                                                    <div
                                                        key={`time-indicator-badge-${index}`}
                                                        className="absolute left-0 pointer-events-none"
                                                        style={{
                                                            top: `calc(${range.position}rem - 1px)`,
                                                            zIndex: 50,
                                                        }}
                                                    >
                                                        <div
                                                            className="absolute top-0 left-0 transform -translate-y-1/2 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded shadow-sm font-medium"
                                                        >
                                                            {currentTimeInfo.time}
                                                        </div>
                                                    </div>
                                                );
                                            }
                                            // Show gray badge at the border between ranges, but only when time is not in any range
                                            else if (!isTimeInAnyRange && index === 0) {
                                                return (
                                                    <div
                                                        key={`time-indicator-badge-${index}`}
                                                        className="absolute left-0 pointer-events-none"
                                                        style={{
                                                            top: `calc(${range.position}rem - 1px)`,
                                                            zIndex: 50,
                                                        }}
                                                    >
                                                        <div
                                                            className="absolute top-0 left-0 transform -translate-y-1/2 bg-gray-400 text-white text-xs px-1.5 py-0.5 rounded shadow-sm font-medium"
                                                        >
                                                            {currentTimeInfo.time}
                                                        </div>
                                                    </div>
                                                );
                                            }

                                            return null;
                                        });
                                    })()}
                                </>
                            );
                        })()
                        }
                        {timeSlots.flatMap((time) => {
                            // Filter out cancelled appointments
                            const appointmentsForTime = (todayAppointmentsByStartTimeMap.get(time) || []).filter(apt => apt.state !== AppointmentState.CANCELLED);
                            const elements: React.ReactNode[] = [];
                            const isCoveredSlot = coveredSlots.has(time);
                            const startAppointments = appointmentsForTime.filter(
                                (apt) => timeSlots.indexOf(apt.startTime) === timeSlots.indexOf(time)
                            );

                            // If no appointments booked at this slot but it's covered by a multi-slot booking
                            if (appointmentsForTime.length === 0 && !isCoveredSlot) {
                                // Keep the existing available slot rendering
                                const dateStr = `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, "0")}-${String(
                                    selectedDate.getDate()
                                ).padStart(2, "0")}`;
                                const slotKey = `${dateStr}:${time}`;
                                const isSelectedSlot = selectedSlot === slotKey;
                                const isPast = isPastTimeSlot(time);
                                // Check if this slot is blocked
                                const isBlocked = isSlotBlocked(blockedSlotsByDate, dateStr, time, appointmentDuration);

                                elements.push(
                                    <button
                                        key={`${time}-available`}
                                        onClick={(e) => {
                                            // Check if the click was on the padlock icon or its container
                                            const target = e.target as HTMLElement;
                                            const isPadlockClick = target.closest('.padlock-icon-container');
                                            if (isPadlockClick) {
                                                // Don't do anything if the click was on the padlock
                                                return;
                                            }

                                            e.stopPropagation();
                                            // Only allow slot selection if not past and not blocked
                                            if (!isPast && !isBlocked) onSlotClick(time);
                                        }}
                                        disabled={isPast} // Don't disable the entire button when blocked, just prevent slot selection in the click handler
                                        className={`w-full grid grid-cols-7 gap-[0.5rem] sm:gap-[0.75rem] md:gap-[1rem] p-[0.5rem] sm:p-[0.625rem] md:p-[0.75rem] text-[0.75rem] sm:text-[0.8rem] md:text-[0.875rem] items-center h-[3rem] group ${
                                            isPast ? "cursor-not-allowed" : isBlocked ? "cursor-default" : "hover:bg-gray-50"
                                        } ${
                                            isSelectedSlot ? "bg-green-50 hover:bg-green-100" : ""
                                        } ${
                                            isBlocked ? "bg-gray-100" : ""
                                        }`}
                                    >
                                        <div className="text-center relative z-10 flex items-center justify-center">
                                            {!isPast && (
                                                <div
                                                    className={`absolute left-2 top-1/2 -translate-y-1/2 p-2 transition-colors cursor-pointer z-10 ${isBlocked ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} padlock-icon-container`}
                                                    onMouseDown={(e) => e.stopPropagation()}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        const request: ToggleBlockedSlotRequest = new ToggleBlockedSlotRequest(
                                                            selectedDate.toISOString().split('T')[0],
                                                            time,
                                                            appointmentDuration,
                                                            "Bloqueo manual",
                                                            professionalId,
                                                            medicalCenterId,
                                                            employeeUserId
                                                        )
                                                        // TODO implement toggle blocked slot
                                                        toggleBlockedSlot(request, isBlocked);
                                                        if (isSelectedSlot && !isBlocked) {
                                                            onSlotClick(time);
                                                        }
                                                    }}
                                                >
                                                    {isBlocked ?
                                                        <Lock
                                                            className={`h-4 w-4 text-red-600 hover:text-green-600`}/> :
                                                        <Unlock
                                                            className={`h-4 w-4 text-gray-400 hover:text-red-600`}/>}
                                                </div>
                                            )}
                                            <span
                                                className={`inline-block truncate font-medium ${isSelectedSlot ? "font-semibold text-green-600" : isPast ? "text-gray-400" : isBlocked ? "text-gray-500" : ""}`}>
                        {time}
                      </span>
                                        </div>
                                        <div
                                            className="text-center z-10 w-full overflow-hidden flex items-center justify-center">
                      <span
                          className={`inline-block truncate w-full ${isSelectedSlot ? "text-green-600" : isPast ? "text-gray-400" : isBlocked ? "text-gray-500" : "text-gray-500"}`}>
                        {!isPast ? (isBlocked ? "Turno bloqueado" : "Turno disponible") : ""}
                      </span>
                                        </div>
                                        <div className="text-center z-10 flex items-center justify-center"></div>
                                        <div className="text-center z-10 flex items-center justify-center"></div>
                                        <div className="text-center z-10 flex items-center justify-center"></div>
                                        <div className="text-center z-10 flex items-center justify-center"></div>
                                        <div className="text-center z-10 flex items-center justify-center"></div>
                                    </button>
                                );
                            }

                            // Render appointments that start at this time
                            startAppointments.forEach((appointment, index) => {
                                const isSelectedAppointment = selectedAppointment?.id === appointment.id;
                                const isSobreturno = appointmentsForTime.length > 1 && index > 0;
                                const slotsNeeded = appointment.appointmentIntervalAmount;
                                const hasSubsequent = hasSubsequentAppointments(appointment);

                                elements.push(
                                    <button
                                        key={appointment.id}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onSlotClick(time, appointment);
                                        }}
                                        className={`w-full grid grid-cols-7 gap-[0.5rem] sm:gap-[0.75rem] md:gap-[1rem] p-[0.5rem] sm:p-[0.625rem] md:p-[0.75rem] text-[0.75rem] sm:text-[0.8rem] md:text-[0.875rem] items-center ${
                                            isSelectedAppointment ? "bg-blue-50 hover:bg-blue-100" : "bg-white hover:bg-gray-50"
                                        }`}
                                        style={{
                                            gridRow: hasSubsequent ? undefined : `span ${slotsNeeded}`,
                                            height: hasSubsequent ? '3rem' : `${3 * slotsNeeded}rem`
                                        }}
                                    >
                                        <div className="text-center z-10 flex items-center justify-center">
                      <span
                          className={`inline-block truncate ${isSelectedAppointment ? "font-semibold text-blue-600" : "font-medium"}`}
                      >
                        {time}
                      </span>
                                        </div>
                                        <div className="text-center relative z-10 w-full">
                                            <div className="flex items-center justify-center w-full relative">
                                                <TruncatedText
                                                    text={`${appointment.patientName} (${appointment.appointmentIntervalAmount * appointmentDuration} min)`}
                                                    className="text-center max-w-full"
                                                    isSelected={isSelectedAppointment}
                                                />
                                                {isSobreturno && (
                                                    <span
                                                        className="text-[0.75rem] text-blue-500 font-semibold whitespace-nowrap absolute right-0 z-20 ml-1">(ST)</span>
                                                )}
                                            </div>
                                        </div>
                                        <div className="flex items-center justify-center z-10">
                                            {appointment.state && (
                                                <span
                                                    className={`inline-flex items-center px-[0.5rem] sm:px-[0.6rem] md:px-[0.75rem] py-[0.2rem] sm:py-[0.25rem] rounded-full text-[0.65rem] sm:text-[0.7rem] md:text-[0.75rem] leading-3 z-20 ${
                                                        appointment.state === AppointmentState.NO_SHOW
                                                            ? "bg-red-100 text-red-700"
                                                            : appointment.state === AppointmentState.IN_CONSULTATION
                                                                ? "bg-green-100 text-green-700"
                                                                : appointment.state === AppointmentState.PENDING
                                                                    ? "bg-yellow-100 text-yellow-700"
                                                                    : appointment.state === AppointmentState.IN_WAITING_ROOM
                                                                        ? "bg-violet-200 text-violet-800"
                                                                        : "bg-blue-100 text-blue-700"
                                                    }`}
                                                >
                          {appointment.getStateAsSpanishString()}
                        </span>
                                            )}
                                        </div>
                                        <div className="flex items-center justify-center z-10">
                                            {appointment?.source && (
                                                <span
                                                    className={`inline-flex items-center px-[0.5rem] sm:px-[0.6rem] md:px-[0.75rem] py-[0.2rem] sm:py-[0.25rem] rounded-full text-[0.65rem] sm:text-[0.7rem] md:text-[0.75rem] leading-3 z-20 ${
                                                        appointment.source === AppointmentSource.TURNERA ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-700"
                                                    }`}
                                                >
                          {appointment.source}
                        </span>
                                            )}
                                        </div>
                                        <div className="text-center z-10 w-full relative">
                                            <div className="flex items-center justify-center w-full">
                                                <TruncatedText
                                                    text={appointment.getConsultationTypesAsString() || ''}
                                                    className="text-center max-w-full"
                                                    isSelected={isSelectedAppointment}
                                                />
                                                {appointment.getConsultationTypesAsString() && appointment.healthInsuranceInformation && appointment.hasConsultationTypeInfo(consultationTypes) && (
                                                    <div
                                                        className="ml-1 cursor-pointer"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            showConsultationInfo(appointment);
                                                        }}
                                                        aria-label={`Ver información de ${appointment.getConsultationTypesAsString()}`}
                                                        role="button"
                                                        tabIndex={0}
                                                        onKeyDown={(e) => {
                                                            if (e.key === 'Enter' || e.key === ' ') {
                                                                e.stopPropagation();
                                                                showConsultationInfo(appointment);
                                                            }
                                                        }}
                                                    >
                                                        <Info
                                                            className="h-3.5 w-3.5 text-blue-500 hover:text-blue-700"/>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="text-center z-10 w-full">
                                            <TruncatedText
                                                text={(() => {
                                                    // Show price for Sin Cobertura
                                                    if (appointment.hasPrice()) {
                                                        const price = appointment.price;
                                                        return price !== null ? `$${price.toLocaleString('es-AR', {minimumFractionDigits: 0}).replace(/,/g, '.')}` : '$N/A';
                                                    }
                                                    return appointment.healthInsuranceInformation;
                                                })()}
                                                className="text-center max-w-full"
                                                isSelected={isSelectedAppointment}
                                            />
                                        </div>
                                        <div className="text-center z-10 w-full">
                                            <TruncatedText
                                                text={appointment.phone || ''}
                                                className="text-center max-w-full"
                                                isSelected={isSelectedAppointment}
                                            />
                                        </div>
                                    </button>
                                );
                            });

                            return elements;
                        })}
                    </div>
                )}
            </div>

            {/* Instructions Dialog */}
            <ConsultationInfoTable
                isOpen={showInstructionsDialog}
                onOpenChange={setShowInstructionsDialog}
                consultationTypesInfo={consultationTypesInfo}
                selectedCoverage={selectedCoverage}
                className="mt-6"
            />
        </div>
    );
}