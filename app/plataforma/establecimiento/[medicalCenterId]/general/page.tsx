"use client"

import React, {Suspense, useContext, useEffect, useMemo, useRef, useState} from "react"
import {ArrowLeft, ChevronLeft, ChevronRight} from "lucide-react"
import {<PERSON><PERSON>} from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import {format, isValid, parse} from "date-fns"
import {useParams, usePathname, useSearchParams} from "next/navigation"
import {GeneralDayView} from "@/components/generalscheduleviews/general-day-view"
import {GeneralWeekView} from "@/components/generalscheduleviews/general-week-view"
import {GeneralMonthView} from "@/components/generalscheduleviews/general-month-view"
import {MedicalCenterContext} from "@/contexts/MedicalCenterContext"
import {DoctorContext} from "@/contexts/DoctorContext"
import {useAppointments} from "@/contexts/AppointmentContext"
// PatientContext is used by child components
import {formatMonthYear, generateTimeSlots, getMonthData} from "@/utils/dateUtils"
import type {Appointment, CalendarDay} from "@/types/scheduler"
import type {MedicalCenter} from "@/types/medical-center"
import type {Doctor} from "@/types/doctor"
import {OvercrowdedContextMenu} from "@/components/ui/overcrowded-context-menu"
import {getActiveHoursForDate, getWeeksDifference, REFERENCE_DATE} from '@/utils/scheduleUtils'
import {MedicalCenterUserPill} from "@/components/ui/MedicalCenterUserPill"
import {useAuth} from "@/hooks/useAuth"

const isDayAvailable = (date: Date, medicalCenter: MedicalCenter, doctors: Doctor[]): boolean => {
    const dateStr = format(date, "yyyy-MM-dd")
    const dayOfWeek = date.getDay().toString()
    const mcDateExceptions = medicalCenter.dateExceptions || {}

    // Check medical center date exceptions first
    if (dateStr in mcDateExceptions) {
        const isEnabled = mcDateExceptions[dateStr].enabled
        // If medical center is explicitly disabled, day is unavailable
        if (!isEnabled) return false
    }

    // Check if any doctor is available considering week frequency
    const hasDoctorsAvailable = medicalCenter.doctors.some((doctorId: string) => {
        const doctor = doctors.find((d) => d.id === doctorId)
        if (!doctor) return false

        const docDateExceptions = doctor.dateExceptions || {}

        // Check doctor's date exceptions first
        if (dateStr in docDateExceptions) {
            return docDateExceptions[dateStr].enabled
        }

        // Check working days with week frequency
        const workingDay = doctor.workingDays[dayOfWeek]
        if (!workingDay?.enabled) return false

        // Calculate week frequency
        const frequency = workingDay.weeksFrequency || 1
        const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE)
        if (weeksSinceReference % frequency !== 0) return false

        // Check if any time blocks are active for this date
        const activeHours = getActiveHoursForDate(workingDay.hours, date)
        return activeHours.length > 0
    })

    // If medical center has an exception and it's enabled, return doctor availability
    if (dateStr in mcDateExceptions) {
        return mcDateExceptions[dateStr].enabled && hasDoctorsAvailable
    }

    // No exception: day is available only if at least one doctor is scheduled
    return hasDoctorsAvailable
}

const findFirstAvailableDate = (startDate: Date, medicalCenter: MedicalCenter, doctors: Doctor[]): Date => {
    const currentDate = new Date(startDate)
    currentDate.setHours(0, 0, 0, 0)
    let iterations = 0
    const maxIterations = 365

    while (!isDayAvailable(currentDate, medicalCenter, doctors) && iterations < maxIterations) {
        currentDate.setDate(currentDate.getDate() + 1)
        iterations++
    }

    if (iterations >= maxIterations) {
        console.warn("No available date found within 365 days from", startDate)
        return startDate
    }

    console.log("findFirstAvailableDate: Start:", startDate, "Result:", currentDate, "Iterations:", iterations)
    return currentDate
}

// Main component instead of a function
// Wrapper component that doesn't use useSearchParams directly
export default function GeneralViewWrapper() {
    return (
        <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Cargando...</div>}>
            <GeneralView/>
        </Suspense>
    )
}

// Main component that uses useSearchParams
function GeneralView() {
    const [shouldRender, setShouldRender] = useState(true);
    const pathname = usePathname();
    const navigationTriggerRef = useRef(false);

    // Use a more controlled approach instead of hard reloading the page
    useEffect(() => {
        // Only run in the browser
        if (typeof window !== 'undefined') {
            // Store the current pathname in sessionStorage
            const currentPath = pathname;
            const previousPath = sessionStorage.getItem('general_page_previous_path');

            // Update for next time
            sessionStorage.setItem('general_page_previous_path', currentPath);

            // If we're coming from a different page and this isn't the first visit
            if (previousPath && previousPath !== currentPath && !previousPath.includes('general') && !navigationTriggerRef.current) {
                console.log('Navigation from external page detected');
                navigationTriggerRef.current = true;

                // Force a re-render instead of page reload
                setShouldRender(false);
                setTimeout(() => {
                    setShouldRender(true);
                }, 50);
            }
        }
    }, [pathname]);

    if (!shouldRender) {
        return null;
    }

    return <GeneralViewInner key={`general-view-${Date.now()}`}/>;
}

// The inner component with all the original logic
function GeneralViewInner() {
    const [isClient, setIsClient] = useState(false)
    const searchParams = useSearchParams()
    const {medicalCenterId} = useParams()
    const {currentUser, logout} = useAuth()

    // Get data from contexts with proper structure
    const {doctors} = useContext(DoctorContext)
    const {medicalCenters, setActiveMedicalCenterId} = useContext(MedicalCenterContext)
    const {
        appointments,
        addAppointment,
        removeAppointment,
        cancelAppointment,
        navigationState,
        setNavigationState
    } = useAppointments()

    // Track initialization with a ref for this render cycle
    const hasInitializedRef = useRef(false)
    const isUpdatingRef = useRef(false)

    // Reset initialization state whenever component is mounted
    useEffect(() => {
        console.log("GeneralView component mounted, resetting initialization")
        hasInitializedRef.current = false

        // Reset navigation state on mount to force fresh state
        if (setNavigationState) {
            console.log("Clearing navigation state on mount")
            setNavigationState(null)
        }

        // Clean up on unmount
        return () => {
            console.log("GeneralView component unmounting")
            hasInitializedRef.current = false
            isUpdatingRef.current = false
        }
    }, [setNavigationState])

    // Track client-side rendering
    useEffect(() => {
        if (!isClient) {
            console.log("Setting isClient to true")
            setIsClient(true)
        }
    }, [isClient])

    // Memoize the medical center
    const medicalCenter = useMemo(() =>
            medicalCenters.find((mc: MedicalCenter) => mc.id === medicalCenterId as string),
        [medicalCenters, medicalCenterId]
    )

    // Set active medical center ID whenever component mounts or route changes
    useEffect(() => {
        if (medicalCenterId) {
            console.log("Setting active medical center:", medicalCenterId)
            setActiveMedicalCenterId(medicalCenterId as string)
        }
    }, [medicalCenterId, setActiveMedicalCenterId])

    const dateParam = searchParams.get("date")
    const viewParam = searchParams.get("view") || "day"
    const appointmentDuration = 15

    const initialView = navigationState?.view as "day" | "week" | "month" || (viewParam as "day" | "week" | "month")
    const navDate = navigationState?.date || dateParam

    const initialDate = useMemo(() => {
        const date = navDate && isValid(parse(navDate, "yyyy-MM-dd", new Date()))
            ? parse(navDate, "yyyy-MM-dd", new Date())
            : new Date()
        date.setHours(0, 0, 0, 0)
        return date
    }, [navDate])

    // State with function-based initialization to avoid unnecessary re-renders
    const [currentDate, setCurrentDate] = useState<Date>(() => initialDate)
    const [selectedDate, setSelectedDate] = useState<Date>(() => initialDate)
    const [view, setView] = useState<"day" | "week" | "month">(() => initialView)

    // Controlled update of states when the date params change
    useEffect(() => {
        if (!isUpdatingRef.current) {
            console.log("Date or view parameters changed, updating state")
            setCurrentDate(initialDate)
            setSelectedDate(initialDate)
            setView(initialView)
        }
    }, [initialDate, initialView])

    const [overcrowdedMenu, setOvercrowdedMenu] = useState<{
        x: number;
        y: number;
        appointments: Appointment[]
    } | null>(null)
    const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null)

    const filteredAppointments = useMemo(() => {
        return Object.values(appointments)
            .flat()
            .filter((apt) => apt.date === format(selectedDate, "yyyy-MM-dd") &&
                (!apt.medicalCenterId || apt.medicalCenterId === medicalCenterId) &&
                apt.status !== "Cancelado")
    }, [appointments, selectedDate, medicalCenterId])

    // Initialize date when medicalCenter and doctors first load
    useEffect(() => {
        if (medicalCenter && doctors.length > 0 && isClient && !hasInitializedRef.current) {
            console.log("Initializing date from useEffect")

            // Prevent concurrent updates
            if (isUpdatingRef.current) return;
            isUpdatingRef.current = true;

            const adjustedDate = isDayAvailable(initialDate, medicalCenter, doctors)
                ? initialDate
                : findFirstAvailableDate(initialDate, medicalCenter, doctors)

            console.log("Setting dates to", adjustedDate)
            setCurrentDate(adjustedDate)
            setSelectedDate(adjustedDate)
            setNavigationState({view: initialView, date: format(adjustedDate, "yyyy-MM-dd")})

            hasInitializedRef.current = true

            // Allow further updates after a short delay
            setTimeout(() => {
                isUpdatingRef.current = false;
            }, 50);
        }
    }, [medicalCenter, doctors, initialDate, initialView, setNavigationState, isClient])

    // Add a deep comparison for doctors to detect changes in their properties (including exceptions)
    const doctorsStringified = useMemo(() => {
        return JSON.stringify(doctors.map(d => ({
            id: d.id,
            workingDays: d.workingDays,
            dateExceptions: d.dateExceptions
        })))
    }, [doctors])

    // Check if the selected date is still available whenever medical center or doctors change
    // If not, find a new available date
    useEffect(() => {
        if (medicalCenter && isClient && hasInitializedRef.current) {
            console.log("Checking date availability after doctor change")

            // Prevent concurrent updates
            if (isUpdatingRef.current) return;

            if (!isDayAvailable(selectedDate, medicalCenter, doctors)) {
                console.log("Selected date no longer available, finding new date")
                isUpdatingRef.current = true;

                const newDate = findFirstAvailableDate(selectedDate, medicalCenter, doctors)
                console.log("Updating date due to availability change:", selectedDate, "->", newDate)

                setCurrentDate(newDate)
                setSelectedDate(newDate)
                setNavigationState({view, date: format(newDate, "yyyy-MM-dd")})

                // Allow further updates after a delay
                setTimeout(() => {
                    isUpdatingRef.current = false;
                }, 50);
            }
        }
    }, [medicalCenter, doctorsStringified, selectedDate, setNavigationState, view, isClient, doctors])

    // Handle exceptional date updates when doctors are modified
    useEffect(() => {
        // Skip if not client-side yet or if we're still initializing
        if (!isClient || !hasInitializedRef.current || !medicalCenter) return;
        if (isUpdatingRef.current) return;

        console.log("Checking for doctor dateExceptions changes");

        // Even if current date is available, we need to refresh the view
        // To handle cases where date exceptions have changed
        console.log("Refreshing view by updating navigation state");
        setNavigationState({view, date: format(selectedDate, "yyyy-MM-dd")});
    }, [doctorsStringified, medicalCenter, isClient, selectedDate, setNavigationState, view]);

    const handleToday = () => {
        if (!medicalCenter) return
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        const adjustedToday = findFirstAvailableDate(today, medicalCenter, doctors)
        setCurrentDate(adjustedToday)
        setSelectedDate(adjustedToday)
        setNavigationState({view, date: format(adjustedToday, "yyyy-MM-dd")})
    }

    const handleDateChange = (date: Date) => {
        date.setHours(0, 0, 0, 0)
        setSelectedDate(date)
        setCurrentDate(date)
        setNavigationState({view, date: format(date, "yyyy-MM-dd")})
    }

    const handleDateSelect = (date: Date) => {
        date.setHours(0, 0, 0, 0)
        setSelectedDate(date)
        setView("day")
        setNavigationState({view: "day", date: format(date, "yyyy-MM-dd")})
    }

    const handleViewChange = (newView: "day" | "week" | "month") => {
        setView(newView)
        setNavigationState({view: newView, date: format(selectedDate, "yyyy-MM-dd")})
    }

    const handlePrevMonth = () => {
        if (!medicalCenter) return
        const firstDayOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
        const newDate = findFirstAvailableDate(firstDayOfPrevMonth, medicalCenter, doctors)
        setCurrentDate(newDate)
        setSelectedDate(newDate)
        setNavigationState({view, date: format(newDate, "yyyy-MM-dd")})
    }

    const handleNextMonth = () => {
        if (!medicalCenter) return
        const firstDayOfNextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)
        const newDate = findFirstAvailableDate(firstDayOfNextMonth, medicalCenter, doctors)
        setCurrentDate(newDate)
        setSelectedDate(newDate)
        setNavigationState({view, date: format(newDate, "yyyy-MM-dd")})
    }

    const handleWeekChange = (newDate: Date) => {
        if (!medicalCenter) return
        const adjustedDate = findFirstAvailableDate(newDate, medicalCenter, doctors)
        if (adjustedDate.getMonth() !== currentDate.getMonth()) {
            setCurrentDate(adjustedDate)
        }
        setSelectedDate(adjustedDate)
        setNavigationState({view, date: format(adjustedDate, "yyyy-MM-dd")})
    }

    const days = getMonthData(currentDate.getFullYear(), currentDate.getMonth()).days
    const startingDay = (getMonthData(currentDate.getFullYear(), currentDate.getMonth()).startingDay - 1 + 7) % 7

    const isDayAvailableMemo = (day: CalendarDay) => {
        if (!medicalCenter) return false
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day.date)
        return isDayAvailable(date, medicalCenter, doctors)
    }

    const handleContextMenu = (e: React.MouseEvent, appointment: Appointment) => {
        e.preventDefault()
        setSelectedAppointment(appointment)
        setOvercrowdedMenu(null)
    }

    const handleOvercrowdedSelect = (e: React.MouseEvent, appointments: Appointment[]) => {
        e.preventDefault()
        setOvercrowdedMenu({x: e.pageX, y: e.pageY, appointments})
    }

    const handleOvercrowdedAppointmentSelect = (appointment: Appointment) => {
        if (overcrowdedMenu) {
            setSelectedAppointment(appointment)
            setOvercrowdedMenu(null)
        }
    }

    const handleAppointmentSelect = (appointment: Appointment | null) => {
        setSelectedAppointment(appointment)
    }

    const handleStatusChange = (appointmentId: string, newStatus: Appointment["status"]) => {
        const appointment = filteredAppointments.find((apt) => apt.id === appointmentId)
        if (appointment) {
            // If the new status is "Cancelado", use cancelAppointment instead
            if (newStatus === "Cancelado") {
                cancelAppointment(appointmentId, "Cambio de estado a Cancelado")
                if (selectedAppointment && selectedAppointment.id === appointmentId) {
                    setSelectedAppointment(null)
                }
            } else {
                removeAppointment(appointmentId)
                const updatedAppointment = {
                    ...appointment,
                    status: newStatus,
                    medicalCenterId: medicalCenterId as string
                }
                addAppointment(updatedAppointment)
                if (selectedAppointment && selectedAppointment.id === appointmentId) {
                    setSelectedAppointment(updatedAppointment)
                }
            }
        }
    }

    const handleModifyAppointment = async (appointmentId: string, newDate: string, newTime: string) => {
        const appointment = filteredAppointments.find((apt) => apt.id === appointmentId)
        if (appointment) {
            removeAppointment(appointmentId)
            const updatedAppointment = {
                ...appointment,
                date: newDate,
                time: newTime,
                medicalCenterId: medicalCenterId as string
            }
            await addAppointment(updatedAppointment)
            if (selectedAppointment && selectedAppointment.id === appointmentId) {
                setSelectedAppointment(updatedAppointment)
            }
            const newDateObj = new Date(newDate)
            newDateObj.setHours(0, 0, 0, 0)
            setSelectedDate(newDateObj)
            setNavigationState({view: "day", date: newDate})
        }
    }

    const handleCancelAppointment = async (appointmentId: string) => {
        await cancelAppointment(appointmentId, "Cancelado por el usuario")
        if (selectedAppointment && selectedAppointment.id === appointmentId) {
            setSelectedAppointment(null)
        }
    }

    if (!isClient || !medicalCenter) {
        return null
    }

    return (
        // JSX unchanged
        <>
            <style jsx global>{`
                html {
                    font-size: 13px;
                }

                @media (min-width: 1500px) {
                    html {
                        font-size: 15px;
                    }
                }

                @media (min-width: 2560px) {
                    html {
                        font-size: 18px;
                    }
                }
            `}</style>

            <div className="min-h-screen bg-blue-50 flex flex-col">
                <header className="border-b bg-white shadow-sm">
                    <div className="container mx-auto px-[1.5rem] py-[0.75rem] flex items-center justify-between">
                        <Link href={`/plataforma/establecimiento/${medicalCenterId}`} className="flex items-center">
                            <Image
                                src="/images/turnera-logo.svg"
                                alt="Turnera Logo"
                                width={120}
                                height={40}
                                className="h-[1.5rem] w-auto"
                                priority
                            />
                        </Link>
                        <MedicalCenterUserPill
                            medicalCenter={medicalCenter}
                            currentUser={currentUser}
                            medicalCenterId={medicalCenterId as string}
                            logout={logout}
                        />
                    </div>
                </header>

                <nav className="bg-blue-50">
                    <div className="container mx-auto px-[1rem] py-[0.75rem] flex items-center justify-between">
                        <Link href={`/plataforma/establecimiento/${medicalCenterId}`} className="group">
                            <Button
                                variant="outline"
                                className="flex items-center gap-[0.375rem] transition-all duration-300 bg-white border-blue-300/50 hover:bg-blue-100/70 hover:border-blue-400 px-[0.75rem] py-[0.375rem] rounded-lg shadow-sm hover:shadow-md text-blue-700 hover:text-blue-900 font-medium text-[0.875rem]"
                            >
                                <ArrowLeft
                                    className="w-[1rem] h-[1rem] text-blue-600 group-hover:text-blue-800 transition-colors"/>
                                Atrás
                            </Button>
                        </Link>

                        <div className="flex-1 flex items-center justify-center">
                            <div
                                className="bg-white border border-blue-300/50 rounded-lg px-[1rem] py-[0.375rem] shadow-sm">
                                <div className="flex items-center gap-[0.5rem]">
                                    <span className="text-blue-800 text-[1rem] font-semibold">Agenda</span>
                                    <span className="-ml-[0.125rem] text-blue-800 font-semibold text-[1rem]">
                    General
                  </span>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-[1rem]">
                            <Button
                                variant="outline"
                                className="text-[0.875rem] h-[3rem] px-[1rem] py-[0.375rem] text-blue-700 border-blue-300/50 shadow-sm hover:bg-blue-50"
                                onClick={handleToday}
                            >
                                Hoy
                            </Button>
                            <div
                                className="flex rounded-lg bg-white p-[0.25rem] border border-blue-300/50 shadow-sm backdrop-blur-sm">
                                <Button
                                    variant="ghost"
                                    className={`px-[1rem] py-[0.375rem] rounded-md transition-all duration-300 text-[0.875rem] ${
                                        view === "day"
                                            ? "bg-white text-blue-700 font-semibold shadow-[0_2px_8px_-1px_rgba(37,99,235,0.15)] ring-1 ring-blue-200 hover:ring-blue-300 hover:scale-[1.02]"
                                            : "text-blue-600 hover:bg-blue-100/50 hover:text-blue-800 hover:shadow-sm"
                                    }`}
                                    onClick={() => handleViewChange("day")}
                                >
                                    Día
                                </Button>
                                <Button
                                    variant="ghost"
                                    className={`px-[1rem] py-[0.375rem] rounded-md transition-all duration-300 text-[0.875rem] ${
                                        view === "week"
                                            ? "bg-white text-blue-700 font-semibold shadow-[0_2px_8px_-1px_rgba(37,99,235,0.15)] ring-1 ring-blue-200 hover:ring-blue-300 hover:scale-[1.02]"
                                            : "text-blue-600 hover:bg-blue-100/50 hover:text-blue-800 hover:shadow-sm"
                                    }`}
                                    onClick={() => handleViewChange("week")}
                                >
                                    Semana
                                </Button>
                                <Button
                                    variant="ghost"
                                    className={`px-[1rem] py-[0.375rem] rounded-md transition-all duration-300 text-[0.875rem] ${
                                        view === "month"
                                            ? "bg-white text-blue-700 font-semibold shadow-[0_2px_8px_-1px_rgba(37,99,235,0.15)] ring-1 ring-blue-200 hover:ring-blue-300 hover:scale-[1.02]"
                                            : "text-blue-600 hover:bg-blue-100/50 hover:text-blue-800 hover:shadow-sm"
                                    }`}
                                    onClick={() => handleViewChange("month")}
                                >
                                    Mes
                                </Button>
                            </div>
                        </div>
                    </div>
                </nav>

                <div className="container mx-auto px-[1rem] py-[0.25rem] flex gap-[1.5rem] flex-1 overflow-visible">
                    <div
                        className="w-[18rem] flex-shrink-0 sticky top-[1.875rem] h-[calc(100vh-7.5rem)] bg-blue-50 z-[40]">
                        <div className="h-full">
                            <div className="border rounded-lg p-[1rem] bg-white border-black">
                                <div className="flex items-center justify-between mb-[1rem]">
                                    <span className="text-[0.875rem] font-medium">{formatMonthYear(currentDate)}</span>
                                    <div className="flex gap-[0.5rem]">
                                        <Button variant="ghost" size="icon" onClick={handlePrevMonth}>
                                            <ChevronLeft className="h-[1rem] w-[1rem]"/>
                                        </Button>
                                        <Button variant="ghost" size="icon" onClick={handleNextMonth}>
                                            <ChevronRight className="h-[1rem] w-[1rem]"/>
                                        </Button>
                                    </div>
                                </div>

                                <div className="grid grid-cols-7 gap-[0.375rem] text-[0.875rem] mb-[0.5rem]">
                                    {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                                        <div key={i} className="text-center font-medium">
                                            {day}
                                        </div>
                                    ))}
                                </div>

                                <div className="grid grid-cols-7 gap-[0.375rem] gap-y-[0.875rem] text-[0.875rem]">
                                    {Array.from({length: startingDay}).map((_, i) => (
                                        <div key={`empty-${i}`} className="p-[0.5rem]"/>
                                    ))}
                                    {days.map((day, i) => {
                                        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day.dateNumber)
                                        const dateStr = date.toISOString().split("T")[0]
                                        const dayOfWeek = date.getDay().toString()
                                        const workingDay = medicalCenter.workingDays[dayOfWeek]

                                        let totalSlots = 0
                                        // Filter appointments by medical center and exclude cancelled appointments
                                        const filteredAppointments = (appointments[dateStr] || []).filter(
                                            apt => (!apt.medicalCenterId || apt.medicalCenterId === medicalCenterId) && apt.status !== "Cancelado"
                                        )
                                        const appointmentCount = filteredAppointments.length
                                        if (workingDay?.enabled) {
                                            // Filter hours based on date ranges
                                            const activeHours = getActiveHoursForDate(workingDay.hours, date)

                                            if (activeHours.length > 0) {
                                                const slots = activeHours.flatMap(({start, end}) =>
                                                    generateTimeSlots(start, end, appointmentDuration)
                                                )
                                                totalSlots = slots.length
                                            } else {
                                                totalSlots = 0
                                            }
                                        }

                                        const isFull = appointmentCount >= totalSlots && totalSlots > 0
                                        const hasAppointments = appointmentCount > 0 && !isFull
                                        const isSelected =
                                            selectedDate.getDate() === day.dateNumber &&
                                            selectedDate.getMonth() === currentDate.getMonth() &&
                                            selectedDate.getFullYear() === currentDate.getFullYear()
                                        const isAvailable = isDayAvailableMemo(day)
                                        // Check if date is today
                                        const isToday = date.toDateString() === new Date().toDateString()

                                        return (
                                            <button
                                                key={i}
                                                onClick={() => handleDateChange(date)}
                                                disabled={!isAvailable}
                                                className={`
                          group w-[2rem] h-[2rem] p-0 rounded-full relative
                          ${!isAvailable ? "text-gray-300 cursor-not-allowed" : ""}
                          ${isSelected ? "bg-blue-500 text-white" : "hover:bg-blue-50"}
                        `}
                                            >
                                                <div
                                                    className={`w-full h-full flex items-center justify-center ${isToday && !isSelected ? "font-semibold" : ""}`}>
                                                    {day.dateNumber}
                                                </div>
                                                {(hasAppointments || isFull) && (
                                                    <div
                                                        className={`
                              absolute left-1/2 -translate-x-1/2
                              w-[0.375rem] h-[0.375rem] rounded-full
                              transition-all duration-200
                              ${isFull ? "bg-red-500" : "bg-blue-500"}
                              ${isSelected ? "-bottom-[0.5625rem]" : "-bottom-[0.125rem] group-hover:-bottom-[0.5625rem]"}
                            `}
                                                    />
                                                )}
                                            </button>
                                        )
                                    })}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex-1 overflow-x-auto pb-[4rem]">
                        {view === "day" && (
                            <GeneralDayView
                                selectedDate={selectedDate}
                                medicalCenter={medicalCenter}
                                doctors={doctors}
                                appointments={filteredAppointments}
                                onAppointmentSelect={handleAppointmentSelect}
                                onContextMenu={handleContextMenu}
                                onOvercrowdedSelect={handleOvercrowdedSelect}
                                onStatusChange={handleStatusChange}
                                onModifyAppointment={handleModifyAppointment}
                                onCancelAppointment={handleCancelAppointment}
                            />
                        )}
                        {view === "week" && (
                            <GeneralWeekView
                                selectedDate={selectedDate}
                                medicalCenter={medicalCenter}
                                doctors={doctors}
                                appointments={appointments}
                                onDateSelect={handleDateSelect}
                                onWeekChange={handleWeekChange}
                            />
                        )}
                        {view === "month" && (
                            <GeneralMonthView
                                selectedDate={selectedDate}
                                medicalCenter={medicalCenter}
                                doctors={doctors}
                                appointments={appointments}
                                onDateChange={handleDateChange}
                            />
                        )}
                    </div>
                </div>

                {overcrowdedMenu && view === "day" && (
                    <OvercrowdedContextMenu
                        x={overcrowdedMenu.x}
                        y={overcrowdedMenu.y}
                        appointments={overcrowdedMenu.appointments}
                        onSelect={handleOvercrowdedAppointmentSelect}
                        onClose={() => setOvercrowdedMenu(null)}
                    />
                )}
            </div>
        </>
    )
}