"use client"

import React, {Suspense, useCallback, useEffect, useState} from "react"
import {useRouter, useSearchParams} from "next/navigation"
import Image from "next/image"
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
import {But<PERSON>} from "@/components/ui/button"
import {Badge} from "@/components/ui/badge"
import {useAuth} from "@/contexts/AuthContext"
import {useAppointments} from "@/contexts/AppointmentContext"
import {User as UserType, UserRole} from "@/types/users"
import {format} from "date-fns"
import {es} from "date-fns/locale"
import {
    AlertCircle,
    Ban,
    Building2,
    Calendar,
    CalendarDays,
    CalendarPlus,
    ChevronRight,
    Clock,
    CreditCard,
    Edit,
    MapPin,
    Phone,
    ShieldCheck,
    User,
    UserCircle
} from "lucide-react"
import Link from "next/link"
import Header from "@/components/searchresults/Header"
import {PatientEditDialog} from "@/components/ui/patient-edit-dialog"
import {Patient} from "@/types/patient"
import {ConsultationInfoTable, ConsultationTypeInfo} from "@/components/schedulecomponents/ConsultationInfoTable"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import {Appointment} from "@/types/scheduler"


function PatientDashboardContent() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const {currentUser, isAuthenticated, getCurrentPatient, getAssociatedPatients} = useAuth()
    const {appointments, cancelAppointment} = useAppointments()

    const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [expandedAppointmentId, setExpandedAppointmentId] = useState<string | null>(null)

    // State for consultation info dialog
    const [showConsultationDialog, setShowConsultationDialog] = useState(false)
    const [consultationTypesInfo, setConsultationTypesInfo] = useState<ConsultationTypeInfo[]>([])
    const [selectedCoverage, setSelectedCoverage] = useState("")

    // Add state for confirmation dialog
    const [isConfirmingCancel, setIsConfirmingCancel] = useState(false)
    const [appointmentToCancel, setAppointmentToCancel] = useState<string | null>(null)

    const [isHistoryExpanded, setIsHistoryExpanded] = useState(false)
    const [pastAppointments, setPastAppointments] = useState<Appointment[]>([])
    const [loadingHistory, setLoadingHistory] = useState(false)

    // Add state for manage patients dialog
    const [isManagePatientsOpen, setIsManagePatientsOpen] = useState(false)
    const [patientToDisassociate, setPatientToDisassociate] = useState<Patient | null>(null)
    const [isConfirmingDisassociation, setIsConfirmingDisassociation] = useState(false)

    // Redirect to login if not authenticated or not a patient
    useEffect(() => {
        if (!isAuthenticated || !currentUser) {
            router.push("/plataforma/paciente/login")
            return
        }

        // Ensure the user is a patient
        if (currentUser.roles !== UserRole.PATIENT) {
            console.warn(`User ${currentUser.name} with role ${currentUser.roles} attempted to access patient dashboard`)

            // Redirect based on role
            if (currentUser.roles === UserRole.DOCTOR && currentUser.doctorId) {
                router.push(`/plataforma/profesional/${currentUser.doctorId}`)
            } else if (currentUser.medicalCenterId) {
                router.push(`/plataforma/establecimiento/${currentUser.medicalCenterId}`)
            } else {
                router.push('/plataforma/unauthorized')
            }
        }
    }, [isAuthenticated, currentUser, router])

    // Get the current patient and associated patients
    const currentPatient = getCurrentPatient()
    const associatedPatients = getAssociatedPatients()

    // Create plans by coverage mapping from DEFAULT_COVERAGES
    const plansByCoverage = DEFAULT_COVERAGES.reduce((acc, coverage) => {
        acc[coverage.name] = coverage.plans
        return acc
    }, {} as Record<string, string[]>)

    // Check if we should auto-open the profile dialog
    useEffect(() => {
        const shouldEditProfile = searchParams.get('editProfile')
        if (shouldEditProfile === 'true' && currentPatient) {
            // Small delay to ensure the page has loaded
            setTimeout(() => {
                openPatientDialog(currentPatient)
                // Clean up the URL parameter
                const url = new URL(window.location.href)
                url.searchParams.delete('editProfile')
                router.replace(url.pathname + url.search, {scroll: false})
            }, 100)
        }
    }, [searchParams, currentPatient, router])

    // Get upcoming appointments for the current patient and associated patients
    const patientIds = associatedPatients.map(p => p.id || "")

    const openPatientDialog = (patient: Patient) => {
        setSelectedPatient(patient)
        setIsDialogOpen(true)
    }

    // Function to close the dialog
    const closeDialog = () => {
        setIsDialogOpen(false)
        setSelectedPatient(null)
    }

    // Function to handle patient save
    const handlePatientSave = async (updatedPatient: Patient) => {
        try {
            // Update the patient in localStorage
            const storedPatients = localStorage.getItem("patients")
            if (storedPatients) {
                const allPatients: Patient[] = JSON.parse(storedPatients)
                const patientIndex = allPatients.findIndex((p: Patient) => p.id === updatedPatient.id)

                if (patientIndex !== -1) {
                    allPatients[patientIndex] = updatedPatient
                    localStorage.setItem("patients", JSON.stringify(allPatients))

                    // Trigger the patientsUpdated event for AuthContext
                    window.dispatchEvent(new Event('patientsUpdated'))

                    // Close the dialog
                    closeDialog()

                    // If this was the current user's patient, refresh the page to update the display
                    if (updatedPatient.id === currentPatient?.id) {
                        window.location.reload()
                    }
                }
            }
        } catch (error) {
            console.error('Error updating patient:', error)
        }
    }

    // Function to handle patient cancel
    const handlePatientCancel = () => {
        closeDialog()
    }

    // Cache for doctor and medical center names and locations
    const [doctorNames, setDoctorNames] = useState<Record<string, string>>({})
    const [medicalCenterNames, setMedicalCenterNames] = useState<Record<string, string>>({})
    // Store detailed locations for display purposes
    const [medicalCenterLocations, setMedicalCenterLocations] = useState<Record<string, string>>({})

    // Function to get a doctor's name from storage
    const getDoctorName = useCallback(async (doctorId: string) => {
        if (doctorNames[doctorId]) return doctorNames[doctorId]

        try {
            const allDoctors = storage.getAllDoctorsInSystem()
            const doctor = allDoctors.find(d => d.id === doctorId)

            if (doctor) {
                setDoctorNames(prev => ({...prev, [doctorId]: doctor.name}))
                return doctor.name
            }
            return doctorId // Fallback to ID if not found
        } catch (error) {
            console.error(`Error loading doctor name for ${doctorId}:`, error)
            return doctorId
        }
    }, [doctorNames])

    // Function to get a medical center's name from storage
    const getMedicalCenterName = useCallback(async (medicalCenterId: string) => {
        if (medicalCenterNames[medicalCenterId]) return medicalCenterNames[medicalCenterId]

        try {
            const medicalCenters = storage.getMedicalCenters()
            const center = medicalCenters.find(c => c.id === medicalCenterId)

            if (center) {
                setMedicalCenterNames(prev => ({...prev, [medicalCenterId]: center.name}))

                // Also set the formatted location - using DETAILED format
                const formattedLocation = formatMedicalCenterLocation(center, LocationFormatType.DETAILED)
                setMedicalCenterLocations(prev => ({...prev, [medicalCenterId]: formattedLocation}))

                return center.name
            }
            return medicalCenterId // Fallback to ID if not found
        } catch (error) {
            console.error(`Error loading medical center name for ${medicalCenterId}:`, error)
            return medicalCenterId
        }
    }, [medicalCenterNames, setMedicalCenterLocations])

    // Helper function to check if a consultation type has additional info
    const getConsultationTypeInfo = useCallback(async (typeName: string, coverage: string): Promise<ConsultationTypeInfo | null> => {
        if (!typeName || !coverage) return null

        try {
            const allDoctors = storage.getAllDoctorsInSystem()

            // Find the first doctor that has this consultation type
            for (const doctor of allDoctors) {
                const consultationType = doctor.consultationTypes?.find(t => t.name === typeName)
                if (consultationType) {
                    // Check if there's any info to show
                    const requiresOrder = consultationType.requiresMedicalOrder
                    const hasInstructions = consultationType.hasInstructions && consultationType.instructions

                    // Get coverage info
                    let coverageId = ""
                    let planId: string | null = null
                    let isExcluded = false
                    let copay = null

                    if (coverage === "Sin Cobertura") {
                        // Handle Sin Cobertura
                        const sinCobertura = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura")
                        if (sinCobertura) {
                            coverageId = sinCobertura.id
                            if (consultationType.acceptsPrivatePay === false) {
                                isExcluded = true
                            }
                        }
                    } else {
                        // Extract base coverage name and plan
                        const foundCoverage = DEFAULT_COVERAGES.find(cov => coverage.startsWith(cov.name))
                        if (foundCoverage) {
                            coverageId = foundCoverage.id

                            // Extract plan if present
                            if (coverage !== foundCoverage.name) {
                                const planPart = coverage.substring(foundCoverage.name.length).trim()
                                if (planPart && foundCoverage.plans.includes(planPart)) {
                                    planId = planPart
                                }
                            }

                            // Check for exclusions
                            const isTypeExcluded = consultationType.excludedCoverages?.some(
                                exclusion => exclusion.coverageId === coverageId &&
                                    (exclusion.planId === null || exclusion.planId === planId)
                            ) || false

                            isExcluded = isTypeExcluded

                            // Check for copay
                            const foundCopay = consultationType.copays?.find(
                                c => c.coverageId === coverageId && (c.planId === null || c.planId === planId)
                            )

                            if (foundCopay) {
                                copay = foundCopay.amount
                            }
                        }
                    }

                    return {
                        name: typeName,
                        requiresMedicalOrder: requiresOrder,
                        instructions: hasInstructions ? consultationType.instructions || "" : "",
                        isExcluded: isExcluded,
                        copayAmount: copay,
                        basePrice: consultationType.basePrice || 0,
                        acceptsPrivatePay: consultationType.acceptsPrivatePay !== false,
                    }
                }
            }
        } catch (error) {
            console.error('Error getting consultation type info:', error)
        }

        return null
    }, [])

    // Function to show consultation type info dialog
    const showConsultationInfo = useCallback(async (typeName: string, coverage: string) => {
        if (!typeName || !coverage) return

        // Handle multiple types separated by commas
        const typeNames = typeName.split(',').map(t => t.trim())

        if (typeNames.length === 0) return

        const typesInfoPromises = typeNames.map(name => getConsultationTypeInfo(name, coverage))
        const typesInfoArray = await Promise.all(typesInfoPromises)
        const typesInfo = typesInfoArray.filter((info): info is ConsultationTypeInfo => info !== null)

        if (typesInfo.length > 0) {
            setConsultationTypesInfo(typesInfo)
            setSelectedCoverage(coverage)
            setShowConsultationDialog(true)
        }
    }, [getConsultationTypeInfo])

    // Convert appointments object to array and filter for upcoming appointments
    const upcomingAppointments = Object.values(appointments || {})
        .flat()
        .filter(appointment =>
            appointment && // Ensure appointment is not null or undefined
            appointment.patient && // Ensure patient ID exists
            appointment.date && // Ensure date exists
            appointment.time && // Ensure time exists
            appointment.status !== "Cancelado" && // Not cancelled
            patientIds.includes(appointment.patient) &&
            new Date(`${appointment.date}T${appointment.time}`) >= new Date()
        )
        .sort((a, b) => {
            const dateA = new Date(`${a.date}T${a.time}`)
            const dateB = new Date(`${b.date}T${b.time}`)
            return dateA.getTime() - dateB.getTime()
        })

    // Load doctor and medical center names for all appointments
    useEffect(() => {
        upcomingAppointments.forEach(appointment => {
            if (appointment.doctorId) {
                getDoctorName(appointment.doctorId)
            }
            if (appointment.medicalCenterId) {
                getMedicalCenterName(appointment.medicalCenterId)
            }
        })
    }, [upcomingAppointments, getDoctorName, getMedicalCenterName])

    const toggleExpandAppointment = (appointmentId: string) => {
        setExpandedAppointmentId(prev => prev === appointmentId ? null : appointmentId)
    }

    // Update to show confirmation dialog first
    const handleCancelAppointment = (appointmentId: string) => {
        setAppointmentToCancel(appointmentId)
        setIsConfirmingCancel(true)
    }

    // New function to confirm and execute cancellation
    const confirmCancelAppointment = () => {
        if (appointmentToCancel) {
            cancelAppointment(appointmentToCancel, "Cancelado por el paciente", currentUser?.id)
            setExpandedAppointmentId(null)
            setIsConfirmingCancel(false)
            setAppointmentToCancel(null)
        }
    }

    // Generate Google Maps URL for medical center - ALWAYS using SIMPLE format
    const getGoogleMapsUrl = async (medicalCenterId: string) => {
        try {
            // Always fetch from storage to ensure we use the SIMPLE format for maps
            const medicalCenters = storage.getMedicalCenters()
            const center = medicalCenters.find(c => c.id === medicalCenterId)

            // Always prioritize coordinates when available for most accurate directions
            if (center && center.location?.latitude && center.location?.longitude) {
                // When coordinates are available, use them directly
                return `https://www.google.com/maps/dir/?api=1&destination=${center.location.latitude},${center.location.longitude}&travelmode=driving`
            } else if (center) {
                // Use SIMPLE formatted location for Google Maps
                const simpleFormattedLocation = formatMedicalCenterLocation(center, LocationFormatType.SIMPLE)
                return `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(simpleFormattedLocation)}&travelmode=driving`
            }

            return `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(medicalCenterId)}&travelmode=driving`
        } catch (error) {
            console.error('Error generating maps URL:', error)
            return `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(medicalCenterId)}&travelmode=driving`
        }
    }

    // Function to load past appointments
    const loadPastAppointments = useCallback(async () => {
        if (pastAppointments.length > 0) return // Already loaded

        setLoadingHistory(true)

        try {
            // Convert appointments object to array and filter for past appointments
            const past = Object.values(appointments || {})
                .flat()
                .filter(appointment =>
                    appointment && // Ensure appointment is not null or undefined
                    appointment.patient && // Ensure patient ID exists
                    appointment.date && // Ensure date exists
                    appointment.time && // Ensure time exists
                    patientIds.includes(appointment.patient) &&
                    new Date(`${appointment.date}T${appointment.time}`) < new Date() &&
                    appointment.status !== "Cancelado" // Filter out cancelled appointments
                )
                .sort((a, b) => {
                    const dateA = new Date(`${a.date}T${a.time}`)
                    const dateB = new Date(`${b.date}T${b.time}`)
                    return dateB.getTime() - dateA.getTime() // Sort by descending date (most recent first)
                })

            // Load doctor and medical center names for past appointments
            past.forEach(appointment => {
                if (appointment.doctorId) {
                    getDoctorName(appointment.doctorId)
                }
                if (appointment.medicalCenterId) {
                    getMedicalCenterName(appointment.medicalCenterId)
                }
            })

            setPastAppointments(past)
        } catch (error) {
            console.error("Error loading past appointments:", error)
        } finally {
            setLoadingHistory(false)
        }
    }, [appointments, patientIds, pastAppointments.length, getDoctorName, getMedicalCenterName])

    // Toggle history expansion
    const toggleHistory = useCallback(() => {
        const newState = !isHistoryExpanded
        setIsHistoryExpanded(newState)

        if (newState) {
            loadPastAppointments()
        }
    }, [isHistoryExpanded, loadPastAppointments])

    // Function to handle patient disassociation
    const handleDisassociatePatient = async (patient: Patient) => {
        try {
            // Debug current user
            console.log('Current user:', currentUser)
            console.log('Patient to disassociate:', patient)

            if (!currentUser?.id) {
                console.error("Current user or user ID is missing")
                return
            }

            // Get current user's data - using the correct localStorage key
            const users = JSON.parse(localStorage.getItem("medical-scheduler-users") || "[]")
            console.log('All users:', users)
            console.log('Looking for user ID:', currentUser.id)

            const currentUserIndex = users.findIndex((u: UserType) => u.id === currentUser.id)

            if (currentUserIndex === -1) {
                console.error("Current user not found in users array")
                console.error("Available user IDs:", users.map((u: UserType) => u.id))
                return
            }

            console.log('Found user at index:', currentUserIndex)
            console.log('Current user data:', users[currentUserIndex])

            // Handle user-side cleanup
            const updatedUser = {...users[currentUserIndex]}
            console.log('User before update:', updatedUser)

            // If this patient was the user's default patient, remove the reference
            if (updatedUser.defaultPatientId === patient.id) {
                console.log('Removing defaultPatientId reference from user')
                updatedUser.defaultPatientId = undefined
            }

            // Remove from associatedPatientIds array if it exists
            if (updatedUser.associatedPatientIds) {
                const originalLength = updatedUser.associatedPatientIds.length
                updatedUser.associatedPatientIds = updatedUser.associatedPatientIds.filter((id: string) => id !== patient.id)
                console.log(`Removed patient ${patient.id} from associatedPatientIds, array length: ${originalLength} -> ${updatedUser.associatedPatientIds.length}`)
            } else {
                console.log('No associatedPatientIds found on user')
            }

            // Update users array
            users[currentUserIndex] = updatedUser
            localStorage.setItem("medical-scheduler-users", JSON.stringify(users))
            console.log('Updated user saved to localStorage')

            // MAIN DISASSOCIATION: Remove the userId from the patient (this is the primary association)
            const allPatients = JSON.parse(localStorage.getItem("patients") || "[]")
            const patientIndex = allPatients.findIndex((p: Patient) => p.id === patient.id)

            if (patientIndex !== -1) {
                const updatedPatient = {...allPatients[patientIndex]}
                console.log('Patient before disassociation:', updatedPatient)

                // Remove the userId field (this is the main association)
                updatedPatient.userId = undefined
                updatedPatient.isDefault = false

                // Remove from associatedUserIds array if it exists (though this is likely not used)
                if (updatedPatient.associatedUserIds) {
                    updatedPatient.associatedUserIds = updatedPatient.associatedUserIds.filter((id: string) => id !== currentUser.id)
                }

                console.log('Patient after disassociation:', updatedPatient)

                allPatients[patientIndex] = updatedPatient
                localStorage.setItem("patients", JSON.stringify(allPatients))
                console.log('Updated patient saved to localStorage - userId removed')
            } else {
                console.log('Patient not found in patients array')
            }

            // Trigger events to update contexts
            window.dispatchEvent(new Event('usersUpdated'))
            window.dispatchEvent(new Event('patientsUpdated'))

            // Close dialogs
            setIsConfirmingDisassociation(false)
            setPatientToDisassociate(null)
            setIsManagePatientsOpen(false)

            console.log('Disassociation completed successfully')

            // Refresh the page to update the display
            window.location.reload()
        } catch (error) {
            console.error('Error disassociating patient:', error)
        }
    }

    // Function to open disassociation confirmation
    const confirmDisassociation = (patient: Patient) => {
        setPatientToDisassociate(patient)
        setIsConfirmingDisassociation(true)
    }

    if (!currentPatient) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
                <Card className="w-full max-w-md shadow-md rounded-lg border border-[#0070F3]/30 bg-white">
                    <CardHeader className="p-6 text-center">
                        <div className="relative mb-6 mx-auto w-fit">
                            <div
                                className="animate-spin rounded-full h-16 w-16 border-4 border-gray-100 border-t-blue-500"></div>
                            <div className="absolute inset-0 flex items-center justify-center">
                                <User className="h-8 w-8 text-blue-500 animate-pulse"/>
                            </div>
                        </div>
                        <CardTitle className="text-xl font-bold text-gray-800">Cargando información</CardTitle>
                        <CardDescription className="text-gray-600 mt-2">Espere un momento mientras cargamos sus
                            datos...</CardDescription>
                    </CardHeader>
                    <CardContent className="p-6 pt-0">
                        <div className="mt-4 space-y-3">
                            <div className="h-3 bg-gray-200 rounded-full w-3/4 mx-auto animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded-full w-1/2 mx-auto animate-pulse"></div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-blue-50 flex flex-col">
            <Header/>

            <main className="flex-1 max-w-6xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
                {/* Welcome Banner */}
                <div className="mb-8">
                    <Card className="rounded-lg border border-[#0070F3]/30 shadow-sm bg-white p-6">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                            <div className="mb-6 md:mb-0">
                                <h1 className="text-2xl font-bold text-gray-800 mb-2">¡Hola, {currentPatient.name.split(' ')[0]}!</h1>
                                <p className="text-gray-600">
                                    En tu panel podés gestionar tus turnos médicos, acceder a tu historial y coordinar
                                    atenciones para tus pacientes asociados.
                                </p>
                            </div>
                            <div>
                                <Button
                                    size="lg"
                                    className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm hover:shadow-md transition-all px-4 py-2 rounded-md"
                                    onClick={() => router.push("/plataforma/buscar")}
                                >
                                    <CalendarPlus className="h-5 w-5 mr-2"/>
                                    Reservar turno
                                </Button>
                            </div>
                        </div>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                    {/* Left Column - Profile & Associated Patients */}
                    <div className="lg:col-span-4 space-y-6 order-2 lg:order-1">
                        {/* Profile Card - Simplified */}
                        <Card className="rounded-lg border border-[#0070F3]/30 shadow-sm bg-white">
                            <CardHeader className="p-5 border-b border-gray-100">
                                <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                                    <UserCircle className="h-5 w-5 mr-2"/>
                                    Mi Perfil
                                </CardTitle>
                            </CardHeader>

                            <CardContent className="p-5 space-y-4">
                                <div>
                                    <h3 className="text-xl font-bold text-gray-800 mb-1">{currentPatient.name}</h3>
                                    <p className="text-sm text-gray-500">Paciente</p>
                                </div>

                                <div className="grid grid-cols-1 gap-3">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0 bg-blue-100 p-2 rounded-full">
                                            <CreditCard className="h-4 w-4 text-blue-600"/>
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-xs font-medium text-gray-500">DNI</p>
                                            <p className="font-semibold text-gray-800">{currentPatient.dni}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center">
                                        <div className="flex-shrink-0 bg-blue-100 p-2 rounded-full">
                                            <Phone className="h-4 w-4 text-blue-600"/>
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-xs font-medium text-gray-500">Teléfono</p>
                                            <p className="font-semibold text-gray-800">{currentPatient.phone}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center">
                                        <div className="flex-shrink-0 bg-blue-100 p-2 rounded-full">
                                            <ShieldCheck className="h-4 w-4 text-blue-600"/>
                                        </div>
                                        <div className="ml-3">
                                            <p className="text-xs font-medium text-gray-500">Cobertura</p>
                                            <p className="font-semibold text-gray-800">{currentPatient.coverage || "Sin Cobertura"}</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>

                            <CardFooter className="px-5 py-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                <Button
                                    variant="outline"
                                    className="w-full"
                                    onClick={() => openPatientDialog(currentPatient)}
                                >
                                    Editar perfil
                                </Button>
                            </CardFooter>
                        </Card>

                        {/* Associated Patients Card - Simplified */}
                        {associatedPatients.length > 1 && (
                            <Card className="rounded-lg border border-[#0070F3]/30 shadow-sm bg-white">
                                <CardHeader className="p-5 border-b border-gray-100">
                                    <CardTitle className="text-lg font-semibold text-gray-800 flex items-center">
                                        <User className="h-5 w-5 mr-2 text-blue-500"/>
                                        Pacientes Asociados
                                    </CardTitle>
                                    <CardDescription className="text-gray-500 text-sm mt-1">
                                        Gestioná los pacientes asociados a tu perfil
                                    </CardDescription>
                                </CardHeader>

                                <CardContent className="p-5">
                                    <ul className="space-y-3">
                                        {associatedPatients
                                            .filter(p => p.id !== currentPatient.id)
                                            .map(patient => (
                                                <li
                                                    key={patient.id}
                                                    className="flex items-center justify-between p-3 bg-gray-50 hover:bg-blue-50 rounded-lg transition-colors border border-gray-100 cursor-pointer"
                                                    onClick={() => openPatientDialog(patient)}
                                                >
                                                    <div className="flex items-center space-x-3">
                                                        <div className="bg-blue-100 p-2 rounded-full">
                                                            <User className="h-4 w-4 text-blue-600"/>
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-gray-800">{patient.name}</p>
                                                            <p className="text-xs text-gray-500">DNI: {patient.dni}</p>
                                                            <p className="text-xs text-gray-500">{patient.coverage || "Sin Cobertura"}</p>
                                                        </div>
                                                    </div>
                                                    <ChevronRight className="h-4 w-4 text-gray-400"/>
                                                </li>
                                            ))}
                                    </ul>
                                </CardContent>

                                <CardFooter className="px-5 py-4 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                    <Button variant="outline" className="w-full"
                                            onClick={() => setIsManagePatientsOpen(true)}>
                                        Gestionar pacientes
                                    </Button>
                                </CardFooter>
                            </Card>
                        )}
                    </div>

                    {/* Right Column - Appointments */}
                    <div className="lg:col-span-8 flex flex-col space-y-6 order-1 lg:order-2">
                        {/* Upcoming Appointments */}
                        <Card className="rounded-xl border-0 shadow-lg bg-white overflow-hidden">
                            <CardHeader className="p-6 pb-4 border-0">
                                <div className="flex justify-between items-center">
                                    <div>
                                        <CardTitle className="text-xl font-bold text-gray-900 mb-1">
                                            Mis Próximos Turnos
                                        </CardTitle>
                                        <CardDescription className="text-gray-600">
                                            {upcomingAppointments.length ? `${upcomingAppointments.length} turno${upcomingAppointments.length !== 1 ? 's' : ''} próximo${upcomingAppointments.length !== 1 ? 's' : ''}` : 'No hay turnos pendientes'}
                                        </CardDescription>
                                    </div>

                                    {upcomingAppointments.length > 0 && (
                                        <div
                                            className="bg-blue-50 text-blue-700 font-semibold px-3 py-1.5 rounded-full text-sm">
                                            {upcomingAppointments.length}
                                        </div>
                                    )}
                                </div>
                            </CardHeader>

                            <CardContent className="p-0">
                                {upcomingAppointments.length === 0 ? (
                                    <div className="text-center py-16 px-6">
                                        <div
                                            className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                                            <CalendarDays className="h-8 w-8 text-blue-500"/>
                                        </div>
                                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No tenés turnos
                                            próximos</h3>
                                        <p className="text-gray-600 mb-6 max-w-md mx-auto">
                                            Reservá un turno con los mejores profesionales médicos para cuidar tu salud.
                                        </p>
                                    </div>
                                ) : (
                                    <div className="space-y-3 p-6 pt-2">
                                        {upcomingAppointments.map((appointment) => {
                                            const patient = associatedPatients.find(p => p.id === appointment.patient)
                                            const appointmentDate = new Date(`${appointment.date}T${appointment.time}`)
                                            const isToday = format(appointmentDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
                                            const isTomorrow = format(appointmentDate, 'yyyy-MM-dd') === format(new Date(Date.now() + 86400000), 'yyyy-MM-dd')
                                            const isExpanded = expandedAppointmentId === appointment.id

                                            return (
                                                <div
                                                    key={appointment.id}
                                                    className={`
                            bg-white rounded-xl border transition-all duration-200 cursor-pointer
                            ${isExpanded
                                                        ? 'border-blue-200 shadow-md ring-2 ring-blue-50'
                                                        : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                                                    }
                          `}
                                                    onClick={() => toggleExpandAppointment(appointment.id)}
                                                >
                                                    <div className="p-5">
                                                        {/* Main appointment info */}
                                                        <div className="flex items-start gap-4">
                                                            {/* Date & Time */}
                                                            <div className="flex-shrink-0">
                                                                <div className={`
                                  text-center px-3 py-3 rounded-lg min-w-[72px]
                                  ${isToday
                                                                    ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white'
                                                                    : 'bg-gray-100 text-gray-800'
                                                                }
                                `}>
                                                                    <div
                                                                        className="text-xs font-medium uppercase tracking-wide mb-1">
                                                                        {isToday ? 'Hoy' : isTomorrow ? 'Mañana' : format(appointmentDate, 'EEE', {locale: es})}
                                                                    </div>
                                                                    <div
                                                                        className="text-lg font-bold leading-none mb-1">
                                                                        {format(appointmentDate, 'd')}
                                                                    </div>
                                                                    <div className="text-sm font-semibold">
                                                                        {format(appointmentDate, 'HH:mm')}
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            {/* Appointment details */}
                                                            <div className="flex-1 min-w-0">
                                                                <div className="flex items-start justify-between mb-3">
                                                                    <h3 className="text-lg font-semibold text-gray-900 truncate pr-2">
                                                                        {appointment.type}
                                                                    </h3>
                                                                    <button
                                                                        className="flex-shrink-0 text-gray-500 hover:text-blue-600 transition-colors p-1"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            toggleExpandAppointment(appointment.id);
                                                                        }}
                                                                    >
                                                                        <ChevronRight
                                                                            className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-90' : ''}`}/>
                                                                    </button>
                                                                </div>

                                                                {/* Key info in simple layout */}
                                                                <div className="space-y-2">
                                                                    {appointment.doctorId && (
                                                                        <div className="text-gray-700">
                                                                            <span
                                                                                className="font-medium">Dr. {doctorNames[appointment.doctorId] || appointment.doctorId}</span>
                                                                        </div>
                                                                    )}

                                                                    {appointment.medicalCenterId && (
                                                                        <div className="text-gray-600 text-sm">
                                                                            {medicalCenterNames[appointment.medicalCenterId] || appointment.medicalCenterId}
                                                                        </div>
                                                                    )}

                                                                    {/* Patient badge if not current patient */}
                                                                    {patient && patient.id !== currentPatient.id && (
                                                                        <div
                                                                            className="inline-flex items-center bg-blue-50 text-blue-700 px-2 py-1 rounded-md text-sm font-medium">
                                                                            Para: {patient.name}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>

                                                        {/* Expanded content */}
                                                        {isExpanded && (
                                                            <div
                                                                className="mt-5 pt-5 border-t border-gray-100 space-y-4">
                                                                {/* Additional details */}
                                                                <div
                                                                    className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                                                                    <div className="space-y-2">
                                                                        <div
                                                                            className="flex items-center text-gray-600">
                                                                            <ShieldCheck
                                                                                className="h-4 w-4 mr-2 flex-shrink-0"/>
                                                                            <span>{appointment.coverage || "Sin Cobertura"}</span>
                                                                        </div>
                                                                    </div>

                                                                    {appointment.medicalCenterId && (
                                                                        <div className="space-y-2">
                                                                            <div
                                                                                className="flex items-center text-gray-600">
                                                                                <MapPin
                                                                                    className="h-4 w-4 mr-2 flex-shrink-0"/>
                                                                                <span
                                                                                    className="cursor-pointer hover:text-blue-600 transition-colors"
                                                                                    onClick={async (e) => {
                                                                                        e.stopPropagation();
                                                                                        const url = await getGoogleMapsUrl(appointment.medicalCenterId!);
                                                                                        window.open(url, '_blank');
                                                                                    }}
                                                                                >
                                          {medicalCenterLocations[appointment.medicalCenterId] || appointment.medicalCenterId}
                                        </span>
                                                                            </div>
                                                                        </div>
                                                                    )}
                                                                </div>

                                                                {/* Consultation info link */}
                                                                {appointment.type && appointment.coverage && (
                                                                    <div
                                                                        className="inline-flex items-center text-blue-600 hover:text-blue-700 cursor-pointer text-sm font-medium transition-colors"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation()
                                                                            showConsultationInfo(appointment.type, appointment.coverage)
                                                                        }}
                                                                    >
                                                                        <AlertCircle className="h-4 w-4 mr-2"/>
                                                                        Ver indicaciones del turno
                                                                    </div>
                                                                )}

                                                                {/* Action buttons */}
                                                                <div className="flex flex-wrap gap-3 pt-2">
                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 transition-colors"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation()
                                                                            // Redirect to the appointment creation page with the original appointment data
                                                                            const queryParams = new URLSearchParams({
                                                                                doctorId: appointment.doctorId || '',
                                                                                medicalCenterId: appointment.medicalCenterId || '',
                                                                                consultationType: appointment.type || '',
                                                                                modifying: appointment.id, // Flag to indicate we're modifying an existing appointment
                                                                                source: 'modifying', // Use 'modifying' as source to return to patient dashboard on back
                                                                                date: appointment.date || '',
                                                                                time: appointment.time || ''
                                                                            })

                                                                            // Add coverage params if available
                                                                            if (appointment.coverage) {
                                                                                if (appointment.coverage === "Sin Cobertura") {
                                                                                    queryParams.append('noCoverage', 'true')
                                                                                } else {
                                                                                    // Check if coverage includes a plan (e.g., "OSDE 310")
                                                                                    const coverageParts = appointment.coverage.split(' ')
                                                                                    queryParams.append('coverage', coverageParts[0])

                                                                                    if (coverageParts.length > 1) {
                                                                                        queryParams.append('plan', coverageParts.slice(1).join(' '))
                                                                                    }
                                                                                }
                                                                            }

                                                                            router.push(`/plataforma/reservar/cita?${queryParams.toString()}`)
                                                                        }}
                                                                    >
                                                                        <Edit className="h-4 w-4 mr-2"/>
                                                                        Modificar
                                                                    </Button>

                                                                    <Button
                                                                        variant="outline"
                                                                        size="sm"
                                                                        className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 transition-colors"
                                                                        onClick={(e) => {
                                                                            e.stopPropagation()
                                                                            handleCancelAppointment(appointment.id)
                                                                        }}
                                                                    >
                                                                        <Ban className="h-4 w-4 mr-2"/>
                                                                        Cancelar
                                                                    </Button>

                                                                    {appointment.medicalCenterId && (
                                                                        <Button
                                                                            variant="outline"
                                                                            size="sm"
                                                                            className="text-green-600 border-green-200 hover:bg-green-50 hover:border-green-300 transition-colors"
                                                                            onClick={async (e) => {
                                                                                e.stopPropagation()
                                                                                const url = await getGoogleMapsUrl(appointment.medicalCenterId!)
                                                                                window.open(url, '_blank')
                                                                            }}
                                                                        >
                                                                            <MapPin className="h-4 w-4 mr-2"/>
                                                                            Cómo llegar
                                                                        </Button>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            )
                                        })}
                                    </div>
                                )}
                            </CardContent>

                            <CardFooter className="p-5 bg-gray-50 border-t border-gray-100 rounded-b-lg">
                                <Button
                                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                                    onClick={() => router.push("/plataforma/buscar")}
                                >
                                    <CalendarPlus className="h-4 w-4 mr-2"/>
                                    Reservar turno
                                </Button>
                            </CardFooter>
                        </Card>

                        {/* Quick Access Links - With Notifications Removed */}
                        <Card className="rounded-lg border border-[#0070F3]/30 shadow-sm bg-white">
                            <CardContent className="p-0">
                                <div
                                    className="flex items-center cursor-pointer p-5 hover:bg-gray-50 transition-colors"
                                    onClick={toggleHistory}
                                >
                                    <div className="mr-4 p-3 bg-blue-100 rounded-lg">
                                        <Clock className="h-5 w-5 text-blue-600"/>
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="font-semibold text-gray-800 mb-1">Historial de turnos</h3>
                                        <p className="text-sm text-gray-600">Consultá tus atenciones anteriores</p>
                                    </div>
                                    <ChevronRight
                                        className={`h-5 w-5 text-gray-400 ml-2 transition-transform ${isHistoryExpanded ? 'rotate-90' : ''}`}/>
                                </div>

                                {/* Expanded history section */}
                                {isHistoryExpanded && (
                                    <div className="border-t border-gray-100 px-5 pb-5 pt-3">
                                        <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                            <CalendarDays className="h-4 w-4 mr-2 text-blue-500"/>
                                            Mis turnos anteriores
                                        </h4>

                                        {loadingHistory ? (
                                            <div className="flex items-center justify-center py-8">
                                                <div
                                                    className="animate-spin rounded-full h-8 w-8 border-4 border-blue-200 border-t-blue-600"></div>
                                                <span
                                                    className="ml-3 text-sm text-gray-600">Cargando historial...</span>
                                            </div>
                                        ) : pastAppointments.length === 0 ? (
                                            <div className="text-center py-6">
                                                <div
                                                    className="bg-gray-100 rounded-full h-12 w-12 flex items-center justify-center mx-auto mb-3">
                                                    <Calendar className="h-6 w-6 text-gray-400"/>
                                                </div>
                                                <p className="text-gray-500 text-sm">No tenés turnos anteriores</p>
                                            </div>
                                        ) : (
                                            <div className="space-y-4 mt-3">
                                                {pastAppointments.map((appointment) => {
                                                    const patient = associatedPatients.find(p => p.id === appointment.patient)
                                                    const appointmentDate = new Date(`${appointment.date}T${appointment.time}`)

                                                    return (
                                                        <div
                                                            key={appointment.id}
                                                            className="rounded-lg border border-gray-200 overflow-hidden bg-white hover:shadow-sm transition-shadow"
                                                        >
                                                            <div className="flex flex-col sm:flex-row">
                                                                {/* Date badge */}
                                                                <div
                                                                    className="sm:w-24 px-4 py-3 bg-gray-50 flex sm:flex-col items-center justify-between sm:justify-center">
                                                                    <div className="text-center">
                                    <span className="text-xs font-medium text-gray-500 block">
                                      {format(appointmentDate, 'MMM', {locale: es}).toUpperCase()}
                                    </span>
                                                                        <span
                                                                            className="text-lg font-bold text-gray-800 block">
                                      {format(appointmentDate, 'd', {locale: es})}
                                    </span>
                                                                        <span className="text-xs text-gray-500 block">
                                      {format(appointmentDate, 'yyyy', {locale: es})}
                                    </span>
                                                                    </div>
                                                                    <div
                                                                        className="sm:mt-2 sm:border-t sm:pt-2 sm:border-gray-200 sm:w-full text-center">
                                    <span className="text-sm font-medium text-gray-700">
                                      {format(appointmentDate, 'HH:mm', {locale: es})}
                                    </span>
                                                                    </div>
                                                                </div>

                                                                {/* Appointment details */}
                                                                <div className="flex-1 p-4">
                                                                    <div className="flex flex-col">
                                                                        <div
                                                                            className="flex justify-between items-start">
                                                                            <h4 className="font-semibold text-gray-800 mb-2">{appointment.type}</h4>
                                                                        </div>

                                                                        <div
                                                                            className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1.5">
                                                                            {/* First column: Doctor and Medical Center */}
                                                                            <div className="space-y-1.5">
                                                                                {/* Doctor - clickable */}
                                                                                {appointment.doctorId && (
                                                                                    <div className="flex items-center">
                                                                                        <User
                                                                                            className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0"/>
                                                                                        <Link
                                                                                            href={`/plataforma/reservar/d/${appointment.doctorId}`}
                                                                                            className="text-sm text-blue-600 hover:text-blue-800 hover:underline truncate"
                                                                                            onClick={(e) => e.stopPropagation()}
                                                                                        >
                                                                                            Dr. {doctorNames[appointment.doctorId] || appointment.doctorId}
                                                                                        </Link>
                                                                                    </div>
                                                                                )}

                                                                                {/* Medical Center - clickable */}
                                                                                {appointment.medicalCenterId && (
                                                                                    <div className="flex items-center">
                                                                                        <Building2
                                                                                            className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0"/>
                                                                                        <Link
                                                                                            href={`/plataforma/reservar/cm/${appointment.medicalCenterId}`}
                                                                                            className="text-sm text-blue-600 hover:text-blue-800 hover:underline truncate"
                                                                                            onClick={(e) => e.stopPropagation()}
                                                                                        >
                                                                                            {medicalCenterNames[appointment.medicalCenterId] || appointment.medicalCenterId}
                                                                                        </Link>
                                                                                    </div>
                                                                                )}
                                                                            </div>

                                                                            {/* Second column: Coverage and Location */}
                                                                            <div className="space-y-1.5">
                                                                                {/* Coverage */}
                                                                                <div className="flex items-center">
                                                                                    <ShieldCheck
                                                                                        className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0"/>
                                                                                    <span
                                                                                        className="text-sm text-gray-700 truncate">{appointment.coverage || "Sin Cobertura"}</span>
                                                                                </div>

                                                                                {/* Location */}
                                                                                {appointment.medicalCenterId && (
                                                                                    <div className="flex items-center">
                                                                                        <MapPin
                                                                                            className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0"/>
                                                                                        <span
                                                                                            className="text-sm text-gray-700 truncate cursor-pointer hover:text-blue-600"
                                                                                            onClick={async (e) => {
                                                                                                e.stopPropagation();
                                                                                                const url = await getGoogleMapsUrl(appointment.medicalCenterId!);
                                                                                                window.open(url, '_blank');
                                                                                            }}
                                                                                        >
                                              {medicalCenterLocations[appointment.medicalCenterId] || appointment.medicalCenterId}
                                            </span>
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        </div>

                                                                        {/* Patient info if not current patient */}
                                                                        {patient && patient.id !== currentPatient.id && (
                                                                            <div className="mt-3">
                                                                                <Badge variant="outline"
                                                                                       className="bg-blue-50 text-blue-700 border-blue-200 font-normal">
                                                                                    <User className="h-3 w-3 mr-1.5"/>
                                                                                    Paciente: {patient.name}
                                                                                </Badge>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )
                                                })}

                                                {pastAppointments.length > 5 && (
                                                    <div className="text-center pt-2">
                                                        <Button
                                                            variant="link"
                                                            className="text-blue-600 hover:text-blue-800"
                                                            onClick={() => router.push("/plataforma/paciente/historial")}
                                                        >
                                                            Ver historial completo
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </main>

            <footer className="bg-white border-t border-gray-100 py-6 mt-8">
                <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="mb-6 md:mb-0 text-center md:text-left">
                            <Link href="/" className="inline-block mb-3">
                                <Image
                                    src="/images/turnera-logo.svg"
                                    alt="Turnera Logo"
                                    width={120}
                                    height={36}
                                    className="h-7 w-auto"
                                />
                            </Link>
                            <p className="text-sm text-gray-500">
                                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
                            </p>
                        </div>
                        <div className="flex space-x-4">
                            <Link href="/terminos-y-condiciones">
                                <Button variant="link" size="sm" className="text-gray-600 hover:text-blue-700">
                                    Términos y Condiciones
                                </Button>
                            </Link>
                            <Link href="/privacidad">
                                <Button variant="link" size="sm" className="text-gray-600 hover:text-blue-700">
                                    Privacidad
                                </Button>
                            </Link>
                            <Button variant="link" size="sm" className="text-gray-600 hover:text-blue-700">
                                Ayuda
                            </Button>
                        </div>
                    </div>
                </div>
            </footer>

            <PatientEditDialog
                open={isDialogOpen}
                onOpenChange={setIsDialogOpen}
                patient={selectedPatient}
                onSave={handlePatientSave}
                onCancel={handlePatientCancel}
                plansByCoverage={plansByCoverage}
            />

            {/* Consultation Info Dialog */}
            <ConsultationInfoTable
                isOpen={showConsultationDialog}
                onOpenChange={setShowConsultationDialog}
                consultationTypesInfo={consultationTypesInfo}
                selectedCoverage={selectedCoverage}
            />

            {/* Manage Patients Dialog */}
            <Dialog open={isManagePatientsOpen} onOpenChange={setIsManagePatientsOpen}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Gestionar Pacientes Asociados</DialogTitle>
                        <DialogDescription>
                            Podés desasociar pacientes de tu perfil. Una vez desasociados, ya no tendrán acceso
                            compartido.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4 max-h-[60vh] overflow-y-auto">
                        {associatedPatients
                            .filter(p => p.id !== currentPatient.id)
                            .map(patient => (
                                <div
                                    key={patient.id}
                                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200"
                                >
                                    <div className="flex items-center space-x-3">
                                        <div className="bg-blue-100 p-2 rounded-full">
                                            <User className="h-4 w-4 text-blue-600"/>
                                        </div>
                                        <div>
                                            <p className="font-medium text-gray-800">{patient.name}</p>
                                            <p className="text-sm text-gray-500">DNI: {patient.dni}</p>
                                            <p className="text-sm text-gray-500">{patient.coverage || "Sin Cobertura"}</p>
                                        </div>
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                                        onClick={() => confirmDisassociation(patient)}
                                    >
                                        Desasociar
                                    </Button>
                                </div>
                            ))}

                        {associatedPatients.filter(p => p.id !== currentPatient.id).length === 0 && (
                            <div className="text-center py-8">
                                <div
                                    className="bg-gray-100 rounded-full h-12 w-12 flex items-center justify-center mx-auto mb-3">
                                    <User className="h-6 w-6 text-gray-400"/>
                                </div>
                                <p className="text-gray-500 text-sm">No tenés pacientes asociados para gestionar</p>
                            </div>
                        )}
                    </div>

                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsManagePatientsOpen(false)}>
                            Cerrar
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Disassociation Confirmation Dialog */}
            <Dialog open={isConfirmingDisassociation} onOpenChange={setIsConfirmingDisassociation}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Confirmar Desasociación</DialogTitle>
                        <DialogDescription>
                            ¿Estás seguro que querés desasociar a {patientToDisassociate?.name}? Esta acción no se puede
                            deshacer y el paciente ya no estará asociado a tu perfil.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex flex-row justify-end gap-2 sm:justify-end">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                                setIsConfirmingDisassociation(false)
                                setPatientToDisassociate(null)
                            }}
                        >
                            Cancelar
                        </Button>
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={() => patientToDisassociate && handleDisassociatePatient(patientToDisassociate)}
                        >
                            Sí, desasociar
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Add the confirmation dialog at the end of the JSX, before the closing div */}
            <Dialog open={isConfirmingCancel} onOpenChange={setIsConfirmingCancel}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Cancelar Turno</DialogTitle>
                        <DialogDescription>
                            ¿Estás seguro que querés cancelar este turno? Esta acción no se puede deshacer.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex flex-row justify-end gap-2 sm:justify-end">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => setIsConfirmingCancel(false)}
                        >
                            Volver
                        </Button>
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={confirmCancelAppointment}
                        >
                            Sí, cancelar turno
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}

export default function PatientDashboardPage() {
    return (
        <Suspense fallback={<div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Cargando panel de paciente...</p>
            </div>
        </div>}>
            <PatientDashboardContent/>
        </Suspense>
    )
}
