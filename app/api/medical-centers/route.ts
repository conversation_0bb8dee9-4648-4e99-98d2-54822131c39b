import { NextRequest, NextResponse } from 'next/server';

export interface CreateMedicalCenterRequest {
    mail: string;
    phone: string;
    imageUrl?: string;
    name: string;
    address: string;
    postalCode: number;
    province: string;
    city: string;
    acceptsSelfPaidPatients: boolean;
    floorNumber: number;
    departmentNumber: string;
    latitude: number;
    longitude: number;
}

export async function POST(request: NextRequest) {
    try {
        const body: CreateMedicalCenterRequest = await request.json();

        // Validate required fields
        const requiredFields: (keyof CreateMedicalCenterRequest)[] = [
            'mail',
            'phone',
            'name',
            'address',
            'postalCode',
            'province',
            'city',
            'latitude',
            'longitude'
        ];

        for (const field of requiredFields) {
            if (body[field] === undefined || body[field] === null) {
                return NextResponse.json(
                    { error: `Missing required field: ${field}` },
                    { status: 400 }
                );
            }
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(body.mail)) {
            return NextResponse.json(
                { error: 'Invalid email format' },
                { status: 400 }
            );
        }

        // Validate postal code is positive
        if (body.postalCode <= 0) {
            return NextResponse.json(
                { error: 'Postal code must be a positive number' },
                { status: 400 }
            );
        }

        // Validate coordinates
        if (body.latitude < -90 || body.latitude > 90) {
            return NextResponse.json(
                { error: 'Invalid latitude value' },
                { status: 400 }
            );
        }

        if (body.longitude < -180 || body.longitude > 180) {
            return NextResponse.json(
                { error: 'Invalid longitude value' },
                { status: 400 }
            );
        }

        // Make request to external API
        const response = await fetch(
            `${process.env.BACKEND_URL}/medical-center`,
            {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            const errorText = await response.text();
            console.error('External API error:', response.status, errorText);

            return NextResponse.json(
                {
                    error: 'Failed to create medical center',
                    details: errorText,
                    status: response.status
                },
                { status: response.status }
            );
        }

        const result = await response.json();
        return NextResponse.json(result, { status: 200 });

    } catch (error) {
        console.error('Error creating medical center:', error);

        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
}
