"use client"

import { createContext, useState, useContext, ReactNode } from "react"
import type { Appointment } from "@/types/scheduler"

interface ScheduleChangesContextType {
  appointmentsToCancel: Appointment[]
  addAppointmentsToCancel: (appointments: Appointment[]) => void
  clearAppointmentsToCancel: () => void
  cancellationReason: string
  setCancellationReason: (reason: string) => void
  cancellationOption: "auto" | "manual" | null
  setCancellationOption: (option: "auto" | "manual" | null) => void
}

const ScheduleChangesContext = createContext<ScheduleChangesContextType>({
  appointmentsToCancel: [],
  addAppointmentsToCancel: () => {},
  clearAppointmentsToCancel: () => {},
  cancellationReason: "",
  setCancellationReason: () => {},
  cancellationOption: null,
  setCancellationOption: () => {},
})

export function ScheduleChangesProvider({ children }: { children: ReactNode }) {
  const [appointmentsToCancel, setAppointmentsToCancel] = useState<Appointment[]>([])
  const [cancellationReason, setCancellationReason] = useState<string>("")
  const [cancellationOption, setCancellationOption] = useState<"auto" | "manual" | null>(null)

  const addAppointmentsToCancel = (appointments: Appointment[]) => {
    setAppointmentsToCancel(prev => {
      // Create a map of existing appointments by ID for quick lookup
      const existingMap = new Map(prev.map(apt => [apt.id, apt]))
      
      // Add new appointments that don't already exist
      appointments.forEach(apt => {
        if (!existingMap.has(apt.id)) {
          existingMap.set(apt.id, apt)
        }
      })
      
      // Convert map back to array
      return Array.from(existingMap.values())
    })
  }

  const clearAppointmentsToCancel = () => {
    setAppointmentsToCancel([])
    setCancellationReason("")
    setCancellationOption(null)
  }

  return (
    <ScheduleChangesContext.Provider
      value={{
        appointmentsToCancel,
        addAppointmentsToCancel,
        clearAppointmentsToCancel,
        cancellationReason,
        setCancellationReason,
        cancellationOption,
        setCancellationOption,
      }}
    >
      {children}
    </ScheduleChangesContext.Provider>
  )
}

export const useScheduleChanges = () => useContext(ScheduleChangesContext)
