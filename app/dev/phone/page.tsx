"use client"

import { useState, useEffect } from "react"
import { mockPhoneProvider, MockPhoneMessage } from "@/services/phone"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Trash2, RefreshCw, Eye, MessageSquare, CheckCircle, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

export default function PhoneMessagesPage() {
  const [messages, setMessages] = useState<MockPhoneMessage[]>([])
  const [selectedMessage, setSelectedMessage] = useState<MockPhoneMessage | null>(null)

  useEffect(() => {
    // Load messages from storage
    loadMessages()
  }, [])

  const loadMessages = () => {
    const storedMessages = mockPhoneProvider.getMockMessages()
    // Sort messages by sentAt date, newest first
    const sortedMessages = [...storedMessages].sort((a, b) =>
      new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime()
    )
    setMessages(sortedMessages)
  }

  const markAsRead = (id: string) => {
    mockPhoneProvider.markMessageAsRead(id)
    loadMessages()
  }

  const clearMessages = () => {
    mockPhoneProvider.clearAllMessages()
    setMessages([])
    setSelectedMessage(null)
    toast.success("Todos los mensajes han sido eliminados")
  }

  const viewMessage = (message: MockPhoneMessage) => {
    setSelectedMessage(message)
    if (!message.read) {
      markAsRead(message.id)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href="/dev" passHref>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dev Tools
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">SMS Preview (Desarrollo)</h1>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadMessages}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" className="text-red-600" onClick={clearMessages}>
            <Trash2 className="h-4 w-4 mr-2" />
            Borrar Todos
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Mensajes Enviados</CardTitle>
            </CardHeader>
            <CardContent>
              {messages.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No hay mensajes enviados</p>
              ) : (
                <ul className="space-y-2">
                  {messages.map(message => (
                    <li
                      key={message.id}
                      className={`p-3 rounded-md cursor-pointer hover:bg-gray-100 ${!message.read ? 'bg-blue-50' : ''}`}
                      onClick={() => viewMessage(message)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            {message.read ? (
                              <CheckCircle className="h-4 w-4 text-gray-400 mr-2" />
                            ) : (
                              <MessageSquare className="h-4 w-4 text-blue-500 mr-2" />
                            )}
                            <p className={`text-sm font-medium ${!message.read ? 'font-bold' : ''}`}>{message.to}</p>
                          </div>
                          <p className="text-xs text-gray-500">
                            {new Date(message.sentAt).toLocaleString()}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm" onClick={(e) => {
                          e.stopPropagation();
                          viewMessage(message);
                        }}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Vista Previa</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedMessage ? (
                <div>
                  <div className="mb-4 p-4 bg-gray-50 rounded-md">
                    <p><strong>Para:</strong> {selectedMessage.to}</p>
                    <p><strong>Enviado:</strong> {new Date(selectedMessage.sentAt).toLocaleString()}</p>
                  </div>
                  <div className="border rounded-md p-4 bg-blue-50">
                    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                      <p className="whitespace-pre-wrap">{selectedMessage.message}</p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Seleccione un mensaje para ver su contenido</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
