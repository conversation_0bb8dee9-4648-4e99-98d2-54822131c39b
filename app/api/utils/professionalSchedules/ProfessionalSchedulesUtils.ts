"use client"

import axios from "axios";
import {ProfessionalSchedulesResponse} from "@/types/professional-schedules";


function getMonthNameFromDate(date: Date): string {
    const months = [
        'JANUARY', 'FEBRUARY', 'MARCH', 'APRIL', 'MAY', 'JUNE',
        'JULY', 'AUGUST', 'SEPTEMBER', 'OCTOBER', 'NOVEMBER', 'DECEMBER'
    ];
    return months[date.getMonth()];
}

export const getSchedules = async (medicalCenterId: number, doctorId: number, employeeUserId: number, date: Date): Promise<ProfessionalSchedulesResponse> => {
    try {
        const month = getMonthNameFromDate(date);
        const response = await axios.get(
            `/api/professional-schedules?medicalCenterId=${medicalCenterId}&professionalId=${doctorId}&month=${month}&employeeUserId=${employeeUserId}`
        );
        if (response.status === 200) {
            return response.data as ProfessionalSchedulesResponse;
        }
        return {
            month: month,
            appointment_schedules: [],
            special_schedules: [],
            vacation_schedules: [],
            appointments: [],
            blocked_slots: []
        };
    } catch (error) {
        console.error('Error fetching professional schedules:', error);
        return {
            month: "JANUARY",
            appointment_schedules: [],
            special_schedules: [],
            vacation_schedules: [],
            appointments: [],
            blocked_slots: []
        };
    }
}
