"use client"

import React, {useEffect, useReducer, useState} from "react"
import type {Appointment} from "@/types/scheduler"
import type {MedicalCenter} from "@/types/medical-center"
import type {Doctor} from "@/types/doctor"
import {useRouter} from "next/navigation"
import {OvercrowdedContextMenu} from "@/components/ui/overcrowded-context-menu"
import {AppointmentContextMenu} from "@/components/ui/appointment-context-menu"
import {AppointmentDetailsDialog} from "@/components/schedulecomponents/appointment-details-dialog"
import {But<PERSON>} from "@/components/ui/button"
import {Calendar} from "@/components/ui/calendar"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Label} from "@/components/ui/label"
import {Check} from "lucide-react"
import {format, isSameDay} from "date-fns"
import {es} from "date-fns/locale"
import {useAppointments} from "@/contexts/AppointmentContext"
import {usePatients} from "@/contexts/PatientContext"
import {generateTimeSlots} from "@/utils/dateUtils"
import {getActiveHoursForDate, getWeeksDifference, REFERENCE_DATE} from '@/utils/scheduleUtils'

const APPOINTMENT_DURATION = 15 // Minutes

interface GeneralDayViewProps {
    selectedDate: Date
    medicalCenter: MedicalCenter
    doctors: Doctor[]
    doctor?: Doctor
    appointments: Appointment[]
    onAppointmentSelect: (appointment: Appointment | null) => void
    onContextMenu: (e: React.MouseEvent, appointment: Appointment) => void
    onOvercrowdedSelect: (e: React.MouseEvent, appointments: Appointment[]) => void
    onStatusChange: (appointmentId: string, newStatus: Appointment["status"]) => void
    onModifyAppointment: (appointmentId: string, newDate: string, newTime: string) => void
    onCancelAppointment: (appointmentId: string) => void
}

interface State {
    contextMenu: { x: number; y: number } | null
    overcrowdedMenu: { x: number; y: number; appointments: ExtendedAppointment[] } | null
    selectedAppointment: Appointment | null
    showStatusModal: boolean
    showObservations: boolean
    showModifyModal: boolean
    showCancelDialog: boolean
    modifyDate: Date
    modifyTime: string
}

interface ExtendedAppointment extends Appointment {
    dni?: string
    duration?: number
}

type Action =
    | { type: "SET_CONTEXT_MENU"; x: number; y: number; appointment: Appointment }
    | { type: "SET_OVERCROWD_MENU"; x: number; y: number; appointments: ExtendedAppointment[] }
    | { type: "CLEAR_CONTEXT_MENU" }
    | { type: "CLEAR_OVERCROWD_MENU" }
    | { type: "SET_STATUS_MODAL"; show: boolean }
    | { type: "SET_OBSERVATIONS"; show: boolean }
    | { type: "SET_MODIFY_MODAL"; show: boolean; date?: Date; time?: string }
    | { type: "SET_CANCEL_DIALOG"; show: boolean }

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case "SET_CONTEXT_MENU":
            return {
                ...state,
                contextMenu: {x: action.x, y: action.y},
                selectedAppointment: action.appointment,
                overcrowdedMenu: null
            }
        case "SET_OVERCROWD_MENU":
            return {
                ...state,
                overcrowdedMenu: {x: action.x, y: action.y, appointments: action.appointments},
                selectedAppointment: null,
                contextMenu: null
            }
        case "CLEAR_CONTEXT_MENU":
            return {...state, contextMenu: null}
        case "CLEAR_OVERCROWD_MENU":
            return {...state, overcrowdedMenu: null}
        case "SET_STATUS_MODAL":
            return {...state, showStatusModal: action.show}
        case "SET_OBSERVATIONS":
            return {...state, showObservations: action.show}
        case "SET_MODIFY_MODAL":
            return {
                ...state,
                showModifyModal: action.show,
                modifyDate: action.date || state.modifyDate,
                modifyTime: action.time || state.modifyTime,
            }
        case "SET_CANCEL_DIALOG":
            return {...state, showCancelDialog: action.show}
        default:
            return state
    }
}

export function GeneralDayView({
                                   selectedDate,
                                   medicalCenter,
                                   doctors,
                                   doctor,
                                   appointments,
                                   onStatusChange,
                                   onModifyAppointment,
                                   onCancelAppointment,
                               }: GeneralDayViewProps) {
    const {getPatientNameForAppointment} = useAppointments()
    const {patients} = usePatients()
    const [state, dispatch] = useReducer(reducer, {
        contextMenu: null,
        overcrowdedMenu: null,
        selectedAppointment: null,
        showStatusModal: false,
        showObservations: false,
        showModifyModal: false,
        showCancelDialog: false,
        modifyDate: selectedDate,
        modifyTime: "",
    })
    // State to track current time
    const [currentTime, setCurrentTime] = useState(new Date())
    const router = useRouter()

    // Update current time every minute
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date())
        }, 60000) // Update every minute

        return () => clearInterval(interval)
    }, [])

    const getTimeRange = () => {
        const dayId = selectedDate.getDay().toString();
        const daySchedule = medicalCenter?.workingDays[dayId];

        if (!daySchedule || !daySchedule.enabled || !daySchedule.hours || daySchedule.hours.length === 0) {
            return {start: 8, end: 20}; // Default hours if no schedule
        }

        const sortedHours = [...daySchedule.hours].sort((a, b) => {
            return a.start.localeCompare(b.start);
        });

        const startHour = Math.min(...sortedHours.map(({start}: { start: string }) => parseInt(start.split(':')[0])));
        const endHour = Math.max(...sortedHours.map(({end}: { end: string }) => {
            const hourPart = parseInt(end.split(':')[0]);
            const minutePart = parseInt(end.split(':')[1]);
            return minutePart > 0 ? hourPart + 1 : hourPart; // Round up if there are minutes
        }));

        return {start: startHour, end: endHour};
    };

    const {start, end} = getTimeRange()
    const hours = Array.from({length: end - start + 1}, (_, i) => i + start)

    const getWorkingDoctors = () => {
        const dateStr = format(selectedDate, "yyyy-MM-dd")
        const dayOfWeek = selectedDate.getDay().toString()

        const baseDoctors = doctor
            ? [doctor]
            : doctors.filter((d) => {
                // First check if doctor works at this medical center
                if (!medicalCenter.doctors.includes(d.id)) return false

                const docDateExceptions = d.dateExceptions || {}

                // Check date exceptions first
                if (dateStr in docDateExceptions) {
                    return docDateExceptions[dateStr].enabled
                }

                // Check working days with week frequency
                const workingDay = d.workingDays[dayOfWeek]
                if (!workingDay?.enabled) return false

                // Check week frequency
                const frequency = workingDay.weeksFrequency || 1
                const weeksSinceReference = getWeeksDifference(selectedDate, REFERENCE_DATE)
                if (weeksSinceReference % frequency !== 0) return false

                // Check if any hours are active for this date based on date ranges
                const activeHours = getActiveHoursForDate(workingDay.hours, selectedDate)
                return activeHours.length > 0
            })

        const doctorColumns: (Doctor | null)[] = [...baseDoctors.slice(0, 7)]
        while (doctorColumns.length < 7) doctorColumns.push(null)
        return doctorColumns
    }

    const doctorColumns = getWorkingDoctors()

    // Helper function to check if a doctor is busy at a specific hour
    const checkIfBusy = (doctor: Doctor, hour: number, dayId: string): boolean => {
        const doctorSchedule = doctor.workingDays[dayId];
        if (!doctorSchedule || !doctorSchedule.enabled || !doctorSchedule.hours) return false;

        // Filter hours based on date ranges
        const activeHours = getActiveHoursForDate(doctorSchedule.hours, selectedDate);
        if (activeHours.length === 0) return false;

        return activeHours.some(({start, end}) => {
            const startHour = parseInt(start.split(':')[0]);
            const endHour = parseInt(end.split(':')[0]);
            const endMinutes = parseInt(end.split(':')[1]);

            return hour >= startHour && (hour < endHour || (hour === endHour && endMinutes > 0));
        });
    };

    const isHourAvailable = (doctor: Doctor | null, hour: number): boolean => {
        if (!doctor) return false
        const dateStr = format(selectedDate, "yyyy-MM-dd")
        const day = selectedDate.getDay().toString()
        const dateExceptions = doctor.dateExceptions || {}

        // Check date exceptions first
        if (dateStr in dateExceptions) {
            const exception = dateExceptions[dateStr]
            return exception.enabled && (exception.hours || []).some(({start, end}) => {
                const [startH] = start.split(":").map(Number)
                const [endH] = end.split(":").map(Number)
                return hour >= startH && hour < endH
            })
        }

        // Check regular working day
        const workingDay = doctor.workingDays[day]
        if (!workingDay?.enabled) return false

        // Check week frequency
        const frequency = workingDay.weeksFrequency || 1
        const weeksSinceReference = getWeeksDifference(selectedDate, REFERENCE_DATE)
        if (weeksSinceReference % frequency !== 0) return false

        // Use checkIfBusy helper to determine if doctor is available at this hour
        return checkIfBusy(doctor, hour, day);
    }

    const getAppointmentPosition = (time: string) => {
        const [, minutes] = time.split(":").map(Number)
        return `${(minutes / 60) * 100}%`
    }

    const getAppointmentHeight = (appointment: Appointment) => {
        // Use the appointment's duration if available, otherwise use the default duration
        const duration = appointment.duration || APPOINTMENT_DURATION
        return `${(duration / 60) * 100}%`
    }

    const handleClick = (e: React.MouseEvent, appointment: Appointment) => {
        e.stopPropagation()
        const x = e.clientX
        const y = e.clientY
        const hourAppointments = appointments.filter((apt) => {
            const [aptHour] = apt.time.split(":")
            return Number.parseInt(aptHour) === Number.parseInt(appointment.time.split(":")[0]) && apt.doctorId === appointment.doctorId
        })
        const sameTimeAppointments = hourAppointments
            .filter((apt) => apt.time === appointment.time)
            .map((apt) => {
                // Get patient by ID
                const patient = patients.find(p => p.id === apt.patient)
                return {...apt, dni: patient?.dni || "DNI no disponible"} as ExtendedAppointment
            })

        if (sameTimeAppointments.length > 1) {
            dispatch({type: "SET_OVERCROWD_MENU", x, y, appointments: sameTimeAppointments})
        } else {
            dispatch({type: "SET_CONTEXT_MENU", x, y, appointment})
        }
    }

    const handleOvercrowdedSelect = (appointment: Appointment) => {
        dispatch({type: "CLEAR_OVERCROWD_MENU"})
        if (state.overcrowdedMenu) {
            const x = state.overcrowdedMenu.x
            const y = state.overcrowdedMenu.y
            dispatch({type: "SET_CONTEXT_MENU", x, y, appointment})
        }
    }

    const handleOutsideClick = (e: React.MouseEvent) => {
        const target = e.target as HTMLElement
        if (
            !target.closest(".appointment-slot") &&
            !target.closest(".appointment-context-menu") &&
            !target.closest(".modal-visible") &&
            !target.closest(".overcrowded-context-menu")
        ) {
            dispatch({type: "CLEAR_CONTEXT_MENU"})
            dispatch({type: "CLEAR_OVERCROWD_MENU"})
            if (state.showStatusModal) dispatch({type: "SET_STATUS_MODAL", show: false})
            if (state.showObservations) dispatch({type: "SET_OBSERVATIONS", show: false})
            if (state.showModifyModal) dispatch({type: "SET_MODIFY_MODAL", show: false})
            if (state.showCancelDialog) dispatch({type: "SET_CANCEL_DIALOG", show: false})
            if (state.selectedAppointment) state.selectedAppointment = null
        }
    }

    const handleContextAction = (action: string) => {
        dispatch({type: "CLEAR_CONTEXT_MENU"})
        if (state.selectedAppointment) {
            switch (action) {
                case "status":
                    dispatch({type: "SET_STATUS_MODAL", show: true})
                    break
                case "observations":
                    dispatch({type: "SET_OBSERVATIONS", show: true})
                    break
                case "modify":
                    dispatch({
                        type: "SET_MODIFY_MODAL",
                        show: true,
                        date: new Date(state.selectedAppointment.date),
                        time: state.selectedAppointment.time
                    })
                    break
                case "cancel":
                    dispatch({type: "SET_CANCEL_DIALOG", show: true})
                    break
            }
        }
    }

    useEffect(() => {
        console.log("State updated:", state)
    }, [state])

    const getHourSlotHeight = () => {
        const slotsPerHour = 60 / APPOINTMENT_DURATION
        return `${slotsPerHour * 24}px`
    }

    // Helper to calculate the current time position for the time indicator line
    const getCurrentTimePosition = (): { position: number; hasSobreturno: boolean } | null => {
        const now = new Date()

        // Check if today is the selected date
        if (!isSameDay(now, selectedDate)) {
            return null // Not today, don't show the line
        }

        // Get current hours and minutes
        const hours = currentTime.getHours()
        const minutes = currentTime.getMinutes()

        // Check if current time is within the hour range we're displaying
        const {start, end} = getTimeRange()
        if (hours < start || hours >= end) {
            return null
        }

        // Calculate position based on hour and minute
        const hourPosition = hours - start // Position in terms of full hours from the top
        const minutePercentage = minutes / 60 // How far through the current hour (0-1)

        // Calculate the pixel position
        const hourSlotHeight = parseInt(getHourSlotHeight().replace('px', ''))
        const position = (hourPosition + minutePercentage) * hourSlotHeight

        // Check if the current time is over a slot with sobreturnos (multiple appointments)
        const year = selectedDate.getFullYear()
        const month = String(selectedDate.getMonth() + 1).padStart(2, '0')
        const day = String(selectedDate.getDate()).padStart(2, '0')
        const dateStr = `${year}-${month}-${day}`

        // Get all appointments for this day
        const dayAppointments = appointments.filter((apt) => apt.date === dateStr)

        // Get appointments for the current hour
        const hourAppointments = dayAppointments.filter((apt) => {
            const [aptHour] = apt.time.split(":")
            return Number.parseInt(aptHour) === hours
        })

        // Check if there are multiple appointments at the current time
        const currentTimeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
        const sameTimeAppointments = hourAppointments.filter(apt => apt.time === currentTimeStr)

        // If there are multiple appointments at this time, it's a sobreturno
        const hasSobreturno = sameTimeAppointments.length > 1

        return {position, hasSobreturno}
    }

    const selectedDateStr = format(selectedDate, "yyyy-MM-dd")
    const filteredAppointments = appointments.filter((apt) => apt.date === selectedDateStr && apt.status !== "Cancelado")

    const isDayAvailable = (date: Date): boolean => {
        const dateStr = format(date, "yyyy-MM-dd")
        const dayOfWeek = date.getDay().toString()
        const mcDateExceptions = medicalCenter.dateExceptions || {}

        if (dateStr in mcDateExceptions) {
            return mcDateExceptions[dateStr].enabled
        }
        return medicalCenter.workingDays[dayOfWeek]?.enabled || false
    }

    const statuses = [
        {value: "Agendado", label: "Agendado", color: "bg-yellow-100 text-yellow-700 border border-yellow-300"},
        {value: "Ausente", label: "Ausente", color: "bg-red-100 text-red-700 border border-red-300"},
        {value: "Recepcionado", label: "Recepcionado", color: "bg-violet-200 text-violet-800 border border-violet-300"},
        {value: "En Atención", label: "En Atención", color: "bg-blue-100 text-blue-700 border border-blue-300"},
        {value: "Atendido", label: "Atendido", color: "bg-green-100 text-green-700 border border-green-300"},
    ]

    return (
        <div className="flex-1 overflow-auto" onClick={handleOutsideClick}>
            <div className="bg-white rounded-lg border border-black">
                <div className="p-5 border-b">
                    <h2 className="text-lg font-semibold text-center">
                        {selectedDate.toLocaleDateString("es-ES", {
                            weekday: "long",
                            day: "numeric",
                            month: "long",
                            year: "numeric",
                        })}
                    </h2>
                </div>

                <div className="grid grid-cols-[80px_repeat(7,1fr)] border-b sticky top-0 bg-white z-10">
                    <div className="p-4 text-sm font-medium text-gray-500">Hora</div>
                    {doctorColumns.map((doctor, i) => (
                        <div
                            key={doctor?.id || i}
                            className={`p-4 text-sm font-medium text-center border-l ${
                                doctor ? "text-black hover:cursor-pointer" : "text-gray-300 bg-gray-100 cursor-not-allowed"
                            }`}
                            onClick={() => {
                                if (doctor) router.push(`/plataforma/establecimiento/${medicalCenter.id}/${doctor.id}`)
                            }}
                        >
                            {doctor?.name || "No disponible"}
                        </div>
                    ))}
                </div>

                <div className="grid grid-cols-[80px_repeat(7,1fr)] relative">
                    {/* Current time indicator line */}
                    {(() => {
                        const timePosition = getCurrentTimePosition();
                        if (!timePosition) return null;

                        const {position, hasSobreturno} = timePosition;
                        const hours = currentTime.getHours();
                        const minutes = currentTime.getMinutes();
                        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

                        return (
                            <div
                                className="absolute left-0 pointer-events-none"
                                style={{
                                    top: `${position}px`,
                                    width: '100%',
                                    zIndex: 10,
                                }}
                            >
                                {/* Horizontal line */}
                                <div className="relative w-full">
                                    <div
                                        className={`absolute top-0 left-0 right-0 h-[1.5px] ${hasSobreturno ? 'bg-white/90' : 'bg-blue-500/70'}`}
                                        style={{
                                            boxShadow: hasSobreturno ? '0 0 3px rgba(255, 255, 255, 0.7)' : '0 0 3px rgba(59, 130, 246, 0.5)'
                                        }}
                                    />
                                </div>

                                {/* Time badge */}
                                <div
                                    className="absolute top-0 left-0 transform -translate-y-1/2 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded shadow-sm font-medium"
                                >
                                    {formattedTime}
                                </div>
                            </div>
                        );
                    })()}

                    {hours.map((hour) => (
                        <React.Fragment key={hour}>
                            <div className="p-4 text-sm text-gray-500 border-b" style={{height: getHourSlotHeight()}}>
                                {`${hour.toString().padStart(2, "0")}:00`}
                            </div>

                            {doctorColumns.map((doctor, colIndex) => {
                                const isAvailable = doctor ? isHourAvailable(doctor, hour) : false
                                const doctorAppointments = doctor
                                    ? filteredAppointments.filter(
                                        (apt) => apt.doctorId === doctor.id && Number(apt.time.split(":")[0]) === hour
                                    )
                                    : []

                                return (
                                    <div
                                        key={`${hour}-${colIndex}`}
                                        className={`border-l border-b relative ${
                                            !doctor ? "text-gray-300 bg-gray-100" : isAvailable ? "bg-white" : "bg-gray-100 text-gray-400"
                                        }`}
                                        style={{height: getHourSlotHeight()}}
                                    >
                                        {doctor && isAvailable && (
                                            <div className="absolute inset-0">
                                                {Object.entries(
                                                    doctorAppointments.reduce((acc, apt) => {
                                                        const timeKey = apt.time
                                                        if (!acc[timeKey]) acc[timeKey] = []
                                                        acc[timeKey].push(apt)
                                                        return acc
                                                    }, {} as Record<string, Appointment[]>)
                                                )
                                                    .sort(([aKey], [bKey]) => {
                                                        const [ha, ma] = aKey.split(":").map(Number)
                                                        const [hb, mb] = bKey.split(":").map(Number)
                                                        return ha * 60 + ma - (hb * 60 + mb)
                                                    })
                                                    .map(([timeKey, appointments], index) => {
                                                        const isOvercrowded = appointments.length > 1
                                                        const appointment = appointments[0]

                                                        return (
                                                            <button
                                                                key={timeKey}
                                                                onClick={(e) => handleClick(e, appointment)}
                                                                className={`appointment-slot absolute left-0 right-0 p-2 text-xs rounded-sm truncate min-h-[24px] flex items-center ${
                                                                    isOvercrowded
                                                                        ? "bg-blue-500 text-white border border-blue-300"
                                                                        : appointment.status === "Agendado"
                                                                            ? "bg-yellow-100 text-yellow-700 border border-yellow-300"
                                                                            : appointment.status === "Ausente"
                                                                                ? "bg-red-100 text-red-700 border border-red-300"
                                                                                : appointment.status === "Recepcionado"
                                                                                    ? "bg-violet-200 text-violet-700 border border-violet-300"
                                                                                    : appointment.status === "En Atención"
                                                                                        ? "bg-blue-100 text-blue-700 border border-blue-300"
                                                                                        : "bg-green-100 text-green-700 border border-green-300"
                                                                } ${
                                                                    state.selectedAppointment?.id === appointment.id ? "ring-2 ring-inset ring-blue-500" : ""
                                                                }`}
                                                                style={{
                                                                    top: getAppointmentPosition(timeKey),
                                                                    height: getAppointmentHeight(appointment),
                                                                    zIndex: index + 1,
                                                                }}
                                                            >
                                <span className="truncate">
                                  {isOvercrowded
                                      ? `${appointments.length} pacientes`
                                      : `${appointment.time} - ${getPatientNameForAppointment(appointment)}`}
                                </span>
                                                            </button>
                                                        )
                                                    })}
                                            </div>
                                        )}
                                    </div>
                                )
                            })}
                        </React.Fragment>
                    ))}
                </div>
            </div>

            {state.overcrowdedMenu && (
                <OvercrowdedContextMenu
                    x={state.overcrowdedMenu.x}
                    y={state.overcrowdedMenu.y}
                    appointments={state.overcrowdedMenu.appointments}
                    onSelect={handleOvercrowdedSelect}
                    onClose={() => dispatch({type: "CLEAR_OVERCROWD_MENU"})}
                />
            )}

            {state.contextMenu && state.selectedAppointment && (
                <AppointmentContextMenu
                    x={state.contextMenu.x}
                    y={state.contextMenu.y}
                    onAction={handleContextAction}
                    onClose={() => dispatch({type: "CLEAR_CONTEXT_MENU"})}
                />
            )}

            {state.selectedAppointment && state.showStatusModal && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
                    <div className="bg-white p-4 rounded-lg shadow-lg w-[200px]">
                        <h3 className="text-lg font-semibold mb-2 text-center text-gray-800">Estado de turno</h3>
                        <ul className="space-y-1">
                            {statuses.map((status) => (
                                <li key={status.value}>
                                    <button
                                        className={`w-full text-left px-2 py-2.5 rounded-md flex items-center ${status.color} hover:opacity-80`}
                                        onClick={() => {
                                            if (state.selectedAppointment) {
                                                onStatusChange(state.selectedAppointment.id, status.value as Appointment["status"])
                                            }
                                            dispatch({type: "SET_STATUS_MODAL", show: false})
                                        }}
                                    >
                                        <Check
                                            className={`mr-2 h-4 w-4 ${
                                                state.selectedAppointment?.status === status.value ? "opacity-100" : "opacity-0"
                                            }`}
                                        />
                                        <span className="text-xs">{status.label}</span>
                                    </button>
                                </li>
                            ))}
                        </ul>
                        <Button
                            variant="outline"
                            className="mt-4 w-full border-gray-300 text-gray-700 hover:bg-gray-50"
                            onClick={() => dispatch({type: "SET_STATUS_MODAL", show: false})}
                        >
                            Cerrar
                        </Button>
                    </div>
                </div>
            )}

            {state.selectedAppointment && state.showObservations && (
                <div className="fixed inset-0 bg-black/50 z-50">
                    <AppointmentDetailsDialog
                        appointment={state.selectedAppointment}
                        isOpen={state.showObservations}
                        onClose={() => dispatch({type: "SET_OBSERVATIONS", show: false})}
                        // appointments prop removed as it's not in the component props
                        // Patient management now handled by PatientContext
                        doctors={doctors}
                    />
                </div>
            )}

            {state.selectedAppointment && state.showModifyModal && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
                    <div className="bg-white p-4 rounded-lg shadow-lg">
                        <h3 className="text-lg font-semibold mb-2 text-gray-800">Modificar turno</h3>
                        <div className="space-y-4">
                            <Calendar
                                mode="single"
                                selected={state.modifyDate}
                                onSelect={(newDate) => newDate && dispatch({
                                    type: "SET_MODIFY_MODAL",
                                    show: true,
                                    date: newDate
                                })}
                                className="rounded-md border border-gray-200"
                                locale={es}
                                weekStartsOn={1}
                                disabled={(date) => !isDayAvailable(date)}
                                initialFocus
                                defaultMonth={new Date(selectedDate)}
                            />
                            <div className="space-y-1">
                                <Label className="text-sm font-medium text-gray-700">Nuevo horario</Label>
                                <Select
                                    value={state.modifyTime}
                                    onValueChange={(value) => dispatch({
                                        type: "SET_MODIFY_MODAL",
                                        show: true,
                                        time: value
                                    })}
                                >
                                    <SelectTrigger className="border-gray-300">
                                        <SelectValue placeholder="Seleccionar horario"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        {generateTimeSlots(`${start}:00`, `${end}:00`, APPOINTMENT_DURATION).map((slot: string) => (
                                            <SelectItem key={slot} value={slot}>
                                                {slot}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex justify-end gap-2">
                                <Button
                                    variant="outline"
                                    className="border-gray-300 text-gray-700 hover:bg-gray-50"
                                    onClick={() => dispatch({type: "SET_MODIFY_MODAL", show: false})}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    className="bg-blue-600 text-white hover:bg-blue-700"
                                    onClick={() => {
                                        if (state.selectedAppointment && state.modifyDate && state.modifyTime) {
                                            onModifyAppointment(
                                                state.selectedAppointment.id,
                                                format(state.modifyDate, "yyyy-MM-dd"),
                                                state.modifyTime
                                            )
                                        }
                                        dispatch({type: "SET_MODIFY_MODAL", show: false})
                                    }}
                                >
                                    Confirmar
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {state.selectedAppointment && state.showCancelDialog && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
                    <div className="bg-white p-4 rounded-lg shadow-lg">
                        <h3 className="text-lg font-semibold text-gray-800">¿Cancelar turno?</h3>
                        <p className="text-sm text-gray-600 mt-2 mb-4">
                            Esta acción no se puede deshacer. El turno quedará disponible para otros pacientes.
                        </p>
                        <div className="flex justify-end gap-2">
                            <Button
                                variant="outline"
                                className="border-gray-300 text-gray-700 hover:bg-gray-50"
                                onClick={() => dispatch({type: "SET_CANCEL_DIALOG", show: false})}
                            >
                                No, mantener turno
                            </Button>
                            <Button
                                variant="destructive"
                                className="bg-red-600 text-white hover:bg-red-700"
                                onClick={() => {
                                    if (state.selectedAppointment) {
                                        onCancelAppointment(state.selectedAppointment.id)
                                    }
                                    dispatch({type: "SET_CANCEL_DIALOG", show: false})
                                }}
                            >
                                Sí, Cancelar turno
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}