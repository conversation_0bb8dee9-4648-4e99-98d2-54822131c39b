// Email and notification templates for appointment reminders
import { format, parseISO } from "date-fns"
import { es } from "date-fns/locale"

// Helper function to format date for display
export const formatAppointmentDate = (dateStr: string): string => {
  try {
    const date = parseISO(dateStr)
    return format(date, "EEEE d 'de' MMMM", { locale: es })
  } catch {
    return dateStr
  }
}

// Helper function to format time for display
export const formatAppointmentTime = (timeStr: string): string => {
  return timeStr
}

// Email template for appointment confirmation
export const getAppointmentConfirmationEmailTemplate = (
  patientName: string,
  doctorName: string,
  date: string,
  time: string,
  medicalCenterName: string,
  medicalCenterAddress?: string
): { subject: string; htmlContent: string } => {
  const formattedDate = formatAppointmentDate(date)
  const formattedTime = formatAppointmentTime(time)

  const subject = `Confirmación de turno - ${doctorName}`

  const htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Confirmación de Turno</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #F8FAFC;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
      <td style="padding: 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #FFFFFF; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header -->
          <tr>
            <td style="background: linear-gradient(135deg, #0EA5E9 0%, #0284C7 100%); padding: 40px; text-align: center; border-radius: 8px 8px 0 0;">
              <h1 style="margin: 0; color: #FFFFFF; font-size: 28px; font-weight: bold;">¡Turno Confirmado!</h1>
              <p style="margin: 8px 0 0 0; color: #E0F2FE; font-size: 16px;">Tu turno médico fue agendado exitosamente</p>
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td style="padding: 40px;">
              <p style="margin: 0 0 24px 0; font-size: 16px; color: #374151;">Hola <strong>${patientName}</strong>,</p>

              <p style="margin: 0 0 32px 0; font-size: 16px; color: #374151; line-height: 1.6;">
                Tu turno médico fue confirmado. A continuación vas a encontrar todos los detalles de tu turno:
              </p>

              <!-- Appointment Details -->
              <div style="background-color: #F1F5F9; border-radius: 8px; padding: 24px; margin: 0 0 32px 0;">
                <h2 style="margin: 0 0 16px 0; font-size: 20px; color: #1E293B; font-weight: 600;">Detalles del Turno</h2>

                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B; width: 140px;">
                      <strong>Profesional:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${doctorName}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Fecha:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${formattedDate}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Hora:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${formattedTime}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Centro Médico:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${medicalCenterName}
                      ${medicalCenterAddress ? `<br><span style="color: #64748B; font-size: 14px;">${medicalCenterAddress}</span>` : ''}
                    </td>
                  </tr>
                </table>
              </div>

              <!-- Important Notes -->
              <div style="background-color: #FEF3C7; border-left: 4px solid #F59E0B; padding: 16px; margin: 0 0 32px 0;">
                <h3 style="margin: 0 0 8px 0; font-size: 16px; color: #92400E; font-weight: 600;">Recordatorios Importantes</h3>
                <ul style="margin: 0; padding-left: 20px; color: #92400E; font-size: 14px;">
                  <li style="margin-bottom: 4px;">Llegá 15 minutos antes de tu turno</li>
                  <li style="margin-bottom: 4px;">Traé tu documento de identidad y credencial médica</li>
                  <li>Te vamos a enviar recordatorios antes de tu turno</li>
                </ul>
              </div>

              <p style="margin: 0 0 16px 0; font-size: 16px; color: #374151; line-height: 1.6;">
                Si necesitás cancelar o reprogramar tu turno, por favor contactá al centro médico con anticipación.
              </p>

              <p style="margin: 0; font-size: 16px; color: #374151;">
                ¡Esperamos verte pronto!<br>
                <strong>Equipo de Turnera</strong>
              </p>
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td style="background-color: #F1F5F9; padding: 24px 40px; text-align: center; border-top: 1px solid #E2E8F0; border-radius: 0 0 8px 8px;">
              <p style="margin: 0; font-size: 14px; color: #64748B;">Este es un correo electrónico automático. Por favor, no responda a este mensaje.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
  `

  return { subject, htmlContent }
}

// Email template for same-day appointment confirmation
export const getSameDayAppointmentConfirmationEmailTemplate = (
  patientName: string,
  doctorName: string,
  date: string,
  time: string,
  medicalCenterName: string,
  medicalCenterAddress?: string
): { subject: string; htmlContent: string } => {
  const formattedDate = formatAppointmentDate(date)
  const formattedTime = formatAppointmentTime(time)

  const subject = `Confirmación de turno HOY - ${doctorName}`

  const htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Confirmación de Turno - HOY</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #F8FAFC;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
      <td style="padding: 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #FFFFFF; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header -->
          <tr>
            <td style="background: linear-gradient(135deg, #10B981 0%, #059669 100%); padding: 40px; text-align: center; border-radius: 8px 8px 0 0;">
              <h1 style="margin: 0; color: #FFFFFF; font-size: 28px; font-weight: bold;">¡Turno Confirmado para HOY!</h1>
              <p style="margin: 8px 0 0 0; color: #D1FAE5; font-size: 16px;">Tu turno médico es hoy</p>
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td style="padding: 40px;">
              <p style="margin: 0 0 24px 0; font-size: 16px; color: #374151;">Hola <strong>${patientName}</strong>,</p>

              <p style="margin: 0 0 32px 0; font-size: 16px; color: #374151; line-height: 1.6;">
                Tu turno médico fue confirmado para <strong>HOY</strong>. A continuación vas a encontrar todos los detalles de tu turno:
              </p>

              <!-- Appointment Details -->
              <div style="background-color: #ECFDF5; border-radius: 8px; padding: 24px; margin: 0 0 32px 0; border: 2px solid #10B981;">
                <h2 style="margin: 0 0 16px 0; font-size: 20px; color: #065F46; font-weight: 600;">Detalles del Turno - HOY</h2>

                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #065F46; width: 140px;">
                      <strong>Profesional:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${doctorName}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #065F46;">
                      <strong>Fecha:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      <strong>HOY - ${formattedDate}</strong>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #065F46;">
                      <strong>Hora:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      <strong>${formattedTime}</strong>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #065F46;">
                      <strong>Centro Médico:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${medicalCenterName}
                      ${medicalCenterAddress ? `<br><span style="color: #64748B; font-size: 14px;">${medicalCenterAddress}</span>` : ''}
                    </td>
                  </tr>
                </table>
              </div>

              <!-- Important Notes for Same Day -->
              <div style="background-color: #DBEAFE; border-left: 4px solid #3B82F6; padding: 16px; margin: 0 0 32px 0;">
                <h3 style="margin: 0 0 8px 0; font-size: 16px; color: #1E40AF; font-weight: 600;">📅 Recordatorio - Tu turno es HOY</h3>
                <ul style="margin: 0; padding-left: 20px; color: #1E40AF; font-size: 14px;">
                  <li style="margin-bottom: 4px;">Llegá 15 minutos antes de tu turno</li>
                  <li style="margin-bottom: 4px;">Traé tu documento de identidad y credencial médica</li>
                  <li>Como tu turno es hoy, no vas a recibir recordatorios adicionales</li>
                </ul>
              </div>

              <p style="margin: 0 0 16px 0; font-size: 16px; color: #374151; line-height: 1.6;">
                Si necesitás cancelar o reprogramar tu turno, por favor contactá al centro médico inmediatamente.
              </p>

              <p style="margin: 0; font-size: 16px; color: #374151;">
                ¡Te esperamos hoy!<br>
                <strong>Equipo de Turnera</strong>
              </p>
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td style="background-color: #F1F5F9; padding: 24px 40px; text-align: center; border-top: 1px solid #E2E8F0; border-radius: 0 0 8px 8px;">
              <p style="margin: 0; font-size: 14px; color: #64748B;">Este es un correo electrónico automático. Por favor, no responda a este mensaje.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
  `

  return { subject, htmlContent }
}

// Email template for appointment reminder (1 day before)
export const getAppointmentReminderEmailTemplate = (
  patientName: string,
  doctorName: string,
  date: string,
  time: string,
  medicalCenterName: string,
  medicalCenterAddress?: string
): { subject: string; htmlContent: string } => {
  const formattedDate = formatAppointmentDate(date)
  const formattedTime = formatAppointmentTime(time)

  const subject = `Recordatorio: Turno mañana con ${doctorName}`

  const htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Recordatorio de Turno</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #F8FAFC;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
      <td style="padding: 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #FFFFFF; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header -->
          <tr>
            <td style="background: linear-gradient(135deg, #10B981 0%, #059669 100%); padding: 40px; text-align: center; border-radius: 8px 8px 0 0;">
              <h1 style="margin: 0; color: #FFFFFF; font-size: 28px; font-weight: bold;">Recordatorio de Turno</h1>
              <p style="margin: 8px 0 0 0; color: #D1FAE5; font-size: 16px;">Tu cita médica es mañana</p>
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td style="padding: 40px;">
              <p style="margin: 0 0 24px 0; font-size: 16px; color: #374151;">Hola <strong>${patientName}</strong>,</p>

              <p style="margin: 0 0 32px 0; font-size: 16px; color: #374151; line-height: 1.6;">
                Te recordamos que <strong>mañana</strong> tenés un turno médico programado:
              </p>

              <!-- Appointment Details -->
              <div style="background-color: #F1F5F9; border-radius: 8px; padding: 24px; margin: 0 0 32px 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B; width: 140px;">
                      <strong>Profesional:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${doctorName}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Fecha:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${formattedDate}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Hora:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${formattedTime}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Centro Médico:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${medicalCenterName}
                      ${medicalCenterAddress ? `<br><span style="color: #64748B; font-size: 14px;">${medicalCenterAddress}</span>` : ''}
                    </td>
                  </tr>
                </table>
              </div>

              <!-- Reminder -->
              <div style="background-color: #DBEAFE; border-left: 4px solid #3B82F6; padding: 16px; margin: 0 0 24px 0;">
                <p style="margin: 0; font-size: 16px; color: #1E40AF; font-weight: 600;">
                  📅 Acordate de llegar 15 minutos antes de tu turno
                </p>
              </div>

              <p style="margin: 0; font-size: 16px; color: #374151;">
                ¡Te esperamos mañana!<br>
                <strong>Equipo de Turnera</strong>
              </p>
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td style="background-color: #F1F5F9; padding: 24px 40px; text-align: center; border-top: 1px solid #E2E8F0; border-radius: 0 0 8px 8px;">
              <p style="margin: 0; font-size: 14px; color: #64748B;">Este es un correo electrónico automático. Por favor, no responda a este mensaje.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
  `

  return { subject, htmlContent }
}

// Email template for appointment cancellation
export const getAppointmentCancellationEmailTemplate = (
  patientName: string,
  doctorName: string,
  date: string,
  time: string,
  medicalCenterName: string,
  reason?: string
): { subject: string; htmlContent: string } => {
  const formattedDate = formatAppointmentDate(date)
  const formattedTime = formatAppointmentTime(time)

  const subject = `Turno cancelado - ${doctorName}`

  const htmlContent = `
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Turno Cancelado</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #F8FAFC;">
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
      <td style="padding: 40px 20px;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #FFFFFF; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">

          <!-- Header -->
          <tr>
            <td style="background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%); padding: 40px; text-align: center; border-radius: 8px 8px 0 0;">
              <h1 style="margin: 0; color: #FFFFFF; font-size: 28px; font-weight: bold;">Turno Cancelado</h1>
              <p style="margin: 8px 0 0 0; color: #FEE2E2; font-size: 16px;">Lamentamos informarte sobre la cancelación</p>
            </td>
          </tr>

          <!-- Content -->
          <tr>
            <td style="padding: 40px;">
              <p style="margin: 0 0 24px 0; font-size: 16px; color: #374151;">Hola <strong>${patientName}</strong>,</p>

              <p style="margin: 0 0 32px 0; font-size: 16px; color: #374151; line-height: 1.6;">
                Lamentamos informarte que tu turno médico fue cancelado:
              </p>

              <!-- Appointment Details -->
              <div style="background-color: #FEF2F2; border-radius: 8px; padding: 24px; margin: 0 0 32px 0; border: 1px solid #FECACA;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B; width: 140px;">
                      <strong>Profesional:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${doctorName}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Fecha:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${formattedDate}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Hora:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${formattedTime}
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Centro Médico:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${medicalCenterName}
                    </td>
                  </tr>
                  ${reason ? `
                  <tr>
                    <td style="padding: 8px 0; font-size: 16px; color: #64748B;">
                      <strong>Motivo:</strong>
                    </td>
                    <td style="padding: 8px 0; font-size: 16px; color: #1E293B;">
                      ${reason}
                    </td>
                  </tr>
                  ` : ''}
                </table>
              </div>

              <p style="margin: 0 0 24px 0; font-size: 16px; color: #374151; line-height: 1.6;">
                Para reagendar tu turno, por favor contactá directamente al centro médico. Disculpá las molestias ocasionadas.
              </p>

              <p style="margin: 0; font-size: 16px; color: #374151;">
                Saludos cordiales,<br>
                <strong>Equipo de Turnera</strong>
              </p>
            </td>
          </tr>

          <!-- Footer -->
          <tr>
            <td style="background-color: #F1F5F9; padding: 24px 40px; text-align: center; border-top: 1px solid #E2E8F0; border-radius: 0 0 8px 8px;">
              <p style="margin: 0; font-size: 14px; color: #64748B;">Este es un correo electrónico automático. Por favor, no responda a este mensaje.</p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
  `

  return { subject, htmlContent }
}