"use client"

import { useState, useEffect } from "react"
import { mockEmailProvider, MockEmail } from "@/services/email"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Trash2, RefreshCw, Eye, Mail, MailOpen, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

export default function EmailsPage() {
  const [emails, setEmails] = useState<MockEmail[]>([])
  const [selectedEmail, setSelectedEmail] = useState<MockEmail | null>(null)

  useEffect(() => {
    // Load emails from storage
    loadEmails()
  }, [])

  const loadEmails = () => {
    const storedEmails = mockEmailProvider.getMockEmails()
    // Sort emails by sentAt date, newest first
    const sortedEmails = [...storedEmails].sort((a, b) =>
      new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime()
    )
    setEmails(sortedEmails)
  }

  const markAsRead = (id: string) => {
    mockEmailProvider.markEmailAsRead(id)
    loadEmails()
  }

  const clearEmails = () => {
    mockEmailProvider.clearAllEmails()
    setEmails([])
    setSelectedEmail(null)
    toast.success("Todos los emails han sido eliminados")
  }

  const viewEmail = (email: MockEmail) => {
    setSelectedEmail(email)
    if (!email.read) {
      markAsRead(email.id)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href="/dev" passHref>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dev Tools
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Email Preview (Desarrollo)</h1>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadEmails}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" className="text-red-600" onClick={clearEmails}>
            <Trash2 className="h-4 w-4 mr-2" />
            Borrar Todos
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Emails Enviados</CardTitle>
            </CardHeader>
            <CardContent>
              {emails.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No hay emails enviados</p>
              ) : (
                <ul className="space-y-2">
                  {emails.map(email => (
                    <li
                      key={email.id}
                      className={`p-3 rounded-md cursor-pointer hover:bg-gray-100 ${!email.read ? 'bg-blue-50' : ''}`}
                      onClick={() => viewEmail(email)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            {email.read ? (
                              <MailOpen className="h-4 w-4 text-gray-400 mr-2" />
                            ) : (
                              <Mail className="h-4 w-4 text-blue-500 mr-2" />
                            )}
                            <p className={`text-sm font-medium ${!email.read ? 'font-bold' : ''}`}>{email.to}</p>
                          </div>
                          <p className="text-sm">{email.subject}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(email.sentAt).toLocaleString()}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm" onClick={(e) => {
                          e.stopPropagation();
                          viewEmail(email);
                        }}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Vista Previa</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedEmail ? (
                <div>
                  <div className="mb-4 p-4 bg-gray-50 rounded-md">
                    <p><strong>Para:</strong> {selectedEmail.to}</p>
                    <p><strong>Asunto:</strong> {selectedEmail.subject}</p>
                    <p><strong>Enviado:</strong> {new Date(selectedEmail.sentAt).toLocaleString()}</p>
                  </div>
                  <div className="border rounded-md p-4">
                    <iframe
                      srcDoc={selectedEmail.htmlContent}
                      className="w-full min-h-[500px]"
                      title="Email Preview"
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <Mail className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Seleccione un email para ver su contenido</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
