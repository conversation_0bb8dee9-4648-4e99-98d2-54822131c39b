"use client"

import { use<PERSON>ontext, useState} from "react"
import { <PERSON><PERSON><PERSON>x<PERSON> } from "@/contexts/DoctorContext"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Plus, X } from "lucide-react"
import { SPECIALTIES, Specialty } from "@/data/specialties"

export default function DoctorInfoTab() {
  const { editedConfig, setEditedConfig } = useContext(DoctorContext)

  const [showSpecialtyPopup, setShowSpecialtyPopup] = useState(false)
  const [selectedSpecialties, setSelectedSpecialties] = useState<Specialty[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  const handleSpecialtySelect = (specialty: Specialty) => {
    setSelectedSpecialties((prev) =>
      prev.includes(specialty) ? prev.filter((s) => s !== specialty) : [...prev, specialty]
    )
  }

  const confirmSpecialties = () => {
    // Merge new selections with existing specialties, avoiding duplicates
    const updatedSpecialties = [...new Set([...editedConfig!.specialties, ...selectedSpecialties])]
    setEditedConfig({ ...editedConfig!, specialties: updatedSpecialties })
    setShowSpecialtyPopup(false)
    setSelectedSpecialties([]) // Reset for next selection
  }

  const cancelSpecialtySelection = () => {
    setShowSpecialtyPopup(false)
    setSelectedSpecialties([]) // Reset selections
    setSearchTerm("") // Reset search
  }

  const handleRemoveSpecialty = (specialtyToRemove: Specialty) => {
    if (!editedConfig) return
    const updatedSpecialties = editedConfig.specialties.filter(specialty => specialty !== specialtyToRemove)
    setEditedConfig({ ...editedConfig, specialties: updatedSpecialties })
  }

  const filteredSpecialties = SPECIALTIES.filter(specialty =>
    specialty.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (!editedConfig) return null

  return (
    <>
      <Card className="border-0 shadow-none">
        <CardHeader className="p-0 pb-4">
          <CardTitle className="text-lg text-gray-900">Información Básica</CardTitle>
        </CardHeader>
        <CardContent className="p-0 space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre Completo</Label>
              <Input
                id="name"
                value={editedConfig.name || ""}
                disabled
                className="bg-gray-100 text-gray-700"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mn">M.N. (Matrícula Nacional)</Label>
              <Input
                id="mn"
                value={editedConfig.mn || ""}
                disabled
                className="bg-gray-100 text-gray-700"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={editedConfig.email || ""}
              disabled={!!editedConfig.email}
              onChange={(e) => setEditedConfig({ ...editedConfig, email: e.target.value || undefined })}
              placeholder="Ej: <EMAIL>"
              className={editedConfig.email ? "bg-gray-100 text-gray-700" : ""}
            />
          </div>
          <div className="space-y-2">
            <Label>Especialidades</Label>
            <Button
              type="button"
              onClick={() => setShowSpecialtyPopup(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Seleccionar Especialidades
            </Button>
            <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 min-h-[80px] mt-2">
              {editedConfig.specialties.length === 0 ? (
                <div className="text-center py-2 text-gray-500">No hay especialidades configuradas</div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {editedConfig.specialties.map((specialty) => (
                    <Badge
                      key={specialty}
                      className="px-3 py-1 bg-white text-blue-700 hover:bg-blue-100 border-blue-200 flex items-center gap-1"
                    >
                      {specialty}
                      <button onClick={() => handleRemoveSpecialty(specialty)}>
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Specialty Selection Popup */}
      {showSpecialtyPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4 max-h-96 flex flex-col">
            <div className="p-4 border-b">
              <h3 className="text-lg font-semibold text-gray-900">Seleccionar Especialidades</h3>
            </div>
            
            <div className="p-4 border-b">
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Buscar especialidades..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-24"
                />
                {searchTerm && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => setSearchTerm("")}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 px-3 text-xs text-gray-500 hover:text-gray-700"
                  >
                    Limpiar
                  </Button>
                )}
              </div>
            </div>
            
            <div className="flex-1 overflow-auto p-4">
              <div className="space-y-1">
                {filteredSpecialties.map((specialty) => (
                  <div
                    key={specialty}
                    className={`p-3 text-sm cursor-pointer rounded-md transition-colors ${
                      selectedSpecialties.includes(specialty) || editedConfig.specialties.includes(specialty)
                        ? "bg-blue-500 text-white hover:bg-blue-600"
                        : "hover:bg-gray-100"
                    }`}
                    onClick={() => handleSpecialtySelect(specialty)}
                  >
                    {specialty}
                  </div>
                ))}
              </div>
            </div>
            
            <div className="p-4 border-t bg-gray-50 flex gap-2">
              <Button
                variant="outline"
                onClick={cancelSpecialtySelection}
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={confirmSpecialties}
                className="flex-1 bg-blue-500 hover:bg-blue-600 text-white"
              >
                Confirmar
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}