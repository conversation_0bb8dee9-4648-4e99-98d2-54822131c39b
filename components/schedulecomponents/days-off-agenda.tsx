"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { startOfDay, isBefore } from "date-fns"
import type { Appointment } from "@/types/scheduler"
import { getWeeksDifference, REFERENCE_DATE } from '@/utils/scheduleUtils'

interface DaysOffAgendaProps {
  doctorId: string
  medicalCenterId: string
  appointments: Record<string, Appointment[]>
  doctorWorkingDays: {
    [key: string]: {
      enabled: boolean;
      hours: { start: string; end: string }[];
      weeksFrequency?: number
    }
  }
  dateExceptions?: { [date: string]: { enabled: boolean; hours?: { start: string; end: string }[] } }
  onSave: (selectedDaysOff: string[], daysToRemove: string[]) => void
}

export function DaysOffAgenda({
  doctorId,
  medicalCenterId,
  appointments,
  doctorWorkingDays,
  dateExceptions,
  onSave,
}: DaysOffAgendaProps) {
  // Ensure we start with the current date
  const [daysOffDate, setDaysOffDate] = useState<Date>(new Date())
  // Get the initial days off from dateExceptions
  const initialDaysOff = useMemo(() => {
    if (!dateExceptions) return []

    return Object.entries(dateExceptions)
      .filter(([, value]) => value.enabled === false)
      .map(([date]) => date)
  }, [dateExceptions])

  // Track currently selected days off
  const [selectedDaysOff, setSelectedDaysOff] = useState<string[]>(initialDaysOff)

  // Track days that were originally off but are now being reinstated (removed)
  const [daysToRemove, setDaysToRemove] = useState<string[]>([])

  // Check if there are any changes to the current configuration
  const hasChanges = () => {
    // Check if any days have been added or removed
    if (selectedDaysOff.length !== initialDaysOff.length) return true

    // Check if any days in selectedDaysOff are not in initialDaysOff
    for (const day of selectedDaysOff) {
      if (!initialDaysOff.includes(day)) return true
    }

    // Check if any days are marked for removal
    if (daysToRemove.length > 0) return true

    // No changes detected
    return false
  }

  const hasAppointmentsOnDays = (days: string[]) => {
    return days.some(date => {
      const apts = appointments[date]?.filter(apt => apt.doctorId === doctorId && apt.medicalCenterId === medicalCenterId && apt.status !== "Cancelado") || []
      return apts.length > 0
    })
  }

  // Helper function to check if a date is before today (not including today)
  const isBeforeToday = (date: Date): boolean => {
    const today = startOfDay(new Date())
    return isBefore(date, today)
  }

  const isDateAvailable = (date: Date) => {
    const dateStr = date.toISOString().split("T")[0]
    const dayOfWeek = date.getDay().toString()

    if (dateExceptions && dateStr in dateExceptions) {
      return dateExceptions[dateStr].enabled
    }

    const dayConfig = doctorWorkingDays[dayOfWeek]
    if (!dayConfig?.enabled) return false

    // Use shared getWeeksDifference and REFERENCE_DATE
    const frequency = dayConfig.weeksFrequency || 1
    const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE)
    return weeksSinceReference % frequency === 0
  }

  const handleDaysOffSave = () => {
    // Filter out days that are in daysToRemove from selectedDaysOff
    const finalSelectedDaysOff = selectedDaysOff.filter(date => !daysToRemove.includes(date))

    if (hasAppointmentsOnDays(finalSelectedDaysOff)) {
      // Let the parent component handle the warning and cancellation
      onSave(finalSelectedDaysOff, daysToRemove)
    } else {
      // No appointments to cancel, just update
      onSave(finalSelectedDaysOff, daysToRemove)
    }
  }

  const renderCalendarDays = (currentDate: Date, selectedDates: string[], onDateClick: (date: Date) => void) => {
    const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
    const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
    const startingDay = (firstDay.getDay() + 6) % 7

    return [
      ...Array.from({ length: startingDay }).map((_, i) => (
        <div key={`empty-${i}`} className="p-2" />
      )),
      ...Array.from({ length: daysInMonth }, (_, i) => {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), i + 1)
        const dateStr = date.toISOString().split("T")[0]
        const hasAppointments = appointments[dateStr]?.some(apt => apt.doctorId === doctorId && apt.medicalCenterId === medicalCenterId && apt.status !== "Cancelado") || false
        const isSelected = selectedDates.includes(dateStr)
        const isBeingReinstated = daysToRemove.includes(dateStr)
        const isPastDate = isBeforeToday(date) // Use our helper to check if date is before today

        // For visual appearance, consider the original availability
        // but also whether the day is being reinstated
        const isAvailable = isDateAvailable(date) || isBeingReinstated

        // Disable days that are:
        // 1. Not available AND not already selected, OR
        // 2. Before today (not including today)
        const isDisabled = (!isAvailable && !isSelected) || isPastDate

        return (
          <button
            key={i}
            onClick={() => onDateClick(date)}
            disabled={isDisabled}
            className={`
              group w-8 h-8 p-0 rounded-full relative
              ${isPastDate ? "text-gray-300 cursor-not-allowed" :
                !isAvailable && !isBeingReinstated ? "text-gray-300" : ""}
              ${isSelected ? "bg-red-500 text-white" :
                isBeingReinstated ? "text-blue-800 font-medium" :
                isPastDate ? "" : "hover:bg-blue-50"}
              ${isDisabled ? "cursor-not-allowed" : ""}
            `}
          >
            <div className="w-full h-full flex items-center justify-center">{i + 1}</div>
            {hasAppointments && (
              <div
                className={`
                  absolute left-1/2 -translate-x-1/2
                  w-[0.375rem] h-[0.375rem] rounded-full
                  transition-all duration-200
                  ${isSelected ? "-bottom-[0.625rem] bg-white" : "-bottom-[0.125rem] bg-blue-500 group-hover:-bottom-[0.625rem]"}
                `}
              />
            )}
          </button>
        )
      })
    ]
  }

  const handleDaysOffDateClick = (date: Date) => {
    // Don't allow selecting dates before today (not including today)
    if (isBeforeToday(date)) {
      return
    }

    const dateStr = date.toISOString().split("T")[0]

    // Check if this date was originally off (in initialDaysOff)
    const wasOriginallyOff = initialDaysOff.includes(dateStr)

    if (selectedDaysOff.includes(dateStr)) {
      // Removing a day that was selected
      setSelectedDaysOff(prev => prev.filter(d => d !== dateStr))

      // If it was originally off, add it to daysToRemove
      if (wasOriginallyOff) {
        setDaysToRemove(prev => [...prev, dateStr])
      }
    } else {
      // Adding a new day
      setSelectedDaysOff(prev => [...prev, dateStr])

      // If it was originally off but marked for removal, remove it from daysToRemove
      if (wasOriginallyOff) {
        setDaysToRemove(prev => prev.filter(d => d !== dateStr))
      }
    }
  }

  return (
    <div className="space-y-4 w-full transition-all duration-300 ease-in-out">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Calendar */}
        <div className="border rounded-lg p-4 bg-white border-gray-200 shadow-sm h-fit">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium">
              {daysOffDate.toLocaleString('es', { month: 'long', year: 'numeric' })}
            </span>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setDaysOffDate(prev => {
                  const newDate = new Date(prev)
                  newDate.setMonth(newDate.getMonth() - 1)
                  return newDate
                })}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setDaysOffDate(prev => {
                  const newDate = new Date(prev)
                  newDate.setMonth(newDate.getMonth() + 1)
                  return newDate
                })}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="grid grid-cols-7 gap-2 text-sm mb-2">
            {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
              <div key={i} className="flex justify-center items-center w-8 mx-auto font-medium">{day}</div>
            ))}
          </div>
          <div className="grid grid-cols-7 gap-2 text-sm h-[250px]">
            {renderCalendarDays(daysOffDate, selectedDaysOff, handleDaysOffDateClick)}
          </div>
        </div>

        {/* Right Column - Instructions and Save Button */}
        <div className="flex flex-col h-full">
          <div className="border rounded-lg p-4 bg-white border-gray-200 shadow-sm mb-4 flex-grow">
            <div className="mb-4">
              <h3 className="text-base font-medium">Días sin agenda</h3>
              <p className="text-sm text-gray-600 mt-2">
                Seleccione los días en los que el profesional no atenderá.
              </p>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-full bg-red-500"></div>
                <span className="text-sm">Día sin agenda</span>
              </div>
              <div className="flex items-center gap-2 mt-[5px]">
                <div className="w-4 h-4 rounded-full bg-blue-50 border border-blue-200"></div>
                <span className="text-sm">Día disponible</span>
              </div>
              <div className="flex items-center gap-2 mt-[5px]">
                <div className="relative w-4 h-6">
                  <div className="absolute top-1/2 -translate-y-1/2 left-0 w-4 h-4 rounded-full bg-white border border-gray-300"></div>
                  <div className="absolute top-[1.55rem] left-1/2 -translate-x-1/2 w-[0.375rem] h-[0.375rem] rounded-full bg-blue-500"></div>
                </div>
                <span className="text-sm">Día con turnos</span>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-center mt-auto">
            <Button
              className="bg-blue-500 hover:bg-blue-600 w-full py-6"
              onClick={handleDaysOffSave}
              disabled={!hasChanges()}
            >
              Guardar días sin agenda
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
