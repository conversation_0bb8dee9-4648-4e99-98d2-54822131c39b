import {MedicalCenterRoleForEmployeeUser} from "@/types/MedicalCenter/medicalCenterRoleForEmployeeUser";

export enum UserRole {
    TURNERA_USER = "TURNERA_USER",
    EMPLOYEE_USER = "EMPLOYEE_USER",
    PROFESSIONAL_USER = "PROFESSIONAL_USER",
}

export interface TurneraUserInformation {
    email: string;
    phone: string;
    state: string;
}

export interface TurneraPatientInformation {
    id: number;
    name: string;
    surname: string;
    identification_number: string;
    phone: string;
    email: string;
    attendance_percentage: number;
    date_of_birth: Date;
    plan: string;
}

export interface TurneraProfessionalInformation {
    identification_number: string;
    medical_license: string;
}

export interface User {
    idFromRole: Map<UserRole, number>;
    name: string;
    surname: string;
    roles: UserRole[];
    medicalCenters?: MedicalCenterRoleForEmployeeUser[];
    turneraUserInformation?: TurneraUserInformation;
    turneraPatientInformation?: TurneraPatientInformation[];
    turneraProfessionalInformation?: TurneraProfessionalInformation;
    auth0Sub: string;
}



