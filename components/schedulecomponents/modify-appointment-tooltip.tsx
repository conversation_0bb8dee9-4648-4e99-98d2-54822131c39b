"use client"

import { useState } from "react"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { es } from "date-fns/locale"

const findNextAvailableDate = (currentDate: Date, availableDates: Date[]): Date => {
  const sortedDates = availableDates.sort((a, b) => a.getTime() - b.getTime())
  return sortedDates.find((date) => date >= currentDate) || currentDate
}

interface ModifyAppointmentTooltipProps {
  onConfirm: (date: Date, time: string) => void
  currentDate: Date
  currentTime: string
  availableDates: Date[]
  availableTimeSlots: string[]
}

export function ModifyAppointmentTooltip({
  onConfirm,
  currentDate,
  currentTime,
  availableDates,
  availableTimeSlots,
}: ModifyAppointmentTooltipProps) {
  const [date, setDate] = useState<Date | undefined>(() => findNextAvailableDate(currentDate, availableDates))
  const [time, setTime] = useState(currentTime)
  const [isOpen, setIsOpen] = useState(false)

  const handleConfirm = () => {
    if (date) {
      // Ensure we're working with a valid date object
      const confirmedDate = new Date(date)
      if (!isNaN(confirmedDate.getTime())) {
        onConfirm(confirmedDate, time)
        setIsOpen(false)
      } else {
        console.error("Invalid date selected")
      }
    } else {
      console.error("No date selected")
    }
  }

  const isDateAvailable = (date: Date) => {
    return availableDates.some(
      (availableDate) =>
        availableDate.getDate() === date.getDate() &&
        availableDate.getMonth() === date.getMonth() &&
        availableDate.getFullYear() === date.getFullYear(),
    )
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline">Modificar turno</Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-4 space-y-4">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            className="rounded-md border"
            locale={es}
            weekStartsOn={1}
            disabled={(date) => !isDateAvailable(date)}
            initialFocus
            defaultMonth={findNextAvailableDate(currentDate, availableDates)}
          />
          <div className="space-y-1">
            <Label>Nuevo horario</Label>
            <Select value={time || ""} onValueChange={setTime}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar horario" />
              </SelectTrigger>
              <SelectContent>
                {availableTimeSlots.map((slot) => (
                  <SelectItem key={slot} value={slot}>
                    {slot}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleConfirm}>Confirmar</Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

