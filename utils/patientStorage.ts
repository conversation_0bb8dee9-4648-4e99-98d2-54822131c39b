import {PatientResponse} from "@/types/patient/patientResponse";

export interface StoredPatientsData {
    medicalCenterId: number
    patients: PatientResponse[]
    timestamp: number
    employeeUserId: number
}

/**
 * Store patients data in localStorage for a specific medical center
 */
export const storePatientsForMedicalCenter = (
    medicalCenterId: number,
    patients: PatientResponse[],
    employeeUserId: number
): boolean => {
    try {
        const patientsData: StoredPatientsData = {
            medicalCenterId,
            patients,
            timestamp: Date.now(),
            employeeUserId
        }

        const key = `medical-center-patients-${medicalCenterId}`
        localStorage.setItem(key, JSON.stringify(patientsData))

        console.log('Patients stored in localStorage for medical center:', medicalCenterId)
        return true
    } catch (error) {
        console.error('Error storing patients in localStorage:', error)
        return false
    }
}

/**
 * Retrieve patients data from localStorage for a specific medical center
 */
export const getPatientsForMedicalCenter = (
    medicalCenterId: number,
    employeeUserId?: number
): PatientResponse[] => {
    try {
        const key = `medical-center-patients-${medicalCenterId}`
        const storedData = localStorage.getItem(key)

        if (!storedData) {
            console.log('No stored patients found for medical center:', medicalCenterId)
            return []
        }

        const parsedData: StoredPatientsData = JSON.parse(storedData)

        // Validate the stored data
        if (parsedData.medicalCenterId !== medicalCenterId) {
            console.warn('Medical center ID mismatch in stored data')
            return []
        }

        // Optional: Check if the data is from the same employee user
        if (!employeeUserId || parsedData.employeeUserId !== employeeUserId) {
            console.warn('Employee user ID mismatch in stored data')
            return []
        }

        // Optional: Check if data is not too old (e.g., older than 1 hour)
        const oneHour = 60 * 60 * 1000
        if (Date.now() - parsedData.timestamp > oneHour) {
            console.log('Stored patients data is older than 1 hour, consider refreshing')
            // Still return the data but log a warning
        }

        console.log('Retrieved patients from localStorage for medical center:', medicalCenterId, 'Count:', parsedData.patients.length)
        return parsedData.patients

    } catch (error) {
        console.error('Error retrieving patients from localStorage:', error)
        return []
    }
}

/**
 * Clear stored patients data for a specific medical center
 */
export const clearPatientsForMedicalCenter = (medicalCenterId: number): boolean => {
    try {
        const key = `medical-center-patients-${medicalCenterId}`
        localStorage.removeItem(key)
        console.log('Cleared stored patients for medical center:', medicalCenterId)
        return true
    } catch (error) {
        console.error('Error clearing stored patients:', error)
        return false
    }
}

/**
 * Check if patients data exists in localStorage for a specific medical center
 */
export const hasPatientsForMedicalCenter = (medicalCenterId: number): boolean => {
    try {
        const key = `medical-center-patients-${medicalCenterId}`
        return localStorage.getItem(key) !== null
    } catch (error) {
        console.error('Error checking for stored patients:', error)
        return false
    }
}


/**
 * Search patients by name or identification number
 */
export const searchStoredPatients = (
    medicalCenterId: number,
    searchTerm: string,
    employeeUserId?: number
): PatientResponse[] => {
    const patients = getPatientsForMedicalCenter(medicalCenterId, employeeUserId)

    if (!searchTerm.trim()) {
        return []
    }

    const lowerSearchTerm = searchTerm.toLowerCase()

    return patients.filter(patient =>
        patient.name.toLowerCase().includes(lowerSearchTerm) ||
        patient.identificationNumber.toLowerCase().includes(lowerSearchTerm) ||
        patient.email.toLowerCase().includes(lowerSearchTerm) ||
        patient.phone.includes(searchTerm)
    )
}


