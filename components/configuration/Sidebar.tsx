import { But<PERSON> } from "@/components/ui/button"
import { TABS } from "@/utils/constants"

interface SidebarProps {
  selectedTab: string
  setSelectedTab: (tab: string) => void
}

export default function Sidebar({ selectedTab, setSelectedTab }: SidebarProps) {
  return (
    <aside className="w-64 mr-[2rem] bg-white rounded-lg p-[1rem] shadow-md h-fit">
      {TABS.map((tab) => (
        <Button
          key={tab.id}
          variant={selectedTab === tab.id ? "default" : "ghost"}
          className={`w-full justify-start mb-2 ${
            selectedTab === tab.id 
              ? "bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800" 
              : "text-gray-700 hover:bg-gray-100"
          }`}
          onClick={() => setSelectedTab(tab.id)}
        >
          <tab.icon className="mr-2 h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium">{tab.name}</span>
        </Button>
      ))}
    </aside>
  )
}