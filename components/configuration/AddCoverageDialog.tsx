"use client"

import { useContext, useState, useRef, useEffect } from "react"
import { CoverageContext } from "@/contexts/CoverageContext"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { X, Check } from "lucide-react"

export default function AddCoverageDialog() {
  const {
    isAddCoverageDialogOpen,
    setIsAddCoverageDialogOpen,
    medicalCoverages,
    availableCoverages,
    selectedCoverages,
    setSelectedCoverages,
    selectedPlans,
    setSelectedPlans,
    addCoverage,
  } = useContext(CoverageContext)

  // State for the coverage dropdown
  const [showPlanDropdown, setShowPlanDropdown] = useState(false)
  const [activeCoverageId, setActiveCoverageId] = useState<string | null>(null)

  // Refs for handling outside clicks
  const planDropdownRef = useRef<HTMLDivElement>(null)

  // Handle outside clicks for dropdowns
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (planDropdownRef.current && !planDropdownRef.current.contains(e.target as Node)) {
        setShowPlanDropdown(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!isAddCoverageDialogOpen) {
      setSelectedCoverages([])
      setSelectedPlans({})
      setActiveCoverageId(null)
    }
  }, [isAddCoverageDialogOpen, setSelectedCoverages, setSelectedPlans])

  // Toggle coverage selection
  const toggleCoverageSelection = (coverageId: string) => {
    setSelectedCoverages((prev) => {
      if (prev.includes(coverageId)) {
        // Remove coverage and its plans
        setSelectedPlans((prevPlans) => {
          const newPlans = { ...prevPlans }
          delete newPlans[coverageId]
          return newPlans
        })
        return prev.filter((id) => id !== coverageId)
      } else {
        // Add coverage
        return [...prev, coverageId]
      }
    })
  }

  // Toggle plan selection for a specific coverage
  const togglePlanSelection = (coverageId: string, plan: string) => {
    setSelectedPlans((prev) => {
      const currentPlans = prev[coverageId] || []
      if (currentPlans.includes(plan)) {
        // Remove plan
        return {
          ...prev,
          [coverageId]: currentPlans.filter((p) => p !== plan)
        }
      } else {
        // Add plan
        return {
          ...prev,
          [coverageId]: [...currentPlans, plan]
        }
      }
    })
  }

  // Select all plans for a coverage
  const selectAllPlans = (coverageId: string) => {
    const coverage = availableCoverages.find(c => c.id === coverageId)
    if (!coverage) return

    setSelectedPlans((prev) => ({
      ...prev,
      [coverageId]: [...coverage.plans]
    }))
  }

  // Deselect all plans for a coverage
  const deselectAllPlans = (coverageId: string) => {
    setSelectedPlans((prev) => ({
      ...prev,
      [coverageId]: []
    }))
  }

  // Handle save button click
  const handleSave = () => {
    addCoverage()
    setIsAddCoverageDialogOpen(false)
  }

  return (
    <Dialog open={isAddCoverageDialogOpen} onOpenChange={setIsAddCoverageDialogOpen}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Agregar cobertura</DialogTitle>
          <DialogDescription>
            Seleccione las coberturas que acepta el establecimiento.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {/* Coverages selection */}
          <div className="mb-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Coberturas disponibles</h3>

            {availableCoverages.length === 0 ? (
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <p className="text-gray-600">
                  No hay coberturas disponibles para agregar.
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Todas las coberturas ya están configuradas para este establecimiento.
                </p>
              </div>
            ) : (
              <div className="space-y-2 max-h-[300px] overflow-y-auto p-2">
                {availableCoverages
                  .filter(coverage => !medicalCoverages.some(c => c.id === coverage.id))
                  .map((coverage) => (
                    <div
                      key={coverage.id}
                      className={`p-3 rounded-md border flex items-center justify-between cursor-pointer ${
                        selectedCoverages.includes(coverage.id)
                          ? "bg-blue-50 border-blue-200"
                          : "bg-white border-gray-200 hover:bg-gray-50"
                      }`}
                      onClick={() => toggleCoverageSelection(coverage.id)}
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-5 h-5 rounded-full flex items-center justify-center ${
                            selectedCoverages.includes(coverage.id)
                              ? "bg-blue-600 text-white"
                              : "border border-gray-300"
                          }`}
                        >
                          {selectedCoverages.includes(coverage.id) && <Check className="h-3 w-3" />}
                        </div>
                        <span className="font-medium">{coverage.name}</span>
                      </div>


                    </div>
                  ))}
              </div>
            )}
          </div>


        </div>

        {/* Plan selection dropdown */}
        {showPlanDropdown && activeCoverageId && (
          <div
            ref={planDropdownRef}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
          >
            <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-4">
              <div className="flex justify-between items-center mb-4 border-b pb-2">
                <h3 className="text-lg font-medium">
                  Planes para {availableCoverages.find(c => c.id === activeCoverageId)?.name}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPlanDropdown(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="mb-4 flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => selectAllPlans(activeCoverageId)}
                >
                  Seleccionar todos
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => deselectAllPlans(activeCoverageId)}
                >
                  Deseleccionar todos
                </Button>
              </div>

              <div className="max-h-[300px] overflow-y-auto mb-4">
                {availableCoverages.find(c => c.id === activeCoverageId)?.plans.map((plan) => (
                  <div
                    key={plan}
                    onClick={() => togglePlanSelection(activeCoverageId, plan)}
                    className={`p-2 rounded-md cursor-pointer flex justify-between items-center mb-2 ${
                      selectedPlans[activeCoverageId]?.includes(plan)
                        ? "bg-blue-100"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    <span>{plan}</span>
                    {selectedPlans[activeCoverageId]?.includes(plan) && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                ))}
              </div>

              <div className="flex justify-end">
                <Button
                  onClick={() => setShowPlanDropdown(false)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Aceptar
                </Button>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsAddCoverageDialogOpen(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleSave}
            disabled={selectedCoverages.length === 0}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Guardar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
