"use client"

import { Loader2 } from "lucide-react"

interface MedicalCenterCardLoadingProps {
  medicalCenterId: string
  isLoading: boolean
}

export function MedicalCenterCardLoading({ medicalCenterId, isLoading }: MedicalCenterCardLoadingProps) {
  if (!isLoading) return null

  return (
    <div 
      id={`medical-center-loading-${medicalCenterId}`}
      className="absolute inset-0 z-40 bg-blue-50 bg-opacity-50 flex items-center justify-center rounded-lg"
    >
      <div className="flex items-center justify-center">
        <Loader2 className="h-6 w-6 text-blue-500 animate-spin" />
      </div>
    </div>
  )
}
