import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertCircle } from "lucide-react";

interface VerificationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onVerify: (code: string) => void;
  patientName: string;
  error?: string;
}

export function VerificationDialog({
  open,
  onOpenChange,
  onVerify,
  patientName,
  error
}: VerificationDialogProps) {
  const [verificationCode, setVerificationCode] = React.useState("");
  const [localError, setLocalError] = React.useState("");

  const handleVerify = () => {
    // Reset error
    setLocalError("");

    // Validate verification code
    if (!verificationCode) {
      setLocalError("Por favor, ingrese el código de verificación");
      return;
    }

    if (verificationCode.length !== 6) {
      setLocalError("El código de verificación debe tener 6 dígitos");
      return;
    }

    // Call the onVerify callback with the verification code
    onVerify(verificationCode);
  };

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (!open) {
      setVerificationCode("");
      setLocalError("");
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Verificar paciente</DialogTitle>
          <DialogDescription>
            Se ha enviado un código de verificación al teléfono registrado para {patientName}. 
            Ingrese el código para asociar este paciente a su cuenta.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="verificationCode">Código de verificación</Label>
            <Input
              id="verificationCode"
              type="text"
              value={verificationCode}
              onChange={(e) => {
                // Only allow numbers
                const value = e.target.value.replace(/[^0-9]/g, '');
                // Limit to 6 digits
                if (value.length <= 6) {
                  setVerificationCode(value);
                }
              }}
              placeholder="Ingrese el código de 6 dígitos"
              maxLength={6}
            />
          </div>

          {(localError || error) && (
            <div className="bg-red-50 p-3 rounded-md flex items-start text-red-600 text-sm">
              <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
              <span>{localError || error}</span>
            </div>
          )}
        </div>

        <DialogFooter className="sm:justify-between">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleVerify}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Verificar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
