import {AppointmentSchedule, SpecialSchedule} from "@/types/professional-schedules";
import {getDateKey} from "@/utils/dateUtils";


export const createIsDayAvailableChecker = (
    datesWithVacationSchedules: Set<Date>,
    specialSchedulesByDate: Record<string, SpecialSchedule[]>,
    appointmentSchedulesByDayOfWeek: Record<string, AppointmentSchedule[]>
) => {

    return (day: Date): boolean => {
        if (datesWithVacationSchedules.has(day)) {
            return false;
        }
        const specialSchedulesForDay: SpecialSchedule[] = specialSchedulesByDate[getDateKey(day)] || [];
        const dayOfWeek: string = day.toLocaleDateString('en-US', {weekday: 'long'}).toLowerCase();
        const appointmentSchedulesForDay: AppointmentSchedule[] = appointmentSchedulesByDayOfWeek[dayOfWeek] || [];
        return specialSchedulesForDay.length > 0 || appointmentSchedulesForDay.length > 0;
    };
};