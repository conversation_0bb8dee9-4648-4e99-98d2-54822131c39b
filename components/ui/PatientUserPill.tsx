"use client"

import { useState, useRef, useEffect } from "react"
import { User, LogOut, ChevronDown } from "lucide-react"
import Link from "next/link"

import type { User as UserType } from "@/types/users"
import { UserRole } from "@/types/users"
import type { Patient } from "@/types/patient"
import { Button } from "@/components/ui/button"
import { getProfessionalUserId } from "@/utils/userUtils"
import { useAuth } from "@/contexts/AuthContext"

export interface PatientUserPillProps {
  currentUser: UserType | null;
  currentPatient: Patient | null;
  logout: () => void;
  variant?: 'light' | 'dark';
}

export function PatientUserPill({
  currentUser,
  currentPatient,
  logout,
  variant = 'light'
}: PatientUserPillProps) {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  const { loginWithAuth0 } = useAuth()

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // If user is not authenticated, show login button
  if (!currentUser) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="bg-white text-slate-700 border-slate-200 hover:bg-slate-50 hover:border-slate-300 font-medium text-base"
        onClick={() => {
          try {
            const currentPath = window.location.pathname + window.location.search
            window.localStorage.setItem('loginRedirectUrl', currentPath)
          } catch (err) {
            console.error('PatientUserPill - Error storing redirect URL:', err)
          }
          loginWithAuth0()
        }}
      >
        <User className="mr-2 h-4 w-4" />
        Iniciar sesión
      </Button>
    )
  }

  return (
    <div className="relative" ref={userMenuRef}>
      <button
        className="flex items-center gap-2 p-2 rounded-lg hover:bg-slate-50 transition-colors"
        onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
        aria-label="Abrir menú de usuario"
      >
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-medium text-sm">
          {currentPatient ? currentPatient.name.charAt(0).toUpperCase() : currentUser.name.charAt(0).toUpperCase()}
        </div>
        <ChevronDown className={`h-4 w-4 text-slate-600 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Simplified Dropdown Menu */}
      {isUserMenuOpen && (
        <div className="absolute top-full right-0 mt-2 w-64 z-50 animate-in fade-in-0 zoom-in-95 duration-200">
          <div className="bg-white rounded-lg shadow-lg border border-slate-200 overflow-hidden">
            {/* User Header - Now clickable as profile button */}
            <Link
              href="/plataforma/paciente?editProfile=true"
              className="block px-4 py-4 bg-slate-50 border-b border-slate-100 hover:bg-blue-50 transition-colors"
              onClick={() => setIsUserMenuOpen(false)}
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-medium text-base">
                  {currentPatient ? currentPatient.name.charAt(0).toUpperCase() : currentUser.name.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-semibold text-slate-900 text-base truncate">
                    {currentPatient ? currentPatient.name : currentUser.name}
                  </p>
                  <p className="text-slate-500 text-sm">Mi perfil</p>
                </div>
              </div>
            </Link>

            {/* Menu Items */}
            <div className="py-1">
              {currentUser?.roles.includes(UserRole.TURNERA_USER) && (
                <Link
                  href="/plataforma/paciente"
                  className="block px-4 py-3 text-base text-slate-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                  onClick={() => setIsUserMenuOpen(false)}
                >
                  <span>Mis turnos</span>
                </Link>
              )}

              {currentUser?.roles.includes(UserRole.PROFESSIONAL_USER) && (
                <Link
                  href={`/plataforma/profesional/${getProfessionalUserId(currentUser)}`}
                  className="block px-4 py-3 text-base text-slate-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                  onClick={() => setIsUserMenuOpen(false)}
                >
                  <span>Mi agenda profesional</span>
                </Link>
              )}

              {currentUser?.roles.includes(UserRole.EMPLOYEE_USER) && (
                <Link
                  href="/plataforma/establecimiento"
                  className="block px-4 py-3 text-base text-slate-700 hover:bg-blue-50 hover:text-blue-700 transition-colors"
                  onClick={() => setIsUserMenuOpen(false)}
                >
                  <span>Mis establecimientos</span>
                </Link>
              )}
            </div>

            {/* Logout */}
            <div className="border-t border-slate-100">
              <button
                className="w-full text-left px-4 py-3 text-base text-red-600 hover:bg-red-50 transition-colors"
                onClick={() => {
                  logout()
                  setIsUserMenuOpen(false)
                }}
              >
                <span>Cerrar sesión</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
