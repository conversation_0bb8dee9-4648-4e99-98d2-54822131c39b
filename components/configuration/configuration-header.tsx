import Link from "next/link"
import Image from "next/image"
import { MedicalCenter } from "@/types/medical-center"
import { MedicalCenterUserPill } from "@/components/ui/MedicalCenterUserPill"
import { User } from "@/types/users"

interface ConfigurationHeaderProps {
  medicalCenter: MedicalCenter
  currentUser: User | null
  logout: () => void
}

export default function ConfigurationHeader({ medicalCenter, currentUser, logout }: ConfigurationHeaderProps) {
  return (
    <header className="border-b bg-white shadow-sm">
      <div className="container mx-auto px-[6rem] py-[0.75rem] flex items-center justify-between">
      <Link href={`/plataforma/establecimiento/${medicalCenter.id}`} className="flex items-center">
          <Image
            src="/images/turnera-logo.svg"
            alt="Turnera Logo"
            width={120}
            height={40}
            className="h-[1.8rem] w-auto"
            priority
          />
        </Link>
        <MedicalCenterUserPill
          medicalCenter={medicalCenter}
          currentUser={currentUser}
          medicalCenterId={medicalCenter.id}
          logout={logout}
        />
      </div>
    </header>
  )
}