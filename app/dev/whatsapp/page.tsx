"use client"

import { useState, useEffect } from "react"
import { mockWhatsAppProvider, MockWhatsAppMessage } from "@/services/whatsapp"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Trash2, RefreshCw, Eye, MessageCircle, CheckCircle, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

export default function WhatsAppMessagesPage() {
  const [messages, setMessages] = useState<MockWhatsAppMessage[]>([])
  const [selectedMessage, setSelectedMessage] = useState<MockWhatsAppMessage | null>(null)

  useEffect(() => {
    // Load messages from storage
    loadMessages()
  }, [])

  const loadMessages = () => {
    const storedMessages = mockWhatsAppProvider.getMockMessages()
    // Sort messages by sentAt date, newest first
    const sortedMessages = [...storedMessages].sort((a, b) =>
      new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime()
    )
    setMessages(sortedMessages)
  }

  const markAsRead = (id: string) => {
    mockWhatsAppProvider.markMessageAsRead(id)
    loadMessages()
  }

  const clearMessages = () => {
    mockWhatsAppProvider.clearAllMessages()
    setMessages([])
    setSelectedMessage(null)
    toast.success("Todos los mensajes de WhatsApp fueron eliminados")
  }

  const viewMessage = (message: MockWhatsAppMessage) => {
    setSelectedMessage(message)
    if (!message.read) {
      markAsRead(message.id)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Link href="/dev" passHref>
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a Dev Tools
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Vista Previa de WhatsApp (Desarrollo)</h1>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadMessages}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" className="text-red-600" onClick={clearMessages}>
            <Trash2 className="h-4 w-4 mr-2" />
            Borrar Todos
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Mensajes de WhatsApp</CardTitle>
            </CardHeader>
            <CardContent>
              {messages.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No hay mensajes de WhatsApp enviados</p>
              ) : (
                <ul className="space-y-2">
                  {messages.map(message => (
                    <li
                      key={message.id}
                      className={`p-3 rounded-md cursor-pointer hover:bg-gray-100 ${!message.read ? 'bg-green-50' : ''}`}
                      onClick={() => viewMessage(message)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            {message.read ? (
                              <CheckCircle className="h-4 w-4 text-gray-400 mr-2" />
                            ) : (
                              <MessageCircle className="h-4 w-4 text-green-500 mr-2" />
                            )}
                            <p className={`text-sm font-medium ${!message.read ? 'font-bold' : ''}`}>{message.to}</p>
                          </div>
                          <p className="text-xs text-gray-500">
                            {new Date(message.sentAt).toLocaleString()}
                          </p>
                        </div>
                        <Button variant="ghost" size="sm" onClick={(e) => {
                          e.stopPropagation();
                          viewMessage(message);
                        }}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Vista Previa</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedMessage ? (
                <div>
                  <div className="mb-4 p-4 bg-gray-50 rounded-md">
                    <p><strong>Para:</strong> {selectedMessage.to}</p>
                    <p><strong>Enviado:</strong> {new Date(selectedMessage.sentAt).toLocaleString()}</p>
                  </div>
                  <div className="border rounded-md p-4 bg-green-50">
                    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 relative">
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                          T
                        </div>
                        <div className="flex-1">
                          <div className="bg-green-100 p-3 rounded-lg rounded-tl-none">
                            <p className="whitespace-pre-wrap text-sm">{selectedMessage.message}</p>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(selectedMessage.sentAt).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Seleccioná un mensaje de WhatsApp para ver su contenido</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
