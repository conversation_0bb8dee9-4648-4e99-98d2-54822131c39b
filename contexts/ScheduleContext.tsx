"use client"

import { create<PERSON>ontext, use<PERSON>ontext, ReactNode } from "react"
import { <PERSON><PERSON>ontex<PERSON> } from "./DoctorContext"
import { Doctor } from "@/types/doctor"

interface WorkingDay {
  enabled: boolean
  hours: Array<{ start: string; end: string }>
  weeksFrequency?: number
}

interface ScheduleContextProps {
  handleToggleWorkingDay: (dayId: string, enabled: boolean) => void
  handleUpdateWorkingHours: (dayId: string, index: number, field: "start" | "end", value: string) => void
  handleAddWorkingHours: (dayId: string) => void
  handleRemoveWorkingHours: (dayId: string, index: number) => void
  getWorkingDaysCount: (doctor: Doctor) => number
  handleUpdateWeeksFrequency: (dayId: string, frequency: number) => void
}

export const ScheduleContext = createContext<ScheduleContextProps>({} as ScheduleContextProps)

export function ScheduleProvider({ children }: { children: ReactNode }) {
  const { editedConfig, setEditedConfig } = useContext(DoctorContext)

  const handleToggleWorkingDay = (dayId: string, enabled: boolean) => {
    if (!editedConfig) return

    // Get the current hours or an empty array
    const currentHours = editedConfig.workingDays[dayId]?.hours || [];

    // If disabling the day, clear the hours array
    const updatedHours = enabled ? currentHours : [];

    const updatedWorkingDays: { [key: string]: WorkingDay } = {
      ...editedConfig.workingDays,
      [dayId]: {
        ...editedConfig.workingDays[dayId],
        enabled,
        hours: updatedHours,
        weeksFrequency: editedConfig.workingDays[dayId]?.weeksFrequency || 1,
      },
    }

    if (!enabled && currentHours.length > 0) {
      console.log(`ScheduleContext: Day ${dayId} disabled, cleared ${currentHours.length} time ranges`);
    }

    setEditedConfig({ ...editedConfig, workingDays: updatedWorkingDays })
  }

  const handleUpdateWorkingHours = (dayId: string, index: number, field: "start" | "end", value: string) => {
    if (!editedConfig) return
    const updatedHours = editedConfig.workingDays[dayId]?.hours.map((hour, i) =>
      i === index ? { ...hour, [field]: value } : hour
    ) || []
    const updatedWorkingDays: { [key: string]: WorkingDay } = {
      ...editedConfig.workingDays,
      [dayId]: {
        ...editedConfig.workingDays[dayId],
        hours: updatedHours,
      },
    }
    setEditedConfig({ ...editedConfig, workingDays: updatedWorkingDays })
  }

  const handleAddWorkingHours = (dayId: string) => {
    if (!editedConfig) return
    const updatedHours = [...(editedConfig.workingDays[dayId]?.hours || []), { start: "09:00", end: "10:00" }]
    const updatedWorkingDays: { [key: string]: WorkingDay } = {
      ...editedConfig.workingDays,
      [dayId]: {
        ...editedConfig.workingDays[dayId],
        hours: updatedHours,
      },
    }
    setEditedConfig({ ...editedConfig, workingDays: updatedWorkingDays })
  }

  const handleRemoveWorkingHours = (dayId: string, index: number) => {
    if (!editedConfig) return
    const updatedHours = editedConfig.workingDays[dayId]?.hours.filter((_, i) => i !== index) || []
    const updatedWorkingDays: { [key: string]: WorkingDay } = {
      ...editedConfig.workingDays,
      [dayId]: {
        ...editedConfig.workingDays[dayId],
        hours: updatedHours,
      },
    }
    setEditedConfig({ ...editedConfig, workingDays: updatedWorkingDays })
  }

  const handleUpdateWeeksFrequency = (dayId: string, frequency: number) => {
    if (!editedConfig) return
    const updatedWorkingDays: { [key: string]: WorkingDay } = {
      ...editedConfig.workingDays,
      [dayId]: {
        ...editedConfig.workingDays[dayId],
        weeksFrequency: frequency,
      },
    }
    setEditedConfig({ ...editedConfig, workingDays: updatedWorkingDays })
  }

  const getWorkingDaysCount = (doctor: Doctor): number => {
    return Object.values(doctor.workingDays).filter((day: WorkingDay) => day.enabled).length
  }

  return (
    <ScheduleContext.Provider
      value={{
        handleToggleWorkingDay,
        handleUpdateWorkingHours,
        handleAddWorkingHours,
        handleRemoveWorkingHours,
        getWorkingDaysCount,
        handleUpdateWeeksFrequency,
      }}
    >
      {children}
    </ScheduleContext.Provider>
  )
}