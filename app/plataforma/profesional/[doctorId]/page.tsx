"use client"

import {useEffect, useState} from "react"
import {use<PERSON><PERSON><PERSON>, useRouter} from "next/navigation"
import Image from "next/image"
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Building2, Calendar, ChevronRight, MapPin, Phone, Stethoscope} from "lucide-react"
import {MedicalCenter} from "@/types/medical-center"
import {useAuth} from "@/hooks/useAuth"
import {PatientUserPill} from "@/components/ui/PatientUserPill"

export default function DoctorSelectionPage() {
    const params = useParams()
    const router = useRouter()
    const doctorId = params.doctorId as string
    const {logout, currentUser} = useAuth()

    const [medicalCenters, setMedicalCenters] = useState<MedicalCenter[]>([])
    const [loading, setLoading] = useState(true)
    const [doctorName, setDoctorName] = useState("")



    // Load medical centers where the doctor works
    useEffect(() => {
        if (doctorId) {
            setLoading(true)

            // Get all medical centers
            const allMedicalCenters = storage.getMedicalCenters()

            // Filter to only include centers where this doctor works
            const doctorCenters = allMedicalCenters.filter(center =>
                center.doctors.includes(doctorId)
            )

            // Sort centers alphabetically by name
            const sortedCenters = [...doctorCenters].sort((a, b) =>
                a.name.localeCompare(b.name)
            )

            // Get doctor name
            const allDoctors = storage.getAllDoctorsInSystem()
            const doctor = allDoctors.find(d => d.id === doctorId)
            if (doctor) {
                setDoctorName(doctor.name)
            }

            setMedicalCenters(sortedCenters)
            setLoading(false)
        }
    }, [doctorId])

    const handleSelectMedicalCenter = (medicalCenterId: string) => {
        router.push(`/plataforma/profesional/${doctorId}/agenda/${medicalCenterId}`)
    }

    const handleLogout = () => {
        logout();
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
                <div className="bg-white rounded-2xl shadow-xl p-12 text-center max-w-md w-full">
                    <div className="w-16 h-16 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <div
                            className="w-8 h-8 border-3 border-cyan-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">Cargando</h3>
                    <p className="text-gray-600">Preparando su área profesional...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white border-b border-gray-200 sticky top-0 z-50 shadow-sm">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <Image
                                src="/images/turnera-logo.svg"
                                alt="Turnera"
                                width={140}
                                height={40}
                                className="h-8 w-auto"
                                priority
                            />
                        </div>

                        <div className="flex items-center space-x-4">
                            <PatientUserPill
                                currentUser={currentUser}
                                currentPatient={null}
                                logout={handleLogout}
                                variant="light"
                            />
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                {/* Hero Section */}
                <div className="text-center mb-16">
                    <div
                        className="inline-flex items-center space-x-2 bg-[#1cd8e1]/20 px-4 py-2 rounded-full border border-[#1cd8e1]/30 mb-6">
                        <Stethoscope className="h-4 w-4 text-[#0a7c82]"/>
                        <span className="text-sm font-medium text-[#0a7c82]">Agenda profesional</span>
                    </div>
                    <h1 className="text-5xl font-bold text-gray-900 mb-6">
                        Bienvenido, <span className="text-cyan-600">Dr. {doctorName}</span>
                    </h1>
                    <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                        Acceda a sus agendas médicas en los establecimientos donde ejerce su práctica profesional
                    </p>

                    {medicalCenters.length > 0 && (
                        <div className="mt-8 flex justify-center text-sm text-gray-500">
                            <div className="flex items-center space-x-2">
                                <Building2 className="h-4 w-4"/>
                                <span>{medicalCenters.length} {medicalCenters.length === 1 ? 'Establecimiento' : 'Establecimientos'}</span>
                            </div>
                        </div>
                    )}
                </div>

                {medicalCenters.length === 0 ? (
                    <div className="flex justify-center">
                        <div className="max-w-lg w-full">
                            <div className="bg-white rounded-2xl shadow-lg p-12 text-center border border-gray-100">
                                <div
                                    className="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <Stethoscope className="h-10 w-10 text-red-400"/>
                                </div>
                                <h3 className="text-2xl font-bold text-gray-900 mb-4">Sin establecimientos
                                    asignados</h3>
                                <p className="text-gray-600 mb-8 leading-relaxed">
                                    No tiene acceso a ningún establecimiento médico. Contacte al administrador para
                                    solicitar permisos.
                                </p>
                                <Button
                                    onClick={handleLogout}
                                    className="bg-red-600 hover:bg-red-700 text-white px-8 py-3"
                                >
                                    Cerrar sesión
                                </Button>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="flex flex-wrap justify-center gap-8 max-w-6xl mx-auto">
                        {medicalCenters.map((center, index) => (
                            <Card
                                key={center.id}
                                className="group bg-white border-0 shadow-lg hover:shadow-2xl rounded-2xl overflow-hidden transition-all duration-300 hover:-translate-y-1 card-grid-item w-[22rem]"
                            >
                                <CardHeader className="p-8 pb-6">
                                    <div className="flex items-start justify-between mb-4">
                                        <div
                                            className="w-14 h-14 bg-cyan-100 rounded-xl flex items-center justify-center group-hover:bg-cyan-600 transition-colors duration-300">
                                            <Building2
                                                className="h-7 w-7 text-cyan-600 group-hover:text-white transition-colors duration-300"/>
                                        </div>
                                        <div className="text-right">
                      <span
                          className="inline-block bg-gray-100 text-gray-600 text-xs font-medium px-3 py-1 rounded-full">
                        #{String(index + 1).padStart(2, '0')}
                      </span>
                                        </div>
                                    </div>
                                    <CardTitle
                                        className="text-xl font-bold text-gray-900 mb-2 group-hover:text-cyan-600 transition-colors duration-300">
                                        {center.name}
                                    </CardTitle>
                                    <CardDescription className="text-gray-600">
                                        {center.address && (
                                            <div className="flex items-start space-x-2">
                                                <MapPin className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0"/>
                                                <span className="text-sm leading-relaxed">{center.address}</span>
                                            </div>
                                        )}
                                    </CardDescription>
                                </CardHeader>

                                <CardContent className="px-8 pb-6">
                                    <div className="space-y-4">
                                        {center.phone && (
                                            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                                <Phone className="h-4 w-4 text-gray-500"/>
                                                <span
                                                    className="text-sm text-gray-700 font-medium">{center.phone}</span>
                                            </div>
                                        )}

                                        <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                                            <Calendar className="h-4 w-4 text-green-600"/>
                                            <span className="text-sm text-green-800 font-medium">
                        {Object.values(center.workingDays).filter(day => day.enabled).length} días operativos
                      </span>
                                        </div>
                                    </div>
                                </CardContent>

                                <CardFooter className="p-8 pt-4">
                                    <Button
                                        onClick={() => handleSelectMedicalCenter(center.id)}
                                        className="w-full bg-cyan-600 hover:bg-cyan-700 text-white py-3 rounded-xl font-medium transition-all duration-300 group-hover:bg-cyan-700 flex items-center justify-center space-x-2"
                                    >
                                        <Stethoscope className="h-4 w-4"/>
                                        <span>Acceder a Agenda</span>
                                        <ChevronRight
                                            className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300"/>
                                    </Button>
                                </CardFooter>
                            </Card>
                        ))}
                    </div>
                )}
            </main>

            {/* Footer */}
            <footer className="bg-white border-t border-gray-200 mt-20">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="text-center text-gray-500 text-sm">
                        <p className="font-medium">© {new Date().getFullYear()} Turnera</p>
                        <p className="mt-1">Plataforma profesional de gestión médica</p>
                    </div>
                </div>
            </footer>

            {/* Animations */}
            <style jsx global>{`
                .card-grid-item {
                    opacity: 0;
                    animation: slideInUp 0.6s ease-out forwards;
                }

                .card-grid-item:nth-child(1) {
                    animation-delay: 0.1s;
                }

                .card-grid-item:nth-child(2) {
                    animation-delay: 0.2s;
                }

                .card-grid-item:nth-child(3) {
                    animation-delay: 0.3s;
                }

                .card-grid-item:nth-child(4) {
                    animation-delay: 0.4s;
                }

                .card-grid-item:nth-child(5) {
                    animation-delay: 0.5s;
                }

                .card-grid-item:nth-child(6) {
                    animation-delay: 0.6s;
                }

                @keyframes slideInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `}</style>
        </div>
    );
}