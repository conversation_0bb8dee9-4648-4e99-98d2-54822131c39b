import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the Auth0 JWT (access token) and other data from HTTP-only cookies
    const auth0JWT = request.cookies.get('auth0_jwt')?.value;
    const auth0AccessToken = request.cookies.get('auth0_access_token')?.value;
    const auth0User = request.cookies.get('auth0_user')?.value;
    const employeeUser = request.cookies.get('employee_user')?.value;

    if (!auth0JWT) {
      return NextResponse.json({
        error: 'No Auth0 JWT found',
        success: false
      }, { status: 401 });
    }

    // Parse user data if available
    let userData = null;
    if (auth0User) {
      try {
        userData = JSON.parse(auth0User);
      } catch (e) {
        console.error('Error parsing user data:', e);
      }
    }

    // Parse employee user data if available
    let employeeUserData = null;
    if (employeeUser) {
      try {
        employeeUserData = JSON.parse(employeeUser);
      } catch (e) {
        console.error('Error parsing employee user data:', e);
      }
    }

    return NextResponse.json({
      // Return the decodable JWT (access token)
      jwt: auth0JWT,
      // Also provide access token for API calls if needed
      accessToken: auth0AccessToken,
      user: userData,
      employeeUser: employeeUserData,
      success: true
    });
  } catch (error) {
    console.error('Error getting JWT token:', error);
    return NextResponse.json({
      error: 'Failed to get JWT token',
      success: false
    }, { status: 500 });
  }
}
