"use client"

import React, {Suspense, useEffect, useState} from "react"
import {useRouter} from "next/navigation"
import {<PERSON>ertCircle, ShieldCheck} from "lucide-react"
import {UserRole} from "@/types/users"
import {<PERSON><PERSON>} from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import {useAuth} from "@/contexts/AuthContext"
import {Auth0LoginButton} from "@/components/auth/Auth0LoginButton"
import {getProfessionalUserId} from "@/utils/userUtils"

// Wrapper component that doesn't use useSearchParams directly
export default function LoginPageWrapper() {
    return (
        <Suspense
            fallback={<div className="flex items-center justify-center min-h-screen bg-blue-50">Cargando...</div>}>
            <LoginPage/>
        </Suspense>
    )
}


function LoginPage() {
    const [error, setError] = useState("")
    const [isCheckingAuth, setIsCheckingAuth] = useState(true)
    const router = useRouter()
    const {isAuthenticated, currentUser} = useAuth()


    useEffect(() => {
        const checkAuthStatus = () => {
            if (isAuthenticated !== undefined) {
                setIsCheckingAuth(false)
            }
        }
        checkAuthStatus()
    }, [isAuthenticated])

    useEffect(() => {
        if (isCheckingAuth) return
        if (isAuthenticated && currentUser) {
            const roles = currentUser.roles || []

            // No roles -> send to patient registration
            if (roles.length === 0) {
                router.push("/plataforma/paciente/registro")
                return
            }

            // Multiple roles -> user role selector page
            if (roles.length > 1) {
                router.push("/plataforma/user")
                return
            }

            // Single role cases
            const onlyRole = roles[0]
            switch (onlyRole) {
                case UserRole.TURNERA_USER:
                    router.push("/")
                    return
                case UserRole.EMPLOYEE_USER:
                    router.push("/plataforma/establecimiento")
                    return
                case UserRole.PROFESSIONAL_USER: {
                    const doctorId = getProfessionalUserId(currentUser)
                    if (doctorId) {
                        router.push(`/plataforma/profesional/${doctorId}`)
                    } else {
                        router.push("/plataforma/user")
                    }
                    return
                }
                default:
                    router.push("/plataforma/user")
                    return
            }
        }
    }, [isAuthenticated, currentUser, router, isCheckingAuth])


    if (isCheckingAuth) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-blue-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>Verificando autenticación...</p>
                </div>
            </div>
        )
    }
    return (
        <div className="min-h-screen bg-blue-50 flex flex-col">
            <header className="py-3">
                <div className="container mx-auto flex justify-center items-center">
                    <Link href="/">
                        <Image
                            src="/images/turnera-logo.svg"
                            alt="Turnera Logo"
                            width={120}
                            height={36}
                            className="h-8 w-auto"
                            priority
                        />
                    </Link>
                </div>
            </header>

            <main className="flex-grow container mx-auto px-4 py-2 sm:py-4 flex items-center justify-center">
                <div className="w-full max-w-md">
                    <div className="bg-white p-8 rounded-2xl shadow-xl border border-gray-100">
                        <div className="text-center mb-6">
                            <h1 className="text-2xl font-bold text-[#1c2533] mb-2">
                                Iniciar sesión
                            </h1>
                            <p className="text-gray-600">
                                Acceda a su cuenta de Turnera.
                            </p>
                        </div>

                        {error && (
                            <div className="bg-red-50 p-3 rounded-md flex items-start text-red-600 text-sm mb-4">
                                <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0"/>
                                <span>{error}</span>
                            </div>
                        )}

                        {/* Auth0 Login Section */}
                        <div className="space-y-4">
                            <Auth0LoginButton
                                className="w-full bg-[#0070F3] hover:bg-[#0070F3]/90 transition-colors h-11 text-base font-medium text-white border-0"
                            >
                                {"Iniciar sesión"}
                            </Auth0LoginButton>
                        </div>

                        <div className="mt-6 pt-6 border-t border-gray-100 text-center">
                            <p className="text-sm text-gray-600 mb-2">
                                ¿No tiene una cuenta? Puede crear una en segundos.
                            </p>
                            <Button
                                variant="outline"
                                className="w-full border text-[#0070F3] border-[#0070F3] hover:bg-[#0070F3]/5 transition-colors h-11 font-medium"
                                onClick={() => router.push("/plataforma/registro")}
                            >
                                Registrarme
                            </Button>
                        </div>
                    </div>
                </div>
            </main>

            <footer className="bg-white border-t border-gray-200 py-8 mt-auto">
                <div className="container mx-auto px-4">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="mb-4 md:mb-0">
                            <Image
                                src="/images/turnera-logo.svg"
                                alt="Turnera Logo"
                                width={120}
                                height={36}
                                className="h-7 w-auto mb-3"
                            />
                            <p className="text-gray-500 text-sm">
                                © {new Date().getFullYear()} Turnera. Todos los derechos reservados.
                            </p>
                        </div>

                        <div className="flex flex-col items-center md:items-end">
                            <div className="flex items-center mb-3">
                                <ShieldCheck className="h-4 w-4 text-[#1cd8e1] mr-1.5"/>
                                <span className="text-sm text-[#1c2533] font-medium">Reservá turnos médicos 24/7</span>
                            </div>
                            <div className="flex space-x-4">
                                <Button variant="ghost" size="sm"
                                        className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                                    Términos y Condiciones
                                </Button>
                                <Button variant="ghost" size="sm"
                                        className="text-gray-600 hover:text-[#0070F3] hover:bg-[#0070F3]/5 rounded-md px-2 py-1">
                                    Privacidad
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    )
}