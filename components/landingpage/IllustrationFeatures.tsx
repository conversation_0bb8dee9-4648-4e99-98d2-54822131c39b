import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, MessageSquare } from "lucide-react"

export default function IllustrationFeatures() {
  return (
    <section className="relative bg-blue-50 py-10 md:py-16">
      {/* Decorative blobs */}
      <div className="pointer-events-none absolute -top-8 -right-8 h-40 w-40 rounded-full bg-blue-200/50 blur-3xl" />
      <div className="pointer-events-none absolute -bottom-8 -left-8 h-40 w-40 rounded-full bg-indigo-200/50 blur-3xl" />

      <div className="relative max-w-7xl mx-auto px-7 grid md:grid-cols-2 gap-10 md:gap-14 items-center">
        {/* Mobile-only header above illustration */}
        <div className="md:hidden order-0">
          <h3 className="font-recoleta text-3xl text-gray-900 mb-2 text-center px-8">
            Encontrar tu próximo turno es fácil y rápido
          </h3>
        </div>

        {/* Illustration card */}
        <div className="relative order-1 md:order-1 -mt-6 md:mt-0">
          <div className="relative rounded-2xl p-4 md:p-6">
            {/* <div className="rounded-xl bg-white/90 ring-1 ring-white/40 p-4 md:p-6"> */}
              <Image
                src="/images/doctor-illustration-3d-blue.svg"
                alt="Doctor illustration"
                width={800}
                height={800}
                className="w-full h-auto"
              />
            {/*</div>*/}

            {/* Floating chips */}
            <div className="absolute -right-3 md:-right-6 top-24 md:top-24">
              <div className="flex items-center gap-2 md:gap-2.5 rounded-xl bg-white shadow-sm ring-1 ring-blue-100 px-3 py-2 md:px-4 md:py-2.5">
                <Calendar className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
                <span className="text-xs md:text-sm font-medium text-gray-700">Turno confirmado</span>
              </div>
            </div>
            <div className="absolute -left-3 md:-left-6 bottom-16 md:bottom-20">
              <div className="flex items-center gap-2 md:gap-2.5 rounded-xl bg-white shadow-sm ring-1 ring-blue-100 px-3 py-2 md:px-4 md:py-2.5">
                <MessageSquare className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
                <span className="text-xs md:text-sm font-medium text-gray-700">Recordatorio enviado</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content (patient-focused) */}
        <div className="order-2 md:order-2">
          <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wider mb-3 inline-block">Cómo funciona</span>
          <h3 className="hidden md:block font-recoleta text-2xl md:text-3xl text-gray-900 mb-3">Encontrar tu próximo turno es fácil y rápido</h3>
          <p className="text-base md:text-lg text-gray-700 mb-5 max-w-xl">
            Buscá por especialidad o estudio, filtrá por tu cobertura y reservá en minutos. Te avisamos un día antes para que puedas confirmar o reprogramar sin llamadas.
          </p>

          <ul className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-gray-700 mb-6">
            <li className="flex items-center gap-2"><CheckIcon /> Recordatorio automático el día anterior</li>
            <li className="flex items-center gap-2"><CheckIcon /> Filtrá por obra social o prepaga y plan</li>
            <li className="flex items-center gap-2"><CheckIcon /> Turnos en el día y horario que necesitás</li>
            <li className="flex items-center gap-2"><CheckIcon /> Reprogramá o cancelá en un clic</li>
          </ul>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-6">
            <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
              <p className="text-sm text-gray-700"><span className="font-medium text-gray-900">Cerca tuyo:</span> encontrá profesionales por tu barrio.</p>
            </div>
            <div className="rounded-xl border border-gray-200 bg-white p-4 shadow-sm">
              <p className="text-sm text-gray-700"><span className="font-medium text-gray-900">Sin llamados:</span> reservá, confirmá o cancelá en un click.</p>
            </div>
          </div>

          <Button asChild className="rounded-xl bg-blue-600 hover:bg-blue-700">
            <Link href="#buscar">Buscar un turno</Link>
          </Button>
        </div>
      </div>
    </section>
  )
}

function CheckIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      className="h-4 w-4 text-blue-600"
    >
      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
    </svg>
  )
}
