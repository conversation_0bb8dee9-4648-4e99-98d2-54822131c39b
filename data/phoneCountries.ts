import { defaultCountries, parseCountry, buildCountryData } from 'react-international-phone';
import type { CountryData } from 'react-international-phone';

// Spanish translations for country names in PhoneInput
export const spanishCountryNames: Record<string, string> = {
  "af": "Afganistán",
  "al": "Albania",
  "dz": "Argelia",
  "as": "Samoa Americana",
  "ad": "Andorra",
  "ao": "Angola",
  "ai": "Anguila",
  "ag": "Antigua y Barbuda",
  "ar": "Argentina",
  "am": "Armenia",
  "aw": "Aruba",
  "au": "Australia",
  "at": "Austria",
  "az": "Azerbaiyán",
  "bs": "Bahamas",
  "bh": "Baréin",
  "bd": "Bangladés",
  "bb": "Barbados",
  "by": "Bielorrusia",
  "be": "Bélgica",
  "bz": "Belice",
  "bj": "<PERSON>ín",
  "bm": "Bermudas",
  "bt": "But<PERSON>",
  "bo": "Bolivia",
  "ba": "Bosnia y Herzegovina",
  "bw": "Botsuana",
  "br": "Brasil",
  "bn": "Brunéi",
  "bg": "Bulgaria",
  "bf": "Burkina Faso",
  "bi": "Burundi",
  "kh": "Camboya",
  "cm": "Camerún",
  "ca": "Canadá",
  "cv": "Cabo Verde",
  "ky": "Islas Caimán",
  "cf": "República Centroafricana",
  "td": "Chad",
  "cl": "Chile",
  "cn": "China",
  "co": "Colombia",
  "km": "Comoras",
  "cg": "Congo",
  "cd": "República Democrática del Congo",
  "ck": "Islas Cook",
  "cr": "Costa Rica",
  "ci": "Costa de Marfil",
  "hr": "Croacia",
  "cu": "Cuba",
  "cy": "Chipre",
  "cz": "República Checa",
  "dk": "Dinamarca",
  "dj": "Yibuti",
  "dm": "Dominica",
  "do": "República Dominicana",
  "ec": "Ecuador",
  "eg": "Egipto",
  "sv": "El Salvador",
  "gq": "Guinea Ecuatorial",
  "er": "Eritrea",
  "ee": "Estonia",
  "et": "Etiopía",
  "fk": "Islas Malvinas",
  "fo": "Islas Feroe",
  "fj": "Fiyi",
  "fi": "Finlandia",
  "fr": "Francia",
  "gf": "Guayana Francesa",
  "pf": "Polinesia Francesa",
  "ga": "Gabón",
  "gm": "Gambia",
  "ge": "Georgia",
  "de": "Alemania",
  "gh": "Ghana",
  "gi": "Gibraltar",
  "gr": "Grecia",
  "gl": "Groenlandia",
  "gd": "Granada",
  "gp": "Guadalupe",
  "gu": "Guam",
  "gt": "Guatemala",
  "gn": "Guinea",
  "gw": "Guinea-Bisáu",
  "gy": "Guyana",
  "ht": "Haití",
  "va": "Ciudad del Vaticano",
  "hn": "Honduras",
  "hk": "Hong Kong",
  "hu": "Hungría",
  "is": "Islandia",
  "in": "India",
  "id": "Indonesia",
  "ir": "Irán",
  "iq": "Irak",
  "ie": "Irlanda",
  "il": "Israel",
  "it": "Italia",
  "jm": "Jamaica",
  "jp": "Japón",
  "jo": "Jordania",
  "kz": "Kazajistán",
  "ke": "Kenia",
  "ki": "Kiribati",
  "kp": "Corea del Norte",
  "kr": "Corea del Sur",
  "kw": "Kuwait",
  "kg": "Kirguistán",
  "la": "Laos",
  "lv": "Letonia",
  "lb": "Líbano",
  "ls": "Lesoto",
  "lr": "Liberia",
  "ly": "Libia",
  "li": "Liechtenstein",
  "lt": "Lituania",
  "lu": "Luxemburgo",
  "mo": "Macao",
  "mk": "Macedonia del Norte",
  "mg": "Madagascar",
  "mw": "Malaui",
  "my": "Malasia",
  "mv": "Maldivas",
  "ml": "Malí",
  "mt": "Malta",
  "mh": "Islas Marshall",
  "mq": "Martinica",
  "mr": "Mauritania",
  "mu": "Mauricio",
  "yt": "Mayotte",
  "mx": "México",
  "fm": "Micronesia",
  "md": "Moldavia",
  "mc": "Mónaco",
  "mn": "Mongolia",
  "me": "Montenegro",
  "ms": "Montserrat",
  "ma": "Marruecos",
  "mz": "Mozambique",
  "mm": "Myanmar",
  "na": "Namibia",
  "nr": "Nauru",
  "np": "Nepal",
  "nl": "Países Bajos",
  "nc": "Nueva Caledonia",
  "nz": "Nueva Zelanda",
  "ni": "Nicaragua",
  "ne": "Níger",
  "ng": "Nigeria",
  "nu": "Niue",
  "nf": "Isla Norfolk",
  "mp": "Islas Marianas del Norte",
  "no": "Noruega",
  "om": "Omán",
  "pk": "Pakistán",
  "pw": "Palaos",
  "ps": "Palestina",
  "pa": "Panamá",
  "pg": "Papúa Nueva Guinea",
  "py": "Paraguay",
  "pe": "Perú",
  "ph": "Filipinas",
  "pl": "Polonia",
  "pt": "Portugal",
  "pr": "Puerto Rico",
  "qa": "Catar",
  "re": "Reunión",
  "ro": "Rumanía",
  "ru": "Rusia",
  "rw": "Ruanda",
  "bl": "San Bartolomé",
  "sh": "Santa Elena",
  "kn": "San Cristóbal y Nieves",
  "lc": "Santa Lucía",
  "mf": "San Martín",
  "pm": "San Pedro y Miquelón",
  "vc": "San Vicente y las Granadinas",
  "ws": "Samoa",
  "sm": "San Marino",
  "st": "Santo Tomé y Príncipe",
  "sa": "Arabia Saudita",
  "sn": "Senegal",
  "rs": "Serbia",
  "sc": "Seychelles",
  "sl": "Sierra Leona",
  "sg": "Singapur",
  "sk": "Eslovaquia",
  "si": "Eslovenia",
  "sb": "Islas Salomón",
  "so": "Somalia",
  "za": "Sudáfrica",
  "gs": "Georgia del Sur e Islas Sandwich del Sur",
  "es": "España",
  "lk": "Sri Lanka",
  "sd": "Sudán",
  "sr": "Surinam",
  "sz": "Esuatini",
  "se": "Suecia",
  "ch": "Suiza",
  "sy": "Siria",
  "tw": "Taiwán",
  "tj": "Tayikistán",
  "tz": "Tanzania",
  "th": "Tailandia",
  "tl": "Timor Oriental",
  "tg": "Togo",
  "tk": "Tokelau",
  "to": "Tonga",
  "tt": "Trinidad y Tobago",
  "tn": "Túnez",
  "tr": "Turquía",
  "tm": "Turkmenistán",
  "tc": "Islas Turcas y Caicos",
  "tv": "Tuvalu",
  "ug": "Uganda",
  "ua": "Ucrania",
  "ae": "Emiratos Árabes Unidos",
  "gb": "Reino Unido",
  "us": "Estados Unidos",
  "uy": "Uruguay",
  "uz": "Uzbekistán",
  "vu": "Vanuatu",
  "ve": "Venezuela",
  "vn": "Vietnam",
  "vg": "Islas Vírgenes Británicas",
  "vi": "Islas Vírgenes de los Estados Unidos",
  "wf": "Wallis y Futuna",
  "eh": "Sahara Occidental",
  "ye": "Yemen",
  "zm": "Zambia",
  "zw": "Zimbabue"
};

/**
 * Creates a modified version of the defaultCountries array with Spanish country names
 */
export const getSpanishCountries = (): CountryData[] => {
  return defaultCountries.map((country) => {
    const parsedCountry = parseCountry(country);
    const iso2 = parsedCountry.iso2;

    // Replace the country name with Spanish translation if available
    if (spanishCountryNames[iso2]) {
      parsedCountry.name = spanishCountryNames[iso2];
    }

    return buildCountryData(parsedCountry);
  });
};
