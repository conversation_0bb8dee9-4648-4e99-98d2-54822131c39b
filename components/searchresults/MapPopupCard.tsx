import React from 'react';
import { Result } from './SearchResultsList'; // Assuming Result is exported from here or a shared types file
import { MapPin, Calendar, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { format } from 'date-fns'; // Re-import format

interface MapPopupCardProps {
  result: Result;
  searchQuery: string;
  searchType: string;
  noInsurance: boolean;
  coverage: string;
  plan: string;
  timeOfDay: string;
}

// Helper function to format price (copied from SearchResultsList)
const formatPrice = (price: string): string => {
  if (!price || !price.startsWith('$')) return price;
  const numericPart = price.substring(1).trim();
  const numValue = parseFloat(numericPart.replace(/\./g, '').replace(',', '.'));
  if (isNaN(numValue)) return price;
  return `$${numValue.toLocaleString('es-AR', { minimumFractionDigits: 0 }).replace(/,/g, '.')}`;
}

export default function MapPopupCard({
  result,
  searchQuery,
  searchType,
  noInsurance,
  coverage,
  plan,
  timeOfDay,
}: MapPopupCardProps) {

  // Determine the display amount and label (logic copied from SearchResultsList)
  let displayAmount: string | undefined = undefined;
  let displayLabel: string | undefined = undefined;

  if (noInsurance) {
    if (searchType?.toLowerCase() === "estudio") {
      displayLabel = searchQuery;
      displayAmount = result.price;
    } else {
      const hasPrimeraConsulta = result.specialties?.some(
        (specialty: string) => specialty === "Primera consulta"
      ) || result.type === "doctor";
      if (hasPrimeraConsulta) {
        displayLabel = "Primera consulta";
        displayAmount = result.price;
      }
    }
  } else if (result.copay) {
    displayLabel = "Copago";
    displayAmount = result.copay;
  }

  // General booking URL (for "Ver todos los turnos")
  let allTurnsBookingUrl = `/plataforma/reservar/cita?doctorId=${result.id}`;
  if (result.medicalCenterId) {
    allTurnsBookingUrl += `&medicalCenterId=${result.medicalCenterId}`;
  }
  // Add other params from search context
  allTurnsBookingUrl += `&source=search&searchType=${encodeURIComponent(searchType.toLowerCase())}&q=${encodeURIComponent(searchQuery)}`;
  if (noInsurance) {
    allTurnsBookingUrl += `&noCoverage=true`;
  } else if (coverage) {
    allTurnsBookingUrl += `&coverage=${encodeURIComponent(coverage)}`;
    if (plan) {
      allTurnsBookingUrl += `&plan=${encodeURIComponent(plan)}`;
    }
  }
  if (searchType?.toLowerCase() === "estudio") {
    allTurnsBookingUrl += `&consultationType=${encodeURIComponent(searchQuery)}`;
  }
  if (timeOfDay !== 'all') {
    allTurnsBookingUrl += `&timeOfDay=${encodeURIComponent(timeOfDay)}`;
  }

  return (
    <div className="w-72 p-3 bg-white rounded-lg shadow-sm text-sm font-sans">
      {/* Header */}
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-bold text-[#1c2533] text-base mr-2 leading-tight">
          {result.name}
        </h3>
        {displayAmount && displayLabel && (
          <div className={`
            ${noInsurance ? "bg-[#0070F3]/10 text-[#0070F3]" : "bg-amber-50 text-amber-600"}
            px-1.5 py-0.5 rounded text-[10px] font-semibold flex-shrink-0 whitespace-nowrap
          `}>
            {displayLabel}: {formatPrice(displayAmount)}
          </div>
        )}
      </div>

      {/* Location */}
      <div className="flex items-start text-gray-600 mb-2.5">
        <MapPin className="h-3.5 w-3.5 mr-1.5 text-[#0070F3] flex-shrink-0 mt-0.5" />
        <span className="text-xs leading-snug">{result.location}</span>
      </div>

      {/* Appointments */}
      <div className="mb-3">
        <h4 className="text-xs font-medium text-[#1c2533] mb-1.5 flex items-center">
          <Clock className="h-3.5 w-3.5 mr-1.5 text-[#0070F3]" />
          Próximos turnos:
        </h4>
        <div className="grid grid-cols-2 gap-1.5">
          {result.appointments.slice(0, 2).map((appointmentStr: string, index: number) => {
            const appointmentDate = result.nextAvailableDate ? new Date(result.nextAvailableDate) : new Date();
            let timeForUrl = "";
            const parts = appointmentStr.split(' - '); // e.g., ["Lun 15", "14:30"]
            
            if (parts.length === 2) {
              timeForUrl = parts[1].trim(); // "14:30"
              const dayParts = parts[0].split(' '); // e.g., ["Lun", "15"]
              if (dayParts.length === 2) {
                const dayInMonth = parseInt(dayParts[1], 10);
                if (!isNaN(dayInMonth) && result.nextAvailableDate) {
                  const firstAppointmentDay = result.nextAvailableDate.getDate();
                  // If current appointment's day is less than first appointment's day, 
                  // AND this isn't the very first appointment being processed (index > 0 implies it could be next month for the second slot)
                  // OR if it's the first slot (index ===0) but dayInMonth is somehow different (should not happen if data is consistent)
                  // This logic handles simple cases; complex month/year spanning might need more robust date library functions if appointments are far apart
                  if (dayInMonth < firstAppointmentDay && index > 0) { // simplified: assumes second slot if day is smaller, it's next month
                     appointmentDate.setMonth(appointmentDate.getMonth() + 1);
                  }
                  appointmentDate.setDate(dayInMonth);
                } else if (isNaN(dayInMonth) && result.nextAvailableDate) {
                    // Fallback if day parsing fails, use nextAvailableDate as is for this slot
                    // This case should ideally not happen with consistent appointmentStr format
                }
              }
            } else if (result.nextAvailableDate) { // Fallback if appointmentStr format is unexpected
                timeForUrl = result.nextAvailableTime || ""; 
            }

            const dateForUrl = format(appointmentDate, 'yyyy-MM-dd');
            
            let slotBookingUrl = `/plataforma/reservar/cita?doctorId=${result.id}`;
            if (result.medicalCenterId) slotBookingUrl += `&medicalCenterId=${result.medicalCenterId}`;
            slotBookingUrl += `&date=${dateForUrl}&time=${timeForUrl}`;
            slotBookingUrl += `&source=search&searchType=${encodeURIComponent(searchType.toLowerCase())}&q=${encodeURIComponent(searchQuery)}`;
            if (noInsurance) slotBookingUrl += `&noCoverage=true`;
            else if (coverage) {
              slotBookingUrl += `&coverage=${encodeURIComponent(coverage)}`;
              if (plan) slotBookingUrl += `&plan=${encodeURIComponent(plan)}`;
            }
            if (searchType?.toLowerCase() === "estudio") slotBookingUrl += `&consultationType=${encodeURIComponent(searchQuery)}`;
            if (timeOfDay !== 'all') slotBookingUrl += `&timeOfDay=${encodeURIComponent(timeOfDay)}`;

            return (
              <Link key={index} href={slotBookingUrl} passHref legacyBehavior>
                <a target="_blank" rel="noopener noreferrer" className="block h-full">
                  <div 
                    className="flex items-center justify-center text-xs text-center text-gray-700 bg-white p-1.5 rounded border border-[#0070F3]/30 h-full hover:bg-blue-50 transition-colors duration-150 cursor-pointer"
                  >
                    <Calendar className="h-3 w-3 mr-1.5 text-[#0070F3] flex-shrink-0" />
                    <span className="font-medium leading-tight">{appointmentStr}</span>
                  </div>
                </a>
              </Link>
            );
          })}
          {result.appointments.length === 0 && (
            <p className="text-xs text-gray-400 italic">No hay turnos próximos.</p>
          )}
        </div>
      </div>

      {/* Action Button */}
      <Link href={allTurnsBookingUrl} passHref legacyBehavior>
        <a target="_blank" rel="noopener noreferrer" className="block">
          <Button 
            className="w-full bg-[#0070F3] hover:bg-[#0070F3]/90 text-white h-8 text-xs rounded"
            size="sm"
          >
            <Calendar className="h-3.5 w-3.5 mr-1.5" />
            Ver todos los turnos
          </Button>
        </a>
      </Link>
    </div>
  );
} 