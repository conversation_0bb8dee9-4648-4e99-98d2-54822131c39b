// Notification service for appointment reminders
import { Appointment } from "@/types/scheduler"
import { Patient } from "@/types/patient"
import { Doctor } from "@/types/doctor"
import { MedicalCenter } from "@/types/medical-center"
import { mockEmailProvider } from "./email"
import { mockWhatsAppProvider } from "./whatsapp"
import {
  getAppointmentConfirmationEmailTemplate,
  getSameDayAppointmentConfirmationEmailTemplate,
  getAppointmentReminderEmailTemplate,
  getAppointmentCancellationEmailTemplate
} from "@/utils/notificationTemplates"
import {
  sendAppointmentConfirmationWhatsApp,
  sendAppointmentReminderWhatsApp,
  sendAppointmentFinalReminderWhatsApp,
  sendAppointmentCancellationWhatsApp
} from "./whatsapp"

// Interface for notification data
export interface NotificationData {
  appointment: Appointment
  patient: Patient
  doctor: Doctor
  medicalCenter: MedicalCenter
}

// Storage key for scheduled notifications
const SCHEDULED_NOTIFICATIONS_KEY = 'medical-scheduler-scheduled-notifications'

// Interface for scheduled notifications
export interface ScheduledNotification {
  id: string
  appointmentId: string
  type: 'reminder_1day' | 'reminder_3hours'
  scheduledFor: string // ISO date string
  notificationData: NotificationData
}

// Helper function to get scheduled notifications from storage
const getScheduledNotifications = (): ScheduledNotification[] => {
  if (typeof localStorage === 'undefined') return []

  const data = localStorage.getItem(SCHEDULED_NOTIFICATIONS_KEY)
  if (!data) return []

  try {
    return JSON.parse(data)
  } catch (e) {
    console.error("Error parsing scheduled notifications:", e)
    return []
  }
}

// Helper function to save scheduled notifications to storage
const saveScheduledNotifications = (notifications: ScheduledNotification[]): void => {
  if (typeof localStorage === 'undefined') return
  localStorage.setItem(SCHEDULED_NOTIFICATIONS_KEY, JSON.stringify(notifications))
}

// Export the helper functions for testing
export { getScheduledNotifications, saveScheduledNotifications }

// Helper function to determine preferred contact method
const getPreferredContactMethod = (patient: Patient): 'email' | 'whatsapp' => {
  // If patient has email, prefer email, otherwise use WhatsApp
  return patient.email && patient.email.trim() !== '' ? 'email' : 'whatsapp'
}

// Helper function to check if appointment is same day
const isSameDayAppointment = (appointment: Appointment): boolean => {
  // Get today's date in YYYY-MM-DD format (local timezone)
  const today = new Date()
  const todayStr = today.getFullYear() + '-' +
    String(today.getMonth() + 1).padStart(2, '0') + '-' +
    String(today.getDate()).padStart(2, '0')

  console.log(`[NOTIFICATIONS] Date comparison - Today: ${todayStr}, Appointment: ${appointment.date}, Same day: ${appointment.date === todayStr}`)

  // Compare date strings directly to avoid timezone issues
  return appointment.date === todayStr
}

// Helper function to calculate notification times
const calculateNotificationTimes = (appointment: Appointment): { oneDayBefore: Date; threeHoursBefore: Date } => {
  // Parse appointment date and time
  const appointmentDateTime = new Date(`${appointment.date}T${appointment.time}`)

  // Calculate 1 day before (at 8 PM the day before)
  const oneDayBefore = new Date(appointmentDateTime)
  oneDayBefore.setDate(oneDayBefore.getDate() - 1)
  oneDayBefore.setHours(20, 0, 0, 0) // 8 PM

  // Calculate 3 hours before
  const threeHoursBefore = new Date(appointmentDateTime)
  threeHoursBefore.setHours(threeHoursBefore.getHours() - 3)

  return { oneDayBefore, threeHoursBefore }
}

// Send immediate confirmation notification
export const sendAppointmentConfirmation = async (notificationData: NotificationData): Promise<void> => {
  const { appointment, patient, doctor, medicalCenter } = notificationData

  console.log(`[NOTIFICATIONS] Sending confirmation for appointment ${appointment.id}`)

  const isSameDay = isSameDayAppointment(appointment)

  if (isSameDay) {
    // For same-day appointments, only send email confirmation with special template
    if (patient.email && patient.email.trim() !== '') {
      const { subject, htmlContent } = getSameDayAppointmentConfirmationEmailTemplate(
        patient.name,
        doctor.name,
        appointment.date,
        appointment.time,
        medicalCenter.name,
        medicalCenter.address
      )

      await mockEmailProvider.sendEmail(patient.email, subject, htmlContent)
      console.log(`[NOTIFICATIONS] Same-day confirmation email sent to ${patient.email}`)
    } else {
      console.warn(`[NOTIFICATIONS] Same-day appointment but no email available for patient ${patient.name}`)
    }
  } else {
    // For future appointments, use normal logic
    const preferredMethod = getPreferredContactMethod(patient)

    if (preferredMethod === 'email' && patient.email) {
      // Send email confirmation
      const { subject, htmlContent } = getAppointmentConfirmationEmailTemplate(
        patient.name,
        doctor.name,
        appointment.date,
        appointment.time,
        medicalCenter.name,
        medicalCenter.address
      )

      await mockEmailProvider.sendEmail(patient.email, subject, htmlContent)
      console.log(`[NOTIFICATIONS] Confirmation email sent to ${patient.email}`)
    } else if (patient.phone) {
      // Send WhatsApp confirmation
      await sendAppointmentConfirmationWhatsApp(
        patient.phone,
        patient.name,
        doctor.name,
        appointment.date,
        appointment.time,
        medicalCenter.name
      )
      console.log(`[NOTIFICATIONS] Confirmation WhatsApp sent to ${patient.phone}`)
    }
  }
}

// Schedule future notifications for an appointment
export const scheduleAppointmentNotifications = (notificationData: NotificationData): void => {
  const { appointment } = notificationData

  console.log(`[NOTIFICATIONS] Scheduling notifications for appointment ${appointment.id}`)

  // Skip scheduling reminders for same-day appointments
  if (isSameDayAppointment(appointment)) {
    console.log(`[NOTIFICATIONS] Skipping reminder scheduling for same-day appointment ${appointment.id}`)
    return
  }

  const { oneDayBefore, threeHoursBefore } = calculateNotificationTimes(appointment)
  const now = new Date()

  const scheduledNotifications = getScheduledNotifications()

  // Schedule 1-day reminder (only if it's in the future)
  if (oneDayBefore > now) {
    const reminderNotification: ScheduledNotification = {
      id: `${appointment.id}_1day`,
      appointmentId: appointment.id,
      type: 'reminder_1day',
      scheduledFor: oneDayBefore.toISOString(),
      notificationData
    }
    scheduledNotifications.push(reminderNotification)
    console.log(`[NOTIFICATIONS] Scheduled 1-day reminder for ${oneDayBefore.toISOString()}`)
  }

  // Schedule 3-hour reminder (only if it's in the future)
  if (threeHoursBefore > now) {
    const finalReminderNotification: ScheduledNotification = {
      id: `${appointment.id}_3hours`,
      appointmentId: appointment.id,
      type: 'reminder_3hours',
      scheduledFor: threeHoursBefore.toISOString(),
      notificationData
    }
    scheduledNotifications.push(finalReminderNotification)
    console.log(`[NOTIFICATIONS] Scheduled 3-hour reminder for ${threeHoursBefore.toISOString()}`)
  }

  saveScheduledNotifications(scheduledNotifications)
}

// Send cancellation notification
export const sendAppointmentCancellation = async (
  notificationData: NotificationData,
  reason?: string
): Promise<void> => {
  const { appointment, patient, doctor, medicalCenter } = notificationData

  console.log(`[NOTIFICATIONS] Sending cancellation for appointment ${appointment.id}`)

  // Send cancellation only via email (as per requirements)
  if (patient.email && patient.email.trim() !== '') {
    // Send email cancellation
    const { subject, htmlContent } = getAppointmentCancellationEmailTemplate(
      patient.name,
      doctor.name,
      appointment.date,
      appointment.time,
      medicalCenter.name,
      reason
    )

    await mockEmailProvider.sendEmail(patient.email, subject, htmlContent)
    console.log(`[NOTIFICATIONS] Cancellation email sent to ${patient.email}`)
  } else {
    console.warn(`[NOTIFICATIONS] No email available for patient ${patient.name}, cancellation notification not sent`)
  }

  // Remove any scheduled notifications for this appointment
  cancelScheduledNotifications(appointment.id)
}

// Cancel scheduled notifications for an appointment
export const cancelScheduledNotifications = (appointmentId: string): void => {
  console.log(`[NOTIFICATIONS] Cancelling scheduled notifications for appointment ${appointmentId}`)

  const scheduledNotifications = getScheduledNotifications()
  const filteredNotifications = scheduledNotifications.filter(
    notification => notification.appointmentId !== appointmentId
  )

  saveScheduledNotifications(filteredNotifications)
}

// Process due notifications (this would be called by a background job in production)
export const processDueNotifications = async (): Promise<void> => {
  const now = new Date()
  const scheduledNotifications = getScheduledNotifications()

  const dueNotifications = scheduledNotifications.filter(
    notification => new Date(notification.scheduledFor) <= now
  )

  if (dueNotifications.length === 0) {
    return
  }

  console.log(`[NOTIFICATIONS] Processing ${dueNotifications.length} due notifications`)

  for (const notification of dueNotifications) {
    const { notificationData, type } = notification
    const { appointment, patient, doctor, medicalCenter } = notificationData

    try {
      if (type === 'reminder_1day') {
        // Send 1-day reminder
        const preferredMethod = getPreferredContactMethod(patient)

        if (preferredMethod === 'email' && patient.email) {
          const { subject, htmlContent } = getAppointmentReminderEmailTemplate(
            patient.name,
            doctor.name,
            appointment.date,
            appointment.time,
            medicalCenter.name,
            medicalCenter.address
          )

          await mockEmailProvider.sendEmail(patient.email, subject, htmlContent)
          console.log(`[NOTIFICATIONS] 1-day reminder email sent to ${patient.email}`)
        } else if (patient.phone) {
          await sendAppointmentReminderWhatsApp(
            patient.phone,
            patient.name,
            doctor.name,
            appointment.date,
            appointment.time,
            medicalCenter.name
          )
          console.log(`[NOTIFICATIONS] 1-day reminder WhatsApp sent to ${patient.phone}`)
        }
      } else if (type === 'reminder_3hours') {
        // Send 3-hour reminder (always via WhatsApp)
        if (patient.phone) {
          await sendAppointmentFinalReminderWhatsApp(
            patient.phone,
            patient.name,
            doctor.name,
            appointment.time,
            medicalCenter.name
          )
          console.log(`[NOTIFICATIONS] 3-hour reminder WhatsApp sent to ${patient.phone}`)
        }
      }
    } catch (error) {
      console.error(`[NOTIFICATIONS] Error sending notification ${notification.id}:`, error)
    }
  }

  // Remove processed notifications
  const remainingNotifications = scheduledNotifications.filter(
    notification => !dueNotifications.some(due => due.id === notification.id)
  )

  saveScheduledNotifications(remainingNotifications)
}

// Main function to handle new appointment notifications
export const handleNewAppointment = async (notificationData: NotificationData): Promise<void> => {
  console.log(`[NOTIFICATIONS] Handling new appointment ${notificationData.appointment.id}`)

  // Send immediate confirmation
  await sendAppointmentConfirmation(notificationData)

  // Schedule future reminders
  scheduleAppointmentNotifications(notificationData)
}

// Main function to handle appointment cancellation notifications
export const handleAppointmentCancellation = async (
  notificationData: NotificationData,
  reason?: string
): Promise<void> => {
  console.log(`[NOTIFICATIONS] Handling appointment cancellation ${notificationData.appointment.id}`)

  // Send cancellation notification
  await sendAppointmentCancellation(notificationData, reason)
}

// Function to manually process due notifications (for testing)
export const processAllDueNotifications = async (): Promise<{ processed: number; errors: number }> => {
  console.log(`[NOTIFICATIONS] Manually processing all due notifications`)

  const now = new Date()
  const scheduledNotifications = getScheduledNotifications()

  const dueNotifications = scheduledNotifications.filter(
    notification => new Date(notification.scheduledFor) <= now
  )

  let processed = 0
  let errors = 0

  console.log(`[NOTIFICATIONS] Found ${dueNotifications.length} due notifications to process`)

  for (const notification of dueNotifications) {
    try {
      await processSingleNotification(notification)
      processed++
    } catch (error) {
      console.error(`[NOTIFICATIONS] Error processing notification ${notification.id}:`, error)
      errors++
    }
  }

  // Remove processed notifications
  const remainingNotifications = scheduledNotifications.filter(
    notification => !dueNotifications.some(due => due.id === notification.id)
  )

  saveScheduledNotifications(remainingNotifications)

  console.log(`[NOTIFICATIONS] Processed ${processed} notifications, ${errors} errors`)

  return { processed, errors }
}

// Helper function to process a single notification
const processSingleNotification = async (notification: ScheduledNotification): Promise<void> => {
  const { notificationData, type } = notification
  const { appointment, patient, doctor, medicalCenter } = notificationData

  if (type === 'reminder_1day') {
    // Send 1-day reminder
    const preferredMethod = getPreferredContactMethod(patient)

    if (preferredMethod === 'email' && patient.email) {
      const { subject, htmlContent } = getAppointmentReminderEmailTemplate(
        patient.name,
        doctor.name,
        appointment.date,
        appointment.time,
        medicalCenter.name,
        medicalCenter.address
      )

      await mockEmailProvider.sendEmail(patient.email, subject, htmlContent)
      console.log(`[NOTIFICATIONS] 1-day reminder email sent to ${patient.email}`)
    } else if (patient.phone) {
      await sendAppointmentReminderWhatsApp(
        patient.phone,
        patient.name,
        doctor.name,
        appointment.date,
        appointment.time,
        medicalCenter.name
      )
      console.log(`[NOTIFICATIONS] 1-day reminder WhatsApp sent to ${patient.phone}`)
    }
  } else if (type === 'reminder_3hours') {
    // Send 3-hour reminder (always via WhatsApp)
    if (patient.phone) {
      await sendAppointmentFinalReminderWhatsApp(
        patient.phone,
        patient.name,
        doctor.name,
        appointment.time,
        medicalCenter.name
      )
      console.log(`[NOTIFICATIONS] 3-hour reminder WhatsApp sent to ${patient.phone}`)
    }
  }
}

// Function to get all scheduled notifications (for debugging)
export const getAllScheduledNotifications = (): ScheduledNotification[] => {
  return getScheduledNotifications()
}

// Function to clear all scheduled notifications (for testing)
export const clearAllScheduledNotifications = (): void => {
  saveScheduledNotifications([])
  console.log(`[NOTIFICATIONS] Cleared all scheduled notifications`)
}
