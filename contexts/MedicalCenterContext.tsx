"use client"

import {createContext, useEffect, useRef, useState} from "react"
import {MedicalCenter} from "@/types/medical-center"

// TODO FACU: Remove storage usage and implement proper API calls
interface MedicalCenterContextType {
    medicalCenters: MedicalCenter[];
    setMedicalCenters: React.Dispatch<React.SetStateAction<MedicalCenter[]>>;
    activeMedicalCenterId: string;
    setActiveMedicalCenterId: (id: string) => void;
    updateMedicalCenter: (updatedCenter: MedicalCenter) => void;
    syncWithUrl: (urlCenterId: string) => void;
}

// Use the local interface for the context
export const MedicalCenterContext = createContext<MedicalCenterContextType>({
    medicalCenters: [],
    setMedicalCenters: () => {
    },
    activeMedicalCenterId: "",
    setActiveMedicalCenterId: () => {
    },
    updateMedicalCenter: () => {
    },
    syncWithUrl: () => {
    },
})

export const MedicalCenterProvider = ({children}: { children: React.ReactNode }) => {
    // Initialize with empty array to avoid hydration mismatch
    const [medicalCenters, setMedicalCenters] = useState<MedicalCenter[]>([]);
    const [activeMedicalCenterId, setActiveMedicalCenterId] = useState<string>("");

    // Ref to track the last time we set active medical center
    const lastSetActiveTimeRef = useRef<number>(0);
    const pendingIdRef = useRef<string | null>(null);

    // Track URL-based center ID changes
    const lastUrlCenterIdRef = useRef<string | null>(null);

    // Method to sync active center with URL-provided center ID
    const syncWithUrl = (urlCenterId: string) => {
        if (!urlCenterId || urlCenterId === lastUrlCenterIdRef.current) return;

        console.log(`Syncing active medical center from URL: ${urlCenterId}`);
        lastUrlCenterIdRef.current = urlCenterId;

        // Always set active center to URL center
        setActiveMedicalCenterId(urlCenterId);
        // TODO FACU: Replace with API call to save active medical center
    };

    // Debounced function to set active medical center ID
    const debouncedSetActiveMedicalCenterId = (id: string) => {
        const now = Date.now();
        // If less than 500ms have passed since the last set, store the ID for later
        if (now - lastSetActiveTimeRef.current < 500) {
            pendingIdRef.current = id;
            return;
        }

        try {
            // Update the last set time
            lastSetActiveTimeRef.current = now;

            // Actually set the ID
            if (id !== activeMedicalCenterId) {
                setActiveMedicalCenterId(id);
                // TODO FACU: Replace with API call to save active medical center
            }

            // If there was a pending ID, process it after a delay
            if (pendingIdRef.current && pendingIdRef.current !== id) {
                const pendingId = pendingIdRef.current;
                pendingIdRef.current = null;
                setTimeout(() => {
                    debouncedSetActiveMedicalCenterId(pendingId);
                }, 500);
            }
        } catch (e) {
            console.error("Error setting active medical center ID:", e);
        }
    };

    // Reference to track latest state values for event handlers
    const latestStateRef = useRef({
        medicalCenters: [] as MedicalCenter[],
        activeMedicalCenterId: ''
    });

    // Update the ref whenever state changes
    useEffect(() => {
        latestStateRef.current = {
            medicalCenters,
            activeMedicalCenterId
        };
    }, [medicalCenters, activeMedicalCenterId]);

    // Client-side initialization - runs only once at component mount
    useEffect(() => {
        // TODO FACU: Replace with API call to load medical centers
        const storedCenters: MedicalCenter[] = [];
        setMedicalCenters(storedCenters);

        // TODO FACU: Replace with API call to load active medical center
        const storedActiveId = "";
        setActiveMedicalCenterId(storedActiveId);

        // TODO FACU: Replace storage event subscriptions with real-time API subscriptions
        // Cleanup subscriptions when component unmounts
        return () => {
            // TODO FACU: Cleanup API subscriptions
        };
    }, []);

    // Function to update a medical center
    const updateMedicalCenter = (updatedCenter: MedicalCenter) => {
        setMedicalCenters(prev =>
            prev.map(center =>
                center.id === updatedCenter.id ? updatedCenter : center
            )
        )
        // TODO FACU: Replace with API call to update medical center
    }

    // TODO FACU: Remove storage update effects and replace with API calls
    // Set first center as active if none is set
    useEffect(() => {
        if (medicalCenters.length > 0 && !activeMedicalCenterId) {
            setActiveMedicalCenterId(medicalCenters[0].id);
        }
    }, [medicalCenters, activeMedicalCenterId]);

    return (
        <MedicalCenterContext.Provider
            value={{
                medicalCenters,
                setMedicalCenters,
                activeMedicalCenterId,
                setActiveMedicalCenterId: debouncedSetActiveMedicalCenterId,
                updateMedicalCenter,
                syncWithUrl,
            }}
        >
            {children}
        </MedicalCenterContext.Provider>
    )
}