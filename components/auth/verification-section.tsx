import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface VerificationSectionProps {
  patientVerified: boolean;
  registerPhone: string;
  originalVerifiedPhone: string;
  registerEmail: string;
  phoneVerificationCode: string;
  setPhoneVerificationCode: (code: string) => void;
  emailVerificationCode: string;
  setEmailVerificationCode: (code: string) => void;
  actualPhoneCode: string;
  actualEmailCode: string;
  handleVerificationSubmit: () => void;
  registerError: string;
}

export function VerificationSection({
  patientVerified,
  registerPhone,
  originalVerifiedPhone,
  registerEmail,
  phoneVerificationCode,
  setPhoneVerificationCode,
  emailVerificationCode,
  setEmailVerificationCode,
  actualPhoneCode,
  actualEmailCode,
  handleVerificationSubmit,
  registerError
}: VerificationSectionProps) {
  return (
    <div className="mt-4 p-4 border border-blue-200 rounded-lg bg-blue-50">
      <h3 className="font-medium text-blue-800 mb-2">Verificación de contacto</h3>
      
      {patientVerified && registerPhone === originalVerifiedPhone ? (
        <p className="text-xs sm:text-sm text-blue-700 mb-4">
          Hemos enviado un código de verificación a su correo electrónico. 
          Por favor, ingréselo a continuación para continuar.
        </p>
      ) : patientVerified && registerPhone !== originalVerifiedPhone ? (
        <p className="text-xs sm:text-sm text-blue-700 mb-4">
          Ha cambiado su número de teléfono. Hemos enviado códigos de verificación 
          a su nuevo teléfono {registerEmail && "y correo electrónico"}. 
          Por favor, ingréselos a continuación para continuar.
        </p>
      ) : (
        <p className="text-xs sm:text-sm text-blue-700 mb-4">
          Hemos enviado códigos de verificación a su teléfono y correo electrónico. 
          Por favor, ingréselos a continuación para continuar.
        </p>
      )}

      <div className="space-y-4">
        {(!patientVerified || registerPhone !== originalVerifiedPhone) && (
          <div className="space-y-2">
            <Label htmlFor="phoneVerificationCode">Código de verificación de teléfono</Label>
            <Input
              id="phoneVerificationCode"
              value={phoneVerificationCode}
              onChange={(e) => setPhoneVerificationCode(e.target.value)}
              placeholder="Ingrese el código de 6 dígitos"
            />
            <p className="text-[10px] sm:text-xs text-blue-600 mt-1 font-bold">
              Código de desarrollo: {actualPhoneCode}
            </p>
          </div>
        )}

        {registerEmail && (
          <div className="space-y-2">
            <Label htmlFor="emailVerificationCode">Código de verificación de email</Label>
            <Input
              id="emailVerificationCode"
              value={emailVerificationCode}
              onChange={(e) => setEmailVerificationCode(e.target.value)}
              placeholder="Ingrese el código de 6 dígitos"
            />
            <p className="text-[10px] sm:text-xs text-blue-600 mt-1 font-bold">
              Código de desarrollo: {actualEmailCode}
            </p>
          </div>
        )}

        {registerError && (
          <div className="bg-red-50 p-2 sm:p-3 rounded-md text-red-600 text-xs sm:text-sm">
            {registerError}
          </div>
        )}

        <Button
          onClick={handleVerificationSubmit}
          className="w-full bg-blue-600 hover:bg-blue-700 mt-4"
          disabled={
            (patientVerified && registerPhone === originalVerifiedPhone
              ? (registerEmail !== "" && emailVerificationCode === "")
              : (phoneVerificationCode === "" || (registerEmail !== "" && emailVerificationCode === "")))
          }
        >
          Verificar y continuar
        </Button>
      </div>
    </div>
  );
}
