import { ConsultationType, DoctorConfiguration } from "@/types/doctor"
import { Dispatch, SetStateAction } from "react"

export function useConsultationTypeActions(
  editedConfig: DoctorConfiguration | null,
  setEditedConfig: Dispatch<SetStateAction<DoctorConfiguration | null>> // Explicitly typed
) {
  const updateConsultationType = (
    name: string,
    field: keyof ConsultationType,
    value: boolean | string | number | { start: string; end: string } | { coverageId: string; planId: string | null; amount: number }[] | { coverageId: string; planId: string | null }[]
  ) => {
    if (editedConfig) {
      setEditedConfig((prevConfig: DoctorConfiguration | null) => {
        if (!prevConfig) return null
        return {
          ...prevConfig,
          consultationTypes: prevConfig.consultationTypes.map(t =>
            t.name === name ? { ...t, [field]: value } : t
          )
        }
      })
    }
  }

  const addCopay = (name: string, coverageId: string, amount: number, planId: string | null = null) => {
    if (editedConfig) {
      setEditedConfig((prevConfig: DoctorConfiguration | null) => {
        if (!prevConfig) return null
        return {
          ...prevConfig,
          consultationTypes: prevConfig.consultationTypes.map(t =>
            t.name === name ? { ...t, copays: [...(t.copays || []), { coverageId, planId, amount }] } : t
          )
        }
      })
    }
  }

  return { updateConsultationType, addCopay }
}