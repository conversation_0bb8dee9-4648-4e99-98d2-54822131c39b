// WhatsApp service for sending messages
import { v4 as uuidv4 } from 'uuid';

// WhatsApp service interface
export interface WhatsAppProvider {
  sendMessage(to: string, message: string): Promise<boolean>;
}

// Mock WhatsApp message interface for storage
export interface MockWhatsAppMessage {
  id: string;
  to: string;
  message: string;
  sentAt: string;
  read: boolean;
}

// Storage key for mock WhatsApp messages
const MOCK_WHATSAPP_MESSAGES_STORAGE_KEY = 'medical-scheduler-mock-whatsapp-messages';

// Mock WhatsApp provider for development
class MockWhatsAppProvider implements WhatsAppProvider {
  async sendMessage(to: string, message: string): Promise<boolean> {
    console.log(`[MOCK WHATSAPP] To: ${to}, Message: ${message}`);

    // Store the message in local storage for preview
    const messages = this.getMockMessages();
    const newMessage: MockWhatsAppMessage = {
      id: uuidv4(),
      to,
      message,
      sentAt: new Date().toISOString(),
      read: false
    };

    this.saveMockMessages([...messages, newMessage]);

    // In development, always return true to simulate successful sending
    return true;
  }

  // Helper methods to manage mock messages
  getMockMessages(): MockWhatsAppMessage[] {
    if (typeof localStorage === 'undefined') return [];

    const data = localStorage.getItem(MOCK_WHATSAPP_MESSAGES_STORAGE_KEY);
    if (!data) return [];

    try {
      return JSON.parse(data);
    } catch (e) {
      console.error("Error parsing mock WhatsApp messages from localStorage:", e);
      return [];
    }
  }

  saveMockMessages(messages: MockWhatsAppMessage[]): void {
    if (typeof localStorage === 'undefined') return;
    localStorage.setItem(MOCK_WHATSAPP_MESSAGES_STORAGE_KEY, JSON.stringify(messages));
  }

  markMessageAsRead(messageId: string): void {
    if (typeof localStorage === 'undefined') return;

    const messages = this.getMockMessages();
    const updatedMessages = messages.map(message =>
      message.id === messageId ? { ...message, read: true } : message
    );

    this.saveMockMessages(updatedMessages);
  }

  clearAllMessages(): void {
    if (typeof localStorage === 'undefined') return;
    localStorage.setItem(MOCK_WHATSAPP_MESSAGES_STORAGE_KEY, JSON.stringify([]));
  }
}

// Default provider - will be replaced with a real provider in production
let whatsappProvider: WhatsAppProvider = new MockWhatsAppProvider();

// Function to set a different WhatsApp provider
export function setWhatsAppProvider(provider: WhatsAppProvider) {
  whatsappProvider = provider;
}

// Export the mock provider for direct access to its methods
export const mockWhatsAppProvider = new MockWhatsAppProvider();

// Helper function to send appointment confirmation WhatsApp
export async function sendAppointmentConfirmationWhatsApp(
  phone: string,
  patientName: string,
  doctorName: string,
  date: string,
  time: string,
  medicalCenterName: string
): Promise<boolean> {
  const message = `¡Hola ${patientName}! Tu turno con ${doctorName} fue confirmado para el ${date} a las ${time} en ${medicalCenterName}. Te vamos a recordar 1 día antes de tu turno. ¡Nos vemos! 👩‍⚕️`;

  try {
    return await whatsappProvider.sendMessage(phone, message);
  } catch (error) {
    console.error("Error sending appointment confirmation WhatsApp:", error);
    return false;
  }
}

// Helper function to send appointment reminder WhatsApp (1 day before)
export async function sendAppointmentReminderWhatsApp(
  phone: string,
  patientName: string,
  doctorName: string,
  date: string,
  time: string,
  medicalCenterName: string
): Promise<boolean> {
  const message = `¡Hola ${patientName}! Te recordamos que mañana tenés turno con ${doctorName} a las ${time} en ${medicalCenterName}. Por favor, llegá 15 minutos antes. ¡Te esperamos! 📅`;

  try {
    return await whatsappProvider.sendMessage(phone, message);
  } catch (error) {
    console.error("Error sending appointment reminder WhatsApp:", error);
    return false;
  }
}

// Helper function to send appointment final reminder WhatsApp (3 hours before)
export async function sendAppointmentFinalReminderWhatsApp(
  phone: string,
  patientName: string,
  doctorName: string,
  time: string,
  medicalCenterName: string
): Promise<boolean> {
  const message = `¡Hola ${patientName}! Tu turno con ${doctorName} es hoy a las ${time} en ${medicalCenterName}. No te olvides de llegar 15 minutos antes. ¡Nos vemos en un ratito! ⏰`;

  try {
    return await whatsappProvider.sendMessage(phone, message);
  } catch (error) {
    console.error("Error sending appointment final reminder WhatsApp:", error);
    return false;
  }
}

// Helper function to send appointment cancellation WhatsApp
export async function sendAppointmentCancellationWhatsApp(
  phone: string,
  patientName: string,
  doctorName: string,
  date: string,
  time: string,
  reason?: string
): Promise<boolean> {
  const reasonText = reason ? ` Motivo: ${reason}` : '';
  const message = `Hola ${patientName}, lamentamos informarte que tu turno con ${doctorName} del ${date} a las ${time} fue cancelado.${reasonText} Por favor, contactá al centro médico para reagendar. Disculpá las molestias. 🙏`;

  try {
    return await whatsappProvider.sendMessage(phone, message);
  } catch (error) {
    console.error("Error sending appointment cancellation WhatsApp:", error);
    return false;
  }
}
