"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { MultiDoctorForm } from "./MultiDoctorForm"
import { MultiMedicalCenterForm } from "./MultiMedicalCenterForm"

export function AdminBulkCreateForms() {
  return (
    <div className="w-full max-w-8xl mx-auto py-4 px-0">
      <h2 className="text-lg font-medium mb-6">Creación Masiva</h2>
      
      <Tabs defaultValue="doctors" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="doctors">Profesionales</TabsTrigger>
          <TabsTrigger value="centers">Establecimientos</TabsTrigger>
        </TabsList>
        
        <TabsContent value="doctors">
          <MultiDoctorForm />
        </TabsContent>
        
        <TabsContent value="centers">
          <MultiMedicalCenterForm />
        </TabsContent>
      </Tabs>
    </div>
  )
} 