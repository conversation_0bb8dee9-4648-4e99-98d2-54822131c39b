"use client"

import React from "react"
import { Check } from "lucide-react"
import { Doctor } from "@/types/doctor"
import { MedicalCenter } from "@/types/medical-center"
import { ReservationSummary } from "./ReservationSummary"

interface ConfirmationStepProps {
  doctor: Doctor
  medicalCenter: MedicalCenter
  patientName: string
  patientLastName: string
  patientEmail: string
  selectedDate: Date | null
  confirmedTime: string | null
  selectedTime: string | null
  selectedUrlCoverage: string | null
  selectedConsultationType: string | null
  getPricingInfo: () => React.ReactNode
  isModifying?: boolean
}

export const ConfirmationStep: React.FC<ConfirmationStepProps> = ({
  doctor,
  medicalCenter,
  patientName,
  patientLastName,
  patientEmail,
  selectedDate,
  confirmedTime,
  selectedTime,
  selectedUrlCoverage,
  selectedConsultationType,
  getPricingInfo,
  isModifying = false,
}) => {
  return (
    <div className="space-y-6">
      {/* Success Message */}
      <div className="text-center">
        <div className="bg-green-100 rounded-full h-16 w-16 sm:h-20 sm:w-20 flex items-center justify-center mx-auto mb-3 sm:mb-4">
          <Check className="h-8 w-8 sm:h-10 sm:w-10 text-green-600" />
        </div>
        <h3 className="text-lg sm:text-xl font-medium text-gray-900 mb-2 sm:mb-3">
          {isModifying ? '¡Turno Modificado!' : '¡Reserva Confirmada!'}
        </h3>
        <p className="text-gray-600 text-sm sm:text-base mb-4 sm:mb-6">
          {isModifying
            ? 'Su turno ha sido modificado exitosamente.'
            : 'Su turno ha sido reservado exitosamente.'}
        </p>
      </div>

      {/* Unified Reservation Summary */}
      <ReservationSummary
        doctor={doctor}
        medicalCenter={medicalCenter}
        selectedDate={selectedDate}
        selectedTime={selectedTime}
        confirmedTime={confirmedTime}
        selectedConsultationType={selectedConsultationType}
        coverage={selectedUrlCoverage}
        patientName={patientName}
        patientLastName={patientLastName}
        patientEmail={patientEmail}
        showPatientInfo={true}
        showEmailNotification={true}
        getPricingInfo={getPricingInfo}
        variant="confirmation"
      />
    </div>
  )
}
