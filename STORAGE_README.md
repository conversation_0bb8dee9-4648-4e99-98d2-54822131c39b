# Medical Scheduler Data Management

This document explains the data management architecture in the Medical Scheduler application.

## Overview

The application uses a combination of:
- React Context API for state management
- Local Storage for data persistence
- Storage Events for real-time cross-tab synchronization
- Initial demo data for first-time setup

## Key Components

### 1. Initial Data

All initial data is consolidated in a single file:
- `data/initialData.ts`: Contains `initialMedicalCenters` and `initialDoctors`

For backward compatibility and clean imports, the following files simply re-export from initialData.ts:
- `data/medicalCenters.ts`: Re-exports initialMedicalCenters as medicalCenters
- `data/doctors.ts`: Re-exports initialDoctors as doctors

This approach ensures a single source of truth while maintaining compatibility with any code that might import from legacy locations.

### 2. Storage Services

#### Storage Service

The storage service in `services/storage.ts` handles interactions with localStorage:
- Consistent storage keys across the application
- Built-in fallbacks to initial data
- Methods for getting and saving data
- Helper for initializing storage
- Doctor ID mapping for consistent reference
- Support for unassigned doctors (not tied to any medical center)

#### Storage Events Service

The storage events service in `services/storageEvents.ts` enables real-time updates across browser tabs:
- Pub/sub pattern for storage change notifications
- Type-safe event subscription system
- Automatic event dispatching when storage changes
- Support for different data types (medical centers, doctors, appointments)

### 3. Context Providers

The application uses several context providers with real-time update capabilities:

- `MedicalCenterContext`: 
  - Manages medical centers and the active center
  - Subscribes to storage events for cross-tab synchronization
  - Provides URL-based center ID synchronization
  - Implements debounced updates to prevent race conditions

- `DoctorContext`: 
  - Manages doctors for the active medical center
  - Subscribes to doctor updates from other tabs
  - Prevents circular updates with update tracking
  - Provides doctor lookup by ID across medical centers

- `AppointmentContext`: 
  - Manages appointments with real-time updates
  - Listens for appointment changes from other tabs
  - Implements debounced storage updates

### 4. Data Flow

The flow of data in the application follows this pattern:
1. Context providers initialize with initial data
2. Context providers load data from localStorage when available
3. Components use context hooks to access data
4. Context providers save data changes to localStorage
5. Storage events are triggered to notify other tabs of changes
6. Subscribed contexts in other tabs receive updates and refresh their state
7. New page loads retrieve data from localStorage via context

## Storage Keys

The application uses the following storage keys:
- `medical-scheduler-centers`: All medical centers
- `medical-scheduler-active-center`: ID of the active medical center
- `medical-scheduler-doctors_[centerId]`: Doctors for a specific medical center
- `medical-scheduler-doctor-id-map`: Map of doctor MN to doctor ID for consistent reference
- `medical-scheduler-unassigned-doctors`: Doctors not assigned to any medical center
- `appointments`: Patient appointments organized by date

## Usage Guidelines

1. **Always use context** to access data instead of importing initial data directly.
2. Use `useContext(MedicalCenterContext)` and `useContext(DoctorContext)` to access their respective data.
3. When creating new pages/components that need to know the current medical center, call `setActiveMedicalCenterId` in a `useEffect` hook.
4. For components that need to initialize data, use the `storage.initializeStorage()` method.
5. To subscribe to real-time updates, use the `subscribeToStorageEvent` function from `storageEvents.ts`.
6. When making changes that should be reflected across tabs, use the appropriate context methods which will handle both state updates and storage events.

## Real-Time Updates

The application supports real-time updates across multiple browser tabs:

1. **Event Types**
   - `MEDICAL_CENTERS_UPDATED`: When medical centers are added, edited, or removed
   - `ACTIVE_MEDICAL_CENTER_CHANGED`: When the active medical center changes
   - `DOCTORS_UPDATED`: When doctors for a specific medical center are updated
   - `UNASSIGNED_DOCTORS_UPDATED`: When unassigned doctors are modified
   - `APPOINTMENTS_UPDATED`: When appointments are added, edited, or removed

2. **Subscribing to Events**
   ```tsx
   
   useEffect(() => {
     const unsubscribe = subscribeToStorageEvent(
       StorageEventType.DOCTORS_UPDATED,
       (newValue) => {
         // Handle the updated doctors
         console.log('Doctors updated from another tab:', newValue);
       }
     );
     
     // Clean up subscription when component unmounts
     return unsubscribe;
   }, []);
   ```

3. **Triggering Events**
   ```tsx
   
   // Trigger an event for the current tab
   triggerStorageEvent(
     StorageEventType.DOCTORS_UPDATED, 
     { medicalCenterId, doctors }, 
     oldValue
   );
   ```

## Example Usage

```tsx
import { useContext, useEffect } from "react";
import { MedicalCenterContext } from "@/contexts/MedicalCenterContext";
import { DoctorContext } from "@/contexts/DoctorContext";
import { useParams } from "next/navigation";

export default function MyComponent() {
  const params = useParams();
  const medicalCenterId = params.medicalCenterId as string;
  
  // Get data from contexts
  const { medicalCenters, setActiveMedicalCenterId } = useContext(MedicalCenterContext);
  const { doctors } = useContext(DoctorContext);
  
  // Set the active medical center ID when the component mounts
  useEffect(() => {
    if (medicalCenterId) {
      setActiveMedicalCenterId(medicalCenterId);
    }
  }, [medicalCenterId, setActiveMedicalCenterId]);
  
  // Find the medical center
  const medicalCenter = medicalCenters.find(mc => mc.id === medicalCenterId);
  
  // Rest of component...
}
```