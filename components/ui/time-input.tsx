"use client"

import { Input } from "./input"
import type React from "react" // Import React

interface TimeInputProps {
  value: string
  onChange: (value: string) => void
}

export function TimeInput({ value, onChange }: TimeInputProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    // Basic validation for time format
    if (/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(newValue)) {
      onChange(newValue)
    }
  }

  return <Input type="time" value={value} onChange={handleChange} className="w-32" />
}